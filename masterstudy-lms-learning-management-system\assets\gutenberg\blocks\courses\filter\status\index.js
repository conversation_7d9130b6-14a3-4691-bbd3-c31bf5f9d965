(()=>{var e,t={4288:(e,t,n)=>{"use strict";const a=window.React,l=window.wp.blocks,r=window.wp.i18n;var s=n(6942),i=n.n(s);const o=window.wp.blockEditor,m=window.wp.element,c=(0,m.createContext)(null),d=({children:e,...t})=>(0,a.createElement)(c.Provider,{value:{...t}},e),u=window.wp.components,g=({condition:e,fallback:t=null,children:n})=>(0,a.createElement)(a.Fragment,null,e?n:t),p=(e,t)=>{if(typeof e!=typeof t)return!1;if(Number.isNaN(e)&&Number.isNaN(t))return!0;if("object"!=typeof e||null===e||null===t)return e===t;if(Object.keys(e).length!==Object.keys(t).length)return!1;if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;const n=e.slice().sort(),a=t.slice().sort();return n.every(((e,t)=>p(e,a[t])))}for(const n of Object.keys(e))if(!p(e[n],t[n]))return!1;return!0};let h=function(e){return e.ALL="all",e.SOME="some",e}({}),v=function(e){return e.NORMAL="Normal",e.HOVER="Hover",e.ACTIVE="Active",e.FOCUS="Focus",e}({}),_=function(e){return e.DESKTOP="Desktop",e.TABLET="Tablet",e.MOBILE="Mobile",e}({}),y=function(e){return e.TOP_lEFT="top-left",e.TOP_CENTER="top-center",e.TOP_RIGHT="top-right",e.BOTTOM_lEFT="bottom-left",e.BOTTOM_CENTER="bottom-center",e.BOTTOM_RIGHT="bottom-right",e}({});const b=["",null,void 0,"null","undefined"],E=[".jpg",".jpeg",".png",".gif"],f=e=>b.includes(e),C=(e,t,n="")=>{const a=e[t];return"object"==typeof a&&null!==a?((e,t)=>{return n=e,Object.values(n).every((e=>b.includes(e)))?null:((e,t="")=>{const n=Object.entries(e).reduce(((e,[n,a])=>(e[n]=(a||"0")+t,e)),{});return`${n.top} ${n.right} ${n.bottom} ${n.left}`})(e,t);var n})(a,n):((e,t)=>f(e)?e:(e=>{if("string"!=typeof e)return!1;const t=e.slice(e.lastIndexOf("."));return E.includes(t)||e.startsWith("blob")})(e)?`url('${e}')`:String(e+t))(a,n)},N=(e,t,n)=>{const a={};return n.forEach((({isAdaptive:n,hasHover:l,unit:r},s)=>{if(t.hasOwnProperty(s)){const{unitMeasureDesktop:o,unitMeasureTablet:m,unitMeasureMobile:c}=((e,t)=>{var n;return{unitMeasureDesktop:null!==(n=e[t])&&void 0!==n?n:"",unitMeasureTablet:t?e[t+"Tablet"]:"",unitMeasureMobile:t?e[t+"Mobile"]:""}})(t,r);if(n&&l){const{desktopHoverPropertyName:n,mobileHoverPropertyName:l,tabletHoverPropertyName:r}=(e=>{const t=e+v.HOVER;return{desktopHoverPropertyName:t,tabletHoverPropertyName:t+"Tablet",mobileHoverPropertyName:t+"Mobile"}})(s),i=C(t,n,o);f(i)||(a[`--lms-${e}-${n}`]=i);const d=C(t,r,m);f(d)||(a[`--lms-${e}-${r}`]=d);const u=C(t,l,c);f(u)||(a[`--lms-${e}-${l}`]=u)}if(l){const n=s+v.HOVER,l=C(t,n,o);f(l)||(a[`--lms-${e}-${n}`]=l)}if(n){const{desktopPropertyName:n,mobilePropertyName:l,tabletPropertyName:r}={desktopPropertyName:i=s,tabletPropertyName:i+"Tablet",mobilePropertyName:i+"Mobile"},d=C(t,n,o);f(d)||(a[`--lms-${e}-${n}`]=d);const u=C(t,r,m);f(u)||(a[`--lms-${e}-${r}`]=u);const g=C(t,l,c);f(g)||(a[`--lms-${e}-${l}`]=g)}const d=C(t,s,o);f(d)||(a[`--lms-${e}-${s}`]=d)}var i})),a},w=(r.__("Small","masterstudy-lms-learning-management-system"),r.__("Normal","masterstudy-lms-learning-management-system"),r.__("Large","masterstudy-lms-learning-management-system"),r.__("Extra Large","masterstudy-lms-learning-management-system"),"wp-block-masterstudy-settings__"),S={top:"",right:"",bottom:"",left:""};function x(e){return Array.isArray(e)?e.map((e=>w+e)):w+e}y.TOP_lEFT,y.TOP_CENTER,y.TOP_RIGHT,y.BOTTOM_lEFT,y.BOTTOM_CENTER,y.BOTTOM_RIGHT,r.__("Newest","masterstudy-lms-learning-management-system"),r.__("Oldest","masterstudy-lms-learning-management-system"),r.__("Overall rating","masterstudy-lms-learning-management-system"),r.__("Popular","masterstudy-lms-learning-management-system"),r.__("Price low","masterstudy-lms-learning-management-system"),r.__("Price high","masterstudy-lms-learning-management-system");const T=window.wp.data,k=()=>(0,T.useSelect)((e=>e("core/editor").getDeviceType()||e("core/edit-site")?.__experimentalGetPreviewDeviceType()||e("core/edit-post")?.__experimentalGetPreviewDeviceType()||e("masterstudy/store")?.getDeviceType()),[])||"Desktop",O=(e=!1)=>{const[t,n]=(0,m.useState)(e),a=(0,m.useCallback)((()=>{n(!0)}),[]);return{isOpen:t,onClose:(0,m.useCallback)((()=>{n(!1)}),[]),onOpen:a,onToggle:(0,m.useCallback)((()=>{n((e=>!e))}),[])}},A=()=>{const e=(0,m.useContext)(c);if(!e)throw new Error("No settings context provided");return e},D=(e="")=>{const{attributes:t,setAttributes:n,onResetByFieldName:a,changedFieldsByName:l}=A();return{value:t[e],onChange:t=>n({[e]:t}),onReset:a.get(e),isChanged:l.get(e)}},M=(e,t=!1,n=!1)=>{const{hoverName:a,onChangeHoverName:l}=(()=>{const[e,t]=(0,m.useState)(v.NORMAL);return{hoverName:e,onChangeHoverName:(0,m.useCallback)((e=>{t(e)}),[])}})(),r=k();return{fieldName:(0,m.useMemo)((()=>{const l=a===v.HOVER?a:"",s=r===_.DESKTOP?"":r;return n&&t?e+l+s:n&&!t?e+l:t&&!n?e+s:e}),[e,n,t,a,r]),hoverName:a,onChangeHoverName:l}},L=(e=[],t=h.ALL)=>{const{attributes:n}=A();return!e.length||(t===h.ALL?e.every((({name:e,value:t})=>{const a=n[e];return Array.isArray(t)?Array.isArray(a)?p(t,a):t.includes(a):t===a})):t!==h.SOME||e.some((({name:e,value:t})=>{const a=n[e];return Array.isArray(t)?Array.isArray(a)?p(t,a):t.includes(a):t===a})))},H=e=>{const t=(0,m.useRef)(null);return(0,m.useEffect)((()=>{const n=n=>{t.current&&!t.current.contains(n.target)&&e()};return document.addEventListener("click",n),()=>{document.removeEventListener("click",n)}}),[t,e]),t},B=e=>(0,a.createElement)(u.SVG,{width:"16",height:"16",viewBox:"0 0 14 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(u.G,{"clip-path":"url(#clip0_1068_38993)"},(0,a.createElement)(u.Path,{d:"M11.1973 8.60005L8.74967 8.11005V5.67171C8.74967 5.517 8.68822 5.36863 8.57882 5.25923C8.46942 5.14984 8.32105 5.08838 8.16634 5.08838H6.99967C6.84496 5.08838 6.69659 5.14984 6.5872 5.25923C6.4778 5.36863 6.41634 5.517 6.41634 5.67171V8.58838H5.24967C5.15021 8.58844 5.05241 8.61391 4.96555 8.66238C4.87869 8.71084 4.80565 8.78068 4.75336 8.86529C4.70106 8.9499 4.67125 9.04646 4.66674 9.14582C4.66223 9.24518 4.68317 9.34405 4.72759 9.43305L6.47759 12.933C6.57676 13.1302 6.77859 13.255 6.99967 13.255H11.083C11.2377 13.255 11.3861 13.1936 11.4955 13.0842C11.6049 12.9748 11.6663 12.8264 11.6663 12.6717V9.17171C11.6665 9.03685 11.6198 8.90612 11.5343 8.80186C11.4487 8.69759 11.3296 8.62626 11.1973 8.60005Z"}),(0,a.createElement)(u.Path,{d:"M10.4997 1.58838H3.49967C2.85626 1.58838 2.33301 2.11221 2.33301 2.75505V6.83838C2.33301 7.4818 2.85626 8.00505 3.49967 8.00505H5.24967V6.83838H3.49967V2.75505H10.4997V6.83838H11.6663V2.75505C11.6663 2.11221 11.1431 1.58838 10.4997 1.58838Z"})),(0,a.createElement)("defs",null,(0,a.createElement)("clipPath",{id:"clip0_1068_38993"},(0,a.createElement)(u.Rect,{width:"14",height:"14",transform:"translate(0 0.421875)"})))),[R,P,F,U,z]=(v.NORMAL,r.__("Normal State","masterstudy-lms-learning-management-system"),v.HOVER,r.__("Hovered State","masterstudy-lms-learning-management-system"),v.ACTIVE,r.__("Hovered State","masterstudy-lms-learning-management-system"),v.FOCUS,r.__("Hovered State","masterstudy-lms-learning-management-system"),v.NORMAL,(0,a.createElement)((e=>(0,a.createElement)(u.SVG,{width:"16",height:"16",viewBox:"0 0 12 13",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(u.Path,{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"}))),null),r.__("Normal State","masterstudy-lms-learning-management-system"),v.HOVER,(0,a.createElement)(B,null),r.__("Hovered State","masterstudy-lms-learning-management-system"),v.ACTIVE,(0,a.createElement)(B,null),r.__("Active State","masterstudy-lms-learning-management-system"),v.FOCUS,(0,a.createElement)(B,null),r.__("Focus State","masterstudy-lms-learning-management-system"),x(["hover-state","hover-state__selected","hover-state__selected__opened-menu","hover-state__menu","hover-state__menu__item"])),V=x("color-indicator"),I=(0,m.memo)((({color:e,onChange:t})=>(0,a.createElement)("div",{className:V},(0,a.createElement)(o.PanelColorSettings,{enableAlpha:!0,disableCustomColors:!1,__experimentalHasMultipleOrigins:!0,__experimentalIsRenderedInSidebar:!0,colorSettings:[{label:"",value:e,onChange:t}]}))));var j;function W(){return W=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},W.apply(null,arguments)}var $,G,K=function(e){return a.createElement("svg",W({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 12 13"},e),j||(j=a.createElement("path",{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})))};function Z(){return Z=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Z.apply(null,arguments)}var X=function(e){return a.createElement("svg",Z({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 14 15"},e),$||($=a.createElement("g",{clipPath:"url(#state-hover_svg__a)"},a.createElement("path",{d:"M11.197 8.6 8.75 8.11V5.672a.583.583 0 0 0-.584-.584H7a.583.583 0 0 0-.584.584v2.916H5.25a.584.584 0 0 0-.522.845l1.75 3.5c.099.197.3.322.522.322h4.083a.583.583 0 0 0 .583-.583v-3.5a.58.58 0 0 0-.469-.572"}),a.createElement("path",{d:"M10.5 1.588h-7c-.644 0-1.167.524-1.167 1.167v4.083c0 .644.523 1.167 1.167 1.167h1.75V6.838H3.5V2.755h7v4.083h1.166V2.755c0-.643-.523-1.167-1.166-1.167"}))),G||(G=a.createElement("defs",null,a.createElement("clipPath",{id:"state-hover_svg__a"},a.createElement("path",{d:"M0 .422h14v14H0z"})))))};const Y=[{value:v.NORMAL,label:r.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(K,{onClick:e})},{value:v.HOVER,label:r.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(X,{onClick:e})}],q={[v.NORMAL]:{icon:(0,a.createElement)(K,null),label:r.__("Normal State","masterstudy-lms-learning-management-system")},[v.HOVER]:{icon:(0,a.createElement)(X,null),label:r.__("Hovered State","masterstudy-lms-learning-management-system")}},J=x("hover-state"),Q=x("hover-state__selected"),ee=x("hover-state__selected__opened-menu"),te=x("has-changes"),ne=x("hover-state__menu"),ae=x("hover-state__menu__item"),le=(0,m.memo)((e=>{const{hoverName:t,onChangeHoverName:n,fieldName:l}=e,{changedFieldsByName:r}=A(),s=r.get(l),{isOpen:o,onOpen:c,onClose:d}=O(),u=H(d),{ICONS_MAP:p,options:h}=(e=>{const t=(0,m.useMemo)((()=>Y.filter((t=>t.value!==e))),[e]);return{ICONS_MAP:q,options:t}})(t),v=(0,m.useCallback)((e=>{n(e),d()}),[n,d]);return(0,a.createElement)("div",{className:J,ref:u},(0,a.createElement)("div",{className:i()([Q],{[ee]:o,[te]:s}),onClick:c,title:p[t]?.label},p[t]?.icon),(0,a.createElement)(g,{condition:o},(0,a.createElement)("div",{className:ne},h.map((({value:e,icon:t,label:n})=>(0,a.createElement)("div",{key:e,className:ae,title:n},t((()=>v(e)))))))))})),re={Desktop:{icon:"desktop",label:r.__("Desktop","masterstudy-lms-learning-management-system")},Tablet:{icon:"tablet",label:r.__("Tablet","masterstudy-lms-learning-management-system")},Mobile:{icon:"smartphone",label:r.__("Mobile","masterstudy-lms-learning-management-system")}},se=[{value:_.DESKTOP,icon:"desktop",label:r.__("Desktop","masterstudy-lms-learning-management-system")},{value:_.TABLET,icon:"tablet",label:r.__("Tablet","masterstudy-lms-learning-management-system")},{value:_.MOBILE,icon:"smartphone",label:r.__("Mobile","masterstudy-lms-learning-management-system")}],ie=x("device-picker"),oe=x("device-picker__selected"),me=x("device-picker__selected__opened-menu"),ce=x("device-picker__menu"),de=x("device-picker__menu__item"),ue=()=>{const{isOpen:e,onOpen:t,onClose:n}=O(),{value:l,onChange:r}=(e=>{const t=k(),n=(0,T.useDispatch)();return{value:(0,m.useMemo)((()=>re[t]),[t]),onChange:t=>{n("core/edit-site")&&n("core/edit-site").__experimentalSetPreviewDeviceType?n("core/edit-site").__experimentalSetPreviewDeviceType(t):n("core/edit-post")&&n("core/edit-post").__experimentalSetPreviewDeviceType?n("core/edit-post").__experimentalSetPreviewDeviceType(t):n("masterstudy/store").setDeviceType(t),e()}}})(n),s=(e=>(0,m.useMemo)((()=>se.filter((t=>t.icon!==e))),[e]))(l.icon),o=H(n);return(0,a.createElement)("div",{className:ie,ref:o},(0,a.createElement)(u.Dashicon,{className:i()([oe],{[me]:e}),icon:l.icon,size:16,onClick:t,title:l.label}),(0,a.createElement)(g,{condition:e},(0,a.createElement)("div",{className:ce},s.map((e=>(0,a.createElement)(u.Dashicon,{key:e.value,icon:e.icon,size:16,onClick:()=>r(e.value),className:de,title:e.label}))))))},ge=x("reset-button"),pe=({onReset:e})=>(0,a.createElement)(u.Dashicon,{icon:"undo",onClick:e,className:ge,size:16}),he=[{label:"PX",value:"px"},{label:"%",value:"%"},{label:"EM",value:"em"},{label:"REM",value:"rem"},{label:"VW",value:"vw"},{label:"VH",value:"vh"}],ve=x("unit"),_e=x("unit__single"),ye=x("unit__list"),be=({name:e,isAdaptive:t})=>{const{isOpen:n,onOpen:l,onClose:r}=O(),{fieldName:s}=M(e,t),{value:i,onChange:o}=D(s),m=H(r);return(0,a.createElement)("div",{className:ve,ref:m},(0,a.createElement)("div",{className:_e,onClick:l},i),(0,a.createElement)(g,{condition:n},(0,a.createElement)("div",{className:ye},he.map((({value:e,label:t})=>(0,a.createElement)("div",{key:e,onClick:()=>(o(e),void r())},t))))))},Ee=x("popover-modal"),fe=x("popover-modal__close dashicon dashicons dashicons-no-alt"),Ce=e=>{const{isOpen:t,onClose:n,popoverContent:l}=e;return(0,a.createElement)(g,{condition:t},(0,a.createElement)(u.Popover,{position:"middle left",onClose:n,className:Ee},l,(0,a.createElement)("span",{onClick:n,className:fe})))},Ne=x("setting-label"),we=x("setting-label__content"),Se=e=>{const{label:t,isChanged:n=!1,onReset:l,showDevicePicker:r=!0,HoverStateControl:s=null,unitName:i,popoverContent:o=null,dependencies:m}=e,{isOpen:c,onClose:d,onToggle:u}=O();return L(m)?(0,a.createElement)("div",{className:Ne},(0,a.createElement)("div",{className:we},(0,a.createElement)("div",{onClick:u},t),(0,a.createElement)(g,{condition:Boolean(o)},(0,a.createElement)(Ce,{isOpen:c,onClose:d,popoverContent:o})),(0,a.createElement)(g,{condition:r},(0,a.createElement)(ue,null)),(0,a.createElement)(g,{condition:Boolean(s)},s)),(0,a.createElement)(g,{condition:Boolean(i)},(0,a.createElement)(be,{name:i,isAdaptive:r})),(0,a.createElement)(g,{condition:n},(0,a.createElement)(pe,{onReset:l}))):null},xe=x("suffix"),Te=()=>(0,a.createElement)("div",{className:xe},(0,a.createElement)(u.Dashicon,{icon:"color-picker",size:16})),ke=x("color-picker"),Oe=e=>{const{name:t,label:n,placeholder:l,dependencyMode:r,dependencies:s,isAdaptive:i=!1,hasHover:o=!1}=e,{fieldName:m,hoverName:c,onChangeHoverName:d}=M(t,i,o),{value:p,isChanged:h,onChange:v,onReset:_}=D(m);return L(s,r)?(0,a.createElement)("div",{className:ke},(0,a.createElement)(g,{condition:Boolean(n)},(0,a.createElement)(Se,{label:n,isChanged:h,onReset:_,showDevicePicker:i,HoverStateControl:(0,a.createElement)(g,{condition:o},(0,a.createElement)(le,{hoverName:c,onChangeHoverName:d,fieldName:m}))})),(0,a.createElement)(u.__experimentalInputControl,{prefix:(0,a.createElement)(I,{color:p,onChange:v}),suffix:(0,a.createElement)(Te,null),onChange:v,value:p,placeholder:l})):null},Ae=x("number-steppers"),De=x("indent-steppers"),Me=x("indent-stepper-plus"),Le=x("indent-stepper-minus"),He=({onIncrement:e,onDecrement:t,withArrows:n=!1})=>n?(0,a.createElement)("span",{className:De},(0,a.createElement)("button",{onClick:e,className:Me}),(0,a.createElement)("button",{onClick:t,className:Le})):(0,a.createElement)("span",{className:Ae},(0,a.createElement)("button",{onClick:e},"+"),(0,a.createElement)("button",{onClick:t},"-")),[Be,Re]=x(["indents","indents-control"]),Pe=({name:e,label:t,unitName:n,popoverContent:l,dependencyMode:s,dependencies:i,isAdaptive:o=!1})=>{const{fieldName:c}=M(e,o),{value:d,onResetSegmentedBox:p,hasChanges:h,handleInputIncrement:v,handleInputDecrement:_,updateDirectionsValues:y,lastFieldValue:b}=((e,t)=>{const{value:n,isChanged:a,onChange:l,onReset:r}=D(e),{onResetByFieldName:s,changedFieldsByName:i}=A(),o=a||i.get(t),c=e=>{l({...n,...e})},[d,u]=(0,m.useState)(!1);return{value:n,onResetSegmentedBox:()=>{r(),s.get(t)()},hasChanges:o,handleInputIncrement:e=>Number(n[e])+1,handleInputDecrement:e=>Number(n[e])-1,updateDirectionsValues:(e,t,n)=>{e?(u(!1),c({top:n,right:n,bottom:n,left:n})):(u(n),c({[t]:n}))},lastFieldValue:d}})(c,n),[E,f]=(0,m.useState)((()=>{const{left:e,right:t,top:n,bottom:a}=d;return""!==e&&e===t&&t===n&&n===a})),C=e=>{const[t,n]=Object.entries(e)[0];y(E,t,n)},N=e=>()=>{const t=v(e);y(E,e,String(t))},w=e=>()=>{const t=_(e);y(E,e,String(t))};return L(i,s)?(0,a.createElement)("div",{className:Be},(0,a.createElement)(g,{condition:Boolean(t)},(0,a.createElement)(Se,{label:null!=t?t:"",isChanged:h,onReset:p,unitName:n,popoverContent:l,showDevicePicker:o})),(0,a.createElement)("div",{className:`${Re} ${E?"active":""}`},(0,a.createElement)("div",null,(0,a.createElement)(u.__experimentalNumberControl,{value:d.top,onChange:e=>{C({top:e})},spinControls:"none",suffix:(0,a.createElement)(He,{onIncrement:N("top"),onDecrement:w("top"),withArrows:!0})}),(0,a.createElement)("div",null,r.__("Top","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(u.__experimentalNumberControl,{value:d.right,onChange:e=>{C({right:e})},spinControls:"none",suffix:(0,a.createElement)(He,{onIncrement:N("right"),onDecrement:w("right"),withArrows:!0})}),(0,a.createElement)("div",null,r.__("Right","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(u.__experimentalNumberControl,{value:d.bottom,onChange:e=>{C({bottom:e})},spinControls:"none",suffix:(0,a.createElement)(He,{onIncrement:N("bottom"),onDecrement:w("bottom"),withArrows:!0})}),(0,a.createElement)("div",null,r.__("Bottom","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(u.__experimentalNumberControl,{value:d.left,onChange:e=>{C({left:e})},spinControls:"none",suffix:(0,a.createElement)(He,{onIncrement:N("left"),onDecrement:w("left"),withArrows:!0})}),(0,a.createElement)("div",null,r.__("Left","masterstudy-lms-learning-management-system"))),(0,a.createElement)(u.Dashicon,{icon:"dashicons "+(E?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{E||!1===b||y(!0,"left",b),f((e=>!e))}}))):null},[Fe,Ue,ze,Ve]=x(["toggle-group-wrapper","toggle-group","toggle-group__toggle","toggle-group__active-toggle"]),[Ie,je,We,$e]=x(["border-control","border-control-solid","border-control-dashed","border-control-dotted"]),Ge=(x("border-radius"),x("border-radius-control"),x("box-shadow-preset"),x("presets")),Ke=x("presets__item-wrapper"),Ze=x("presets__item-wrapper__preset"),Xe=x("presets__item-wrapper__name"),Ye=((0,m.memo)((e=>{const{presets:t,activePreset:n,onSelectPreset:l,PresetItem:r,detectIsActive:s,detectByIndex:o=!1}=e;return(0,a.createElement)("div",{className:Ge},t.map((({name:e,...t},m)=>(0,a.createElement)("div",{key:m,className:i()([Ke],{active:s(n,o?m:t)}),onClick:()=>l(t)},(0,a.createElement)("div",{className:Ze},(0,a.createElement)(r,{preset:t})),(0,a.createElement)("span",{className:Xe},e)))))})),x("range-control")),qe=e=>{const{name:t,label:n,min:l,max:r,unitName:s,dependencyMode:i,dependencies:o,isAdaptive:m=!1}=e,{fieldName:c}=M(t,m),{value:d,onChange:p,onResetNumberField:h,hasChanges:v}=((e,t)=>{const{value:n,isChanged:a,onChange:l,onReset:r}=D(e),{onResetByFieldName:s,changedFieldsByName:i}=A();return{value:n,onChange:l,onResetNumberField:()=>{r(),s.get(t)()},hasChanges:a||i.get(t)}})(c,s);return L(o,i)?(0,a.createElement)("div",{className:Ye},(0,a.createElement)(g,{condition:Boolean(n)},(0,a.createElement)(Se,{label:n,isChanged:v,onReset:h,unitName:s,showDevicePicker:m})),(0,a.createElement)(u.RangeControl,{value:d,onChange:p,min:l,max:r})):null},Je=x("switch"),Qe=e=>{const{name:t,label:n,dependencyMode:l,dependencies:r,isAdaptive:s=!1}=e,{fieldName:i}=M(t,s),{value:o,onChange:m}=D(i);return L(r,l)?(0,a.createElement)("div",{className:Je,"data-has-label":Boolean(n).toString()},(0,a.createElement)(u.ToggleControl,{label:n,checked:o,onChange:m}),(0,a.createElement)(g,{condition:s},(0,a.createElement)(ue,null))):null},et=(x("box-shadow-settings"),x("box-shadow-presets-title"),x("input-field"),x("input-field-control"),x("number-field"),x("number-field-control"),({className:e})=>(0,a.createElement)("div",{className:e},r.__("No options","masterstudy-lms-learning-management-system"))),tt=x("select__single-item"),nt=x("select__container"),at=x("select__container__multi-item"),lt=({multiple:e,value:t,options:n,onChange:l})=>{const{singleValue:r,multipleValue:s}=((e,t,n)=>({singleValue:(0,m.useMemo)((()=>t?null:n.find((t=>t.value===e))?.label),[t,e,n]),multipleValue:(0,m.useMemo)((()=>t?e:null),[t,e])}))(t,e,n);return(0,a.createElement)(g,{condition:e,fallback:(0,a.createElement)("div",{className:tt},r)},(0,a.createElement)("div",{className:nt},s?.map((e=>{const t=n.find((t=>t.value===e));return t?(0,a.createElement)("div",{key:t.value,className:at},(0,a.createElement)("div",null,t.label),(0,a.createElement)(u.Dashicon,{icon:"no-alt",onClick:()=>l(t.value),size:16})):null}))))},rt=x("select"),st=x("select__select-box"),it=x("select__placeholder"),ot=x("select__select-box-multiple"),mt=x("select__menu"),ct=x("select__menu__options-container"),dt=x("select__menu__item"),ut=e=>{const{options:t,multiple:n=!1,placeholder:l="Select",value:r,onSelect:s}=e,{isOpen:o,onToggle:c,onClose:d}=O(),p=H(d),h=((e,t,n)=>(0,m.useMemo)((()=>n&&Array.isArray(e)?t.filter((t=>!e.includes(t.value))):t.filter((t=>t.value!==e))),[e,t,n]))(r,t,n),v=((e,t,n,a)=>(0,m.useCallback)((l=>{if(t&&Array.isArray(e)){const t=e.includes(l)?e.filter((e=>e!==l)):[...e,l];n(t)}else n(l),a()}),[t,e,n,a]))(r,n,s,d),_=((e,t)=>(0,m.useMemo)((()=>t&&Array.isArray(e)?Boolean(e.length):Boolean(e)),[t,e]))(r,n),y=n&&Array.isArray(r)&&r?.length>0;return(0,a.createElement)("div",{className:rt,ref:p},(0,a.createElement)("div",{className:i()([st],{[ot]:y}),onClick:c},(0,a.createElement)(g,{condition:_,fallback:(0,a.createElement)("div",{className:it},l)},(0,a.createElement)(lt,{onChange:v,options:t,multiple:n,value:r})),(0,a.createElement)(u.Dashicon,{icon:o?"arrow-up-alt2":"arrow-down-alt2",size:16,style:{color:"#4D5E6F"}})),(0,a.createElement)(g,{condition:o},(0,a.createElement)("div",{className:mt},(0,a.createElement)(g,{condition:Boolean(h.length),fallback:(0,a.createElement)(et,{className:dt})},(0,a.createElement)("div",{className:ct},h.map((e=>(0,a.createElement)("div",{key:e.value,onClick:()=>v(e.value),className:dt},e.label))))))))},gt=x("setting-select"),pt=e=>{const{name:t,options:n,label:l,multiple:r=!1,placeholder:s,isAdaptive:i=!1,dependencyMode:o,dependencies:m}=e,{fieldName:c}=M(t,i),{value:d,isChanged:u,onChange:p,onReset:h}=D(c);return L(m,o)?(0,a.createElement)("div",{className:gt},(0,a.createElement)(g,{condition:Boolean(l)},(0,a.createElement)(Se,{label:l,isChanged:u,onReset:h,showDevicePicker:i})),(0,a.createElement)(ut,{options:n,value:d,onSelect:p,multiple:r,placeholder:s})):null},ht=(x("row-select"),x("row-select__label"),x("row-select__control"),x("typography-select")),vt=x("typography-select-label"),_t=e=>{const{name:t,label:n,options:l,isAdaptive:r=!1}=e,{fieldName:s}=M(t,r),{isChanged:i,onReset:o}=D(s);return(0,a.createElement)("div",{className:ht},(0,a.createElement)("div",{className:vt},(0,a.createElement)("div",null,n),(0,a.createElement)(g,{condition:r},(0,a.createElement)(ue,null))),(0,a.createElement)(pt,{name:t,options:l,isAdaptive:r}),(0,a.createElement)(g,{condition:i},(0,a.createElement)(pe,{onReset:o})))},yt=x("typography"),bt=e=>{const{fontSizeName:t,fontWeightName:n,textTransformName:l,fontStyleName:s,textDecorationName:i,lineHeightName:o,letterSpacingName:m,wordSpacingName:c,fontSizeUnitName:d,lineHeightUnitName:u,letterSpacingUnitName:g,wordSpacingUnitName:p,dependencyMode:h,dependencies:v,isAdaptive:_=!1}=e,{fontWeightOptions:y,textTransformOptions:b,fontStyleOptions:E,textDecorationOptions:f}={fontWeightOptions:[{label:r.__("100 (Thin)","masterstudy-lms-learning-management-system"),value:"100"},{label:r.__("200 (Extra Light)","masterstudy-lms-learning-management-system"),value:"200"},{label:r.__("300 (Light)","masterstudy-lms-learning-management-system"),value:"300"},{label:r.__("400 (Normal)","masterstudy-lms-learning-management-system"),value:"400"},{label:r.__("500 (Medium)","masterstudy-lms-learning-management-system"),value:"500"},{label:r.__("600 (Semi Bold)","masterstudy-lms-learning-management-system"),value:"600"},{label:r.__("700 (Bold)","masterstudy-lms-learning-management-system"),value:"700"},{label:r.__("800 (Extra Bold)","masterstudy-lms-learning-management-system"),value:"800"},{label:r.__("900 (Extra)","masterstudy-lms-learning-management-system"),value:"900"}],textTransformOptions:[{label:r.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:r.__("Uppercase","masterstudy-lms-learning-management-system"),value:"uppercase"},{label:r.__("Lowercase","masterstudy-lms-learning-management-system"),value:"lowercase"},{label:r.__("Capitalize","masterstudy-lms-learning-management-system"),value:"capitalize"},{label:r.__("Normal","masterstudy-lms-learning-management-system"),value:"none"}],fontStyleOptions:[{label:r.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:r.__("Normal","masterstudy-lms-learning-management-system"),value:"none"},{label:r.__("Italic","masterstudy-lms-learning-management-system"),value:"italic"},{label:r.__("Oblique","masterstudy-lms-learning-management-system"),value:"oblique"}],textDecorationOptions:[{label:r.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:r.__("Underline","masterstudy-lms-learning-management-system"),value:"underline"},{label:r.__("Line Through","masterstudy-lms-learning-management-system"),value:"line-through"},{label:r.__("None","masterstudy-lms-learning-management-system"),value:"none"}]};return L(v,h)?(0,a.createElement)("div",{className:yt},(0,a.createElement)(qe,{name:t,label:r.__("Size","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:d,isAdaptive:_}),(0,a.createElement)(_t,{name:n,label:r.__("Weight","masterstudy-lms-learning-management-system"),options:y}),(0,a.createElement)(_t,{name:l,label:r.__("Transform","masterstudy-lms-learning-management-system"),options:b}),(0,a.createElement)(_t,{name:s,label:r.__("Style","masterstudy-lms-learning-management-system"),options:E}),(0,a.createElement)(_t,{name:i,label:r.__("Decoration","masterstudy-lms-learning-management-system"),options:f}),(0,a.createElement)(qe,{name:o,label:r.__("Line Height","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:u,isAdaptive:_}),(0,a.createElement)(qe,{name:m,label:r.__("Letter Spacing","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:g,isAdaptive:_}),c&&(0,a.createElement)(qe,{name:c,label:r.__("Word Spacing","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:p,isAdaptive:_})):null},Et=(x("file-upload"),x("file-upload__wrap"),x("file-upload__image"),x("file-upload__remove"),x("file-upload__replace"),(0,m.createContext)({activeTab:0,setActiveTab:()=>{}})),ft=()=>{const e=(0,m.useContext)(Et);if(!e)throw new Error("useTabs should be used inside Tabs");return e},Ct=({children:e})=>{const[t,n]=(0,m.useState)(0);return(0,a.createElement)(Et.Provider,{value:{activeTab:t,setActiveTab:n}},(0,a.createElement)("div",{className:`active-tab-${t}`},e))},Nt=x("tab-list"),wt=({children:e})=>(0,a.createElement)("div",{className:Nt},m.Children.map(e,((e,t)=>(0,m.cloneElement)(e,{index:t})))),St=x("tab"),xt=x("tab-active"),Tt=x("content"),kt=({index:e,title:t,icon:n})=>{const{activeTab:l,setActiveTab:r}=ft();return(0,a.createElement)("div",{className:i()([St],{[xt]:l===e}),onClick:()=>r(e)},(0,a.createElement)("div",{className:Tt},(0,a.createElement)("div",null,n),(0,a.createElement)("div",null,t)))},Ot=({children:e})=>(0,a.createElement)("div",null,m.Children.map(e,((e,t)=>(0,m.cloneElement)(e,{index:t})))),At=x("tab-panel"),Dt=({index:e,children:t})=>{const{activeTab:n}=ft();return n===e?(0,a.createElement)("div",{className:At},t):null},Mt=({generalTab:e,styleTab:t,advancedTab:n})=>(0,a.createElement)(Ct,null,(0,a.createElement)(wt,null,(0,a.createElement)(kt,{title:r.__("General","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(u.Dashicon,{icon:"layout"})}),(0,a.createElement)(kt,{title:r.__("Style","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(u.Dashicon,{icon:"admin-appearance"})}),(0,a.createElement)(kt,{title:r.__("Advanced","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(u.Dashicon,{icon:"admin-settings"})})),(0,a.createElement)(Ot,null,(0,a.createElement)(Dt,null,e),(0,a.createElement)(Dt,null,t),(0,a.createElement)(Dt,null,n)));window.ReactDOM;"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement;function Lt(e){const t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function Ht(e){return"nodeType"in e}function Bt(e){var t,n;return e?Lt(e)?e:Ht(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function Rt(e){const{Document:t}=Bt(e);return e instanceof t}function Pt(e){return!Lt(e)&&e instanceof Bt(e).HTMLElement}function Ft(e){return e instanceof Bt(e).SVGElement}function Ut(e){return e?Lt(e)?e.document:Ht(e)?Rt(e)?e:Pt(e)||Ft(e)?e.ownerDocument:document:document:document}function zt(e){return function(t){for(var n=arguments.length,a=new Array(n>1?n-1:0),l=1;l<n;l++)a[l-1]=arguments[l];return a.reduce(((t,n)=>{const a=Object.entries(n);for(const[n,l]of a){const a=t[n];null!=a&&(t[n]=a+e*l)}return t}),{...t})}}const Vt=zt(-1);function It(e){if(function(e){if(!e)return!1;const{TouchEvent:t}=Bt(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return function(e){return"clientX"in e&&"clientY"in e}(e)?{x:e.clientX,y:e.clientY}:null}var jt;!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(jt||(jt={}));const Wt=Object.freeze({x:0,y:0});var $t,Gt,Kt,Zt;!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}($t||($t={}));class Xt{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach((e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)}))},this.target=e}add(e,t,n){var a;null==(a=this.target)||a.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function Yt(e,t){const n=Math.abs(e.x),a=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+a**2)>t:"x"in t&&"y"in t?n>t.x&&a>t.y:"x"in t?n>t.x:"y"in t&&a>t.y}function qt(e){e.preventDefault()}function Jt(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(Gt||(Gt={})),(Zt=Kt||(Kt={})).Space="Space",Zt.Down="ArrowDown",Zt.Right="ArrowRight",Zt.Left="ArrowLeft",Zt.Up="ArrowUp",Zt.Esc="Escape",Zt.Enter="Enter";Kt.Space,Kt.Enter,Kt.Esc,Kt.Space,Kt.Enter;function Qt(e){return Boolean(e&&"distance"in e)}function en(e){return Boolean(e&&"delay"in e)}class tn{constructor(e,t,n){var a;void 0===n&&(n=function(e){const{EventTarget:t}=Bt(e);return e instanceof t?e:Ut(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;const{event:l}=e,{target:r}=l;this.props=e,this.events=t,this.document=Ut(r),this.documentListeners=new Xt(this.document),this.listeners=new Xt(n),this.windowListeners=new Xt(Bt(r)),this.initialCoordinates=null!=(a=It(l))?a:Wt,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),this.windowListeners.add(Gt.Resize,this.handleCancel),this.windowListeners.add(Gt.DragStart,qt),this.windowListeners.add(Gt.VisibilityChange,this.handleCancel),this.windowListeners.add(Gt.ContextMenu,qt),this.documentListeners.add(Gt.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(en(t))return void(this.timeoutId=setTimeout(this.handleStart,t.delay));if(Qt(t))return}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handleStart(){const{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(Gt.Click,Jt,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(Gt.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;const{activated:n,initialCoordinates:a,props:l}=this,{onMove:r,options:{activationConstraint:s}}=l;if(!a)return;const i=null!=(t=It(e))?t:Wt,o=Vt(a,i);if(!n&&s){if(Qt(s)){if(null!=s.tolerance&&Yt(o,s.tolerance))return this.handleCancel();if(Yt(o,s.distance))return this.handleStart()}return en(s)&&Yt(o,s.tolerance)?this.handleCancel():void 0}e.cancelable&&e.preventDefault(),r(i)}handleEnd(){const{onEnd:e}=this.props;this.detach(),e()}handleCancel(){const{onCancel:e}=this.props;this.detach(),e()}handleKeydown(e){e.code===Kt.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}const nn={move:{name:"pointermove"},end:{name:"pointerup"}};(class extends tn{constructor(e){const{event:t}=e,n=Ut(t.target);super(e,nn,n)}}).activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;return!(!n.isPrimary||0!==n.button||(null==a||a({event:n}),0))}}];const an={move:{name:"mousemove"},end:{name:"mouseup"}};var ln;!function(e){e[e.RightClick=2]="RightClick"}(ln||(ln={})),class extends tn{constructor(e){super(e,an,Ut(e.event.target))}}.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;return n.button!==ln.RightClick&&(null==a||a({event:n}),!0)}}];const rn={move:{name:"touchmove"},end:{name:"touchend"}};var sn,on,mn,cn,dn;(class extends tn{constructor(e){super(e,rn)}static setup(){return window.addEventListener(rn.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(rn.move.name,e)};function e(){}}}).activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;const{touches:l}=n;return!(l.length>1||(null==a||a({event:n}),0))}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(sn||(sn={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(on||(on={})),$t.Backward,$t.Forward,$t.Backward,$t.Forward,function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(mn||(mn={})),function(e){e.Optimized="optimized"}(cn||(cn={})),mn.WhileDragging,cn.Optimized,Map,function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(dn||(dn={})),Kt.Down,Kt.Right,Kt.Up,Kt.Left,r.__("Lectures","masterstudy-lms-learning-management-system"),r.__("Duration","masterstudy-lms-learning-management-system"),r.__("Views","masterstudy-lms-learning-management-system"),r.__("Level","masterstudy-lms-learning-management-system"),r.__("Members","masterstudy-lms-learning-management-system"),r.__("Empty","masterstudy-lms-learning-management-system"),x("sortable__item"),x("sortable__item__disabled"),x("sortable__item__content"),x("sortable__item__content__drag-item"),x("sortable__item__content__drag-item__disabled"),x("sortable__item__content__title"),x("sortable__item__control"),x("sortable__item__icon"),x("nested-sortable"),x("nested-sortable__item"),x("sortable");const un=x("accordion"),gn=x("accordion__header"),pn=x("accordion__header-flex"),hn=x("accordion__content"),vn=x("accordion__icon"),yn=x("accordion__title"),bn=x("accordion__title-disabled"),En=x("accordion__indicator"),fn=x("accordion__controls"),Cn=x("accordion__controls-disabled"),Nn=({title:e,children:t,accordionFields:n,switchName:l,visible:r=!0,isDefaultOpen:s=!1})=>{const{isOpen:o,onToggle:c,disabled:d,onReset:h,hasChanges:v,onClose:_}=((e,t,n)=>{var a;const{isOpen:l,onToggle:r,onClose:s}=O(t),{defaultValues:i,attributes:o,setAttributes:m}=A(),c=((e,t,n)=>{for(const a of n)if(!p(e[a],t[a]))return!0;return!1})(i,o,e);return{isOpen:l,onToggle:r,disabled:!(null===(a=o[n])||void 0===a||a),hasChanges:c,onReset:t=>{t.stopPropagation(),m(e.reduce(((e,t)=>(e[t]=i[t],e)),{}))},onClose:s}})(n,s,l);return((e,t)=>{const{attributes:n}=A(),a=!n[t];(0,m.useEffect)((()=>{a&&e()}),[a,e])})(_,l),r?(0,a.createElement)("div",{className:un},(0,a.createElement)("div",{className:gn},(0,a.createElement)("div",{className:pn,onClick:d?null:c},(0,a.createElement)("div",{className:i()(yn,{[bn]:d,"with-switch":Boolean(l)})},(0,a.createElement)("div",null,e),(0,a.createElement)(g,{condition:v&&!d},(0,a.createElement)("div",{className:En}))),(0,a.createElement)("div",{className:i()(fn,{[Cn]:d})},(0,a.createElement)(u.Dashicon,{icon:o?"arrow-up-alt2":"arrow-down-alt2",className:vn,size:16}))),(0,a.createElement)(g,{condition:Boolean(l)},(0,a.createElement)(Qe,{name:l})),(0,a.createElement)(g,{condition:v&&!d},(0,a.createElement)(pe,{onReset:h}))),o&&(0,a.createElement)("div",{className:hn},t)):null};x("preset-picker"),x("preset-picker__label"),x("preset-picker__remove"),x("preset-picker__presets-list"),x("preset-picker__presets-list__item"),x("preset-picker__presets-list__item__preset"),x("preset-picker__presets-list__item__preset-active");const wn={hideDefault:!1},Sn=Object.keys(wn),xn={padding:S,paddingTablet:S,paddingMobile:S,paddingUnit:"px",paddingUnitTablet:"px",paddingUnitMobile:"px",background:""},Tn=Object.keys(xn),kn={titleColor:"",titleFontSize:16,titleFontSizeUnit:"px",titleFontWeight:"700",titleTextTransform:"",titleFontStyle:"",titleTextDecoration:"",titleLineHeight:24,titleLineHeightUnit:"px",titleLetterSpacing:null,titleLetterSpacingUnit:"px",titleWordSpacing:null,titleWordSpacingUnit:"px",togglerColor:""},On=Object.keys(kn),An={itemColor:"",itemFontSize:14,itemFontSizeUnit:"px",itemFontWeight:"500",itemTextTransform:"",itemFontStyle:"",itemTextDecoration:"",itemLineHeight:16,itemLineHeightUnit:"px",itemLetterSpacing:null,itemLetterSpacingUnit:"px",itemWordSpacing:null,itemWordSpacingUnit:"px",inputColor:"",inputColorActive:"",inputBgColor:"",inputBgColorActive:"",inputBorderColor:"",inputBorderColorActive:""},Dn=Object.keys(An),Mn={...{...wn},...{...xn,...kn,...An}},Ln=new Map([["padding",{unit:"paddingUnit",isAdaptive:!0}],["background",{}],["titleColor",{}],["titleFontSize",{unit:"titleFontSizeUnit"}],["titleFontWeight",{}],["titleTextTransform",{}],["titleFontStyle",{}],["titleTextDecoration",{}],["titleLineHeight",{unit:"titleLineHeightUnit"}],["titleLetterSpacing",{unit:"titleLetterSpacingUnit"}],["titleWordSpacing",{unit:"titleWordSpacingUnit"}],["togglerColor",{}],["itemColor",{}],["itemFontSize",{unit:"itemFontSizeUnit"}],["itemFontWeight",{}],["itemTextTransform",{}],["itemFontStyle",{}],["itemTextDecoration",{}],["itemLineHeight",{unit:"itemLineHeightUnit"}],["itemLetterSpacing",{unit:"itemLetterSpacingUnit"}],["itemWordSpacing",{unit:"itemWordSpacingUnit"}],["inputColor",{}],["inputColorActive",{}],["inputBgColor",{}],["inputBgColorActive",{}],["inputBorderColor",{}],["inputBorderColorActive",{}]]),Hn=()=>(0,a.createElement)(a.Fragment,null,(0,a.createElement)(Nn,{title:r.__("Layout","masterstudy-lms-learning-management-system"),accordionFields:Sn},(0,a.createElement)(Qe,{name:"hideDefault",label:r.__("Hide default","masterstudy-lms-learning-management-system")}))),Bn=()=>(0,a.createElement)(a.Fragment,null,(0,a.createElement)(Nn,{title:r.__("Layout","masterstudy-lms-learning-management-system"),accordionFields:Tn},(0,a.createElement)(Pe,{name:"padding",label:r.__("Padding","masterstudy-lms-learning-management-system"),unitName:"paddingUnit",isAdaptive:!0}),(0,a.createElement)(Oe,{name:"background",label:r.__("Background","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")})),(0,a.createElement)(Nn,{title:r.__("Header","masterstudy-lms-learning-management-system"),accordionFields:On},(0,a.createElement)(Oe,{name:"titleColor",label:r.__("Title color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(bt,{fontSizeName:"titleFontSize",fontSizeUnitName:"titleFontSizeUnit",fontWeightName:"titleFontWeight",textTransformName:"titleTextTransform",fontStyleName:"titleFontStyle",textDecorationName:"titleTextDecoration",lineHeightName:"titleLineHeight",lineHeightUnitName:"titleLineHeightUnit",letterSpacingName:"titleLetterSpacing",letterSpacingUnitName:"titleLetterSpacingUnit",wordSpacingName:"titleWordSpacing",wordSpacingUnitName:"titleWordSpacingUnit"}),(0,a.createElement)(Oe,{name:"togglerColor",label:r.__("Toggler color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")})),(0,a.createElement)(Nn,{title:r.__("Items","masterstudy-lms-learning-management-system"),accordionFields:Dn},(0,a.createElement)(Oe,{name:"itemColor",label:r.__("Checkbox text color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(bt,{fontSizeName:"itemFontSize",fontSizeUnitName:"itemFontSizeUnit",fontWeightName:"itemFontWeight",textTransformName:"itemTextTransform",fontStyleName:"itemFontStyle",textDecorationName:"itemTextDecoration",lineHeightName:"itemLineHeight",lineHeightUnitName:"itemLineHeightUnit",letterSpacingName:"itemLetterSpacing",letterSpacingUnitName:"itemLetterSpacingUnit",wordSpacingName:"itemWordSpacing",wordSpacingUnitName:"itemWordSpacingUnit"}),(0,a.createElement)(Oe,{name:"inputColor",label:r.__("Checkbox color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Oe,{name:"inputColorActive",label:r.__("Checkbox selected color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Oe,{name:"inputBgColor",label:r.__("Checkbox background color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Oe,{name:"inputBgColorActive",label:r.__("Checkbox selected background color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Oe,{name:"inputBorderColor",label:r.__("Checkbox border color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Oe,{name:"inputBorderColorActive",label:r.__("Checkbox selected border color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}))),Rn=({attributes:e,setAttributes:t})=>{const{onResetByFieldName:n,changedFieldsByName:l}=((e,t,n,a=[])=>{const l=(e=>{const t={};return Object.entries(e).forEach((([e,n])=>{e.includes("UAG")||(t[e]=n)})),t})(t),r=!p(e,l),s=((e,t,n)=>{const a=new Map;return n.forEach((n=>{a.has(n)||a.set(n,(()=>t({[n]:e[n]})))})),a})(e,n,a),i=((e,t,n)=>{const a=new Map;return n.forEach((n=>{a.has(n)?a.set(n,!1):a.set(n,!p(e[n],t[n]))})),a})(e,l,a);return{hasChanges:r,onResetByFieldName:s,changedFieldsByName:i}})(Mn,e,t,Object.keys(Mn));return(0,a.createElement)(o.InspectorControls,null,(0,a.createElement)(d,{attributes:e,setAttributes:t,defaultValues:Mn,onResetByFieldName:n,changedFieldsByName:l},(0,a.createElement)(Mt,{generalTab:(0,a.createElement)(Hn,null),styleTab:(0,a.createElement)(Bn,null),advancedTab:(0,a.createElement)(a.Fragment,null)})))},Pn=[{value:"hot",label:r.__("Hot","masterstudy-lms-learning-management-system")},{value:"new",label:r.__("New","masterstudy-lms-learning-management-system")},{value:"special",label:r.__("Special","masterstudy-lms-learning-management-system")}],Fn=JSON.parse('{"UU":"masterstudy/courses-filter-status"}');(0,l.registerBlockType)(Fn.UU,{edit:({attributes:e,setAttributes:t})=>{const n=(0,o.useBlockProps)({className:i()("archive-courses-filter-status","archive-courses-filter-item",{"hide-filter":e.hideDefault}),style:N("filter-item",e,Ln)});return(0,a.createElement)(a.Fragment,null,(0,a.createElement)(Rn,{attributes:e,setAttributes:t}),(0,a.createElement)("div",{...n},(0,a.createElement)("div",{className:"lms-courses-filter-option-title"},r.__("Status","masterstudy-lms-learning-management-system"),(0,a.createElement)("div",{className:"lms-courses-filter-option-switcher"})),(0,a.createElement)("div",{className:"lms-courses-filter-option-collapse"},(0,a.createElement)("ul",{className:"lms-courses-filter-option-list"},Pn.map((e=>(0,a.createElement)("li",{key:e.value,className:"lms-courses-filter-option-item"},(0,a.createElement)("label",{className:"lms-courses-filter-checkbox"},(0,a.createElement)("input",{type:"checkbox",value:e.value,name:"status"}),(0,a.createElement)("span",{className:"lms-courses-filter-checkbox-label"},e.label)))))))))},save:({attributes:e})=>{const t=o.useBlockProps.save({className:i()("archive-courses-filter-status","archive-courses-filter-item",{"hide-filter":e.hideDefault}),style:N("filter-item",e,Ln)});return(0,a.createElement)("div",{...t},(0,a.createElement)("div",{className:"lms-courses-filter-option-title"},r.__("Status","masterstudy-lms-learning-management-system"),(0,a.createElement)("div",{className:"lms-courses-filter-option-switcher"})),(0,a.createElement)("div",{className:"lms-courses-filter-option-collapse"},(0,a.createElement)("ul",{className:"lms-courses-filter-option-list"},Pn.map((e=>(0,a.createElement)("li",{key:e.value,className:"lms-courses-filter-option-item"},(0,a.createElement)("label",{className:"lms-courses-filter-checkbox"},(0,a.createElement)("input",{type:"checkbox",value:e.value,name:"status"}),(0,a.createElement)("span",{className:"lms-courses-filter-checkbox-label"},e.label))))))))},icon:(0,a.createElement)("svg",{width:"532",height:"532",viewBox:"0 0 532 532",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,a.createElement)("g",{clipPath:"url(#clip0_3861_69772)"},(0,a.createElement)("rect",{opacity:"0.3",x:"78.7286",y:"78.7286",width:"374.543",height:"374.543",rx:"187.271",fill:"#227AFF"}),(0,a.createElement)("path",{d:"M266 0C412.673 0 532 119.327 532 266C532 412.673 412.673 532 266 532C119.327 532 0 412.673 0 266C0 119.327 119.327 0 266 0ZM266 512C401.645 512 512 401.645 512 266C512 130.355 401.645 20 266 20C130.355 20 20 130.355 20 266C20 401.645 130.355 512 266 512Z",fill:"black"}),(0,a.createElement)("path",{d:"M287.331 404H265.997C263.345 404 260.801 402.946 258.926 401.071C257.051 399.196 255.997 396.652 255.997 394V254.667H244.664C242.012 254.667 239.468 253.613 237.593 251.738C235.718 249.863 234.664 247.319 234.664 244.667C234.664 242.015 235.718 239.471 237.593 237.596C239.468 235.721 242.012 234.667 244.664 234.667H266C268.652 234.667 271.196 235.721 273.071 237.596C274.947 239.471 276 242.015 276 244.667V384H287.333C289.985 384 292.529 385.054 294.404 386.929C296.279 388.804 297.333 391.348 297.333 394C297.333 396.652 296.279 399.196 294.404 401.071C292.529 402.946 289.985 404 287.333 404H287.331Z",fill:"black"}),(0,a.createElement)("path",{d:"M265.998 175.334C280.726 175.334 292.665 163.395 292.665 148.667C292.665 133.939 280.726 122 265.998 122C251.27 122 239.331 133.939 239.331 148.667C239.331 163.395 251.27 175.334 265.998 175.334Z",fill:"black"})),(0,a.createElement)("defs",null,(0,a.createElement)("clipPath",{id:"clip0_3861_69772"},(0,a.createElement)("rect",{width:"532",height:"532",fill:"white"}))))})},6942:(e,t)=>{var n;!function(){"use strict";var a={}.hasOwnProperty;function l(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=s(e,r(n)))}return e}function r(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return l.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)a.call(e,n)&&e[n]&&(t=s(t,n));return t}function s(e,t){return t?e?e+" "+t:e+t:e}e.exports?(l.default=l,e.exports=l):void 0===(n=function(){return l}.apply(t,[]))||(e.exports=n)}()}},n={};function a(e){var l=n[e];if(void 0!==l)return l.exports;var r=n[e]={exports:{}};return t[e](r,r.exports,a),r.exports}a.m=t,e=[],a.O=(t,n,l,r)=>{if(!n){var s=1/0;for(c=0;c<e.length;c++){for(var[n,l,r]=e[c],i=!0,o=0;o<n.length;o++)(!1&r||s>=r)&&Object.keys(a.O).every((e=>a.O[e](n[o])))?n.splice(o--,1):(i=!1,r<s&&(s=r));if(i){e.splice(c--,1);var m=l();void 0!==m&&(t=m)}}return t}r=r||0;for(var c=e.length;c>0&&e[c-1][2]>r;c--)e[c]=e[c-1];e[c]=[n,l,r]},a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={896:0,3928:0};a.O.j=t=>0===e[t];var t=(t,n)=>{var l,r,[s,i,o]=n,m=0;if(s.some((t=>0!==e[t]))){for(l in i)a.o(i,l)&&(a.m[l]=i[l]);if(o)var c=o(a)}for(t&&t(n);m<s.length;m++)r=s[m],a.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return a.O(c)},n=globalThis.webpackChunkmasterstudy_lms_learning_management_system=globalThis.webpackChunkmasterstudy_lms_learning_management_system||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})();var l=a.O(void 0,[3928],(()=>a(4288)));l=a.O(l)})();