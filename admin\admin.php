<?php
/**
 * Admin page functionality for Custom Linking Plugin
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Display the admin page
 */
function custom_linking_admin_page() {
    // Check user capability
    if (!current_user_can('manage_options')) {
        return;
    }
    
    // Handle form submission
    if (isset($_POST['custom_linking_submit']) && check_admin_referer('custom_linking_save', 'custom_linking_nonce')) {
        custom_linking_handle_form_submission();
    }
    
    // Handle delete action
    if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['id']) && check_admin_referer('custom_linking_delete_' . $_GET['id'])) {
        custom_linking_delete_link((int)$_GET['id']);
    }
    
    // Get existing links
    $links = custom_linking_get_all_links();
    
    // Get courses and products for dropdown
    $courses = custom_linking_get_available_courses();
    $products = custom_linking_get_available_products();
    
    // Display admin UI
    ?>
    <div class="wrap">
        <h1><?php echo esc_html__('Course-Product Linking', 'custom-linking-plugin'); ?></h1>
        
        <?php if (isset($_GET['message']) && $_GET['message'] === 'success'): ?>
            <div class="notice notice-success is-dismissible">
                <p><?php echo esc_html__('Link saved successfully', 'custom-linking-plugin'); ?></p>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_GET['message']) && $_GET['message'] === 'deleted'): ?>
            <div class="notice notice-success is-dismissible">
                <p><?php echo esc_html__('Link deleted successfully', 'custom-linking-plugin'); ?></p>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_GET['message']) && $_GET['message'] === 'error'): ?>
            <div class="notice notice-error is-dismissible">
                <p><?php echo esc_html__('Error occurred', 'custom-linking-plugin'); ?></p>
            </div>
        <?php endif; ?>
        
        <div class="custom-linking-admin-container">
            <div class="custom-linking-admin-form">
                <h2><?php echo esc_html__('Add New Link', 'custom-linking-plugin'); ?></h2>
                <form method="post" action="">
                    <?php wp_nonce_field('custom_linking_save', 'custom_linking_nonce'); ?>
                    
                    <div class="form-group">
                        <label for="course_id">
                            <?php echo esc_html__('Course', 'custom-linking-plugin'); ?>:
                        </label>
                        <select name="course_id" id="course_id" class="regular-text" required>
                            <option value=""><?php echo esc_html__('-- Select Course --', 'custom-linking-plugin'); ?></option>
                            <?php foreach ($courses as $course): ?>
                                <option value="<?php echo esc_attr($course->ID); ?>">
                                    <?php echo esc_html($course->post_title); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="product_id">
                            <?php echo esc_html__('Product', 'custom-linking-plugin'); ?>:
                        </label>
                        <select name="product_id" id="product_id" class="regular-text" required>
                            <option value=""><?php echo esc_html__('-- Select Product --', 'custom-linking-plugin'); ?></option>
                            <?php foreach ($products as $product): ?>
                                <option value="<?php echo esc_attr($product->ID); ?>">
                                    <?php echo esc_html($product->post_title); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="type">
                            <?php echo esc_html__('Type', 'custom-linking-plugin'); ?>:
                        </label>
                        <select name="type" id="type" class="regular-text" required>
                            <option value="course"><?php echo esc_html__('Course', 'custom-linking-plugin'); ?></option>
                            <option value="bundle"><?php echo esc_html__('Bundle', 'custom-linking-plugin'); ?></option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <input type="submit" name="custom_linking_submit" class="button button-primary" value="<?php echo esc_attr__('Save Link', 'custom-linking-plugin'); ?>"> 
                    </div>
                </form>
            </div>
            
            <div class="custom-linking-admin-list">
                <h2><?php echo esc_html__('Existing Links', 'custom-linking-plugin'); ?></h2>
                
                <?php if (empty($links)): ?>
                    <div class="notice notice-info">
                        <p><?php echo esc_html__('No links found. Add your first course-product link above.', 'custom-linking-plugin'); ?></p>
                    </div>
                <?php else: ?>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th><?php echo esc_html__('ID', 'custom-linking-plugin'); ?></th>
                                <th><?php echo esc_html__('Course', 'custom-linking-plugin'); ?></th>
                                <th><?php echo esc_html__('Product', 'custom-linking-plugin'); ?></th>
                                <th><?php echo esc_html__('Type', 'custom-linking-plugin'); ?></th>
                                <th><?php echo esc_html__('Actions', 'custom-linking-plugin'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($links as $link): ?>
                                <tr>
                                    <td><?php echo esc_html($link->id); ?></td>
                                    <td>
                                        <?php 
                                        $course_title = get_the_title($link->course_id);
                                        echo $course_title ? esc_html($course_title) : esc_html__('Course not found', 'custom-linking-plugin') . ' (ID: ' . esc_html($link->course_id) . ')';
                                        ?>
                                    </td>
                                    <td>
                                        <?php 
                                        $product_title = get_the_title($link->product_id);
                                        echo $product_title ? esc_html($product_title) : esc_html__('Product not found', 'custom-linking-plugin') . ' (ID: ' . esc_html($link->product_id) . ')';
                                        ?>
                                    </td>
                                    <td><?php echo esc_html($link->type); ?></td>
                                    <td>
                                        <?php
                                        $delete_url = add_query_arg(
                                            array(
                                                'page' => 'custom-linking-plugin',
                                                'action' => 'delete',
                                                'id' => $link->id,
                                                '_wpnonce' => wp_create_nonce('custom_linking_delete_' . $link->id),
                                            ),
                                            admin_url('admin.php')
                                        );
                                        ?>
                                        <a href="<?php echo esc_url($delete_url); ?>" class="button button-small" 
                                           onclick="return confirm('<?php echo esc_js(__('Are you sure you want to delete this link?', 'custom-linking-plugin')); ?>')">
                                            <?php echo esc_html__('Delete', 'custom-linking-plugin'); ?>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php
}

/**
 * Handle form submission to save a new course-product link
 */
function custom_linking_handle_form_submission() {
    // Validate and sanitize input
    $course_id = isset($_POST['course_id']) ? (int) $_POST['course_id'] : 0;
    $product_id = isset($_POST['product_id']) ? (int) $_POST['product_id'] : 0;
    $type = isset($_POST['type']) && in_array($_POST['type'], array('course', 'bundle')) ? sanitize_text_field($_POST['type']) : 'course';
    
    // Validate required fields
    if (!$course_id || !$product_id) {
        // Redirect with error
        wp_redirect(add_query_arg('message', 'error', admin_url('admin.php?page=custom-linking-plugin')));
        exit;
    }
    
    // Save to database
    $result = link_product_to_course($course_id, $product_id, $type);
    
    // Redirect with appropriate message
    $message = $result ? 'success' : 'error';
    wp_redirect(add_query_arg('message', $message, admin_url('admin.php?page=custom-linking-plugin')));
    exit;
}

/**
 * Delete a course-product link
 *
 * @param int $id The link ID to delete
 */
function custom_linking_delete_link($id) {
    // Delete from database
    $result = unlink_product_from_course_by_id($id);
    
    // Redirect with appropriate message
    $message = $result ? 'deleted' : 'error';
    wp_redirect(add_query_arg('message', $message, admin_url('admin.php?page=custom-linking-plugin')));
    exit;
}

/**
 * Get all course-product links from the database
 *
 * @return array Array of link objects
 */
function custom_linking_get_all_links() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'linking_table';
    
    $links = $wpdb->get_results("SELECT * FROM $table_name ORDER BY id DESC");
    
    return $links ?: array();
}

/**
 * Get available MasterStudy LMS courses
 *
 * @return array Array of course post objects
 */
function custom_linking_get_available_courses() {
    // Check if MasterStudy LMS is active and has the stm-lms-course post type
    if (!post_type_exists('stm-courses')) {
        return array();
    }
    
    return get_posts(array(
        'post_type' => 'stm-courses',
        'post_status' => 'publish',
        'numberposts' => -1,
        'orderby' => 'title',
        'order' => 'ASC',
    ));
}

/**
 * Get available WooCommerce products
 *
 * @return array Array of product post objects
 */
function custom_linking_get_available_products() {
    // Check if WooCommerce is active and has the product post type
    if (!post_type_exists('product')) {
        return array();
    }
    
    return get_posts(array(
        'post_type' => 'product',
        'post_status' => 'publish',
        'numberposts' => -1,
        'orderby' => 'title',
        'order' => 'ASC',
    ));
}