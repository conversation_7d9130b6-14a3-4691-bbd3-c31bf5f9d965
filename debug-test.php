<?php
/**
 * Debug test file for Custom Linking Plugin
 * 
 * This file will write to debug.log immediately when accessed
 * Used to test if the plugin can write to the debug file
 */

// Path to the plugin directory
$plugin_dir = dirname(__FILE__);
$log_file = $plugin_dir . '/debug.log';

/**
 * Simple function to write directly to the debug log
 * Uses basic file operations to ensure writing works
 * 
 * @param string $message Message to log
 */
function write_debug($message) {
    global $log_file;
    
    // Create the message with timestamp
    $timestamp = date('[Y-m-d H:i:s]');
    $log_message = $timestamp . ' ' . $message . "\n";
    
    // Write using basic file operations
    $handle = fopen($log_file, 'a');
    if ($handle) {
        fwrite($handle, $log_message);
        fclose($handle);
        return true;
    }
    return false;
}

// Write test entries - will execute immediately
write_debug('DEBUG TEST: Debug file writing test started');
write_debug('DEBUG TEST: PHP Version: ' . phpversion());
write_debug('DEBUG TEST: Plugin directory: ' . $plugin_dir);

// Try to create a blank file to test permissions
$test_file = $plugin_dir . '/test-write.tmp';
$write_test = @file_put_contents($test_file, 'Test write access');
write_debug('DEBUG TEST: File write test result: ' . ($write_test ? 'Success' : 'Failed'));

// Try to delete the test file
if (file_exists($test_file)) {
    $delete_test = @unlink($test_file);
    write_debug('DEBUG TEST: File delete test result: ' . ($delete_test ? 'Success' : 'Failed'));
}

// Write basic plugin info
write_debug('DEBUG TEST: WordPress version: ' . (defined('WP_VERSION') ? WP_VERSION : 'Unknown'));
write_debug('DEBUG TEST: Debug test completed');

// Output success message if this file is accessed directly
if (isset($_GET['test'])) {
    echo 'Debug test completed. Check debug.log in the plugin directory.';
}
