<?php
/**
 * Test script to check if the plugin can be loaded without fatal errors
 */

// Simulate WordPress environment minimally
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(dirname(dirname(dirname(__FILE__)))) . '/');
}

// Define basic WordPress functions that might be needed
if (!function_exists('plugin_dir_path')) {
    function plugin_dir_path($file) {
        return trailingslashit(dirname($file));
    }
}

if (!function_exists('plugin_dir_url')) {
    function plugin_dir_url($file) {
        return 'http://localhost/paylearn/wp-content/plugins/' . basename(dirname($file)) . '/';
    }
}

if (!function_exists('trailingslashit')) {
    function trailingslashit($string) {
        return rtrim($string, '/\\') . '/';
    }
}

// Try to include the main plugin file
echo "Testing plugin activation...\n";

try {
    // Include the main plugin file
    include_once 'custom-linking-plugin.php';
    echo "SUCCESS: Plugin file loaded without fatal errors\n";
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
} catch (ParseError $e) {
    echo "PARSE ERROR: " . $e->getMessage() . "\n";
} catch (Error $e) {
    echo "FATAL ERROR: " . $e->getMessage() . "\n";
}

echo "Test completed.\n";
