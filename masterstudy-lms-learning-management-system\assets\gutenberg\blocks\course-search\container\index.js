(()=>{var e,t={6492:(e,t,r)=>{"use strict";const a=window.React,o=window.wp.i18n,n=window.wp.blocks,s=window.wp.element,l=window.wp.blockEditor;var i=r(6942),c=r.n(i);const d=window.wp.components,u=(e,t)=>{if(typeof e!=typeof t)return!1;if(Number.isNaN(e)&&Number.isNaN(t))return!0;if("object"!=typeof e||null===e||null===t)return e===t;if(Object.keys(e).length!==Object.keys(t).length)return!1;if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;const r=e.slice().sort(),a=t.slice().sort();return r.every(((e,t)=>u(e,a[t])))}for(const r of Object.keys(e))if(!u(e[r],t[r]))return!1;return!0},m=(e=[])=>e.map((e=>({label:e.name,value:e.term_id})));let h=function(e){return e.ALL="all",e.SOME="some",e}({}),g=function(e){return e.NORMAL="Normal",e.HOVER="Hover",e.ACTIVE="Active",e.FOCUS="Focus",e}({}),p=function(e){return e.DESKTOP="Desktop",e.TABLET="Tablet",e.MOBILE="Mobile",e}({}),S=function(e){return e.TOP_lEFT="top-left",e.TOP_CENTER="top-center",e.TOP_RIGHT="top-right",e.BOTTOM_lEFT="bottom-left",e.BOTTOM_CENTER="bottom-center",e.BOTTOM_RIGHT="bottom-right",e}({});const y=["",null,void 0,"null","undefined"],C=[".jpg",".jpeg",".png",".gif"],b=e=>y.includes(e),v=(e,t,r="")=>{const a=e[t];return"object"==typeof a&&null!==a?((e,t)=>{return r=e,Object.values(r).every((e=>y.includes(e)))?null:((e,t="")=>{const r=Object.entries(e).reduce(((e,[r,a])=>(e[r]=(a||"0")+t,e)),{});return`${r.top} ${r.right} ${r.bottom} ${r.left}`})(e,t);var r})(a,r):((e,t)=>b(e)?e:(e=>{if("string"!=typeof e)return!1;const t=e.slice(e.lastIndexOf("."));return C.includes(t)||e.startsWith("blob")})(e)?`url('${e}')`:String(e+t))(a,r)},w=e=>({desktopPropertyName:e,tabletPropertyName:e+"Tablet",mobilePropertyName:e+"Mobile"}),B=(e,t,r)=>{const a={};return r.forEach((({isAdaptive:r,hasHover:o,unit:n},s)=>{if(t.hasOwnProperty(s)){const{unitMeasureDesktop:l,unitMeasureTablet:i,unitMeasureMobile:c}=((e,t)=>{var r;return{unitMeasureDesktop:null!==(r=e[t])&&void 0!==r?r:"",unitMeasureTablet:t?e[t+"Tablet"]:"",unitMeasureMobile:t?e[t+"Mobile"]:""}})(t,n);if(r&&o){const{desktopHoverPropertyName:r,mobileHoverPropertyName:o,tabletHoverPropertyName:n}=(e=>{const t=e+g.HOVER;return{desktopHoverPropertyName:t,tabletHoverPropertyName:t+"Tablet",mobileHoverPropertyName:t+"Mobile"}})(s),d=v(t,r,l);b(d)||(a[`--lms-${e}-${r}`]=d);const u=v(t,n,i);b(u)||(a[`--lms-${e}-${n}`]=u);const m=v(t,o,c);b(m)||(a[`--lms-${e}-${o}`]=m)}if(o){const r=s+g.HOVER,o=v(t,r,l);b(o)||(a[`--lms-${e}-${r}`]=o)}if(r){const{desktopPropertyName:r,mobilePropertyName:o,tabletPropertyName:n}=w(s),d=v(t,r,l);b(d)||(a[`--lms-${e}-${r}`]=d);const u=v(t,n,i);b(u)||(a[`--lms-${e}-${n}`]=u);const m=v(t,o,c);b(m)||(a[`--lms-${e}-${o}`]=m)}const d=v(t,s,l);b(d)||(a[`--lms-${e}-${s}`]=d)}})),a},_=(e,t,r,a,o,n)=>`${!0===e?"inset ":""} ${t}px ${r}px ${""!==a?`${a}px`:""} ${""!==o?`${o}px`:""} ${n}`,F=(e,t,r,a,o,n,s,l,i)=>{const c={};if(t[r]&&null!==t[a]&&null!==t[o]&&(c[`--lms-${e}-boxShadow`]=_(t[l],t[a],t[o],t[n],t[s],t[r])),i){const{tabletPropertyName:i,mobilePropertyName:d}=w(l),{tabletPropertyName:u,mobilePropertyName:m}=w(a),{tabletPropertyName:h,mobilePropertyName:g}=w(o),{tabletPropertyName:p,mobilePropertyName:S}=w(r),{tabletPropertyName:y,mobilePropertyName:C}=w(n),{tabletPropertyName:b,mobilePropertyName:v}=w(s);t[p]&&null!==t[u]&&null!==t[h]&&(c[`--lms-${e}-boxShadowTablet`]=_(t[i],t[u],t[h],t[y],t[b],t[p])),t[S]&&null!==t[m]&&null!==t[g]&&(c[`--lms-${e}-boxShadowMobile`]=_(t[d],t[m],t[g],t[C],t[v],t[S]))}return c},D=(o.__("Small","masterstudy-lms-learning-management-system"),o.__("Normal","masterstudy-lms-learning-management-system"),o.__("Large","masterstudy-lms-learning-management-system"),o.__("Extra Large","masterstudy-lms-learning-management-system"),"wp-block-masterstudy-settings__"),E={top:"",right:"",bottom:"",left:""},f=(S.TOP_lEFT,S.TOP_CENTER,S.TOP_RIGHT,S.BOTTOM_lEFT,S.BOTTOM_CENTER,S.BOTTOM_RIGHT,[{label:o.__("Newest","masterstudy-lms-learning-management-system"),value:"date_high"},{label:o.__("Oldest","masterstudy-lms-learning-management-system"),value:"date_low"},{label:o.__("Overall rating","masterstudy-lms-learning-management-system"),value:"rating"},{label:o.__("Popular","masterstudy-lms-learning-management-system"),value:"popular"},{label:o.__("Price low","masterstudy-lms-learning-management-system"),value:"price_low"},{label:o.__("Price high","masterstudy-lms-learning-management-system"),value:"price_high"}]);function N(e){return Array.isArray(e)?e.map((e=>D+e)):D+e}const x=window.wp.apiFetch;var T=r.n(x);const M="masterstudy-lms/v2/",U=`${M}course-categories`,H=`${M}courses`,P=()=>{const[e,t]=(0,s.useState)(!0),[r,a]=(0,s.useState)("");return{isFetching:e,setIsFetching:t,error:r,setError:a}},A=(e,t)=>{const[r,a]=(0,s.useState)([]);return(0,s.useEffect)((()=>{a(""===t?e.slice(0,5):e.filter((e=>e.label.toLowerCase().startsWith(t.toLowerCase()))).sort(((e,t)=>e.label.localeCompare(t.label))).slice(0,5))}),[t,e]),r},W=({categories:e})=>{const t=(0,a.useRef)([]);(0,a.useEffect)((()=>{const e=t.current;return e.forEach((e=>{e&&e.addEventListener("click",r)})),()=>{e.forEach((e=>{e&&e.removeEventListener("click",r)}))}}),[e]);const r=e=>{const t=e.target.closest("li");t&&t.classList.toggle("children-expanded")};return(0,a.createElement)("div",{className:"lms-courses-search-box__category"},(0,a.createElement)("div",{className:"lms-courses-search-box__category-label"},(0,a.createElement)("i",{className:"stmlms-hamburger"})," ",o.__("Category","masterstudy-lms-learning-management-system")),(0,a.createElement)("div",{className:"lms-courses-search-box__category-list-wrap"},(0,a.createElement)("ul",{className:"lms-courses-search-box__category-list"},e.map((({value:e,label:r,children:o},n)=>(0,a.createElement)("li",{key:e},(0,a.createElement)("a",{href:"#",onClick:e=>{e.preventDefault()}},(0,a.createElement)("span",null,r)),Array.isArray(o)&&o.length>0&&(0,a.createElement)(a.Fragment,null,(0,a.createElement)("span",{className:"stmlms-chevron-down children-expand",ref:e=>t.current[n]=e}),(0,a.createElement)("ul",null,o.map((({value:e,label:t})=>(0,a.createElement)("li",{key:e},(0,a.createElement)("a",{href:"#",onClick:e=>{e.preventDefault()}},t))))))))))))},k=({filteredCourses:e,searchTerm:t,setSearchTerm:r,showDropdown:o,setShowDropdown:n})=>(0,a.createElement)("div",{className:"lms-courses-search-box__bar"},(0,a.createElement)("div",{className:"lms-courses-search-box__bar-field"},(0,a.createElement)("input",{type:"text",placeholder:"Search",value:t,onChange:e=>r(e.target.value),onFocus:()=>n(!0),onBlur:()=>setTimeout((()=>n(!1)),200)}),(0,a.createElement)("div",{className:"lms-courses-search-box__bar-button"},(0,a.createElement)("i",{className:"stmlms-magnifier"}))),o&&e.length>0&&(0,a.createElement)("ul",{className:"lms-courses-search-box__bar-results"},e.map((({value:e,label:t})=>(0,a.createElement)("li",{key:e},(0,a.createElement)("a",{href:"#","data-value":e},t)))))),L=({options:e,categories:t,courses:r,...o})=>{const[n,l]=(0,s.useState)(""),[i,c]=(0,s.useState)(!1),d=A(r||[],n);return(0,a.createElement)("div",{...o},(0,a.createElement)(W,{categories:t||[]}),(0,a.createElement)(k,{courses:r||[],filteredCourses:d,searchTerm:n,setSearchTerm:l,showDropdown:i,setShowDropdown:c}))},R=({options:e,categories:t,courses:r,...o})=>{const[n,l]=(0,s.useState)(""),[i,c]=(0,s.useState)(!1),d=A(r||[],n),{buttonRef:u,innerBoxRef:m,innerWrapRef:h,handleInnerBoxClick:g}=(()=>{const[e,t]=(0,s.useState)(!1),r=(0,s.useRef)(null),a=(0,s.useRef)(null),o=(0,s.useRef)(null);return(0,s.useEffect)((()=>{const e=r.current,n=a.current,s=o.current;if(e&&n&&s){const r=()=>{t((e=>!e))},a=e=>{n.classList.contains("expanded")&&!s.contains(e.target)&&t(!1)};return e.addEventListener("click",r),n.addEventListener("click",a),()=>{e.removeEventListener("click",r),n.removeEventListener("click",a)}}}),[r,a,o]),(0,s.useEffect)((()=>{const t=a.current;t&&(e?t.classList.add("expanded"):t.classList.remove("expanded"))}),[e]),{isExpanded:e,buttonRef:r,innerBoxRef:a,innerWrapRef:o}})();return(0,a.createElement)("div",{...o},(0,a.createElement)("div",{className:"lms-courses-search-box__button",ref:u},(0,a.createElement)("i",{className:"stmlms-magnifier"})),(0,a.createElement)("div",{className:"lms-courses-search-box__inner",ref:m,onClick:g},(0,a.createElement)("div",{className:"lms-courses-search-box__inner-wrap",ref:h},(0,a.createElement)("div",{className:"lms-courses-search-box__inner-sidebar"},(0,a.createElement)(W,{categories:t||[]}),(0,a.createElement)(k,{courses:r||[],filteredCourses:d,searchTerm:n,setSearchTerm:l,showDropdown:i,setShowDropdown:c})))))},O=({condition:e,fallback:t=null,children:r})=>(0,a.createElement)(a.Fragment,null,e?r:t),z=window.wp.data,I=()=>(0,z.useSelect)((e=>e("core/editor").getDeviceType()||e("core/edit-site")?.__experimentalGetPreviewDeviceType()||e("core/edit-post")?.__experimentalGetPreviewDeviceType()||e("masterstudy/store")?.getDeviceType()),[])||"Desktop",V=(e=!1)=>{const[t,r]=(0,s.useState)(e),a=(0,s.useCallback)((()=>{r(!0)}),[]);return{isOpen:t,onClose:(0,s.useCallback)((()=>{r(!1)}),[]),onOpen:a,onToggle:(0,s.useCallback)((()=>{r((e=>!e))}),[])}},$=(0,s.createContext)(null),j=({children:e,...t})=>(0,a.createElement)($.Provider,{value:{...t}},e),Z=()=>{const e=(0,s.useContext)($);if(!e)throw new Error("No settings context provided");return e},G=(e="")=>{const{attributes:t,setAttributes:r,onResetByFieldName:a,changedFieldsByName:o}=Z();return{value:t[e],onChange:t=>r({[e]:t}),onReset:a.get(e),isChanged:o.get(e)}},K=(e,t=!1,r=!1)=>{const{hoverName:a,onChangeHoverName:o}=(()=>{const[e,t]=(0,s.useState)(g.NORMAL);return{hoverName:e,onChangeHoverName:(0,s.useCallback)((e=>{t(e)}),[])}})(),n=I();return{fieldName:(0,s.useMemo)((()=>{const o=a===g.HOVER?a:"",s=n===p.DESKTOP?"":n;return r&&t?e+o+s:r&&!t?e+o:t&&!r?e+s:e}),[e,r,t,a,n]),hoverName:a,onChangeHoverName:o}},X=(e,t=!1,r="Normal")=>{const a=I(),o=(0,s.useMemo)((()=>{const o=r===g.NORMAL?"":r,n=a===p.DESKTOP?"":a;return o&&t?e+o+n:o&&!t?e+o:t&&!o?e+n:e}),[e,t,r,a]),{value:n,isChanged:l,onReset:i}=G(o);return{fieldName:o,value:n,isChanged:l,onReset:i}},Y=(e=[],t=h.ALL)=>{const{attributes:r}=Z();return!e.length||(t===h.ALL?e.every((({name:e,value:t})=>{const a=r[e];return Array.isArray(t)?Array.isArray(a)?u(t,a):t.includes(a):t===a})):t!==h.SOME||e.some((({name:e,value:t})=>{const a=r[e];return Array.isArray(t)?Array.isArray(a)?u(t,a):t.includes(a):t===a})))},q=e=>{const t=(0,s.useRef)(null);return(0,s.useEffect)((()=>{const r=r=>{t.current&&!t.current.contains(r.target)&&e()};return document.addEventListener("click",r),()=>{document.removeEventListener("click",r)}}),[t,e]),t},J=e=>(0,a.createElement)(d.SVG,{width:"16",height:"16",viewBox:"0 0 12 13",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(d.Path,{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})),Q=e=>(0,a.createElement)(d.SVG,{width:"16",height:"16",viewBox:"0 0 14 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(d.G,{"clip-path":"url(#clip0_1068_38993)"},(0,a.createElement)(d.Path,{d:"M11.1973 8.60005L8.74967 8.11005V5.67171C8.74967 5.517 8.68822 5.36863 8.57882 5.25923C8.46942 5.14984 8.32105 5.08838 8.16634 5.08838H6.99967C6.84496 5.08838 6.69659 5.14984 6.5872 5.25923C6.4778 5.36863 6.41634 5.517 6.41634 5.67171V8.58838H5.24967C5.15021 8.58844 5.05241 8.61391 4.96555 8.66238C4.87869 8.71084 4.80565 8.78068 4.75336 8.86529C4.70106 8.9499 4.67125 9.04646 4.66674 9.14582C4.66223 9.24518 4.68317 9.34405 4.72759 9.43305L6.47759 12.933C6.57676 13.1302 6.77859 13.255 6.99967 13.255H11.083C11.2377 13.255 11.3861 13.1936 11.4955 13.0842C11.6049 12.9748 11.6663 12.8264 11.6663 12.6717V9.17171C11.6665 9.03685 11.6198 8.90612 11.5343 8.80186C11.4487 8.69759 11.3296 8.62626 11.1973 8.60005Z"}),(0,a.createElement)(d.Path,{d:"M10.4997 1.58838H3.49967C2.85626 1.58838 2.33301 2.11221 2.33301 2.75505V6.83838C2.33301 7.4818 2.85626 8.00505 3.49967 8.00505H5.24967V6.83838H3.49967V2.75505H10.4997V6.83838H11.6663V2.75505C11.6663 2.11221 11.1431 1.58838 10.4997 1.58838Z"})),(0,a.createElement)("defs",null,(0,a.createElement)("clipPath",{id:"clip0_1068_38993"},(0,a.createElement)(d.Rect,{width:"14",height:"14",transform:"translate(0 0.421875)"})))),ee=[{value:g.NORMAL,label:o.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(J,{onClick:e})},{value:g.HOVER,label:o.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(Q,{onClick:e})},{value:g.ACTIVE,label:o.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(Q,{onClick:e})},{value:g.FOCUS,label:o.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(Q,{onClick:e})}],te={[g.NORMAL]:{icon:(0,a.createElement)(J,null),label:o.__("Normal State","masterstudy-lms-learning-management-system")},[g.HOVER]:{icon:(0,a.createElement)(Q,null),label:o.__("Hovered State","masterstudy-lms-learning-management-system")},[g.ACTIVE]:{icon:(0,a.createElement)(Q,null),label:o.__("Active State","masterstudy-lms-learning-management-system")},[g.FOCUS]:{icon:(0,a.createElement)(Q,null),label:o.__("Focus State","masterstudy-lms-learning-management-system")}},re=(e,t)=>{let r=[];return r=e.length?ee.filter((t=>e.includes(t.value))):ee,r=r.filter((e=>e.value!==t)),{ICONS_MAP:te,options:r}},[ae,oe,ne,se,le]=N(["hover-state","hover-state__selected","hover-state__selected__opened-menu","hover-state__menu","hover-state__menu__item"]),ie=({stateOptions:e,currentState:t,onSelect:r})=>{const{isOpen:o,onOpen:n,onClose:s}=V(),l=q(s),{ICONS_MAP:i,options:d}=re(e,t);return(0,a.createElement)("div",{className:ae,ref:l},(0,a.createElement)("div",{className:c()([oe],{[ne]:o}),onClick:n,title:i[t]?.label},i[t]?.icon),(0,a.createElement)(O,{condition:o},(0,a.createElement)("div",{className:se},d.map((({value:e,icon:t,label:o})=>(0,a.createElement)("div",{key:e,className:le,title:o},t((()=>r(e)))))))))},ce=N("color-indicator"),de=(0,s.memo)((({color:e,onChange:t})=>(0,a.createElement)("div",{className:ce},(0,a.createElement)(l.PanelColorSettings,{enableAlpha:!0,disableCustomColors:!1,__experimentalHasMultipleOrigins:!0,__experimentalIsRenderedInSidebar:!0,colorSettings:[{label:"",value:e,onChange:t}]}))));var ue;function me(){return me=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)({}).hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},me.apply(null,arguments)}var he,ge,pe=function(e){return a.createElement("svg",me({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 12 13"},e),ue||(ue=a.createElement("path",{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})))};function Se(){return Se=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)({}).hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},Se.apply(null,arguments)}var ye=function(e){return a.createElement("svg",Se({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 14 15"},e),he||(he=a.createElement("g",{clipPath:"url(#state-hover_svg__a)"},a.createElement("path",{d:"M11.197 8.6 8.75 8.11V5.672a.583.583 0 0 0-.584-.584H7a.583.583 0 0 0-.584.584v2.916H5.25a.584.584 0 0 0-.522.845l1.75 3.5c.099.197.3.322.522.322h4.083a.583.583 0 0 0 .583-.583v-3.5a.58.58 0 0 0-.469-.572"}),a.createElement("path",{d:"M10.5 1.588h-7c-.644 0-1.167.524-1.167 1.167v4.083c0 .644.523 1.167 1.167 1.167h1.75V6.838H3.5V2.755h7v4.083h1.166V2.755c0-.643-.523-1.167-1.166-1.167"}))),ge||(ge=a.createElement("defs",null,a.createElement("clipPath",{id:"state-hover_svg__a"},a.createElement("path",{d:"M0 .422h14v14H0z"})))))};const Ce=[{value:g.NORMAL,label:o.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(pe,{onClick:e})},{value:g.HOVER,label:o.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(ye,{onClick:e})}],be={[g.NORMAL]:{icon:(0,a.createElement)(pe,null),label:o.__("Normal State","masterstudy-lms-learning-management-system")},[g.HOVER]:{icon:(0,a.createElement)(ye,null),label:o.__("Hovered State","masterstudy-lms-learning-management-system")}},ve=N("hover-state"),we=N("hover-state__selected"),Be=N("hover-state__selected__opened-menu"),_e=N("has-changes"),Fe=N("hover-state__menu"),De=N("hover-state__menu__item"),Ee=(0,s.memo)((e=>{const{hoverName:t,onChangeHoverName:r,fieldName:o}=e,{changedFieldsByName:n}=Z(),l=n.get(o),{isOpen:i,onOpen:d,onClose:u}=V(),m=q(u),{ICONS_MAP:h,options:g}=(e=>{const t=(0,s.useMemo)((()=>Ce.filter((t=>t.value!==e))),[e]);return{ICONS_MAP:be,options:t}})(t),p=(0,s.useCallback)((e=>{r(e),u()}),[r,u]);return(0,a.createElement)("div",{className:ve,ref:m},(0,a.createElement)("div",{className:c()([we],{[Be]:i,[_e]:l}),onClick:d,title:h[t]?.label},h[t]?.icon),(0,a.createElement)(O,{condition:i},(0,a.createElement)("div",{className:Fe},g.map((({value:e,icon:t,label:r})=>(0,a.createElement)("div",{key:e,className:De,title:r},t((()=>p(e)))))))))})),fe={Desktop:{icon:"desktop",label:o.__("Desktop","masterstudy-lms-learning-management-system")},Tablet:{icon:"tablet",label:o.__("Tablet","masterstudy-lms-learning-management-system")},Mobile:{icon:"smartphone",label:o.__("Mobile","masterstudy-lms-learning-management-system")}},Ne=[{value:p.DESKTOP,icon:"desktop",label:o.__("Desktop","masterstudy-lms-learning-management-system")},{value:p.TABLET,icon:"tablet",label:o.__("Tablet","masterstudy-lms-learning-management-system")},{value:p.MOBILE,icon:"smartphone",label:o.__("Mobile","masterstudy-lms-learning-management-system")}],xe=N("device-picker"),Te=N("device-picker__selected"),Me=N("device-picker__selected__opened-menu"),Ue=N("device-picker__menu"),He=N("device-picker__menu__item"),Pe=()=>{const{isOpen:e,onOpen:t,onClose:r}=V(),{value:o,onChange:n}=(e=>{const t=I(),r=(0,z.useDispatch)();return{value:(0,s.useMemo)((()=>fe[t]),[t]),onChange:t=>{r("core/edit-site")&&r("core/edit-site").__experimentalSetPreviewDeviceType?r("core/edit-site").__experimentalSetPreviewDeviceType(t):r("core/edit-post")&&r("core/edit-post").__experimentalSetPreviewDeviceType?r("core/edit-post").__experimentalSetPreviewDeviceType(t):r("masterstudy/store").setDeviceType(t),e()}}})(r),l=(e=>(0,s.useMemo)((()=>Ne.filter((t=>t.icon!==e))),[e]))(o.icon),i=q(r);return(0,a.createElement)("div",{className:xe,ref:i},(0,a.createElement)(d.Dashicon,{className:c()([Te],{[Me]:e}),icon:o.icon,size:16,onClick:t,title:o.label}),(0,a.createElement)(O,{condition:e},(0,a.createElement)("div",{className:Ue},l.map((e=>(0,a.createElement)(d.Dashicon,{key:e.value,icon:e.icon,size:16,onClick:()=>n(e.value),className:He,title:e.label}))))))},Ae=N("reset-button"),We=({onReset:e})=>(0,a.createElement)(d.Dashicon,{icon:"undo",onClick:e,className:Ae,size:16}),ke=[{label:"PX",value:"px"},{label:"%",value:"%"},{label:"EM",value:"em"},{label:"REM",value:"rem"},{label:"VW",value:"vw"},{label:"VH",value:"vh"}],Le=N("unit"),Re=N("unit__single"),Oe=N("unit__list"),ze=({name:e,isAdaptive:t})=>{const{isOpen:r,onOpen:o,onClose:n}=V(),{fieldName:s}=K(e,t),{value:l,onChange:i}=G(s),c=q(n);return(0,a.createElement)("div",{className:Le,ref:c},(0,a.createElement)("div",{className:Re,onClick:o},l),(0,a.createElement)(O,{condition:r},(0,a.createElement)("div",{className:Oe},ke.map((({value:e,label:t})=>(0,a.createElement)("div",{key:e,onClick:()=>(i(e),void n())},t))))))},Ie=N("popover-modal"),Ve=N("popover-modal__close dashicon dashicons dashicons-no-alt"),$e=e=>{const{isOpen:t,onClose:r,popoverContent:o}=e;return(0,a.createElement)(O,{condition:t},(0,a.createElement)(d.Popover,{position:"middle left",onClose:r,className:Ie},o,(0,a.createElement)("span",{onClick:r,className:Ve})))},je=N("setting-label"),Ze=N("setting-label__content"),Ge=e=>{const{label:t,isChanged:r=!1,onReset:o,showDevicePicker:n=!0,HoverStateControl:s=null,unitName:l,popoverContent:i=null,dependencies:c}=e,{isOpen:d,onClose:u,onToggle:m}=V();return Y(c)?(0,a.createElement)("div",{className:je},(0,a.createElement)("div",{className:Ze},(0,a.createElement)("div",{onClick:m},t),(0,a.createElement)(O,{condition:Boolean(i)},(0,a.createElement)($e,{isOpen:d,onClose:u,popoverContent:i})),(0,a.createElement)(O,{condition:n},(0,a.createElement)(Pe,null)),(0,a.createElement)(O,{condition:Boolean(s)},s)),(0,a.createElement)(O,{condition:Boolean(l)},(0,a.createElement)(ze,{name:l,isAdaptive:n})),(0,a.createElement)(O,{condition:r},(0,a.createElement)(We,{onReset:o}))):null},Ke=N("suffix"),Xe=()=>(0,a.createElement)("div",{className:Ke},(0,a.createElement)(d.Dashicon,{icon:"color-picker",size:16})),Ye=N("color-picker"),qe=e=>{const{name:t,label:r,placeholder:o,dependencyMode:n,dependencies:s,isAdaptive:l=!1,hasHover:i=!1}=e,{fieldName:c,hoverName:u,onChangeHoverName:m}=K(t,l,i),{value:h,isChanged:g,onChange:p,onReset:S}=G(c);return Y(s,n)?(0,a.createElement)("div",{className:Ye},(0,a.createElement)(O,{condition:Boolean(r)},(0,a.createElement)(Ge,{label:r,isChanged:g,onReset:S,showDevicePicker:l,HoverStateControl:(0,a.createElement)(O,{condition:i},(0,a.createElement)(Ee,{hoverName:u,onChangeHoverName:m,fieldName:c}))})),(0,a.createElement)(d.__experimentalInputControl,{prefix:(0,a.createElement)(de,{color:h,onChange:p}),suffix:(0,a.createElement)(Xe,null),onChange:p,value:h,placeholder:o})):null},Je=N("number-steppers"),Qe=N("indent-steppers"),et=N("indent-stepper-plus"),tt=N("indent-stepper-minus"),rt=({onIncrement:e,onDecrement:t,withArrows:r=!1})=>r?(0,a.createElement)("span",{className:Qe},(0,a.createElement)("button",{onClick:e,className:et}),(0,a.createElement)("button",{onClick:t,className:tt})):(0,a.createElement)("span",{className:Je},(0,a.createElement)("button",{onClick:e},"+"),(0,a.createElement)("button",{onClick:t},"-")),[at,ot]=N(["indents","indents-control"]),nt=({name:e,label:t,unitName:r,popoverContent:n,dependencyMode:l,dependencies:i,isAdaptive:c=!1})=>{const{fieldName:u}=K(e,c),{value:m,onResetSegmentedBox:h,hasChanges:g,handleInputIncrement:p,handleInputDecrement:S,updateDirectionsValues:y,lastFieldValue:C}=((e,t)=>{const{value:r,isChanged:a,onChange:o,onReset:n}=G(e),{onResetByFieldName:l,changedFieldsByName:i}=Z(),c=a||i.get(t),d=e=>{o({...r,...e})},[u,m]=(0,s.useState)(!1);return{value:r,onResetSegmentedBox:()=>{n(),l.get(t)()},hasChanges:c,handleInputIncrement:e=>Number(r[e])+1,handleInputDecrement:e=>Number(r[e])-1,updateDirectionsValues:(e,t,r)=>{e?(m(!1),d({top:r,right:r,bottom:r,left:r})):(m(r),d({[t]:r}))},lastFieldValue:u}})(u,r),[b,v]=(0,s.useState)((()=>{const{left:e,right:t,top:r,bottom:a}=m;return""!==e&&e===t&&t===r&&r===a})),w=e=>{const[t,r]=Object.entries(e)[0];y(b,t,r)},B=e=>()=>{const t=p(e);y(b,e,String(t))},_=e=>()=>{const t=S(e);y(b,e,String(t))};return Y(i,l)?(0,a.createElement)("div",{className:at},(0,a.createElement)(O,{condition:Boolean(t)},(0,a.createElement)(Ge,{label:null!=t?t:"",isChanged:g,onReset:h,unitName:r,popoverContent:n,showDevicePicker:c})),(0,a.createElement)("div",{className:`${ot} ${b?"active":""}`},(0,a.createElement)("div",null,(0,a.createElement)(d.__experimentalNumberControl,{value:m.top,onChange:e=>{w({top:e})},spinControls:"none",suffix:(0,a.createElement)(rt,{onIncrement:B("top"),onDecrement:_("top"),withArrows:!0})}),(0,a.createElement)("div",null,o.__("Top","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(d.__experimentalNumberControl,{value:m.right,onChange:e=>{w({right:e})},spinControls:"none",suffix:(0,a.createElement)(rt,{onIncrement:B("right"),onDecrement:_("right"),withArrows:!0})}),(0,a.createElement)("div",null,o.__("Right","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(d.__experimentalNumberControl,{value:m.bottom,onChange:e=>{w({bottom:e})},spinControls:"none",suffix:(0,a.createElement)(rt,{onIncrement:B("bottom"),onDecrement:_("bottom"),withArrows:!0})}),(0,a.createElement)("div",null,o.__("Bottom","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(d.__experimentalNumberControl,{value:m.left,onChange:e=>{w({left:e})},spinControls:"none",suffix:(0,a.createElement)(rt,{onIncrement:B("left"),onDecrement:_("left"),withArrows:!0})}),(0,a.createElement)("div",null,o.__("Left","masterstudy-lms-learning-management-system"))),(0,a.createElement)(d.Dashicon,{icon:"dashicons "+(b?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{b||!1===C||y(!0,"left",C),v((e=>!e))}}))):null},[st,lt,it,ct]=N(["toggle-group-wrapper","toggle-group","toggle-group__toggle","toggle-group__active-toggle"]),dt=e=>{const{name:t,options:r,label:o,isAdaptive:n=!1,dependencyMode:s,dependencies:l}=e,{fieldName:i}=K(t,n),{value:d,isChanged:u,onChange:m,onReset:h}=G(i);return Y(l,s)?(0,a.createElement)("div",{className:st},(0,a.createElement)(O,{condition:Boolean(o)},(0,a.createElement)(Ge,{label:o,isChanged:u,onReset:h,showDevicePicker:n})),(0,a.createElement)("div",{className:lt},r.map((e=>(0,a.createElement)("div",{key:e.value,className:c()([it],{[ct]:e.value===d}),onClick:()=>m(e.value)},e.label))))):null},[ut,mt,ht,gt]=N(["border-control","border-control-solid","border-control-dashed","border-control-dotted"]),pt=e=>{const{label:t,borderStyleName:r,borderColorName:n,borderWidthName:l,dependencyMode:i,dependencies:d,isAdaptive:u=!1,hasHover:m=!1}=e,[h,g]=(0,s.useState)("Normal"),{fieldName:p,value:S,isChanged:y,onReset:C}=X(r,u,h),{fieldName:b,isChanged:v,onReset:w}=X(n,u,h),{fieldName:B,isChanged:_,onReset:F}=X(l,u,h);if(!Y(d,i))return null;const D=y||v||_;return(0,a.createElement)("div",{className:c()([ut],{"has-reset-button":D})},(0,a.createElement)(Ge,{label:t,isChanged:D,onReset:()=>{C(),w(),F()},showDevicePicker:u,HoverStateControl:(0,a.createElement)(O,{condition:m},(0,a.createElement)(ie,{stateOptions:["Normal","Hover"],currentState:h,onSelect:g}))}),(0,a.createElement)(dt,{options:[{label:(0,a.createElement)("span",null,o.__("None","masterstudy-lms-learning-management-system")),value:"none"},{label:(0,a.createElement)("span",{className:mt}),value:"solid"},{label:(0,a.createElement)("span",{className:ht},(0,a.createElement)("span",null)),value:"dashed"},{label:(0,a.createElement)("span",{className:gt},(0,a.createElement)("span",null,(0,a.createElement)("span",null))),value:"dotted"}],name:p}),(0,a.createElement)(O,{condition:"none"!==S},(0,a.createElement)(qe,{name:b,placeholder:o.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(nt,{name:B})))},St=N("border-radius"),yt=N("border-radius-control"),Ct=({name:e,label:t,unitName:r,popoverContent:o,dependencyMode:n,dependencies:l,isAdaptive:i=!1,hasHover:u=!1})=>{const{fieldName:m}=K(e,i,u),{value:h,onResetBorderRadius:g,hasChanges:p,handleInputIncrement:S,handleInputDecrement:y,updateDirectionsValues:C,lastFieldValue:b}=((e,t)=>{const[r,a]=(0,s.useState)(!1),{value:o,isChanged:n,onChange:l,onReset:i}=G(e),{onResetByFieldName:c,changedFieldsByName:d}=Z(),u=n||d.get(t),m=e=>{l({...o,...e})};return{value:o,onResetBorderRadius:()=>{i(),c.get(t)()},hasChanges:u,handleInputIncrement:e=>Number(o[e])+1,handleInputDecrement:e=>Number(o[e])-1,updateDirectionsValues:(e,t,r)=>{e?(m({top:r,right:r,bottom:r,left:r}),a(!1)):(m({[t]:r}),a(r))},lastFieldValue:r}})(m,r),[v,w]=(0,s.useState)((()=>{const{left:e,right:t,top:r,bottom:a}=h;return""!==e&&e===t&&t===r&&r===a})),B=e=>{const[t,r]=Object.entries(e)[0];C(v,t,r)},_=e=>()=>{const t=S(e);C(v,e,String(t))},F=e=>()=>{const t=y(e);C(v,e,String(t))};return Y(l,n)?(0,a.createElement)("div",{className:St},(0,a.createElement)(Ge,{label:t,isChanged:p,onReset:g,unitName:r,popoverContent:o,showDevicePicker:i}),(0,a.createElement)("div",{className:c()([yt],{"has-reset-button":p,active:v})},(0,a.createElement)("div",{className:"number-control-top"},(0,a.createElement)(d.__experimentalNumberControl,{value:h.top,onChange:e=>{B({top:e})},spinControls:"none",suffix:(0,a.createElement)(rt,{onIncrement:_("top"),onDecrement:F("top"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-right"},(0,a.createElement)(d.__experimentalNumberControl,{value:h.right,onChange:e=>{B({right:e})},spinControls:"none",suffix:(0,a.createElement)(rt,{onIncrement:_("right"),onDecrement:F("right"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-left"},(0,a.createElement)(d.__experimentalNumberControl,{value:h.left,onChange:e=>{B({left:e})},spinControls:"none",suffix:(0,a.createElement)(rt,{onIncrement:_("left"),onDecrement:F("left"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-bottom"},(0,a.createElement)(d.__experimentalNumberControl,{value:h.bottom,onChange:e=>{B({bottom:e})},spinControls:"none",suffix:(0,a.createElement)(rt,{onIncrement:_("bottom"),onDecrement:F("bottom"),withArrows:!0})})),(0,a.createElement)(d.Dashicon,{icon:"dashicons "+(v?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{v||!1===b||C(!0,"left",b),w((e=>!e))}}))):null},bt=N("box-shadow-preset"),vt=({preset:e})=>(0,a.createElement)("div",{className:bt},(0,a.createElement)("div",{style:{boxShadow:`${e.horizontal}px ${e.vertical}px ${e.blur}px ${e.spread}px rgba(0, 0, 0, 0.25) ${e.inset?"inset":""}`}})),wt=N("presets"),Bt=N("presets__item-wrapper"),_t=N("presets__item-wrapper__preset"),Ft=N("presets__item-wrapper__name"),Dt=(0,s.memo)((e=>{const{presets:t,activePreset:r,onSelectPreset:o,PresetItem:n,detectIsActive:s,detectByIndex:l=!1}=e;return(0,a.createElement)("div",{className:wt},t.map((({name:e,...t},i)=>(0,a.createElement)("div",{key:i,className:c()([Bt],{active:s(r,l?i:t)}),onClick:()=>o(t)},(0,a.createElement)("div",{className:_t},(0,a.createElement)(n,{preset:t})),(0,a.createElement)("span",{className:Ft},e)))))})),Et=N("range-control"),ft=e=>{const{name:t,label:r,min:o,max:n,unitName:s,dependencyMode:l,dependencies:i,isAdaptive:c=!1}=e,{fieldName:u}=K(t,c),{value:m,onChange:h,onResetNumberField:g,hasChanges:p}=((e,t)=>{const{value:r,isChanged:a,onChange:o,onReset:n}=G(e),{onResetByFieldName:s,changedFieldsByName:l}=Z();return{value:r,onChange:o,onResetNumberField:()=>{n(),s.get(t)()},hasChanges:a||l.get(t)}})(u,s);return Y(i,l)?(0,a.createElement)("div",{className:Et},(0,a.createElement)(O,{condition:Boolean(r)},(0,a.createElement)(Ge,{label:r,isChanged:p,onReset:g,unitName:s,showDevicePicker:c})),(0,a.createElement)(d.RangeControl,{value:m,onChange:h,min:o,max:n})):null},Nt=N("switch"),xt=e=>{const{name:t,label:r,dependencyMode:o,dependencies:n,isAdaptive:s=!1}=e,{fieldName:l}=K(t,s),{value:i,onChange:c}=G(l);return Y(n,o)?(0,a.createElement)("div",{className:Nt,"data-has-label":Boolean(r).toString()},(0,a.createElement)(d.ToggleControl,{label:r,checked:i,onChange:c}),(0,a.createElement)(O,{condition:s},(0,a.createElement)(Pe,null))):null},Tt=N("box-shadow-settings"),Mt=N("box-shadow-presets-title"),Ut=[{name:"Drop",horizontal:0,vertical:2,blur:2,spread:0,inset:!1},{name:"Glow",horizontal:0,vertical:4,blur:20,spread:0,inset:!1},{name:"Outline",horizontal:0,vertical:2,blur:10,spread:0,inset:!1},{name:"Sparse",horizontal:0,vertical:10,blur:50,spread:0,inset:!1}],Ht=e=>{const{label:t,min:r,max:n,shadowColorName:l,shadowHorizontalName:i,shadowVerticalName:c,shadowBlurName:d,shadowSpreadName:m,shadowInsetName:h,popoverContent:g,dependencyMode:p,dependencies:S,isAdaptive:y=!1,hasHover:C=!1,presets:b=Ut}=e,[v,w]=(0,s.useState)("Normal"),{fieldName:B}=X(l,y,v),{fieldName:_}=X(i,y,v),{fieldName:F}=X(c,y,v),{fieldName:D}=X(d,y,v),{fieldName:E}=X(m,y,v),{fieldName:f}=X(h,y,v),{isChanged:N,onReset:x,onSelectPreset:T,activePreset:M}=((e,t,r,a,o,n)=>{const{setAttributes:l}=Z(),{value:i,isChanged:c,onReset:d}=G(e),{value:u,isChanged:m,onReset:h}=G(t),{value:g,isChanged:p,onReset:S}=G(r),{value:y,isChanged:C,onReset:b}=G(a),{value:v,isChanged:w,onReset:B}=G(o),{value:_,isChanged:F,onReset:D}=G(n),E=c||m||p||C||w||F,f=(0,s.useCallback)((e=>{const{horizontal:s,vertical:i,blur:c,spread:d,inset:u}=e;l({[t]:s,[r]:i,[a]:c,[o]:d,[n]:u})}),[t,r,a,o,l,n]);return{activePreset:(0,s.useMemo)((()=>({horizontal:u,vertical:g,blur:y,spread:v,inset:null!=_&&_})),[u,g,y,v,_]),onReset:()=>{[d,h,S,b,B,D].forEach((e=>e()))},isChanged:E,onSelectPreset:f}})(B,_,F,D,E,f);return Y(S,p)?(0,a.createElement)("div",{className:Tt},(0,a.createElement)(Ge,{label:t,isChanged:N,onReset:x,popoverContent:g,showDevicePicker:y,HoverStateControl:(0,a.createElement)(O,{condition:C},(0,a.createElement)(ie,{stateOptions:["Normal","Hover"],currentState:v,onSelect:w}))}),(0,a.createElement)(Dt,{presets:b,onSelectPreset:T,activePreset:M,PresetItem:vt,detectIsActive:u,detectByIndex:!1}),(0,a.createElement)(qe,{name:B,label:o.__("Color","masterstudy-lms-learning-management-system"),placeholder:"Select color"}),(0,a.createElement)(ft,{name:_,label:o.__("Horizontal Offset","masterstudy-lms-learning-management-system"),min:r,max:n}),(0,a.createElement)(ft,{name:F,label:o.__("Vertical Offset","masterstudy-lms-learning-management-system"),min:r,max:n}),(0,a.createElement)(ft,{name:D,label:o.__("Blur","masterstudy-lms-learning-management-system"),min:r,max:n}),(0,a.createElement)(ft,{name:E,label:o.__("Spread","masterstudy-lms-learning-management-system"),min:r,max:n}),(0,a.createElement)("div",{className:Mt},o.__("Position","masterstudy-lms-learning-management-system")),(0,a.createElement)(xt,{name:f,label:o.__("Inset","masterstudy-lms-learning-management-system")})):null},Pt=(N("input-field"),N("input-field-control"),N("number-field")),At=N("number-field-control"),Wt=e=>{const{name:t,label:r,unitName:o,help:n,popoverContent:s,dependencyMode:l,dependencies:i,isAdaptive:c=!1}=e,{fieldName:u}=K(t,c),{value:m,onResetNumberField:h,hasChanges:g,handleIncrement:p,handleDecrement:S,handleInputChange:y}=((e,t)=>{const{value:r,isChanged:a,onChange:o,onReset:n}=G(e),{onResetByFieldName:s,changedFieldsByName:l}=Z(),i=a||l.get(t);return{value:r,onResetNumberField:()=>{n(),s.get(t)()},hasChanges:i,handleIncrement:()=>{o(r+1)},handleDecrement:()=>{o(r-1)},handleInputChange:e=>{const t=Number(""===e?0:e);o(t)}}})(u,o);return Y(i,l)?(0,a.createElement)("div",{className:Pt},(0,a.createElement)(Ge,{label:r,isChanged:g,onReset:h,unitName:o,showDevicePicker:c,popoverContent:s}),(0,a.createElement)("div",{className:At},(0,a.createElement)(d.__experimentalNumberControl,{value:m,onChange:y,spinControls:"none",suffix:(0,a.createElement)(rt,{onIncrement:p,onDecrement:S})})),n&&(0,a.createElement)("small",null,n)):null},kt=({className:e})=>(0,a.createElement)("div",{className:e},o.__("No options","masterstudy-lms-learning-management-system")),Lt=N("select__single-item"),Rt=N("select__container"),Ot=N("select__container__multi-item"),zt=({multiple:e,value:t,options:r,onChange:o})=>{const{singleValue:n,multipleValue:l}=((e,t,r)=>({singleValue:(0,s.useMemo)((()=>t?null:r.find((t=>t.value===e))?.label),[t,e,r]),multipleValue:(0,s.useMemo)((()=>t?e:null),[t,e])}))(t,e,r);return(0,a.createElement)(O,{condition:e,fallback:(0,a.createElement)("div",{className:Lt},n)},(0,a.createElement)("div",{className:Rt},l?.map((e=>{const t=r.find((t=>t.value===e));return t?(0,a.createElement)("div",{key:t.value,className:Ot},(0,a.createElement)("div",null,t.label),(0,a.createElement)(d.Dashicon,{icon:"no-alt",onClick:()=>o(t.value),size:16})):null}))))},It=N("select"),Vt=N("select__select-box"),$t=N("select__placeholder"),jt=N("select__select-box-multiple"),Zt=N("select__menu"),Gt=N("select__menu__options-container"),Kt=N("select__menu__item"),Xt=e=>{const{options:t,multiple:r=!1,placeholder:o="Select",value:n,onSelect:l}=e,{isOpen:i,onToggle:u,onClose:m}=V(),h=q(m),g=((e,t,r)=>(0,s.useMemo)((()=>r&&Array.isArray(e)?t.filter((t=>!e.includes(t.value))):t.filter((t=>t.value!==e))),[e,t,r]))(n,t,r),p=((e,t,r,a)=>(0,s.useCallback)((o=>{if(t&&Array.isArray(e)){const t=e.includes(o)?e.filter((e=>e!==o)):[...e,o];r(t)}else r(o),a()}),[t,e,r,a]))(n,r,l,m),S=((e,t)=>(0,s.useMemo)((()=>t&&Array.isArray(e)?Boolean(e.length):Boolean(e)),[t,e]))(n,r),y=r&&Array.isArray(n)&&n?.length>0;return(0,a.createElement)("div",{className:It,ref:h},(0,a.createElement)("div",{className:c()([Vt],{[jt]:y}),onClick:u},(0,a.createElement)(O,{condition:S,fallback:(0,a.createElement)("div",{className:$t},o)},(0,a.createElement)(zt,{onChange:p,options:t,multiple:r,value:n})),(0,a.createElement)(d.Dashicon,{icon:i?"arrow-up-alt2":"arrow-down-alt2",size:16,style:{color:"#4D5E6F"}})),(0,a.createElement)(O,{condition:i},(0,a.createElement)("div",{className:Zt},(0,a.createElement)(O,{condition:Boolean(g.length),fallback:(0,a.createElement)(kt,{className:Kt})},(0,a.createElement)("div",{className:Gt},g.map((e=>(0,a.createElement)("div",{key:e.value,onClick:()=>p(e.value),className:Kt},e.label))))))))},Yt=N("setting-select"),qt=e=>{const{name:t,options:r,label:o,multiple:n=!1,placeholder:s,isAdaptive:l=!1,dependencyMode:i,dependencies:c}=e,{fieldName:d}=K(t,l),{value:u,isChanged:m,onChange:h,onReset:g}=G(d);return Y(c,i)?(0,a.createElement)("div",{className:Yt},(0,a.createElement)(O,{condition:Boolean(o)},(0,a.createElement)(Ge,{label:o,isChanged:m,onReset:g,showDevicePicker:l})),(0,a.createElement)(Xt,{options:r,value:u,onSelect:h,multiple:n,placeholder:s})):null},Jt=N("row-select"),Qt=N("row-select__label"),er=N("row-select__control"),tr=e=>{const{name:t,label:r,options:o,isAdaptive:n=!1}=e,{fieldName:s}=K(t,n),{isChanged:l,onReset:i}=G(s);return(0,a.createElement)("div",{className:Jt},(0,a.createElement)("div",{className:Qt},(0,a.createElement)("div",null,r),(0,a.createElement)(O,{condition:n},(0,a.createElement)(Pe,null))),(0,a.createElement)("div",{className:er},(0,a.createElement)(qt,{name:t,options:o,isAdaptive:n}),(0,a.createElement)(O,{condition:l},(0,a.createElement)(We,{onReset:i}))))},rr=N("typography-select"),ar=N("typography-select-label"),or=e=>{const{name:t,label:r,options:o,isAdaptive:n=!1}=e,{fieldName:s}=K(t,n),{isChanged:l,onReset:i}=G(s);return(0,a.createElement)("div",{className:rr},(0,a.createElement)("div",{className:ar},(0,a.createElement)("div",null,r),(0,a.createElement)(O,{condition:n},(0,a.createElement)(Pe,null))),(0,a.createElement)(qt,{name:t,options:o,isAdaptive:n}),(0,a.createElement)(O,{condition:l},(0,a.createElement)(We,{onReset:i})))},nr=N("typography"),sr=e=>{const{fontSizeName:t,fontWeightName:r,textTransformName:n,fontStyleName:s,textDecorationName:l,lineHeightName:i,letterSpacingName:c,wordSpacingName:d,fontSizeUnitName:u,lineHeightUnitName:m,letterSpacingUnitName:h,wordSpacingUnitName:g,dependencyMode:p,dependencies:S,isAdaptive:y=!1}=e,{fontWeightOptions:C,textTransformOptions:b,fontStyleOptions:v,textDecorationOptions:w}={fontWeightOptions:[{label:o.__("100 (Thin)","masterstudy-lms-learning-management-system"),value:"100"},{label:o.__("200 (Extra Light)","masterstudy-lms-learning-management-system"),value:"200"},{label:o.__("300 (Light)","masterstudy-lms-learning-management-system"),value:"300"},{label:o.__("400 (Normal)","masterstudy-lms-learning-management-system"),value:"400"},{label:o.__("500 (Medium)","masterstudy-lms-learning-management-system"),value:"500"},{label:o.__("600 (Semi Bold)","masterstudy-lms-learning-management-system"),value:"600"},{label:o.__("700 (Bold)","masterstudy-lms-learning-management-system"),value:"700"},{label:o.__("800 (Extra Bold)","masterstudy-lms-learning-management-system"),value:"800"},{label:o.__("900 (Extra)","masterstudy-lms-learning-management-system"),value:"900"}],textTransformOptions:[{label:o.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:o.__("Uppercase","masterstudy-lms-learning-management-system"),value:"uppercase"},{label:o.__("Lowercase","masterstudy-lms-learning-management-system"),value:"lowercase"},{label:o.__("Capitalize","masterstudy-lms-learning-management-system"),value:"capitalize"},{label:o.__("Normal","masterstudy-lms-learning-management-system"),value:"none"}],fontStyleOptions:[{label:o.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:o.__("Normal","masterstudy-lms-learning-management-system"),value:"none"},{label:o.__("Italic","masterstudy-lms-learning-management-system"),value:"italic"},{label:o.__("Oblique","masterstudy-lms-learning-management-system"),value:"oblique"}],textDecorationOptions:[{label:o.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:o.__("Underline","masterstudy-lms-learning-management-system"),value:"underline"},{label:o.__("Line Through","masterstudy-lms-learning-management-system"),value:"line-through"},{label:o.__("None","masterstudy-lms-learning-management-system"),value:"none"}]};return Y(S,p)?(0,a.createElement)("div",{className:nr},(0,a.createElement)(ft,{name:t,label:o.__("Size","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:u,isAdaptive:y}),(0,a.createElement)(or,{name:r,label:o.__("Weight","masterstudy-lms-learning-management-system"),options:C}),(0,a.createElement)(or,{name:n,label:o.__("Transform","masterstudy-lms-learning-management-system"),options:b}),(0,a.createElement)(or,{name:s,label:o.__("Style","masterstudy-lms-learning-management-system"),options:v}),(0,a.createElement)(or,{name:l,label:o.__("Decoration","masterstudy-lms-learning-management-system"),options:w}),(0,a.createElement)(ft,{name:i,label:o.__("Line Height","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:m,isAdaptive:y}),(0,a.createElement)(ft,{name:c,label:o.__("Letter Spacing","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:h,isAdaptive:y}),d&&(0,a.createElement)(ft,{name:d,label:o.__("Word Spacing","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:g,isAdaptive:y})):null},lr=(N("file-upload"),N("file-upload__wrap"),N("file-upload__image"),N("file-upload__remove"),N("file-upload__replace"),(0,s.createContext)({activeTab:0,setActiveTab:()=>{}})),ir=()=>{const e=(0,s.useContext)(lr);if(!e)throw new Error("useTabs should be used inside Tabs");return e},cr=({children:e})=>{const[t,r]=(0,s.useState)(0);return(0,a.createElement)(lr.Provider,{value:{activeTab:t,setActiveTab:r}},(0,a.createElement)("div",{className:`active-tab-${t}`},e))},dr=N("tab-list"),ur=({children:e})=>(0,a.createElement)("div",{className:dr},s.Children.map(e,((e,t)=>(0,s.cloneElement)(e,{index:t})))),mr=N("tab"),hr=N("tab-active"),gr=N("content"),pr=({index:e,title:t,icon:r})=>{const{activeTab:o,setActiveTab:n}=ir();return(0,a.createElement)("div",{className:c()([mr],{[hr]:o===e}),onClick:()=>n(e)},(0,a.createElement)("div",{className:gr},(0,a.createElement)("div",null,r),(0,a.createElement)("div",null,t)))},Sr=({children:e})=>(0,a.createElement)("div",null,s.Children.map(e,((e,t)=>(0,s.cloneElement)(e,{index:t})))),yr=N("tab-panel"),Cr=({index:e,children:t})=>{const{activeTab:r}=ir();return r===e?(0,a.createElement)("div",{className:yr},t):null},br=({generalTab:e,styleTab:t,advancedTab:r})=>(0,a.createElement)(cr,null,(0,a.createElement)(ur,null,(0,a.createElement)(pr,{title:o.__("General","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(d.Dashicon,{icon:"layout"})}),(0,a.createElement)(pr,{title:o.__("Style","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(d.Dashicon,{icon:"admin-appearance"})}),(0,a.createElement)(pr,{title:o.__("Advanced","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(d.Dashicon,{icon:"admin-settings"})})),(0,a.createElement)(Sr,null,(0,a.createElement)(Cr,null,e),(0,a.createElement)(Cr,null,t),(0,a.createElement)(Cr,null,r)));window.ReactDOM;"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement;function vr(e){const t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function wr(e){return"nodeType"in e}function Br(e){var t,r;return e?vr(e)?e:wr(e)&&null!=(t=null==(r=e.ownerDocument)?void 0:r.defaultView)?t:window:window}function _r(e){const{Document:t}=Br(e);return e instanceof t}function Fr(e){return!vr(e)&&e instanceof Br(e).HTMLElement}function Dr(e){return e instanceof Br(e).SVGElement}function Er(e){return e?vr(e)?e.document:wr(e)?_r(e)?e:Fr(e)||Dr(e)?e.ownerDocument:document:document:document}function fr(e){return function(t){for(var r=arguments.length,a=new Array(r>1?r-1:0),o=1;o<r;o++)a[o-1]=arguments[o];return a.reduce(((t,r)=>{const a=Object.entries(r);for(const[r,o]of a){const a=t[r];null!=a&&(t[r]=a+e*o)}return t}),{...t})}}const Nr=fr(-1);function xr(e){if(function(e){if(!e)return!1;const{TouchEvent:t}=Br(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:r}=e.touches[0];return{x:t,y:r}}if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:r}=e.changedTouches[0];return{x:t,y:r}}}return function(e){return"clientX"in e&&"clientY"in e}(e)?{x:e.clientX,y:e.clientY}:null}var Tr;!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(Tr||(Tr={}));const Mr=Object.freeze({x:0,y:0});var Ur,Hr,Pr,Ar;!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(Ur||(Ur={}));class Wr{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach((e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)}))},this.target=e}add(e,t,r){var a;null==(a=this.target)||a.addEventListener(e,t,r),this.listeners.push([e,t,r])}}function kr(e,t){const r=Math.abs(e.x),a=Math.abs(e.y);return"number"==typeof t?Math.sqrt(r**2+a**2)>t:"x"in t&&"y"in t?r>t.x&&a>t.y:"x"in t?r>t.x:"y"in t&&a>t.y}function Lr(e){e.preventDefault()}function Rr(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(Hr||(Hr={})),(Ar=Pr||(Pr={})).Space="Space",Ar.Down="ArrowDown",Ar.Right="ArrowRight",Ar.Left="ArrowLeft",Ar.Up="ArrowUp",Ar.Esc="Escape",Ar.Enter="Enter";Pr.Space,Pr.Enter,Pr.Esc,Pr.Space,Pr.Enter;function Or(e){return Boolean(e&&"distance"in e)}function zr(e){return Boolean(e&&"delay"in e)}class Ir{constructor(e,t,r){var a;void 0===r&&(r=function(e){const{EventTarget:t}=Br(e);return e instanceof t?e:Er(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;const{event:o}=e,{target:n}=o;this.props=e,this.events=t,this.document=Er(n),this.documentListeners=new Wr(this.document),this.listeners=new Wr(r),this.windowListeners=new Wr(Br(n)),this.initialCoordinates=null!=(a=xr(o))?a:Mr,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:r}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),this.windowListeners.add(Hr.Resize,this.handleCancel),this.windowListeners.add(Hr.DragStart,Lr),this.windowListeners.add(Hr.VisibilityChange,this.handleCancel),this.windowListeners.add(Hr.ContextMenu,Lr),this.documentListeners.add(Hr.Keydown,this.handleKeydown),t){if(null!=r&&r({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(zr(t))return void(this.timeoutId=setTimeout(this.handleStart,t.delay));if(Or(t))return}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handleStart(){const{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(Hr.Click,Rr,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(Hr.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;const{activated:r,initialCoordinates:a,props:o}=this,{onMove:n,options:{activationConstraint:s}}=o;if(!a)return;const l=null!=(t=xr(e))?t:Mr,i=Nr(a,l);if(!r&&s){if(Or(s)){if(null!=s.tolerance&&kr(i,s.tolerance))return this.handleCancel();if(kr(i,s.distance))return this.handleStart()}return zr(s)&&kr(i,s.tolerance)?this.handleCancel():void 0}e.cancelable&&e.preventDefault(),n(l)}handleEnd(){const{onEnd:e}=this.props;this.detach(),e()}handleCancel(){const{onCancel:e}=this.props;this.detach(),e()}handleKeydown(e){e.code===Pr.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}const Vr={move:{name:"pointermove"},end:{name:"pointerup"}};(class extends Ir{constructor(e){const{event:t}=e,r=Er(t.target);super(e,Vr,r)}}).activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:r}=e,{onActivation:a}=t;return!(!r.isPrimary||0!==r.button||(null==a||a({event:r}),0))}}];const $r={move:{name:"mousemove"},end:{name:"mouseup"}};var jr;!function(e){e[e.RightClick=2]="RightClick"}(jr||(jr={})),class extends Ir{constructor(e){super(e,$r,Er(e.event.target))}}.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:r}=e,{onActivation:a}=t;return r.button!==jr.RightClick&&(null==a||a({event:r}),!0)}}];const Zr={move:{name:"touchmove"},end:{name:"touchend"}};var Gr,Kr,Xr,Yr,qr;(class extends Ir{constructor(e){super(e,Zr)}static setup(){return window.addEventListener(Zr.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(Zr.move.name,e)};function e(){}}}).activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:r}=e,{onActivation:a}=t;const{touches:o}=r;return!(o.length>1||(null==a||a({event:r}),0))}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(Gr||(Gr={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(Kr||(Kr={})),Ur.Backward,Ur.Forward,Ur.Backward,Ur.Forward,function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(Xr||(Xr={})),function(e){e.Optimized="optimized"}(Yr||(Yr={})),Xr.WhileDragging,Yr.Optimized,Map,function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(qr||(qr={})),Pr.Down,Pr.Right,Pr.Up,Pr.Left,o.__("Lectures","masterstudy-lms-learning-management-system"),o.__("Duration","masterstudy-lms-learning-management-system"),o.__("Views","masterstudy-lms-learning-management-system"),o.__("Level","masterstudy-lms-learning-management-system"),o.__("Members","masterstudy-lms-learning-management-system"),o.__("Empty","masterstudy-lms-learning-management-system"),N("sortable__item"),N("sortable__item__disabled"),N("sortable__item__content"),N("sortable__item__content__drag-item"),N("sortable__item__content__drag-item__disabled"),N("sortable__item__content__title"),N("sortable__item__control"),N("sortable__item__icon"),N("nested-sortable"),N("nested-sortable__item"),N("sortable");const Jr=N("accordion"),Qr=N("accordion__header"),ea=N("accordion__header-flex"),ta=N("accordion__content"),ra=N("accordion__icon"),aa=N("accordion__title"),oa=N("accordion__title-disabled"),na=N("accordion__indicator"),sa=N("accordion__controls"),la=N("accordion__controls-disabled"),ia=({title:e,children:t,accordionFields:r,switchName:o,visible:n=!0,isDefaultOpen:l=!1})=>{const{isOpen:i,onToggle:m,disabled:h,onReset:g,hasChanges:p,onClose:S}=((e,t,r)=>{var a;const{isOpen:o,onToggle:n,onClose:s}=V(t),{defaultValues:l,attributes:i,setAttributes:c}=Z(),d=((e,t,r)=>{for(const a of r)if(!u(e[a],t[a]))return!0;return!1})(l,i,e);return{isOpen:o,onToggle:n,disabled:!(null===(a=i[r])||void 0===a||a),hasChanges:d,onReset:t=>{t.stopPropagation(),c(e.reduce(((e,t)=>(e[t]=l[t],e)),{}))},onClose:s}})(r,l,o);return((e,t)=>{const{attributes:r}=Z(),a=!r[t];(0,s.useEffect)((()=>{a&&e()}),[a,e])})(S,o),n?(0,a.createElement)("div",{className:Jr},(0,a.createElement)("div",{className:Qr},(0,a.createElement)("div",{className:ea,onClick:h?null:m},(0,a.createElement)("div",{className:c()(aa,{[oa]:h,"with-switch":Boolean(o)})},(0,a.createElement)("div",null,e),(0,a.createElement)(O,{condition:p&&!h},(0,a.createElement)("div",{className:na}))),(0,a.createElement)("div",{className:c()(sa,{[la]:h})},(0,a.createElement)(d.Dashicon,{icon:i?"arrow-up-alt2":"arrow-down-alt2",className:ra,size:16}))),(0,a.createElement)(O,{condition:Boolean(o)},(0,a.createElement)(xt,{name:o})),(0,a.createElement)(O,{condition:p&&!h},(0,a.createElement)(We,{onReset:g}))),i&&(0,a.createElement)("div",{className:ta},t)):null};N("preset-picker"),N("preset-picker__label"),N("preset-picker__remove"),N("preset-picker__presets-list"),N("preset-picker__presets-list__item"),N("preset-picker__presets-list__item__preset"),N("preset-picker__presets-list__item__preset-active");const ca={coursesSearchCategory:!0,coursesSearchPopup:!1},da=Object.keys(ca),ua={...ca},ma={coursesSearchMargin:E,coursesSearchMarginTablet:E,coursesSearchMarginMobile:E,coursesSearchMarginUnit:"px",coursesSearchMarginUnitTablet:"px",coursesSearchMarginUnitMobile:"px",coursesSearchPadding:E,coursesSearchPaddingTablet:E,coursesSearchPaddingMobile:E,coursesSearchPaddingUnit:"px",coursesSearchPaddingUnitTablet:"px",coursesSearchPaddingUnitMobile:"px",coursesSearchBackground:"",coursesSearchBorderStyle:"none",coursesSearchBorderStyleTablet:"",coursesSearchBorderStyleMobile:"",coursesSearchBorderColor:"",coursesSearchBorderColorTablet:"",coursesSearchBorderColorMobile:"",coursesSearchBorderWidth:E,coursesSearchBorderWidthTablet:E,coursesSearchBorderWidthMobile:E,coursesSearchBorderWidthUnit:"px",coursesSearchBorderWidthUnitTablet:"px",coursesSearchBorderWidthUnitMobile:"px",coursesSearchBorderRadius:E,coursesSearchBorderRadiusTablet:E,coursesSearchBorderRadiusMobile:E,coursesSearchBorderRadiusUnit:"px",coursesSearchBorderRadiusUnitTablet:"px",coursesSearchBorderRadiusUnitMobile:"px",coursesSearchWidth:"alignauto",coursesSearchAlign:"center",coursesSearchMaxWidth:370,coursesSearchMaxWidthUnit:"px",coursesSearchZIndex:null,coursesSearchZIndexTablet:null,coursesSearchZIndexMobile:null},ha=Object.keys(ma),ga={coursesSearchFieldFontSize:14,coursesSearchFieldFontSizeTablet:null,coursesSearchFieldFontSizeMobile:null,coursesSearchFieldFontSizeUnit:"px",coursesSearchFieldFontSizeUnitTablet:"px",coursesSearchFieldFontSizeUnitMobile:"px",coursesSearchFieldFontWeight:"500",coursesSearchFieldTextTransform:"inherit",coursesSearchFieldFontStyle:"inherit",coursesSearchFieldTextDecoration:"inherit",coursesSearchFieldLineHeight:20,coursesSearchFieldLineHeightTablet:null,coursesSearchFieldLineHeightMobile:null,coursesSearchFieldLineHeightUnit:"px",coursesSearchFieldLineHeightUnitTablet:"px",coursesSearchFieldLineHeightUnitMobile:"px",coursesSearchFieldLetterSpacing:0,coursesSearchFieldLetterSpacingTablet:null,coursesSearchFieldLetterSpacingMobile:null,coursesSearchFieldLetterSpacingUnit:"px",coursesSearchFieldLetterSpacingUnitTablet:"px",coursesSearchFieldLetterSpacingUnitMobile:"px",coursesSearchFieldWordSpacing:0,coursesSearchFieldWordSpacingTablet:null,coursesSearchFieldWordSpacingMobile:null,coursesSearchFieldWordSpacingUnit:"px",coursesSearchFieldWordSpacingUnitTablet:"px",coursesSearchFieldWordSpacingUnitMobile:"px",coursesSearchFieldColor:"#001931",coursesSearchFieldPlaceholderColor:"#B3BAC2",coursesSearchFieldBackground:"#FFFFFF",coursesSearchFieldBorderStyle:"solid",coursesSearchFieldBorderStyleTablet:"",coursesSearchFieldBorderStyleMobile:"",coursesSearchFieldBorderColor:"#DBE0E9",coursesSearchFieldBorderColorTablet:"",coursesSearchFieldBorderColorMobile:"",coursesSearchFieldBorderWidth:{top:"1",right:"1",bottom:"1",left:"0"},coursesSearchFieldBorderWidthTablet:E,coursesSearchFieldBorderWidthMobile:E,coursesSearchFieldBorderWidthUnit:"px",coursesSearchFieldBorderWidthUnitTablet:"px",coursesSearchFieldBorderWidthUnitMobile:"px",coursesSearchFieldBorderRadius:{top:"0",right:"4",bottom:"4",left:"0"},coursesSearchFieldBorderRadiusTablet:E,coursesSearchFieldBorderRadiusMobile:E,coursesSearchFieldBorderRadiusUnit:"px",coursesSearchFieldBorderRadiusUnitTablet:"px",coursesSearchFieldBorderRadiusUnitMobile:"px",coursesSearchFieldMargin:E,coursesSearchFieldMarginTablet:E,coursesSearchFieldMarginMobile:E,coursesSearchFieldMarginUnit:"px",coursesSearchFieldMarginUnitTablet:"px",coursesSearchFieldMarginUnitMobile:"px",coursesSearchFieldPadding:{top:"0",right:"0",bottom:"0",left:"20"},coursesSearchFieldPaddingTablet:E,coursesSearchFieldPaddingMobile:E,coursesSearchFieldPaddingUnit:"px",coursesSearchFieldPaddingUnitTablet:"px",coursesSearchFieldPaddingUnitMobile:"px",coursesSearchFieldShadowColor:"",coursesSearchFieldShadowColorTablet:"",coursesSearchFieldShadowColorMobile:"",coursesSearchFieldShadowHorizontal:8,coursesSearchFieldShadowHorizontalTablet:null,coursesSearchFieldShadowHorizontalMobile:null,coursesSearchFieldShadowVertical:0,coursesSearchFieldShadowVerticalTablet:null,coursesSearchFieldShadowVerticalMobile:null,coursesSearchFieldShadowBlur:20,coursesSearchFieldShadowBlurTablet:null,coursesSearchFieldShadowBlurMobile:null,coursesSearchFieldShadowSpread:0,coursesSearchFieldShadowSpreadTablet:null,coursesSearchFieldShadowSpreadMobile:null,coursesSearchFieldShadowInset:!1,coursesSearchFieldShadowInsetTablet:!1,coursesSearchFieldShadowInsetMobile:!1},pa=Object.keys(ga),Sa={coursesSearchFieldDropdownFontSize:14,coursesSearchFieldDropdownFontSizeTablet:null,coursesSearchFieldDropdownFontSizeMobile:null,coursesSearchFieldDropdownFontSizeUnit:"px",coursesSearchFieldDropdownFontSizeUnitTablet:"px",coursesSearchFieldDropdownFontSizeUnitMobile:"px",coursesSearchFieldDropdownFontWeight:"500",coursesSearchFieldDropdownTextTransform:"inherit",coursesSearchFieldDropdownFontStyle:"inherit",coursesSearchFieldDropdownTextDecoration:"inherit",coursesSearchFieldDropdownLineHeight:20,coursesSearchFieldDropdownLineHeightTablet:null,coursesSearchFieldDropdownLineHeightMobile:null,coursesSearchFieldDropdownLineHeightUnit:"px",coursesSearchFieldDropdownLineHeightUnitTablet:"px",coursesSearchFieldDropdownLineHeightUnitMobile:"px",coursesSearchFieldDropdownLetterSpacing:0,coursesSearchFieldDropdownLetterSpacingTablet:null,coursesSearchFieldDropdownLetterSpacingMobile:null,coursesSearchFieldDropdownLetterSpacingUnit:"px",coursesSearchFieldDropdownLetterSpacingUnitTablet:"px",coursesSearchFieldDropdownLetterSpacingUnitMobile:"px",coursesSearchFieldDropdownWordSpacing:0,coursesSearchFieldDropdownWordSpacingTablet:null,coursesSearchFieldDropdownWordSpacingMobile:null,coursesSearchFieldDropdownWordSpacingUnit:"px",coursesSearchFieldDropdownWordSpacingUnitTablet:"px",coursesSearchFieldDropdownWordSpacingUnitMobile:"px",coursesSearchFieldDropdownColor:"#001931",coursesSearchFieldDropdownBackground:"#FFFFFF",coursesSearchFieldDropdownBorderStyle:"solid",coursesSearchFieldDropdownBorderStyleTablet:"",coursesSearchFieldDropdownBorderStyleMobile:"",coursesSearchFieldDropdownBorderColor:"#DBE0E9",coursesSearchFieldDropdownBorderColorTablet:"",coursesSearchFieldDropdownBorderColorMobile:"",coursesSearchFieldDropdownBorderWidth:{top:"1",right:"1",bottom:"1",left:"1"},coursesSearchFieldDropdownBorderWidthTablet:E,coursesSearchFieldDropdownBorderWidthMobile:E,coursesSearchFieldDropdownBorderWidthUnit:"px",coursesSearchFieldDropdownBorderWidthUnitTablet:"px",coursesSearchFieldDropdownBorderWidthUnitMobile:"px",coursesSearchFieldDropdownBorderRadius:{top:"4",right:"4",bottom:"4",left:"4"},coursesSearchFieldDropdownBorderRadiusTablet:E,coursesSearchFieldDropdownBorderRadiusMobile:E,coursesSearchFieldDropdownBorderRadiusUnit:"px",coursesSearchFieldDropdownBorderRadiusUnitTablet:"px",coursesSearchFieldDropdownBorderRadiusUnitMobile:"px",coursesSearchFieldDropdownMargin:{top:"5",right:"0",bottom:"0",left:"0"},coursesSearchFieldDropdownMarginTablet:E,coursesSearchFieldDropdownMarginMobile:E,coursesSearchFieldDropdownMarginUnit:"px",coursesSearchFieldDropdownMarginUnitTablet:"px",coursesSearchFieldDropdownMarginUnitMobile:"px",coursesSearchFieldDropdownPadding:{top:"10",right:"10",bottom:"10",left:"10"},coursesSearchFieldDropdownPaddingTablet:E,coursesSearchFieldDropdownPaddingMobile:E,coursesSearchFieldDropdownPaddingUnit:"px",coursesSearchFieldDropdownPaddingUnitTablet:"px",coursesSearchFieldDropdownPaddingUnitMobile:"px",coursesSearchFieldDropdownShadowColor:"",coursesSearchFieldDropdownShadowColorTablet:"",coursesSearchFieldDropdownShadowColorMobile:"",coursesSearchFieldDropdownShadowHorizontal:8,coursesSearchFieldDropdownShadowHorizontalTablet:null,coursesSearchFieldDropdownShadowHorizontalMobile:null,coursesSearchFieldDropdownShadowVertical:0,coursesSearchFieldDropdownShadowVerticalTablet:null,coursesSearchFieldDropdownShadowVerticalMobile:null,coursesSearchFieldDropdownShadowBlur:20,coursesSearchFieldDropdownShadowBlurTablet:null,coursesSearchFieldDropdownShadowBlurMobile:null,coursesSearchFieldDropdownShadowSpread:0,coursesSearchFieldDropdownShadowSpreadTablet:null,coursesSearchFieldDropdownShadowSpreadMobile:null,coursesSearchFieldDropdownShadowInset:!1,coursesSearchFieldDropdownShadowInsetTablet:!1,coursesSearchFieldDropdownShadowInsetMobile:!1},ya=Object.keys(Sa),Ca={coursesSearchFieldButtonFontSize:14,coursesSearchFieldButtonFontSizeTablet:null,coursesSearchFieldButtonFontSizeMobile:null,coursesSearchFieldButtonFontSizeUnit:"px",coursesSearchFieldButtonFontSizeUnitTablet:"px",coursesSearchFieldButtonFontSizeUnitMobile:"px",coursesSearchFieldButtonFontWeight:"700",coursesSearchFieldButtonTextTransform:"inherit",coursesSearchFieldButtonFontStyle:"inherit",coursesSearchFieldButtonTextDecoration:"inherit",coursesSearchFieldButtonLineHeight:20,coursesSearchFieldButtonLineHeightTablet:null,coursesSearchFieldButtonLineHeightMobile:null,coursesSearchFieldButtonLineHeightUnit:"px",coursesSearchFieldButtonLineHeightUnitTablet:"px",coursesSearchFieldButtonLineHeightUnitMobile:"px",coursesSearchFieldButtonLetterSpacing:0,coursesSearchFieldButtonLetterSpacingTablet:null,coursesSearchFieldButtonLetterSpacingMobile:null,coursesSearchFieldButtonLetterSpacingUnit:"px",coursesSearchFieldButtonLetterSpacingUnitTablet:"px",coursesSearchFieldButtonLetterSpacingUnitMobile:"px",coursesSearchFieldButtonWordSpacing:0,coursesSearchFieldButtonWordSpacingTablet:null,coursesSearchFieldButtonWordSpacingMobile:null,coursesSearchFieldButtonWordSpacingUnit:"px",coursesSearchFieldButtonWordSpacingUnitTablet:"px",coursesSearchFieldButtonWordSpacingUnitMobile:"px",coursesSearchFieldButtonColor:"#001931",coursesSearchFieldButtonColorHover:"",coursesSearchFieldButtonBackground:"",coursesSearchFieldButtonBackgroundHover:"",coursesSearchFieldButtonWidth:40,coursesSearchFieldButtonWidthTablet:null,coursesSearchFieldButtonWidthMobile:null,coursesSearchFieldButtonHeight:40,coursesSearchFieldButtonHeightTablet:null,coursesSearchFieldButtonHeightMobile:null,coursesSearchFieldButtonBorderStyle:"none",coursesSearchFieldButtonBorderStyleTablet:"",coursesSearchFieldButtonBorderStyleMobile:"",coursesSearchFieldButtonBorderColor:"",coursesSearchFieldButtonBorderColorTablet:"",coursesSearchFieldButtonBorderColorMobile:"",coursesSearchFieldButtonBorderWidth:E,coursesSearchFieldButtonBorderWidthTablet:E,coursesSearchFieldButtonBorderWidthMobile:E,coursesSearchFieldButtonBorderWidthUnit:"px",coursesSearchFieldButtonBorderWidthUnitTablet:"px",coursesSearchFieldButtonBorderWidthUnitMobile:"px",coursesSearchFieldButtonBorderStyleHover:"none",coursesSearchFieldButtonBorderStyleHoverTablet:"",coursesSearchFieldButtonBorderStyleHoverMobile:"",coursesSearchFieldButtonBorderColorHover:"",coursesSearchFieldButtonBorderColorHoverTablet:"",coursesSearchFieldButtonBorderColorHoverMobile:"",coursesSearchFieldButtonBorderWidthHover:E,coursesSearchFieldButtonBorderWidthHoverTablet:E,coursesSearchFieldButtonBorderWidthHoverMobile:E,coursesSearchFieldButtonBorderWidthHoverUnit:"px",coursesSearchFieldButtonBorderWidthHoverUnitTablet:"px",coursesSearchFieldButtonBorderWidthHoverUnitMobile:"px",coursesSearchFieldButtonBorderRadius:{top:"4",right:"4",bottom:"4",left:"4"},coursesSearchFieldButtonBorderRadiusTablet:E,coursesSearchFieldButtonBorderRadiusMobile:E,coursesSearchFieldButtonBorderRadiusUnit:"px",coursesSearchFieldButtonBorderRadiusUnitTablet:"px",coursesSearchFieldButtonBorderRadiusUnitMobile:"px",coursesSearchFieldButtonMargin:{top:"0",right:"0",bottom:"0",left:"0"},coursesSearchFieldButtonMarginTablet:E,coursesSearchFieldButtonMarginMobile:E,coursesSearchFieldButtonMarginUnit:"px",coursesSearchFieldButtonMarginUnitTablet:"px",coursesSearchFieldButtonMarginUnitMobile:"px",coursesSearchFieldButtonPadding:{top:"10",right:"10",bottom:"10",left:"10"},coursesSearchFieldButtonPaddingTablet:E,coursesSearchFieldButtonPaddingMobile:E,coursesSearchFieldButtonPaddingUnit:"px",coursesSearchFieldButtonPaddingUnitTablet:"px",coursesSearchFieldButtonPaddingUnitMobile:"px",coursesSearchFieldButtonShadowColor:"",coursesSearchFieldButtonShadowColorTablet:"",coursesSearchFieldButtonShadowColorMobile:"",coursesSearchFieldButtonShadowHorizontal:8,coursesSearchFieldButtonShadowHorizontalTablet:null,coursesSearchFieldButtonShadowHorizontalMobile:null,coursesSearchFieldButtonShadowVertical:0,coursesSearchFieldButtonShadowVerticalTablet:null,coursesSearchFieldButtonShadowVerticalMobile:null,coursesSearchFieldButtonShadowBlur:20,coursesSearchFieldButtonShadowBlurTablet:null,coursesSearchFieldButtonShadowBlurMobile:null,coursesSearchFieldButtonShadowSpread:0,coursesSearchFieldButtonShadowSpreadTablet:null,coursesSearchFieldButtonShadowSpreadMobile:null,coursesSearchFieldButtonShadowInset:!1,coursesSearchFieldButtonShadowInsetTablet:!1,coursesSearchFieldButtonShadowInsetMobile:!1},ba=Object.keys(Ca),va={coursesSearchCategoryButtonFontSize:14,coursesSearchCategoryButtonFontSizeTablet:null,coursesSearchCategoryButtonFontSizeMobile:null,coursesSearchCategoryButtonFontSizeUnit:"px",coursesSearchCategoryButtonFontSizeUnitTablet:"px",coursesSearchCategoryButtonFontSizeUnitMobile:"px",coursesSearchCategoryButtonFontWeight:"500",coursesSearchCategoryButtonTextTransform:"inherit",coursesSearchCategoryButtonFontStyle:"inherit",coursesSearchCategoryButtonTextDecoration:"inherit",coursesSearchCategoryButtonLineHeight:20,coursesSearchCategoryButtonLineHeightTablet:null,coursesSearchCategoryButtonLineHeightMobile:null,coursesSearchCategoryButtonLineHeightUnit:"px",coursesSearchCategoryButtonLineHeightUnitTablet:"px",coursesSearchCategoryButtonLineHeightUnitMobile:"px",coursesSearchCategoryButtonLetterSpacing:0,coursesSearchCategoryButtonLetterSpacingTablet:null,coursesSearchCategoryButtonLetterSpacingMobile:null,coursesSearchCategoryButtonLetterSpacingUnit:"px",coursesSearchCategoryButtonLetterSpacingUnitTablet:"px",coursesSearchCategoryButtonLetterSpacingUnitMobile:"px",coursesSearchCategoryButtonWordSpacing:0,coursesSearchCategoryButtonWordSpacingTablet:null,coursesSearchCategoryButtonWordSpacingMobile:null,coursesSearchCategoryButtonWordSpacingUnit:"px",coursesSearchCategoryButtonWordSpacingUnitTablet:"px",coursesSearchCategoryButtonWordSpacingUnitMobile:"px",coursesSearchCategoryButtonColor:"#001931",coursesSearchCategoryButtonColorHover:"",coursesSearchCategoryButtonBackground:"#EEF1F7",coursesSearchCategoryButtonBackgroundHover:"#e7eaf1",coursesSearchCategoryButtonIconFontSize:15,coursesSearchCategoryButtonIconFontSizeUnit:"px",coursesSearchCategoryButtonIconColor:"#001931",coursesSearchCategoryButtonIconColorHover:"",coursesSearchCategoryButtonBorderStyle:"none",coursesSearchCategoryButtonBorderStyleTablet:"",coursesSearchCategoryButtonBorderStyleMobile:"",coursesSearchCategoryButtonBorderColor:"",coursesSearchCategoryButtonBorderColorTablet:"",coursesSearchCategoryButtonBorderColorMobile:"",coursesSearchCategoryButtonBorderWidth:{top:"1",right:"1",bottom:"1",left:"1"},coursesSearchCategoryButtonBorderWidthTablet:E,coursesSearchCategoryButtonBorderWidthMobile:E,coursesSearchCategoryButtonBorderWidthUnit:"px",coursesSearchCategoryButtonBorderWidthUnitTablet:"px",coursesSearchCategoryButtonBorderWidthUnitMobile:"px",coursesSearchCategoryButtonBorderStyleHover:"none",coursesSearchCategoryButtonBorderStyleTHoverablet:"",coursesSearchCategoryButtonBorderStyleHoverMobile:"",coursesSearchCategoryButtonBorderColorHover:"",coursesSearchCategoryButtonBorderColorHoverTablet:"",coursesSearchCategoryButtonBorderColorHoverMobile:"",coursesSearchCategoryButtonBorderWidthHover:{top:"1",right:"1",bottom:"1",left:"1"},coursesSearchCategoryButtonBorderWidthHoverTablet:E,coursesSearchCategoryButtonBorderWidthHoverMobile:E,coursesSearchCategoryButtonBorderWidthHoverUnit:"px",coursesSearchCategoryButtonBorderWidthHoverUnitTablet:"px",coursesSearchCategoryButtonBorderWidthHoverUnitMobile:"px",coursesSearchCategoryButtonBorderRadius:{top:"4",right:"0",bottom:"0",left:"4"},coursesSearchCategoryButtonBorderRadiusTablet:E,coursesSearchCategoryButtonBorderRadiusMobile:E,coursesSearchCategoryButtonBorderRadiusUnit:"px",coursesSearchCategoryButtonBorderRadiusUnitTablet:"px",coursesSearchCategoryButtonBorderRadiusUnitMobile:"px",coursesSearchCategoryButtonMargin:E,coursesSearchCategoryButtonMarginTablet:E,coursesSearchCategoryButtonMarginMobile:E,coursesSearchCategoryButtonMarginUnit:"px",coursesSearchCategoryButtonMarginUnitTablet:"px",coursesSearchCategoryButtonMarginUnitMobile:"px",coursesSearchCategoryButtonPadding:{top:"11",right:"20",bottom:"11",left:"20"},coursesSearchCategoryButtonPaddingTablet:E,coursesSearchCategoryButtonPaddingMobile:{top:"0",right:"5",bottom:"0",left:"5"},coursesSearchCategoryButtonPaddingUnit:"px",coursesSearchCategoryButtonPaddingUnitTablet:"px",coursesSearchCategoryButtonPaddingUnitMobile:"px"},wa=Object.keys(va),Ba={coursesSearchCategoryDropdownFontSize:13,coursesSearchCategoryDropdownFontSizeTablet:null,coursesSearchCategoryDropdownFontSizeMobile:null,coursesSearchCategoryDropdownFontSizeUnit:"px",coursesSearchCategoryDropdownFontSizeUnitTablet:"px",coursesSearchCategoryDropdownFontSizeUnitMobile:"px",coursesSearchCategoryDropdownFontWeight:"700",coursesSearchCategoryDropdownTextTransform:"uppercase",coursesSearchCategoryDropdownFontStyle:"inherit",coursesSearchCategoryDropdownTextDecoration:"inherit",coursesSearchCategoryDropdownLineHeight:20,coursesSearchCategoryDropdownLineHeightTablet:null,coursesSearchCategoryDropdownLineHeightMobile:null,coursesSearchCategoryDropdownLineHeightUnit:"px",coursesSearchCategoryDropdownLineHeightUnitTablet:"px",coursesSearchCategoryDropdownLineHeightUnitMobile:"px",coursesSearchCategoryDropdownLetterSpacing:0,coursesSearchCategoryDropdownLetterSpacingTablet:null,coursesSearchCategoryDropdownLetterSpacingMobile:null,coursesSearchCategoryDropdownLetterSpacingUnit:"px",coursesSearchCategoryDropdownLetterSpacingUnitTablet:"px",coursesSearchCategoryDropdownLetterSpacingUnitMobile:"px",coursesSearchCategoryDropdownWordSpacing:0,coursesSearchCategoryDropdownWordSpacingTablet:null,coursesSearchCategoryDropdownWordSpacingMobile:null,coursesSearchCategoryDropdownWordSpacingUnit:"px",coursesSearchCategoryDropdownWordSpacingUnitTablet:"px",coursesSearchCategoryDropdownWordSpacingUnitMobile:"px",coursesSearchCategoryDropdownColor:"#ffffff",coursesSearchCategoryDropdownColorHover:"#234dd4",coursesSearchCategoryDropdownBackground:"#227aff",coursesSearchCategoryDropdownBackgroundHover:"#ffffff",coursesSearchCategoryDropdownWidth:240,coursesSearchCategoryDropdownWidthTablet:null,coursesSearchCategoryDropdownWidthMobile:null,coursesSearchCategoryDropdownBorderStyle:"none",coursesSearchCategoryDropdownBorderStyleTablet:"",coursesSearchCategoryDropdownBorderStyleMobile:"",coursesSearchCategoryDropdownBorderColor:"",coursesSearchCategoryDropdownBorderColorTablet:"",coursesSearchCategoryDropdownBorderColorMobile:"",coursesSearchCategoryDropdownBorderWidth:{top:"1",right:"1",bottom:"1",left:"1"},coursesSearchCategoryDropdownBorderWidthTablet:E,coursesSearchCategoryDropdownBorderWidthMobile:E,coursesSearchCategoryDropdownBorderWidthUnit:"px",coursesSearchCategoryDropdownBorderWidthUnitTablet:"px",coursesSearchCategoryDropdownBorderWidthUnitMobile:"px",coursesSearchCategoryDropdownBorderRadius:E,coursesSearchCategoryDropdownBorderRadiusTablet:E,coursesSearchCategoryDropdownBorderRadiusMobile:E,coursesSearchCategoryDropdownBorderRadiusUnit:"px",coursesSearchCategoryDropdownBorderRadiusUnitTablet:"px",coursesSearchCategoryDropdownBorderRadiusUnitMobile:"px",coursesSearchCategoryDropdownPadding:{top:"10",right:"30",bottom:"10",left:"30"},coursesSearchCategoryDropdownPaddingTablet:E,coursesSearchCategoryDropdownPaddingMobile:E,coursesSearchCategoryDropdownPaddingUnit:"px",coursesSearchCategoryDropdownPaddingUnitTablet:"px",coursesSearchCategoryDropdownPaddingUnitMobile:"px",coursesSearchCategoryDropdownChildsFontSize:16,coursesSearchCategoryDropdownChildsFontSizeTablet:null,coursesSearchCategoryDropdownChildsFontSizeMobile:14,coursesSearchCategoryDropdownChildsFontSizeUnit:"px",coursesSearchCategoryDropdownChildsFontSizeUnitTablet:"px",coursesSearchCategoryDropdownChildsFontSizeUnitMobile:"px",coursesSearchCategoryDropdownChildsFontWeight:"500",coursesSearchCategoryDropdownChildsTextTransform:"none",coursesSearchCategoryDropdownChildsFontStyle:"inherit",coursesSearchCategoryDropdownChildsTextDecoration:"inherit",coursesSearchCategoryDropdownChildsLineHeight:20,coursesSearchCategoryDropdownChildsLineHeightTablet:null,coursesSearchCategoryDropdownChildsLineHeightMobile:null,coursesSearchCategoryDropdownChildsLineHeightUnit:"px",coursesSearchCategoryDropdownChildsLineHeightUnitTablet:"px",coursesSearchCategoryDropdownChildsLineHeightUnitMobile:"px",coursesSearchCategoryDropdownChildsLetterSpacing:0,coursesSearchCategoryDropdownChildsLetterSpacingTablet:null,coursesSearchCategoryDropdownChildsLetterSpacingMobile:null,coursesSearchCategoryDropdownChildsLetterSpacingUnit:"px",coursesSearchCategoryDropdownChildsLetterSpacingUnitTablet:"px",coursesSearchCategoryDropdownChildsLetterSpacingUnitMobile:"px",coursesSearchCategoryDropdownChildsWordSpacing:0,coursesSearchCategoryDropdownChildsWordSpacingTablet:null,coursesSearchCategoryDropdownChildsWordSpacingMobile:null,coursesSearchCategoryDropdownChildsWordSpacingUnit:"px",coursesSearchCategoryDropdownChildsWordSpacingUnitTablet:"px",coursesSearchCategoryDropdownChildsWordSpacingUnitMobile:"px",coursesSearchCategoryDropdownChildsColor:"#4D5E6F",coursesSearchCategoryDropdownChildsColorHover:"",coursesSearchCategoryDropdownChildsBackground:"#ffffff",coursesSearchCategoryDropdownChildsBorderStyle:"none",coursesSearchCategoryDropdownChildsBorderStyleTablet:"",coursesSearchCategoryDropdownChildsBorderStyleMobile:"",coursesSearchCategoryDropdownChildsBorderColor:"",coursesSearchCategoryDropdownChildsBorderColorTablet:"",coursesSearchCategoryDropdownChildsBorderColorMobile:"",coursesSearchCategoryDropdownChildsBorderWidth:E,coursesSearchCategoryDropdownChildsBorderWidthTablet:E,coursesSearchCategoryDropdownChildsBorderWidthMobile:E,coursesSearchCategoryDropdownChildsBorderWidthUnit:"px",coursesSearchCategoryDropdownChildsBorderWidthUnitTablet:"px",coursesSearchCategoryDropdownChildsBorderWidthUnitMobile:"px",coursesSearchCategoryDropdownChildsBorderRadius:E,coursesSearchCategoryDropdownChildsBorderRadiusTablet:E,coursesSearchCategoryDropdownChildsBorderRadiusMobile:E,coursesSearchCategoryDropdownChildsBorderRadiusUnit:"px",coursesSearchCategoryDropdownChildsBorderRadiusUnitTablet:"px",coursesSearchCategoryDropdownChildsBorderRadiusUnitMobile:"px",coursesSearchCategoryDropdownChildsPadding:{top:"40",right:"40",bottom:"40",left:"40"},coursesSearchCategoryDropdownChildsPaddingTablet:E,coursesSearchCategoryDropdownChildsPaddingMobile:{top:"10",right:"30",bottom:"30",left:"30"},coursesSearchCategoryDropdownChildsPaddingUnit:"px",coursesSearchCategoryDropdownChildsPaddingUnitTablet:"px",coursesSearchCategoryDropdownChildsPaddingUnitMobile:"px",coursesSearchCategoryDropdownShadowColor:"#00000040",coursesSearchCategoryDropdownShadowColorTablet:"",coursesSearchCategoryDropdownShadowColorMobile:"",coursesSearchCategoryDropdownShadowHorizontal:0,coursesSearchCategoryDropdownShadowHorizontalTablet:null,coursesSearchCategoryDropdownShadowHorizontalMobile:null,coursesSearchCategoryDropdownShadowVertical:20,coursesSearchCategoryDropdownShadowVerticalTablet:null,coursesSearchCategoryDropdownShadowVerticalMobile:null,coursesSearchCategoryDropdownShadowBlur:50,coursesSearchCategoryDropdownShadowBlurTablet:null,coursesSearchCategoryDropdownShadowBlurMobile:null,coursesSearchCategoryDropdownShadowSpread:0,coursesSearchCategoryDropdownShadowSpreadTablet:null,coursesSearchCategoryDropdownShadowSpreadMobile:null,coursesSearchCategoryDropdownShadowInset:!1,coursesSearchCategoryDropdownShadowInsetTablet:!1,coursesSearchCategoryDropdownShadowInsetMobile:!1},_a=Object.keys(Ba),Fa={coursesSearchPopupBackground:"#000000cc",coursesSearchPopupSidebarPadding:{top:"20",right:"20",bottom:"20",left:"20"},coursesSearchPopupSidebarPaddingTablet:E,coursesSearchPopupSidebarPaddingMobile:E,coursesSearchPopupSidebarPaddingUnit:"px",coursesSearchPopupSidebarPaddingUnitTablet:"px",coursesSearchPopupSidebarPaddingUnitMobile:"px",coursesSearchPopupSidebarBackground:"#2D2D2DCC"},Da=Object.keys(Fa),Ea={...ua,...{...ma,...ga,...Sa,...Ca,...va,...Ba,...Fa}},fa=new Map([["coursesSearchAlign",{}],["coursesSearchMargin",{unit:"coursesSearchMarginUnit",isAdaptive:!0}],["coursesSearchPadding",{unit:"coursesSearchPaddingUnit",isAdaptive:!0}],["coursesSearchBackground",{}],["coursesSearchBorderStyle",{isAdaptive:!0}],["coursesSearchBorderColor",{isAdaptive:!0}],["coursesSearchBorderWidth",{isAdaptive:!0,unit:"coursesSearchBorderWidthUnit"}],["coursesSearchBorderRadius",{isAdaptive:!0,unit:"coursesSearchBorderRadiusUnit"}],["coursesSearchMaxWidth",{unit:"coursesSearchMaxWidthUnit"}],["coursesSearchZIndex",{isAdaptive:!0}],["coursesSearchFieldFontSize",{unit:"coursesSearchFieldFontSizeUnit",isAdaptive:!0}],["coursesSearchFieldFontWeight",{}],["coursesSearchFieldTextTransform",{}],["coursesSearchFieldFontStyle",{}],["coursesSearchFieldTextDecoration",{}],["coursesSearchFieldLineHeight",{unit:"coursesSearchFieldLineHeightUnit",isAdaptive:!0}],["coursesSearchFieldLetterSpacing",{unit:"coursesSearchFieldLetterSpacingUnit",isAdaptive:!0}],["coursesSearchFieldWordSpacing",{unit:"coursesSearchFieldWordSpacingUnit",isAdaptive:!0}],["coursesSearchFieldBackground",{hasHover:!0}],["coursesSearchFieldColor",{hasHover:!0}],["coursesSearchFieldPlaceholderColor",{hasHover:!0}],["coursesSearchFieldBorderStyle",{isAdaptive:!0,hasHover:!0}],["coursesSearchFieldBorderColor",{isAdaptive:!0,hasHover:!0}],["coursesSearchFieldBorderWidth",{isAdaptive:!0,unit:"coursesSearchFieldBorderWidthUnit",hasHover:!0}],["coursesSearchFieldBorderRadius",{isAdaptive:!0,unit:"coursesSearchFieldBorderRadiusUnit"}],["coursesSearchFieldMargin",{unit:"coursesSearchFieldMarginUnit",isAdaptive:!0}],["coursesSearchFieldPadding",{unit:"coursesSearchFieldPaddingUnit",isAdaptive:!0}],["coursesSearchFieldDropdownFontSize",{unit:"coursesSearchFieldDropdownFontSizeUnit",isAdaptive:!0}],["coursesSearchFieldDropdownFontWeight",{}],["coursesSearchFieldDropdownTextTransform",{}],["coursesSearchFieldDropdownFontStyle",{}],["coursesSearchFieldDropdownTextDecoration",{}],["coursesSearchFieldDropdownLineHeight",{unit:"coursesSearchFieldDropdownLineHeightUnit",isAdaptive:!0}],["coursesSearchFieldDropdownLetterSpacing",{unit:"coursesSearchFieldDropdownLetterSpacingUnit",isAdaptive:!0}],["coursesSearchFieldDropdownWordSpacing",{unit:"coursesSearchFieldDropdownWordSpacingUnit",isAdaptive:!0}],["coursesSearchFieldDropdownBackground",{hasHover:!0}],["coursesSearchFieldDropdownColor",{hasHover:!0}],["coursesSearchFieldDropdownBorderStyle",{isAdaptive:!0,hasHover:!0}],["coursesSearchFieldDropdownBorderColor",{isAdaptive:!0,hasHover:!0}],["coursesSearchFieldDropdownBorderWidth",{isAdaptive:!0,unit:"coursesSearchFieldDropdownBorderWidthUnit",hasHover:!0}],["coursesSearchFieldDropdownBorderRadius",{isAdaptive:!0,unit:"coursesSearchFieldDropdownBorderRadiusUnit"}],["coursesSearchFieldDropdownMargin",{unit:"coursesSearchFieldDropdownMarginUnit",isAdaptive:!0}],["coursesSearchFieldDropdownPadding",{unit:"coursesSearchFieldDropdownPaddingUnit",isAdaptive:!0}],["coursesSearchFieldButtonFontSize",{unit:"coursesSearchFieldButtonFontSizeUnit",isAdaptive:!0}],["coursesSearchFieldButtonFontWeight",{}],["coursesSearchFieldButtonTextTransform",{}],["coursesSearchFieldButtonFontStyle",{}],["coursesSearchFieldButtonTextDecoration",{}],["coursesSearchFieldButtonLineHeight",{unit:"coursesSearchFieldButtonLineHeightUnit",isAdaptive:!0}],["coursesSearchFieldButtonLetterSpacing",{unit:"coursesSearchFieldButtonLetterSpacingUnit",isAdaptive:!0}],["coursesSearchFieldButtonWordSpacing",{unit:"coursesSearchFieldButtonWordSpacingUnit",isAdaptive:!0}],["coursesSearchFieldButtonBackground",{hasHover:!0}],["coursesSearchFieldButtonWidth",{unit:"coursesSearchFieldButtonWidthUnit",isAdaptive:!0}],["coursesSearchFieldButtonHeight",{unit:"coursesSearchFieldButtonHeightUnit",isAdaptive:!0}],["coursesSearchFieldButtonColor",{hasHover:!0}],["coursesSearchFieldButtonBorderStyle",{isAdaptive:!0,hasHover:!0}],["coursesSearchFieldButtonBorderColor",{isAdaptive:!0,hasHover:!0}],["coursesSearchFieldButtonBorderWidth",{isAdaptive:!0,unit:"coursesSearchFieldButtonBorderWidthUnit",hasHover:!0}],["coursesSearchFieldButtonBorderRadius",{isAdaptive:!0,unit:"coursesSearchFieldButtonBorderRadiusUnit"}],["coursesSearchFieldButtonMargin",{unit:"coursesSearchFieldButtonMarginUnit",isAdaptive:!0}],["coursesSearchFieldButtonPadding",{unit:"coursesSearchFieldButtonPaddingUnit",isAdaptive:!0}],["coursesSearchCategoryButtonFontSize",{unit:"coursesSearchCategoryButtonFontSizeUnit",isAdaptive:!0}],["coursesSearchCategoryButtonColor",{hasHover:!0}],["coursesSearchCategoryButtonFontWeight",{}],["coursesSearchCategoryButtonTextTransform",{}],["coursesSearchCategoryButtonFontStyle",{}],["coursesSearchCategoryButtonTextDecoration",{}],["coursesSearchCategoryButtonLineHeight",{unit:"coursesSearchCategoryButtonLineHeightUnit",isAdaptive:!0}],["coursesSearchCategoryButtonLetterSpacing",{unit:"coursesSearchCategoryButtonLetterSpacingUnit",isAdaptive:!0}],["coursesSearchCategoryButtonWordSpacing",{unit:"coursesSearchCategoryButtonWordSpacingUnit",isAdaptive:!0}],["coursesSearchCategoryButtonBackground",{hasHover:!0}],["coursesSearchCategoryButtonIconFontSize",{unit:"coursesSearchCategoryButtonIconFontSizeUnit",isAdaptive:!0}],["coursesSearchCategoryButtonIconColor",{hasHover:!0}],["coursesSearchCategoryButtonBorderStyle",{isAdaptive:!0,hasHover:!0}],["coursesSearchCategoryButtonBorderColor",{isAdaptive:!0,hasHover:!0}],["coursesSearchCategoryButtonBorderWidth",{isAdaptive:!0,unit:"coursesSearchCategoryButtonBorderWidthUnit",hasHover:!0}],["coursesSearchCategoryButtonBorderRadius",{isAdaptive:!0,unit:"coursesSearchCategoryButtonBorderRadiusUnit"}],["coursesSearchCategoryButtonMargin",{unit:"coursesSearchCategoryButtonMarginUnit",isAdaptive:!0}],["coursesSearchCategoryButtonPadding",{unit:"coursesSearchCategoryButtonPaddingUnit",isAdaptive:!0}],["coursesSearchCategoryDropdownFontSize",{unit:"coursesSearchCategoryDropdownFontSizeUnit",isAdaptive:!0}],["coursesSearchCategoryDropdownFontWeight",{}],["coursesSearchCategoryDropdownTextTransform",{}],["coursesSearchCategoryDropdownFontStyle",{}],["coursesSearchCategoryDropdownTextDecoration",{}],["coursesSearchCategoryDropdownLineHeight",{unit:"coursesSearchCategoryDropdownLineHeightUnit",isAdaptive:!0}],["coursesSearchCategoryDropdownLetterSpacing",{unit:"coursesSearchCategoryDropdownLetterSpacingUnit",isAdaptive:!0}],["coursesSearchCategoryDropdownWordSpacing",{unit:"coursesSearchCategoryDropdownWordSpacingUnit",isAdaptive:!0}],["coursesSearchCategoryDropdownBackground",{hasHover:!0}],["coursesSearchCategoryDropdownWidth",{unit:"coursesSearchCategoryDropdownWidthUnit",isAdaptive:!0}],["coursesSearchCategoryDropdownColor",{hasHover:!0}],["coursesSearchCategoryDropdownBorderStyle",{isAdaptive:!0,hasHover:!0}],["coursesSearchCategoryDropdownBorderColor",{isAdaptive:!0,hasHover:!0}],["coursesSearchCategoryDropdownBorderWidth",{isAdaptive:!0,unit:"coursesSearchCategoryDropdownBorderWidthUnit",hasHover:!0}],["coursesSearchCategoryDropdownBorderRadius",{isAdaptive:!0,unit:"coursesSearchCategoryDropdownBorderRadiusUnit"}],["coursesSearchCategoryDropdownPadding",{unit:"coursesSearchCategoryDropdownPaddingUnit",isAdaptive:!0}],["coursesSearchCategoryDropdownChildsFontSize",{unit:"coursesSearchCategoryDropdownChildsFontSizeUnit",isAdaptive:!0}],["coursesSearchCategoryDropdownChildsFontWeight",{}],["coursesSearchCategoryDropdownChildsTextTransform",{}],["coursesSearchCategoryDropdownChildsFontStyle",{}],["coursesSearchCategoryDropdownChildsTextDecoration",{}],["coursesSearchCategoryDropdownChildsLineHeight",{unit:"coursesSearchCategoryDropdownChildsLineHeightUnit",isAdaptive:!0}],["coursesSearchCategoryDropdownChildsLetterSpacing",{unit:"coursesSearchCategoryDropdownChildsLetterSpacingUnit",isAdaptive:!0}],["coursesSearchCategoryDropdownChildsWordSpacing",{unit:"coursesSearchCategoryDropdownChildsWordSpacingUnit",isAdaptive:!0}],["coursesSearchCategoryDropdownChildsBackground",{hasHover:!0}],["coursesSearchCategoryDropdownChildsWidth",{unit:"coursesSearchCategoryDropdownChildsWidthUnit",isAdaptive:!0}],["coursesSearchCategoryDropdownChildsHeight",{unit:"coursesSearchCategoryDropdownChildsHeightUnit",isAdaptive:!0}],["coursesSearchCategoryDropdownChildsColor",{hasHover:!0}],["coursesSearchCategoryDropdownChildsBorderStyle",{isAdaptive:!0,hasHover:!0}],["coursesSearchCategoryDropdownChildsBorderColor",{isAdaptive:!0,hasHover:!0}],["coursesSearchCategoryDropdownChildsBorderWidth",{isAdaptive:!0,unit:"coursesSearchCategoryDropdownChildsBorderWidthUnit",hasHover:!0}],["coursesSearchCategoryDropdownChildsBorderRadius",{isAdaptive:!0,unit:"coursesSearchCategoryDropdownChildsBorderRadiusUnit"}],["coursesSearchCategoryDropdownChildsPadding",{unit:"coursesSearchCategoryDropdownChildsPaddingUnit",isAdaptive:!0}],["coursesSearchPopupBackground",{}],["coursesSearchPopupSidebarPadding",{unit:"coursesSearchPopupSidebarPaddingUnit",isAdaptive:!0}],["coursesSearchPopupSidebarBackground",{}]]),Na={buttonInside:{coursesSearchFieldPadding:{top:"0",right:"0",bottom:"0",left:"20"},coursesSearchFieldButtonMargin:{top:"0",right:"0",bottom:"0",left:"0"},coursesSearchFieldButtonBackground:"",coursesSearchFieldButtonBackgroundHover:"",coursesSearchFieldButtonColor:"#001931",coursesSearchFieldButtonColorHover:"#001931",coursesSearchFieldButtonWidth:40,coursesSearchFieldButtonHeight:40},buttonInsideLeft:{coursesSearchFieldPadding:{top:"0",right:"0",bottom:"0",left:"0"},coursesSearchFieldButtonMargin:{top:"0",right:"0",bottom:"0",left:"0"},coursesSearchFieldButtonBackground:"",coursesSearchFieldButtonBackgroundHover:"",coursesSearchFieldButtonColor:"#001931",coursesSearchFieldButtonColorHover:"#001931",coursesSearchFieldButtonWidth:40,coursesSearchFieldButtonHeight:40},buttonOutside:{coursesSearchFieldPadding:{top:"0",right:"0",bottom:"0",left:"20"},coursesSearchFieldButtonMargin:{top:"0",right:"0",bottom:"0",left:"10"},coursesSearchFieldButtonBackground:"#227aff",coursesSearchFieldButtonBackgroundHover:"#227aff",coursesSearchFieldButtonColor:"#ffffff",coursesSearchFieldButtonColorHover:"#ffffff",coursesSearchFieldButtonWidth:42,coursesSearchFieldButtonHeight:42},buttonCompact:{coursesSearchFieldPadding:{top:"0",right:"0",bottom:"0",left:"20"},coursesSearchFieldButtonMargin:{top:"0",right:"0",bottom:"0",left:"0"},coursesSearchFieldButtonBackground:"#227aff",coursesSearchFieldButtonBackgroundHover:"#227aff",coursesSearchFieldButtonColor:"#ffffff",coursesSearchFieldButtonColorHover:"#ffffff",coursesSearchFieldButtonWidth:42,coursesSearchFieldButtonHeight:42}},xa=({src:e,alt:t,width:r="370",height:o="253"})=>(0,a.createElement)("img",{src:e,alt:t,width:r,height:o}),Ta=N("course-search-preset"),Ma=({preset:e})=>(0,a.createElement)("div",{className:Ta},(0,a.createElement)("div",{className:`${Ta}--${e.value}`},(0,a.createElement)(O,{condition:Boolean(e.src)},(0,a.createElement)(xa,{src:e.src,alt:e.alt})))),Ua=N("course-search-style-settings"),Ha=(e,t)=>e.value===t.value,Pa=({label:e,attributeName:t,presets:r,popoverContent:o=null,isAdaptive:n=!1,dependencyMode:l,dependencies:i})=>{const{fieldName:c}=K(t,n),{isChanged:d,onReset:u,onSelectPreset:m,activePreset:h}=(e=>{const{setAttributes:t}=Z(),{value:r,isChanged:a,onReset:o}=G(e),n=(0,s.useCallback)((r=>{const{value:a}=r;t({[e]:a})}),[e,t]);return{activePreset:(0,s.useMemo)((()=>({value:r})),[r]),onReset:o,isChanged:a,onSelectPreset:n}})(c);return Y(i,l)?(0,a.createElement)("div",{className:Ua},(0,a.createElement)(Ge,{label:e,isChanged:d,onReset:u,popoverContent:o,showDevicePicker:n}),(0,a.createElement)(Dt,{presets:r,onSelectPreset:m,activePreset:h,PresetItem:Ma,detectIsActive:Ha,detectByIndex:!1})):null},Aa=r.p+"images/button-inside.f6438f9e.png",Wa=r.p+"images/button-inside-left.2ae3cf41.png",ka=r.p+"images/button-outside.dbd35cf8.png",La=r.p+"images/button-compact.6e5dabd3.png",Ra=[{name:o.__("Button Inside (Default)","masterstudy-lms-learning-management-system"),value:"buttonInside",src:Aa,alt:"Button Inside (Default)"},{name:o.__("Button Inside Left","masterstudy-lms-learning-management-system"),value:"buttonInsideLeft",src:Wa,alt:"Button Inside Left"},{name:o.__("Button Outside","masterstudy-lms-learning-management-system"),value:"buttonOutside",src:ka,alt:"Button Inside Left"},{name:o.__("Button Compact","masterstudy-lms-learning-management-system"),value:"buttonCompact",src:La,alt:"Button Inside Left"}],Oa=({categories:e})=>{const t=[{label:o.__("Without Wrapper","masterstudy-lms-learning-management-system"),value:"default"},{label:o.__("With Wrapper","masterstudy-lms-learning-management-system"),value:"with_sidebar"}];return(0,a.createElement)(a.Fragment,null,(0,a.createElement)(ia,{title:o.__("Layout","masterstudy-lms-learning-management-system"),accordionFields:da},(0,a.createElement)(Pa,{label:o.__("Presets","masterstudy-lms-learning-management-system"),presets:Ra,attributeName:"courseSearchPresets"}),(0,a.createElement)(xt,{name:"coursesSearchCategory",label:o.__("Categories","masterstudy-lms-learning-management-system")}),(0,a.createElement)(xt,{name:"coursesSearchPopup",label:o.__("Popup","masterstudy-lms-learning-management-system"),dependencies:[{name:"courseSearchPresets",value:["buttonCompact"]}]}),(0,a.createElement)(qt,{name:"coursesSearchPopupStyle",label:o.__("Presets","masterstudy-lms-learning-management-system"),options:t,dependencies:[{name:"courseSearchPresets",value:["buttonCompact"]},{name:"coursesSearchPopup",value:!0}]})))},za=()=>{const{widthOptions:e}=(()=>{const e=[{label:o.__("Auto","masterstudy-lms-learning-management-system"),value:"alignauto"},{label:o.__("Full width","masterstudy-lms-learning-management-system"),value:"alignfull"}],t=[{label:o.__("Cover","masterstudy-lms-learning-management-system"),value:"cover"},{label:o.__("Contain","masterstudy-lms-learning-management-system"),value:"contain"},{label:o.__("Inherit","masterstudy-lms-learning-management-system"),value:"inherit"},{label:o.__("Initial","masterstudy-lms-learning-management-system"),value:"initial"},{label:o.__("Revert","masterstudy-lms-learning-management-system"),value:"revert"},{label:o.__("Revert-layer","masterstudy-lms-learning-management-system"),value:"revert-layer"},{label:o.__("Unset","masterstudy-lms-learning-management-system"),value:"unset"}],r=[{label:o.__("Center center","masterstudy-lms-learning-management-system"),value:"center center"},{label:o.__("Center left","masterstudy-lms-learning-management-system"),value:"center left"},{label:o.__("Center right","masterstudy-lms-learning-management-system"),value:"center right"},{label:o.__("Top center","masterstudy-lms-learning-management-system"),value:"top center"},{label:o.__("Top left","masterstudy-lms-learning-management-system"),value:"top left"},{label:o.__("Top right","masterstudy-lms-learning-management-system"),value:"top right"},{label:o.__("Bottom center","masterstudy-lms-learning-management-system"),value:"bottom center"},{label:o.__("Bottom left","masterstudy-lms-learning-management-system"),value:"bottom left"},{label:o.__("Bottom right","masterstudy-lms-learning-management-system"),value:"bottom right"}],a=[{label:o.__("Center","masterstudy-lms-learning-management-system"),value:"center"},{label:o.__("Start","masterstudy-lms-learning-management-system"),value:"flex-start"},{label:o.__("End","masterstudy-lms-learning-management-system"),value:"flex-end"},{label:o.__("Space Between","masterstudy-lms-learning-management-system"),value:"space-between"},{label:o.__("Space Around","masterstudy-lms-learning-management-system"),value:"space-around"},{label:o.__("Space Evenly","masterstudy-lms-learning-management-system"),value:"space-evenly"}];return{filterOptions:f,widthOptions:e,backgroundSizeOptions:t,backgroundPositionOptions:r,alignContentOptions:a}})();return(0,a.createElement)(a.Fragment,null,(0,a.createElement)(ia,{title:o.__("Layout","masterstudy-lms-learning-management-system"),accordionFields:ha},(0,a.createElement)(nt,{name:"coursesSearchMargin",label:o.__("Margin","masterstudy-lms-learning-management-system"),unitName:"coursesSearchMarginUnit",isAdaptive:!0}),(0,a.createElement)(nt,{name:"coursesSearchPadding",label:o.__("Padding","masterstudy-lms-learning-management-system"),unitName:"coursesSearchPaddingUnit",isAdaptive:!0}),(0,a.createElement)(qe,{name:"coursesSearchBackground",label:o.__("Background","masterstudy-lms-learning-management-system"),placeholder:o.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(pt,{label:o.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"coursesSearchBorderStyle",borderColorName:"coursesSearchBorderColor",borderWidthName:"coursesSearchBorderWidth",isAdaptive:!0}),(0,a.createElement)(Ct,{name:"coursesSearchBorderRadius",label:o.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,a.createElement)(dt,{name:"coursesSearchAlign",label:o.__("Alignment","masterstudy-lms-learning-management-system"),options:[{label:(0,a.createElement)(d.Dashicon,{icon:"align-pull-left"}),value:"start"},{label:(0,a.createElement)(d.Dashicon,{icon:"align-center"}),value:"center"},{label:(0,a.createElement)(d.Dashicon,{icon:"align-pull-right"}),value:"end"}]}),(0,a.createElement)(tr,{name:"coursesSearchWidth",label:o.__("Width","masterstudy-lms-learning-management-system"),options:e}),(0,a.createElement)(Wt,{name:"coursesSearchMaxWidth",label:o.__("Content Max Width","masterstudy-lms-learning-management-system"),unitName:"coursesSearchMaxWidthUnit",isAdaptive:!0}),(0,a.createElement)(ft,{name:"coursesSearchZIndex",label:o.__("Z-Index","masterstudy-lms-learning-management-system"),min:0,max:100,isAdaptive:!0})),(0,a.createElement)(ia,{title:o.__("Search Field","masterstudy-lms-learning-management-system"),accordionFields:pa},(0,a.createElement)(sr,{fontSizeName:"coursesSearchFieldFontSize",fontSizeUnitName:"coursesSearchFieldFontSizeUnit",fontWeightName:"coursesSearchFieldFontWeight",textTransformName:"coursesSearchFieldTextTransform",fontStyleName:"coursesSearchFieldFontStyle",textDecorationName:"coursesSearchFieldTextDecoration",lineHeightName:"coursesSearchFieldLineHeight",lineHeightUnitName:"coursesSearchFieldLineHeightUnit",letterSpacingName:"coursesSearchFieldLetterSpacing",letterSpacingUnitName:"coursesSearchFieldLetterSpacingUnit",wordSpacingName:"coursesSearchFieldWordSpacing",wordSpacingUnitName:"coursesSearchFieldWordSpacingUnit",isAdaptive:!0}),(0,a.createElement)(qe,{name:"coursesSearchFieldColor",label:o.__("Color","masterstudy-lms-learning-management-system"),placeholder:o.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(qe,{name:"coursesSearchFieldPlaceholderColor",label:o.__("Placeholder Color","masterstudy-lms-learning-management-system"),placeholder:o.__("Select color","masterstudy-lms-learning-management-system"),dependencies:[{name:"courseSearchPresets",value:["buttonInside","ButtonInsideLeft"]}]}),(0,a.createElement)(qe,{name:"coursesSearchFieldBackground",label:o.__("Background","masterstudy-lms-learning-management-system"),placeholder:o.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(pt,{label:o.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"coursesSearchFieldBorderStyle",borderColorName:"coursesSearchFieldBorderColor",borderWidthName:"coursesSearchFieldBorderWidth",isAdaptive:!0}),(0,a.createElement)(Ct,{name:"coursesSearchFieldBorderRadius",label:o.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,a.createElement)(nt,{name:"coursesSearchFieldMargin",label:o.__("Margin","masterstudy-lms-learning-management-system"),unitName:"coursesSearchFieldMarginUnit",isAdaptive:!0}),(0,a.createElement)(nt,{name:"coursesSearchFieldPadding",label:o.__("Padding","masterstudy-lms-learning-management-system"),unitName:"coursesSearchFieldPaddingUnit",isAdaptive:!0}),(0,a.createElement)(Ht,{label:o.__("Select Preset","masterstudy-lms-learning-management-system"),min:0,max:100,shadowColorName:"coursesSearchFieldShadowColor",shadowHorizontalName:"coursesSearchFieldShadowHorizontal",shadowVerticalName:"coursesSearchFieldShadowVertical",shadowBlurName:"coursesSearchFieldShadowBlur",shadowSpreadName:"coursesSearchFieldShadowSpread",shadowInsetName:"coursesSearchFieldShadowInset",popoverContent:null,isAdaptive:!0})),(0,a.createElement)(ia,{title:o.__("Search Dropdown","masterstudy-lms-learning-management-system"),accordionFields:ya},(0,a.createElement)(sr,{fontSizeName:"coursesSearchFieldDropdownFontSize",fontSizeUnitName:"coursesSearchFieldDropdownFontSizeUnit",fontWeightName:"coursesSearchFieldDropdownFontWeight",textTransformName:"coursesSearchFieldDropdownTextTransform",fontStyleName:"coursesSearchFieldDropdownFontStyle",textDecorationName:"coursesSearchFieldDropdownTextDecoration",lineHeightName:"coursesSearchFieldDropdownLineHeight",lineHeightUnitName:"coursesSearchFieldDropdownLineHeightUnit",letterSpacingName:"coursesSearchFieldDropdownLetterSpacing",letterSpacingUnitName:"coursesSearchFieldDropdownLetterSpacingUnit",wordSpacingName:"coursesSearchFieldDropdownWordSpacing",wordSpacingUnitName:"coursesSearchFieldDropdownWordSpacingUnit",isAdaptive:!0}),(0,a.createElement)(qe,{name:"coursesSearchFieldDropdownColor",label:o.__("Color","masterstudy-lms-learning-management-system"),placeholder:o.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,a.createElement)(qe,{name:"coursesSearchFieldDropdownBackground",label:o.__("Background","masterstudy-lms-learning-management-system"),placeholder:o.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(pt,{label:o.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"coursesSearchFieldDropdownBorderStyle",borderColorName:"coursesSearchFieldDropdownBorderColor",borderWidthName:"coursesSearchFieldDropdownBorderWidth",isAdaptive:!0}),(0,a.createElement)(Ct,{name:"coursesSearchFieldDropdownBorderRadius",label:o.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,a.createElement)(nt,{name:"coursesSearchFieldDropdownMargin",label:o.__("Margin","masterstudy-lms-learning-management-system"),unitName:"coursesSearchFieldDropdownMarginUnit",isAdaptive:!0}),(0,a.createElement)(nt,{name:"coursesSearchFieldDropdownPadding",label:o.__("Padding","masterstudy-lms-learning-management-system"),unitName:"coursesSearchFieldDropdownPaddingUnit",isAdaptive:!0}),(0,a.createElement)(Ht,{label:o.__("Select Preset","masterstudy-lms-learning-management-system"),min:0,max:100,shadowColorName:"coursesSearchFieldDropdownShadowColor",shadowHorizontalName:"coursesSearchFieldDropdownShadowHorizontal",shadowVerticalName:"coursesSearchFieldDropdownShadowVertical",shadowBlurName:"coursesSearchFieldDropdownShadowBlur",shadowSpreadName:"coursesSearchFieldDropdownShadowSpread",shadowInsetName:"coursesSearchFieldDropdownShadowInset",popoverContent:null,isAdaptive:!0})),(0,a.createElement)(ia,{title:o.__("Search Button","masterstudy-lms-learning-management-system"),accordionFields:ba},(0,a.createElement)(sr,{fontSizeName:"coursesSearchFieldButtonFontSize",fontSizeUnitName:"coursesSearchFieldButtonFontSizeUnit",fontWeightName:"coursesSearchFieldButtonFontWeight",textTransformName:"coursesSearchFieldButtonTextTransform",fontStyleName:"coursesSearchFieldButtonFontStyle",textDecorationName:"coursesSearchFieldButtonTextDecoration",lineHeightName:"coursesSearchFieldButtonLineHeight",lineHeightUnitName:"coursesSearchFieldButtonLineHeightUnit",letterSpacingName:"coursesSearchFieldButtonLetterSpacing",letterSpacingUnitName:"coursesSearchFieldButtonLetterSpacingUnit",wordSpacingName:"coursesSearchFieldButtonWordSpacing",wordSpacingUnitName:"coursesSearchFieldButtonWordSpacingUnit",isAdaptive:!0}),(0,a.createElement)(qe,{name:"coursesSearchFieldButtonColor",label:o.__("Color","masterstudy-lms-learning-management-system"),placeholder:o.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,a.createElement)(qe,{name:"coursesSearchFieldButtonBackground",label:o.__("Background","masterstudy-lms-learning-management-system"),placeholder:o.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,a.createElement)(ft,{name:"coursesSearchFieldButtonWidth",label:o.__("Width","masterstudy-lms-learning-management-system"),min:0,max:200,isAdaptive:!0}),(0,a.createElement)(ft,{name:"coursesSearchFieldButtonHeight",label:o.__("Height","masterstudy-lms-learning-management-system"),min:0,max:200,isAdaptive:!0}),(0,a.createElement)(pt,{label:o.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"coursesSearchFieldButtonBorderStyle",borderColorName:"coursesSearchFieldButtonBorderColor",borderWidthName:"coursesSearchFieldButtonBorderWidth",isAdaptive:!0,hasHover:!0}),(0,a.createElement)(Ct,{name:"coursesSearchFieldButtonBorderRadius",label:o.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,a.createElement)(nt,{name:"coursesSearchFieldButtonMargin",label:o.__("Margin","masterstudy-lms-learning-management-system"),unitName:"coursesSearchFieldButtonMarginUnit",isAdaptive:!0}),(0,a.createElement)(nt,{name:"coursesSearchFieldButtonPadding",label:o.__("Padding","masterstudy-lms-learning-management-system"),unitName:"coursesSearchFieldButtonPaddingUnit",isAdaptive:!0}),(0,a.createElement)(Ht,{label:o.__("Select Preset","masterstudy-lms-learning-management-system"),min:0,max:100,shadowColorName:"coursesSearchFieldButtonShadowColor",shadowHorizontalName:"coursesSearchFieldButtonShadowHorizontal",shadowVerticalName:"coursesSearchFieldButtonShadowVertical",shadowBlurName:"coursesSearchFieldButtonShadowBlur",shadowSpreadName:"coursesSearchFieldButtonShadowSpread",shadowInsetName:"coursesSearchFieldButtonShadowInset",popoverContent:null,isAdaptive:!0})),(0,a.createElement)(ia,{title:o.__("Categories Button","masterstudy-lms-learning-management-system"),accordionFields:wa},(0,a.createElement)(sr,{fontSizeName:"coursesSearchCategoryButtonFontSize",fontSizeUnitName:"coursesSearchCategoryButtonFontSizeUnit",fontWeightName:"coursesSearchCategoryButtonFontWeight",textTransformName:"coursesSearchCategoryButtonTextTransform",fontStyleName:"coursesSearchCategoryButtonFontStyle",textDecorationName:"coursesSearchCategoryButtonTextDecoration",lineHeightName:"coursesSearchCategoryButtonLineHeight",lineHeightUnitName:"coursesSearchCategoryButtonLineHeightUnit",letterSpacingName:"coursesSearchCategoryButtonLetterSpacing",letterSpacingUnitName:"coursesSearchCategoryButtonLetterSpacingUnit",wordSpacingName:"coursesSearchCategoryButtonWordSpacing",wordSpacingUnitName:"coursesSearchCategoryButtonWordSpacingUnit",isAdaptive:!0}),(0,a.createElement)(qe,{name:"coursesSearchCategoryButtonColor",label:o.__("Color","masterstudy-lms-learning-management-system"),placeholder:o.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,a.createElement)(qe,{name:"coursesSearchCategoryButtonBackground",label:o.__("Background","masterstudy-lms-learning-management-system"),placeholder:o.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,a.createElement)(ft,{name:"coursesSearchCategoryButtonIconFontSize",label:o.__("Icon Size","masterstudy-lms-learning-management-system"),min:0,max:100,isAdaptive:!0}),(0,a.createElement)(qe,{name:"coursesSearchCategoryButtonIconColor",label:o.__("Icon Color","masterstudy-lms-learning-management-system"),placeholder:o.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,a.createElement)(pt,{label:o.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"coursesSearchCategoryButtonBorderStyle",borderColorName:"coursesSearchCategoryButtonBorderColor",borderWidthName:"coursesSearchCategoryButtonBorderWidth",isAdaptive:!0,hasHover:!0}),(0,a.createElement)(Ct,{name:"coursesSearchCategoryButtonBorderRadius",label:o.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,a.createElement)(nt,{name:"coursesSearchCategoryButtonMargin",label:o.__("Margin","masterstudy-lms-learning-management-system"),unitName:"coursesSearchCategoryButtonMarginUnit",isAdaptive:!0}),(0,a.createElement)(nt,{name:"coursesSearchCategoryButtonPadding",label:o.__("Padding","masterstudy-lms-learning-management-system"),unitName:"coursesSearchCategoryButtonPaddingUnit",isAdaptive:!0})),(0,a.createElement)(ia,{title:o.__("Categories Dropdown","masterstudy-lms-learning-management-system"),accordionFields:_a},(0,a.createElement)(ft,{name:"coursesSearchCategoryDropdownWidth",label:o.__("Width","masterstudy-lms-learning-management-system"),min:0,max:600,isAdaptive:!0}),(0,a.createElement)(Ge,{label:o.__("Parents Link Typography","masterstudy-lms-learning-management-system"),isChanged:!1,onReset:()=>{},showDevicePicker:!1}),(0,a.createElement)(sr,{fontSizeName:"coursesSearchCategoryDropdownFontSize",fontSizeUnitName:"coursesSearchCategoryDropdownFontSizeUnit",fontWeightName:"coursesSearchCategoryDropdownFontWeight",textTransformName:"coursesSearchCategoryDropdownTextTransform",fontStyleName:"coursesSearchCategoryDropdownFontStyle",textDecorationName:"coursesSearchCategoryDropdownTextDecoration",lineHeightName:"coursesSearchCategoryDropdownLineHeight",lineHeightUnitName:"coursesSearchCategoryDropdownLineHeightUnit",letterSpacingName:"coursesSearchCategoryDropdownLetterSpacing",letterSpacingUnitName:"coursesSearchCategoryDropdownLetterSpacingUnit",wordSpacingName:"coursesSearchCategoryDropdownWordSpacing",wordSpacingUnitName:"coursesSearchCategoryDropdownWordSpacingUnit",isAdaptive:!0}),(0,a.createElement)(qe,{name:"coursesSearchCategoryDropdownColor",label:o.__("Parents Link Color","masterstudy-lms-learning-management-system"),placeholder:o.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,a.createElement)(qe,{name:"coursesSearchCategoryDropdownBackground",label:o.__("Parents Tab Background","masterstudy-lms-learning-management-system"),placeholder:o.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,a.createElement)(pt,{label:o.__("Parents Tab Border","masterstudy-lms-learning-management-system"),borderStyleName:"coursesSearchCategoryDropdownBorderStyle",borderColorName:"coursesSearchCategoryDropdownBorderColor",borderWidthName:"coursesSearchCategoryDropdownBorderWidth",isAdaptive:!0}),(0,a.createElement)(Ct,{name:"coursesSearchCategoryDropdownBorderRadius",label:o.__("Parents Tab Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,a.createElement)(nt,{name:"coursesSearchCategoryDropdownPadding",label:o.__("Parents Tab Padding","masterstudy-lms-learning-management-system"),unitName:"coursesSearchCategoryDropdownPaddingUnit",isAdaptive:!0}),(0,a.createElement)(Ge,{label:o.__("Childs Link Typography","masterstudy-lms-learning-management-system"),isChanged:!1,onReset:()=>{},showDevicePicker:!1}),(0,a.createElement)(sr,{fontSizeName:"coursesSearchCategoryDropdownChildsFontSize",fontSizeUnitName:"coursesSearchCategoryDropdownChildsFontSizeUnit",fontWeightName:"coursesSearchCategoryDropdownChildsFontWeight",textTransformName:"coursesSearchCategoryDropdownChildsTextTransform",fontStyleName:"coursesSearchCategoryDropdownChildsFontStyle",textDecorationName:"coursesSearchCategoryDropdownChildsTextDecoration",lineHeightName:"coursesSearchCategoryDropdownChildsLineHeight",lineHeightUnitName:"coursesSearchCategoryDropdownChildsLineHeightUnit",letterSpacingName:"coursesSearchCategoryDropdownChildsLetterSpacing",letterSpacingUnitName:"coursesSearchCategoryDropdownChildsLetterSpacingUnit",wordSpacingName:"coursesSearchCategoryDropdownChildsWordSpacing",wordSpacingUnitName:"coursesSearchCategoryDropdownChildsWordSpacingUnit",isAdaptive:!0}),(0,a.createElement)(qe,{name:"coursesSearchCategoryDropdownChildsColor",label:o.__("Childs Link Color","masterstudy-lms-learning-management-system"),placeholder:o.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,a.createElement)(qe,{name:"coursesSearchCategoryDropdownChildsBackground",label:o.__("Childs Tab Background","masterstudy-lms-learning-management-system"),placeholder:o.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(pt,{label:o.__("Childs Tab Border","masterstudy-lms-learning-management-system"),borderStyleName:"coursesSearchCategoryDropdownChildsBorderStyle",borderColorName:"coursesSearchCategoryDropdownChildsBorderColor",borderWidthName:"coursesSearchCategoryDropdownChildsBorderWidth",isAdaptive:!0}),(0,a.createElement)(Ct,{name:"coursesSearchCategoryDropdownChildsBorderRadius",label:o.__("Childs Tab Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,a.createElement)(nt,{name:"coursesSearchCategoryDropdownChildsPadding",label:o.__("Childs Tab Padding","masterstudy-lms-learning-management-system"),unitName:"coursesSearchCategoryDropdownChildsPaddingUnit",isAdaptive:!0}),(0,a.createElement)(Ht,{label:o.__("Select Preset","masterstudy-lms-learning-management-system"),min:0,max:100,shadowColorName:"coursesSearchCategoryDropdownShadowColor",shadowHorizontalName:"coursesSearchCategoryDropdownShadowHorizontal",shadowVerticalName:"coursesSearchCategoryDropdownShadowVertical",shadowBlurName:"coursesSearchCategoryDropdownShadowBlur",shadowSpreadName:"coursesSearchCategoryDropdownShadowSpread",shadowInsetName:"coursesSearchCategoryDropdownShadowInset",popoverContent:null,isAdaptive:!0})),(0,a.createElement)(ia,{title:o.__("Popup","masterstudy-lms-learning-management-system"),accordionFields:Da},(0,a.createElement)(qe,{name:"coursesSearchPopupBackground",label:o.__("Background","masterstudy-lms-learning-management-system"),placeholder:o.__("Select color","masterstudy-lms-learning-management-system"),dependencies:[{name:"courseSearchPresets",value:["buttonCompact"]}]}),(0,a.createElement)(nt,{name:"coursesSearchPopupSidebarPadding",label:o.__("Sidebar Padding","masterstudy-lms-learning-management-system"),unitName:"coursesSearchPopupSidebarPaddingUnit",isAdaptive:!0,dependencies:[{name:"courseSearchPresets",value:["buttonCompact"]},{name:"coursesSearchPopupStyle",value:["with_sidebar"]}]}),(0,a.createElement)(qe,{name:"coursesSearchPopupSidebarBackground",label:o.__("Sidebar Background","masterstudy-lms-learning-management-system"),placeholder:o.__("Select color","masterstudy-lms-learning-management-system"),dependencies:[{name:"courseSearchPresets",value:["buttonCompact"]},{name:"coursesSearchPopupStyle",value:["with_sidebar"]}]})))},Ia=({attributes:e,setAttributes:t,categories:r})=>{const{onResetByFieldName:o,changedFieldsByName:n}=((e,t,r,a=[])=>{const o=(e=>{const t={};return Object.entries(e).forEach((([e,r])=>{e.includes("UAG")||(t[e]=r)})),t})(t),n=!u(e,o),s=((e,t,r)=>{const a=new Map;return r.forEach((r=>{a.has(r)||a.set(r,(()=>t({[r]:e[r]})))})),a})(e,r,a),l=((e,t,r)=>{const a=new Map;return r.forEach((r=>{a.has(r)?a.set(r,!1):a.set(r,!u(e[r],t[r]))})),a})(e,o,a);return{hasChanges:n,onResetByFieldName:s,changedFieldsByName:l}})(Ea,e,t,Object.keys(Ea));return(0,a.createElement)(l.InspectorControls,null,(0,a.createElement)(j,{attributes:e,setAttributes:t,defaultValues:Ea,onResetByFieldName:o,changedFieldsByName:n},(0,a.createElement)(br,{generalTab:(0,a.createElement)(Oa,{categories:r}),styleTab:(0,a.createElement)(za,null),advancedTab:(0,a.createElement)(a.Fragment,null)})))},Va=JSON.parse('{"UU":"masterstudy/courses-search-container"}');(0,n.registerBlockType)(Va.UU,{title:o._x("MasterStudy Courses Search","block title","masterstudy-lms-learning-management-system"),description:o._x("Customize how course search will look on the page","block description","masterstudy-lms-learning-management-system"),category:"masterstudy-lms-blocks",icon:{src:(0,a.createElement)("svg",{width:"512",height:"512",viewBox:"0 0 512 512",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,a.createElement)("rect",{opacity:"0.3",x:"174",y:"207",width:"157",height:"157",rx:"78.5",fill:"#227AFF"}),(0,a.createElement)("path",{d:"M471.776 0.996094H40.2236C18.5678 1.01602 1.01602 18.5675 0.996094 40.2229V471.767C1.01602 493.423 18.5678 510.974 40.2236 511.004H471.776C493.432 510.984 510.984 493.432 511.014 471.767V40.2229C510.994 18.5675 493.442 1.01602 471.776 0.996094ZM40.2236 20.6095H471.776C482.604 20.6194 491.38 29.3952 491.39 40.2229V79.4496H20.6099V40.2229C20.6198 29.3952 29.3957 20.6194 40.2236 20.6095ZM471.776 491.391H40.2236C29.3957 491.381 20.6198 482.605 20.6099 471.777V99.073H491.4V471.767C491.39 482.605 482.614 491.381 471.776 491.391Z",fill:"black"}),(0,a.createElement)("path",{d:"M471.786 512H40.2237C18.0698 511.98 0.0199226 493.931 0 471.777V40.2229C0.0199226 18.0694 18.0698 0.0199222 40.2237 0H471.776C493.94 0.0199222 511.98 18.0694 512 40.2229V471.767C511.99 493.931 493.94 511.98 471.786 512ZM40.2237 1.99222C19.1655 2.01214 2.01218 19.1651 1.99226 40.2328V471.777C2.01218 492.835 19.1655 509.988 40.2336 510.008H471.786C492.845 509.988 509.998 492.835 510.018 471.767V40.2229C509.998 19.1651 492.844 2.01214 471.776 1.99222H40.2237ZM471.786 492.387H40.2237C28.8678 492.377 19.6237 483.133 19.6138 471.777V98.0769H492.386V471.767C492.386 483.133 483.142 492.377 471.786 492.387ZM21.606 100.069V471.767C21.616 482.027 29.9735 490.374 40.2237 490.384H471.776C482.036 490.374 490.384 482.027 490.394 471.767V100.069H21.606ZM492.396 80.4557H19.6138V40.2229C19.6237 28.8672 28.8678 19.6233 40.2237 19.6134H471.776C483.132 19.6233 492.376 28.8672 492.386 40.2229V80.4557H492.396ZM21.606 78.4635H490.394V40.2229C490.384 29.963 482.027 21.6156 471.776 21.6056H40.2237C29.9635 21.6156 21.616 29.9729 21.606 40.2229V78.4635Z",fill:"black"}),(0,a.createElement)("path",{d:"M128.82 59.9859C134.25 59.9859 138.651 55.5842 138.651 50.1543C138.651 44.7245 134.25 40.3228 128.82 40.3228C123.39 40.3228 118.988 44.7245 118.988 50.1543C118.988 55.5842 123.39 59.9859 128.82 59.9859Z",fill:"black"}),(0,a.createElement)("path",{d:"M128.82 60.9918C122.853 60.9918 117.992 56.1308 117.992 50.1641C117.992 44.1974 122.853 39.3364 128.82 39.3364C134.787 39.3364 139.648 44.1974 139.648 50.1641C139.648 56.1308 134.787 60.9918 128.82 60.9918ZM128.82 41.3187C123.949 41.3187 119.984 45.2832 119.984 50.1542C119.984 55.0251 123.949 58.9897 128.82 58.9897C133.691 58.9897 137.655 55.0251 137.655 50.1542C137.655 45.2832 133.691 41.3187 128.82 41.3187Z",fill:"black"}),(0,a.createElement)("path",{d:"M89.4917 59.9859C94.9216 59.9859 99.3233 55.5842 99.3233 50.1543C99.3233 44.7245 94.9216 40.3228 89.4917 40.3228C84.0619 40.3228 79.6602 44.7245 79.6602 50.1543C79.6602 55.5842 84.0619 59.9859 89.4917 59.9859Z",fill:"black"}),(0,a.createElement)("path",{d:"M89.4918 60.9918C83.5251 60.9918 78.6641 56.1308 78.6641 50.1641C78.6641 44.1974 83.5251 39.3364 89.4918 39.3364C95.4585 39.3364 100.319 44.1974 100.319 50.1641C100.319 56.1308 95.4585 60.9918 89.4918 60.9918ZM89.4918 41.3187C84.6208 41.3187 80.6563 45.2832 80.6563 50.1542C80.6563 55.0251 84.6208 58.9897 89.4918 58.9897C94.3627 58.9897 98.3273 55.0251 98.3273 50.1542C98.3273 45.2832 94.3627 41.3187 89.4918 41.3187Z",fill:"black"}),(0,a.createElement)("path",{d:"M50.1558 59.9859C55.5857 59.9859 59.9874 55.5842 59.9874 50.1543C59.9874 44.7245 55.5857 40.3228 50.1558 40.3228C44.726 40.3228 40.3242 44.7245 40.3242 50.1543C40.3242 55.5842 44.726 59.9859 50.1558 59.9859Z",fill:"black"}),(0,a.createElement)("path",{d:"M50.1558 60.9918C44.1891 60.9918 39.3281 56.1308 39.3281 50.1641C39.3281 44.1974 44.1891 39.3364 50.1558 39.3364C56.1225 39.3364 60.9835 44.1974 60.9835 50.1641C60.9835 56.1308 56.1325 60.9918 50.1558 60.9918ZM50.1558 41.3187C45.2849 41.3187 41.3203 45.2832 41.3203 50.1542C41.3203 55.0251 45.2849 58.9897 50.1558 58.9897C55.0268 58.9897 58.9913 55.0251 58.9913 50.1542C58.9913 45.2832 55.0368 41.3187 50.1558 41.3187Z",fill:"black"}),(0,a.createElement)("path",{d:"M305.301 327.532C318.01 311.909 324.993 292.408 325.084 272.274C325.084 223.104 285.227 183.237 236.046 183.237C186.865 183.237 147.008 223.094 147.008 272.274C147.008 321.453 186.865 361.3 236.036 361.3C256.17 361.209 275.672 354.227 291.295 341.517L347.74 397.981C351.548 401.91 357.817 402.01 361.746 398.202C365.674 394.394 365.775 388.125 361.967 384.197C361.896 384.126 361.816 384.046 361.746 383.976L305.301 327.532ZM236.046 341.517C197.796 341.517 166.801 310.512 166.801 272.274C166.801 234.035 197.806 203.03 236.046 203.03C274.285 203.03 305.291 234.035 305.291 272.274C305.251 310.502 274.275 341.477 236.046 341.517Z",fill:"black"}),(0,a.createElement)("path",{d:"M354.874 402C354.814 402 354.754 402 354.693 402C351.78 401.95 349.057 400.774 347.028 398.684L291.226 342.873C275.602 355.332 256.04 362.224 236.047 362.314C186.394 362.314 146.004 321.925 146.004 272.273C146.004 222.621 186.393 182.232 236.037 182.232C285.68 182.232 326.069 222.621 326.069 272.273C325.979 292.277 319.086 311.828 306.628 327.461L362.46 383.272C362.53 383.343 362.611 383.423 362.691 383.503C366.871 387.823 366.76 394.746 362.44 398.925C360.41 400.915 357.728 402 354.874 402ZM291.376 340.171L348.464 397.278C350.132 398.996 352.353 399.96 354.724 400C357.095 400 359.355 399.146 361.053 397.489C364.58 394.073 364.67 388.426 361.254 384.9L361.053 384.699L303.935 327.602L304.508 326.899C317.037 311.507 323.979 292.106 324.07 272.273C324.07 223.736 284.584 184.252 236.047 184.252C187.509 184.252 148.023 223.736 148.023 272.283C148.023 320.82 187.509 360.315 236.047 360.315C255.88 360.224 275.281 353.282 290.673 340.753L291.376 340.171ZM236.047 342.522C197.305 342.522 165.797 311.004 165.797 272.273C165.797 233.542 197.315 202.025 236.047 202.025C274.788 202.025 306.296 233.542 306.296 272.273C306.256 310.964 274.748 342.482 236.047 342.522ZM236.047 204.034C198.42 204.034 167.806 234.647 167.806 272.273C167.806 309.899 198.42 340.512 236.047 340.512C273.633 340.472 304.247 309.859 304.287 272.273C304.297 234.647 273.683 204.034 236.047 204.034Z",fill:"black"}))},edit:({attributes:e,setAttributes:t})=>{const{isFetching:r,categories:o}=((e=!1)=>{const[t,r]=(0,s.useState)([]),[a,o]=(0,s.useState)({}),[n,l]=(0,s.useState)({}),{setIsFetching:i,setError:c,isFetching:d,error:u}=P();return(0,s.useEffect)((()=>{i(!0),(async(e=!1)=>{try{let t="?children=true";return e&&(t+="&details=true"),await T()({path:`${U}${t}`})}catch(e){throw new Error(e)}})(e).then((({categories:e})=>{r((e=>e.map((e=>({label:e.name,value:e.id,image:e.image,icon:e.icon,color:e.color,children:e.children?m(e.children):[]}))))(e)),o(e.reduce(((e,t)=>(e[String(t.id)]=t.name,e)),{})),l(e.reduce(((e,t)=>(e[String(t.id)]={label:t.name,value:t.id,image:t.image,icon:t.icon,color:t.color,courses:t.courses,children:t.children},e)),{}))})).catch((e=>{c(e.message)})).finally((()=>{i(!1)}))}),[]),{categories:t,categoriesMap:a,categoriesMapFull:n,isFetching:d,error:u}})(),{courses:n}=(e=>{const{courses:t,isFetching:r}=(e=>{const[t,r]=(0,s.useState)([]),{setIsFetching:a,setError:o,isFetching:n,error:l}=P();return(0,s.useEffect)((()=>{""!==e&&(a(!0),(async e=>{try{return await T()({path:`${H}?${e}`})}catch(e){throw new Error(e)}})(e).then((e=>{r((e=>e.courses.map((t=>{const r=t.categories.map((t=>({name:t.name,permalink:`${e.courses_page}?terms[]=${t.term_id}&category[]=${t.term_id}`})));return{id:t.ID,authorName:t.author.name,authorAvatar:t.user_avatar||t.author.avatar,postTitle:t.post_title,categories:r,comingSoon:t.coming_soon_start_time,comingSoonStatus:t.coming_soon_status,status:t.status,cover:!!t.image&&t.image.url,lessons:t.lessons,durationInfo:t.duration_info,ratingVisibility:t.rating_visibility,rating:t.rating,ratingPercent:20*parseFloat(t.rating)+"%",price:t.price,salePrice:t.sale_price,level:t.level||"",postExcerpt:t.post_excerpt,permalink:t.permalink,members:t.members,featured:t.featured,views:t.views,symbol:t.symbol,membership:t.membership,userUrl:t.user_url,userWishlist:t.user_wishlist,trial:t.trial}})))(e))})).catch((e=>{o(e.message)})).finally((()=>{a(!1)})))}),[e]),{courses:t,isFetching:n,error:l}})("/api/courses");return{isFetching:r,courses:t.map((e=>({value:e.id,label:e.postTitle})))}})(),[i,u]=(0,s.useState)(e.courseSearchPresets);(0,s.useEffect)((()=>{e.courseSearchPresets!==i&&(t({...Na[e.courseSearchPresets]}),u(e.courseSearchPresets))}),[e.courseSearchPresets,t,i]);const h=(0,l.useBlockProps)({className:c()("lms-courses-search-container",{alignfull:"alignfull"===e.coursesSearchWidth},e.coursesSearchCategory&&"lms-courses-search-container__with-category",e.coursesSearchPopup&&"lms-courses-search-container__with-popup",{with_sidebar:"with_sidebar"===e.coursesSearchPopupStyle},e.coursesSearchAlign),style:{...B("courses",e,fa),...F("courses-coursesSearchField",e,"coursesSearchFieldShadowColor","coursesSearchFieldShadowHorizontal","coursesSearchFieldShadowVertical","coursesSearchFieldShadowBlur","coursesSearchFieldShadowSpread","coursesSearchFieldShadowInset",!0),...F("courses-coursesSearchFieldDropdown",e,"coursesSearchFieldDropdownShadowColor","coursesSearchFieldDropdownShadowHorizontal","coursesSearchFieldDropdownShadowVertical","coursesSearchFieldDropdownShadowBlur","coursesSearchFieldDropdownShadowSpread","coursesSearchFieldDropdownShadowInset",!0),...F("courses-coursesSearchFieldButton",e,"coursesSearchFieldButtonShadowColor","coursesSearchFieldButtonShadowHorizontal","coursesSearchFieldButtonShadowVertical","coursesSearchFieldButtonShadowBlur","coursesSearchFieldButtonShadowSpread","coursesSearchFieldButtonShadowInset",!0),...F("courses-coursesSearchCategoryDropdown",e,"coursesSearchCategoryDropdownShadowColor","coursesSearchCategoryDropdownShadowHorizontal","coursesSearchCategoryDropdownShadowVertical","coursesSearchCategoryDropdownShadowBlur","coursesSearchCategoryDropdownShadowSpread","coursesSearchCategoryDropdownShadowInset",!0)}});return(0,a.createElement)("div",{...h},(0,a.createElement)("div",{className:"lms-courses-search-container__wrap"},(0,a.createElement)(Ia,{attributes:e,setAttributes:t,categories:o}),(0,a.createElement)(O,{condition:!r,fallback:(0,a.createElement)(d.Spinner,null)},(()=>{switch(e.courseSearchPresets){case"buttonCompact":return(0,a.createElement)(R,{options:e.coursesSearchOptions,categories:o,courses:n,className:"lms-courses-search-box lms-courses-search-box__button-compact"});case"buttonOutside":return(0,a.createElement)(L,{options:e.coursesSearchOptions,categories:o,courses:n,className:"lms-courses-search-box lms-courses-search-box__button-outside"});case"buttonInsideLeft":return(0,a.createElement)(L,{options:e.coursesSearchOptions,categories:o,courses:n,className:"lms-courses-search-box lms-courses-search-box__button-left"});default:return(0,a.createElement)(L,{options:e.coursesSearchOptions,categories:o,courses:n,className:"lms-courses-search-box"})}})())))},save:({attributes:e})=>{const t=l.useBlockProps.save({className:c()("lms-courses-search-container",{alignfull:"alignfull"===e.coursesSearchWidth},e.coursesSearchCategory&&"lms-courses-search-container__with-category",e.coursesSearchPopup&&"lms-courses-search-container__with-popup",{with_sidebar:"with_sidebar"===e.coursesSearchPopupStyle},e.textAlign),style:{...B("courses",e,fa),...F("courses-coursesSearchField",e,"coursesSearchFieldShadowColor","coursesSearchFieldShadowHorizontal","coursesSearchFieldShadowVertical","coursesSearchFieldShadowBlur","coursesSearchFieldShadowSpread","coursesSearchFieldShadowInset",!0),...F("courses-coursesSearchFieldDropdown",e,"coursesSearchFieldDropdownShadowColor","coursesSearchFieldDropdownShadowHorizontal","coursesSearchFieldDropdownShadowVertical","coursesSearchFieldDropdownShadowBlur","coursesSearchFieldDropdownShadowSpread","coursesSearchFieldDropdownShadowInset",!0),...F("courses-coursesSearchFieldButton",e,"coursesSearchFieldButtonShadowColor","coursesSearchFieldButtonShadowHorizontal","coursesSearchFieldButtonShadowVertical","coursesSearchFieldButtonShadowBlur","coursesSearchFieldButtonShadowSpread","coursesSearchFieldButtonShadowInset",!0),...F("courses-coursesSearchCategoryDropdown",e,"coursesSearchCategoryDropdownShadowColor","coursesSearchCategoryDropdownShadowHorizontal","coursesSearchCategoryDropdownShadowVertical","coursesSearchCategoryDropdownShadowBlur","coursesSearchCategoryDropdownShadowSpread","coursesSearchCategoryDropdownShadowInset",!0)}});return(0,a.createElement)("div",{...t},(0,a.createElement)("div",{className:"lms-course-preloader"},(0,a.createElement)("div",{className:"lms-course-preloader-item"})),(0,a.createElement)("div",{className:"lms-courses-search-container__wrap"},(0,a.createElement)("input",{type:"hidden",className:"lms-courses-search-list-data","data-style":e.courseSearchPresets})))}})},6942:(e,t)=>{var r;!function(){"use strict";var a={}.hasOwnProperty;function o(){for(var e="",t=0;t<arguments.length;t++){var r=arguments[t];r&&(e=s(e,n(r)))}return e}function n(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return o.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var r in e)a.call(e,r)&&e[r]&&(t=s(t,r));return t}function s(e,t){return t?e?e+" "+t:e+t:e}e.exports?(o.default=o,e.exports=o):void 0===(r=function(){return o}.apply(t,[]))||(e.exports=r)}()}},r={};function a(e){var o=r[e];if(void 0!==o)return o.exports;var n=r[e]={exports:{}};return t[e](n,n.exports,a),n.exports}a.m=t,e=[],a.O=(t,r,o,n)=>{if(!r){var s=1/0;for(d=0;d<e.length;d++){for(var[r,o,n]=e[d],l=!0,i=0;i<r.length;i++)(!1&n||s>=n)&&Object.keys(a.O).every((e=>a.O[e](r[i])))?r.splice(i--,1):(l=!1,n<s&&(s=n));if(l){e.splice(d--,1);var c=o();void 0!==c&&(t=c)}}return t}n=n||0;for(var d=e.length;d>0&&e[d-1][2]>n;d--)e[d]=e[d-1];e[d]=[r,o,n]},a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var r in t)a.o(t,r)&&!a.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},a.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e;a.g.importScripts&&(e=a.g.location+"");var t=a.g.document;if(!e&&t&&(t.currentScript&&(e=t.currentScript.src),!e)){var r=t.getElementsByTagName("script");if(r.length)for(var o=r.length-1;o>-1&&(!e||!/^http(s?):/.test(e));)e=r[o--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),a.p=e+"../../"})(),(()=>{var e={1052:0,1540:0};a.O.j=t=>0===e[t];var t=(t,r)=>{var o,n,[s,l,i]=r,c=0;if(s.some((t=>0!==e[t]))){for(o in l)a.o(l,o)&&(a.m[o]=l[o]);if(i)var d=i(a)}for(t&&t(r);c<s.length;c++)n=s[c],a.o(e,n)&&e[n]&&e[n][0](),e[n]=0;return a.O(d)},r=globalThis.webpackChunkmasterstudy_lms_learning_management_system=globalThis.webpackChunkmasterstudy_lms_learning_management_system||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})();var o=a.O(void 0,[1540],(()=>a(6492)));o=a.O(o)})();