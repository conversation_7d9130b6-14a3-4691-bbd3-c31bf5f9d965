(()=>{var e={2694:(e,t,n)=>{"use strict";var a=n(6925);function i(){}function r(){}r.resetWarningCache=i,e.exports=function(){function e(e,t,n,i,r,s){if(s!==a){var o=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw o.name="Invariant Violation",o}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:r,resetWarningCache:i};return n.PropTypes=n,n}},5556:(e,t,n)=>{e.exports=n(2694)()},6925:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"}},t={};function n(a){var i=t[a];if(void 0!==i)return i.exports;var r=t[a]={exports:{}};return e[a](r,r.exports,n),r.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var a in t)n.o(t,a)&&!n.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";const e=window.React;var t=n.n(e);function a(e){return null!==e&&"object"==typeof e&&"constructor"in e&&e.constructor===Object}function i(e,t){void 0===e&&(e={}),void 0===t&&(t={}),Object.keys(t).forEach((n=>{void 0===e[n]?e[n]=t[n]:a(t[n])&&a(e[n])&&Object.keys(t[n]).length>0&&i(e[n],t[n])}))}const r={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector:()=>null,querySelectorAll:()=>[],getElementById:()=>null,createEvent:()=>({initEvent(){}}),createElement:()=>({children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName:()=>[]}),createElementNS:()=>({}),importNode:()=>null,location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function s(){const e="undefined"!=typeof document?document:{};return i(e,r),e}const o={document:r,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle:()=>({getPropertyValue:()=>""}),Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia:()=>({}),requestAnimationFrame:e=>"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0),cancelAnimationFrame(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};function l(){const e="undefined"!=typeof window?window:{};return i(e,o),e}function c(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function d(){return Date.now()}function u(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function f(){const e=Object(arguments.length<=0?void 0:arguments[0]),t=["__proto__","constructor","prototype"];for(let a=1;a<arguments.length;a+=1){const i=a<0||arguments.length<=a?void 0:arguments[a];if(null!=i&&(n=i,!("undefined"!=typeof window&&void 0!==window.HTMLElement?n instanceof HTMLElement:n&&(1===n.nodeType||11===n.nodeType)))){const n=Object.keys(Object(i)).filter((e=>t.indexOf(e)<0));for(let t=0,a=n.length;t<a;t+=1){const a=n[t],r=Object.getOwnPropertyDescriptor(i,a);void 0!==r&&r.enumerable&&(u(e[a])&&u(i[a])?i[a].__swiper__?e[a]=i[a]:f(e[a],i[a]):!u(e[a])&&u(i[a])?(e[a]={},i[a].__swiper__?e[a]=i[a]:f(e[a],i[a])):e[a]=i[a])}}}var n;return e}function p(e,t,n){e.style.setProperty(t,n)}function m(e){let{swiper:t,targetPosition:n,side:a}=e;const i=l(),r=-t.translate;let s,o=null;const c=t.params.speed;t.wrapperEl.style.scrollSnapType="none",i.cancelAnimationFrame(t.cssModeFrameID);const d=n>r?"next":"prev",u=(e,t)=>"next"===d&&e>=t||"prev"===d&&e<=t,f=()=>{s=(new Date).getTime(),null===o&&(o=s);const e=Math.max(Math.min((s-o)/c,1),0),l=.5-Math.cos(e*Math.PI)/2;let d=r+l*(n-r);if(u(d,n)&&(d=n),t.wrapperEl.scrollTo({[a]:d}),u(d,n))return t.wrapperEl.style.overflow="hidden",t.wrapperEl.style.scrollSnapType="",setTimeout((()=>{t.wrapperEl.style.overflow="",t.wrapperEl.scrollTo({[a]:d})})),void i.cancelAnimationFrame(t.cssModeFrameID);t.cssModeFrameID=i.requestAnimationFrame(f)};f()}function h(e,t){return void 0===t&&(t=""),[...e.children].filter((e=>e.matches(t)))}function v(e){try{return void console.warn(e)}catch(e){}}function g(e,t){void 0===t&&(t=[]);const n=document.createElement(e);return n.classList.add(...Array.isArray(t)?t:function(e){return void 0===e&&(e=""),e.trim().split(" ").filter((e=>!!e.trim()))}(t)),n}function b(e,t){return l().getComputedStyle(e,null).getPropertyValue(t)}function y(e){let t,n=e;if(n){for(t=0;null!==(n=n.previousSibling);)1===n.nodeType&&(t+=1);return t}}function w(e,t){const n=[];let a=e.parentElement;for(;a;)t?a.matches(t)&&n.push(a):n.push(a),a=a.parentElement;return n}function x(e,t,n){const a=l();return n?e["width"===t?"offsetWidth":"offsetHeight"]+parseFloat(a.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-right":"margin-top"))+parseFloat(a.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-left":"margin-bottom")):e.offsetWidth}function S(e){return(Array.isArray(e)?e:[e]).filter((e=>!!e))}let k,T,E;function C(){return k||(k=function(){const e=l(),t=s();return{smoothScroll:t.documentElement&&t.documentElement.style&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch)}}()),k}function P(e){return void 0===e&&(e={}),T||(T=function(e){let{userAgent:t}=void 0===e?{}:e;const n=C(),a=l(),i=a.navigator.platform,r=t||a.navigator.userAgent,s={ios:!1,android:!1},o=a.screen.width,c=a.screen.height,d=r.match(/(Android);?[\s\/]+([\d.]+)?/);let u=r.match(/(iPad).*OS\s([\d_]+)/);const f=r.match(/(iPod)(.*OS\s([\d_]+))?/),p=!u&&r.match(/(iPhone\sOS|iOS)\s([\d_]+)/),m="Win32"===i;let h="MacIntel"===i;return!u&&h&&n.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(`${o}x${c}`)>=0&&(u=r.match(/(Version)\/([\d.]+)/),u||(u=[0,1,"13_0_0"]),h=!1),d&&!m&&(s.os="android",s.android=!0),(u||p||f)&&(s.os="ios",s.ios=!0),s}(e)),T}var M={on(e,t,n){const a=this;if(!a.eventsListeners||a.destroyed)return a;if("function"!=typeof t)return a;const i=n?"unshift":"push";return e.split(" ").forEach((e=>{a.eventsListeners[e]||(a.eventsListeners[e]=[]),a.eventsListeners[e][i](t)})),a},once(e,t,n){const a=this;if(!a.eventsListeners||a.destroyed)return a;if("function"!=typeof t)return a;function i(){a.off(e,i),i.__emitterProxy&&delete i.__emitterProxy;for(var n=arguments.length,r=new Array(n),s=0;s<n;s++)r[s]=arguments[s];t.apply(a,r)}return i.__emitterProxy=t,a.on(e,i,n)},onAny(e,t){const n=this;if(!n.eventsListeners||n.destroyed)return n;if("function"!=typeof e)return n;const a=t?"unshift":"push";return n.eventsAnyListeners.indexOf(e)<0&&n.eventsAnyListeners[a](e),n},offAny(e){const t=this;if(!t.eventsListeners||t.destroyed)return t;if(!t.eventsAnyListeners)return t;const n=t.eventsAnyListeners.indexOf(e);return n>=0&&t.eventsAnyListeners.splice(n,1),t},off(e,t){const n=this;return!n.eventsListeners||n.destroyed?n:n.eventsListeners?(e.split(" ").forEach((e=>{void 0===t?n.eventsListeners[e]=[]:n.eventsListeners[e]&&n.eventsListeners[e].forEach(((a,i)=>{(a===t||a.__emitterProxy&&a.__emitterProxy===t)&&n.eventsListeners[e].splice(i,1)}))})),n):n},emit(){const e=this;if(!e.eventsListeners||e.destroyed)return e;if(!e.eventsListeners)return e;let t,n,a;for(var i=arguments.length,r=new Array(i),s=0;s<i;s++)r[s]=arguments[s];return"string"==typeof r[0]||Array.isArray(r[0])?(t=r[0],n=r.slice(1,r.length),a=e):(t=r[0].events,n=r[0].data,a=r[0].context||e),n.unshift(a),(Array.isArray(t)?t:t.split(" ")).forEach((t=>{e.eventsAnyListeners&&e.eventsAnyListeners.length&&e.eventsAnyListeners.forEach((e=>{e.apply(a,[t,...n])})),e.eventsListeners&&e.eventsListeners[t]&&e.eventsListeners[t].forEach((e=>{e.apply(a,n)}))})),e}};const O=(e,t,n)=>{t&&!e.classList.contains(n)?e.classList.add(n):!t&&e.classList.contains(n)&&e.classList.remove(n)},A=(e,t,n)=>{t&&!e.classList.contains(n)?e.classList.add(n):!t&&e.classList.contains(n)&&e.classList.remove(n)},I=(e,t)=>{if(!e||e.destroyed||!e.params)return;const n=t.closest(e.isElement?"swiper-slide":`.${e.params.slideClass}`);if(n){let t=n.querySelector(`.${e.params.lazyPreloaderClass}`);!t&&e.isElement&&(n.shadowRoot?t=n.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`):requestAnimationFrame((()=>{n.shadowRoot&&(t=n.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`),t&&t.remove())}))),t&&t.remove()}},L=(e,t)=>{if(!e.slides[t])return;const n=e.slides[t].querySelector('[loading="lazy"]');n&&n.removeAttribute("loading")},z=e=>{if(!e||e.destroyed||!e.params)return;let t=e.params.lazyPreloadPrevNext;const n=e.slides.length;if(!n||!t||t<0)return;t=Math.min(t,n);const a="auto"===e.params.slidesPerView?e.slidesPerViewDynamic():Math.ceil(e.params.slidesPerView),i=e.activeIndex;if(e.params.grid&&e.params.grid.rows>1){const n=i,r=[n-t];return r.push(...Array.from({length:t}).map(((e,t)=>n+a+t))),void e.slides.forEach(((t,n)=>{r.includes(t.column)&&L(e,n)}))}const r=i+a-1;if(e.params.rewind||e.params.loop)for(let a=i-t;a<=r+t;a+=1){const t=(a%n+n)%n;(t<i||t>r)&&L(e,t)}else for(let a=Math.max(i-t,0);a<=Math.min(r+t,n-1);a+=1)a!==i&&(a>r||a<i)&&L(e,a)};var N={updateSize:function(){const e=this;let t,n;const a=e.el;t=void 0!==e.params.width&&null!==e.params.width?e.params.width:a.clientWidth,n=void 0!==e.params.height&&null!==e.params.height?e.params.height:a.clientHeight,0===t&&e.isHorizontal()||0===n&&e.isVertical()||(t=t-parseInt(b(a,"padding-left")||0,10)-parseInt(b(a,"padding-right")||0,10),n=n-parseInt(b(a,"padding-top")||0,10)-parseInt(b(a,"padding-bottom")||0,10),Number.isNaN(t)&&(t=0),Number.isNaN(n)&&(n=0),Object.assign(e,{width:t,height:n,size:e.isHorizontal()?t:n}))},updateSlides:function(){const e=this;function t(t,n){return parseFloat(t.getPropertyValue(e.getDirectionLabel(n))||0)}const n=e.params,{wrapperEl:a,slidesEl:i,size:r,rtlTranslate:s,wrongRTL:o}=e,l=e.virtual&&n.virtual.enabled,c=l?e.virtual.slides.length:e.slides.length,d=h(i,`.${e.params.slideClass}, swiper-slide`),u=l?e.virtual.slides.length:d.length;let f=[];const m=[],v=[];let g=n.slidesOffsetBefore;"function"==typeof g&&(g=n.slidesOffsetBefore.call(e));let y=n.slidesOffsetAfter;"function"==typeof y&&(y=n.slidesOffsetAfter.call(e));const w=e.snapGrid.length,S=e.slidesGrid.length;let k=n.spaceBetween,T=-g,E=0,C=0;if(void 0===r)return;"string"==typeof k&&k.indexOf("%")>=0?k=parseFloat(k.replace("%",""))/100*r:"string"==typeof k&&(k=parseFloat(k)),e.virtualSize=-k,d.forEach((e=>{s?e.style.marginLeft="":e.style.marginRight="",e.style.marginBottom="",e.style.marginTop=""})),n.centeredSlides&&n.cssMode&&(p(a,"--swiper-centered-offset-before",""),p(a,"--swiper-centered-offset-after",""));const P=n.grid&&n.grid.rows>1&&e.grid;let M;P?e.grid.initSlides(d):e.grid&&e.grid.unsetSlides();const O="auto"===n.slidesPerView&&n.breakpoints&&Object.keys(n.breakpoints).filter((e=>void 0!==n.breakpoints[e].slidesPerView)).length>0;for(let a=0;a<u;a+=1){let i;if(M=0,d[a]&&(i=d[a]),P&&e.grid.updateSlide(a,i,d),!d[a]||"none"!==b(i,"display")){if("auto"===n.slidesPerView){O&&(d[a].style[e.getDirectionLabel("width")]="");const r=getComputedStyle(i),s=i.style.transform,o=i.style.webkitTransform;if(s&&(i.style.transform="none"),o&&(i.style.webkitTransform="none"),n.roundLengths)M=e.isHorizontal()?x(i,"width",!0):x(i,"height",!0);else{const e=t(r,"width"),n=t(r,"padding-left"),a=t(r,"padding-right"),s=t(r,"margin-left"),o=t(r,"margin-right"),l=r.getPropertyValue("box-sizing");if(l&&"border-box"===l)M=e+s+o;else{const{clientWidth:t,offsetWidth:r}=i;M=e+n+a+s+o+(r-t)}}s&&(i.style.transform=s),o&&(i.style.webkitTransform=o),n.roundLengths&&(M=Math.floor(M))}else M=(r-(n.slidesPerView-1)*k)/n.slidesPerView,n.roundLengths&&(M=Math.floor(M)),d[a]&&(d[a].style[e.getDirectionLabel("width")]=`${M}px`);d[a]&&(d[a].swiperSlideSize=M),v.push(M),n.centeredSlides?(T=T+M/2+E/2+k,0===E&&0!==a&&(T=T-r/2-k),0===a&&(T=T-r/2-k),Math.abs(T)<.001&&(T=0),n.roundLengths&&(T=Math.floor(T)),C%n.slidesPerGroup==0&&f.push(T),m.push(T)):(n.roundLengths&&(T=Math.floor(T)),(C-Math.min(e.params.slidesPerGroupSkip,C))%e.params.slidesPerGroup==0&&f.push(T),m.push(T),T=T+M+k),e.virtualSize+=M+k,E=M,C+=1}}if(e.virtualSize=Math.max(e.virtualSize,r)+y,s&&o&&("slide"===n.effect||"coverflow"===n.effect)&&(a.style.width=`${e.virtualSize+k}px`),n.setWrapperSize&&(a.style[e.getDirectionLabel("width")]=`${e.virtualSize+k}px`),P&&e.grid.updateWrapperSize(M,f),!n.centeredSlides){const t=[];for(let a=0;a<f.length;a+=1){let i=f[a];n.roundLengths&&(i=Math.floor(i)),f[a]<=e.virtualSize-r&&t.push(i)}f=t,Math.floor(e.virtualSize-r)-Math.floor(f[f.length-1])>1&&f.push(e.virtualSize-r)}if(l&&n.loop){const t=v[0]+k;if(n.slidesPerGroup>1){const a=Math.ceil((e.virtual.slidesBefore+e.virtual.slidesAfter)/n.slidesPerGroup),i=t*n.slidesPerGroup;for(let e=0;e<a;e+=1)f.push(f[f.length-1]+i)}for(let a=0;a<e.virtual.slidesBefore+e.virtual.slidesAfter;a+=1)1===n.slidesPerGroup&&f.push(f[f.length-1]+t),m.push(m[m.length-1]+t),e.virtualSize+=t}if(0===f.length&&(f=[0]),0!==k){const t=e.isHorizontal()&&s?"marginLeft":e.getDirectionLabel("marginRight");d.filter(((e,t)=>!(n.cssMode&&!n.loop)||t!==d.length-1)).forEach((e=>{e.style[t]=`${k}px`}))}if(n.centeredSlides&&n.centeredSlidesBounds){let e=0;v.forEach((t=>{e+=t+(k||0)})),e-=k;const t=e-r;f=f.map((e=>e<=0?-g:e>t?t+y:e))}if(n.centerInsufficientSlides){let e=0;v.forEach((t=>{e+=t+(k||0)})),e-=k;const t=(n.slidesOffsetBefore||0)+(n.slidesOffsetAfter||0);if(e+t<r){const n=(r-e-t)/2;f.forEach(((e,t)=>{f[t]=e-n})),m.forEach(((e,t)=>{m[t]=e+n}))}}if(Object.assign(e,{slides:d,snapGrid:f,slidesGrid:m,slidesSizesGrid:v}),n.centeredSlides&&n.cssMode&&!n.centeredSlidesBounds){p(a,"--swiper-centered-offset-before",-f[0]+"px"),p(a,"--swiper-centered-offset-after",e.size/2-v[v.length-1]/2+"px");const t=-e.snapGrid[0],n=-e.slidesGrid[0];e.snapGrid=e.snapGrid.map((e=>e+t)),e.slidesGrid=e.slidesGrid.map((e=>e+n))}if(u!==c&&e.emit("slidesLengthChange"),f.length!==w&&(e.params.watchOverflow&&e.checkOverflow(),e.emit("snapGridLengthChange")),m.length!==S&&e.emit("slidesGridLengthChange"),n.watchSlidesProgress&&e.updateSlidesOffset(),e.emit("slidesUpdated"),!(l||n.cssMode||"slide"!==n.effect&&"fade"!==n.effect)){const t=`${n.containerModifierClass}backface-hidden`,a=e.el.classList.contains(t);u<=n.maxBackfaceHiddenSlides?a||e.el.classList.add(t):a&&e.el.classList.remove(t)}},updateAutoHeight:function(e){const t=this,n=[],a=t.virtual&&t.params.virtual.enabled;let i,r=0;"number"==typeof e?t.setTransition(e):!0===e&&t.setTransition(t.params.speed);const s=e=>a?t.slides[t.getSlideIndexByData(e)]:t.slides[e];if("auto"!==t.params.slidesPerView&&t.params.slidesPerView>1)if(t.params.centeredSlides)(t.visibleSlides||[]).forEach((e=>{n.push(e)}));else for(i=0;i<Math.ceil(t.params.slidesPerView);i+=1){const e=t.activeIndex+i;if(e>t.slides.length&&!a)break;n.push(s(e))}else n.push(s(t.activeIndex));for(i=0;i<n.length;i+=1)if(void 0!==n[i]){const e=n[i].offsetHeight;r=e>r?e:r}(r||0===r)&&(t.wrapperEl.style.height=`${r}px`)},updateSlidesOffset:function(){const e=this,t=e.slides,n=e.isElement?e.isHorizontal()?e.wrapperEl.offsetLeft:e.wrapperEl.offsetTop:0;for(let a=0;a<t.length;a+=1)t[a].swiperSlideOffset=(e.isHorizontal()?t[a].offsetLeft:t[a].offsetTop)-n-e.cssOverflowAdjustment()},updateSlidesProgress:function(e){void 0===e&&(e=this&&this.translate||0);const t=this,n=t.params,{slides:a,rtlTranslate:i,snapGrid:r}=t;if(0===a.length)return;void 0===a[0].swiperSlideOffset&&t.updateSlidesOffset();let s=-e;i&&(s=e),t.visibleSlidesIndexes=[],t.visibleSlides=[];let o=n.spaceBetween;"string"==typeof o&&o.indexOf("%")>=0?o=parseFloat(o.replace("%",""))/100*t.size:"string"==typeof o&&(o=parseFloat(o));for(let e=0;e<a.length;e+=1){const l=a[e];let c=l.swiperSlideOffset;n.cssMode&&n.centeredSlides&&(c-=a[0].swiperSlideOffset);const d=(s+(n.centeredSlides?t.minTranslate():0)-c)/(l.swiperSlideSize+o),u=(s-r[0]+(n.centeredSlides?t.minTranslate():0)-c)/(l.swiperSlideSize+o),f=-(s-c),p=f+t.slidesSizesGrid[e],m=f>=0&&f<=t.size-t.slidesSizesGrid[e],h=f>=0&&f<t.size-1||p>1&&p<=t.size||f<=0&&p>=t.size;h&&(t.visibleSlides.push(l),t.visibleSlidesIndexes.push(e)),O(l,h,n.slideVisibleClass),O(l,m,n.slideFullyVisibleClass),l.progress=i?-d:d,l.originalProgress=i?-u:u}},updateProgress:function(e){const t=this;if(void 0===e){const n=t.rtlTranslate?-1:1;e=t&&t.translate&&t.translate*n||0}const n=t.params,a=t.maxTranslate()-t.minTranslate();let{progress:i,isBeginning:r,isEnd:s,progressLoop:o}=t;const l=r,c=s;if(0===a)i=0,r=!0,s=!0;else{i=(e-t.minTranslate())/a;const n=Math.abs(e-t.minTranslate())<1,o=Math.abs(e-t.maxTranslate())<1;r=n||i<=0,s=o||i>=1,n&&(i=0),o&&(i=1)}if(n.loop){const n=t.getSlideIndexByData(0),a=t.getSlideIndexByData(t.slides.length-1),i=t.slidesGrid[n],r=t.slidesGrid[a],s=t.slidesGrid[t.slidesGrid.length-1],l=Math.abs(e);o=l>=i?(l-i)/s:(l+s-r)/s,o>1&&(o-=1)}Object.assign(t,{progress:i,progressLoop:o,isBeginning:r,isEnd:s}),(n.watchSlidesProgress||n.centeredSlides&&n.autoHeight)&&t.updateSlidesProgress(e),r&&!l&&t.emit("reachBeginning toEdge"),s&&!c&&t.emit("reachEnd toEdge"),(l&&!r||c&&!s)&&t.emit("fromEdge"),t.emit("progress",i)},updateSlidesClasses:function(){const e=this,{slides:t,params:n,slidesEl:a,activeIndex:i}=e,r=e.virtual&&n.virtual.enabled,s=e.grid&&n.grid&&n.grid.rows>1,o=e=>h(a,`.${n.slideClass}${e}, swiper-slide${e}`)[0];let l,c,d;if(r)if(n.loop){let t=i-e.virtual.slidesBefore;t<0&&(t=e.virtual.slides.length+t),t>=e.virtual.slides.length&&(t-=e.virtual.slides.length),l=o(`[data-swiper-slide-index="${t}"]`)}else l=o(`[data-swiper-slide-index="${i}"]`);else s?(l=t.filter((e=>e.column===i))[0],d=t.filter((e=>e.column===i+1))[0],c=t.filter((e=>e.column===i-1))[0]):l=t[i];l&&(s||(d=function(e,t){const n=[];for(;e.nextElementSibling;){const a=e.nextElementSibling;t?a.matches(t)&&n.push(a):n.push(a),e=a}return n}(l,`.${n.slideClass}, swiper-slide`)[0],n.loop&&!d&&(d=t[0]),c=function(e,t){const n=[];for(;e.previousElementSibling;){const a=e.previousElementSibling;t?a.matches(t)&&n.push(a):n.push(a),e=a}return n}(l,`.${n.slideClass}, swiper-slide`)[0],n.loop&&0===!c&&(c=t[t.length-1]))),t.forEach((e=>{A(e,e===l,n.slideActiveClass),A(e,e===d,n.slideNextClass),A(e,e===c,n.slidePrevClass)})),e.emitSlidesClasses()},updateActiveIndex:function(e){const t=this,n=t.rtlTranslate?t.translate:-t.translate,{snapGrid:a,params:i,activeIndex:r,realIndex:s,snapIndex:o}=t;let l,c=e;const d=e=>{let n=e-t.virtual.slidesBefore;return n<0&&(n=t.virtual.slides.length+n),n>=t.virtual.slides.length&&(n-=t.virtual.slides.length),n};if(void 0===c&&(c=function(e){const{slidesGrid:t,params:n}=e,a=e.rtlTranslate?e.translate:-e.translate;let i;for(let e=0;e<t.length;e+=1)void 0!==t[e+1]?a>=t[e]&&a<t[e+1]-(t[e+1]-t[e])/2?i=e:a>=t[e]&&a<t[e+1]&&(i=e+1):a>=t[e]&&(i=e);return n.normalizeSlideIndex&&(i<0||void 0===i)&&(i=0),i}(t)),a.indexOf(n)>=0)l=a.indexOf(n);else{const e=Math.min(i.slidesPerGroupSkip,c);l=e+Math.floor((c-e)/i.slidesPerGroup)}if(l>=a.length&&(l=a.length-1),c===r&&!t.params.loop)return void(l!==o&&(t.snapIndex=l,t.emit("snapIndexChange")));if(c===r&&t.params.loop&&t.virtual&&t.params.virtual.enabled)return void(t.realIndex=d(c));const u=t.grid&&i.grid&&i.grid.rows>1;let f;if(t.virtual&&i.virtual.enabled&&i.loop)f=d(c);else if(u){const e=t.slides.filter((e=>e.column===c))[0];let n=parseInt(e.getAttribute("data-swiper-slide-index"),10);Number.isNaN(n)&&(n=Math.max(t.slides.indexOf(e),0)),f=Math.floor(n/i.grid.rows)}else if(t.slides[c]){const e=t.slides[c].getAttribute("data-swiper-slide-index");f=e?parseInt(e,10):c}else f=c;Object.assign(t,{previousSnapIndex:o,snapIndex:l,previousRealIndex:s,realIndex:f,previousIndex:r,activeIndex:c}),t.initialized&&z(t),t.emit("activeIndexChange"),t.emit("snapIndexChange"),(t.initialized||t.params.runCallbacksOnInit)&&(s!==f&&t.emit("realIndexChange"),t.emit("slideChange"))},updateClickedSlide:function(e,t){const n=this,a=n.params;let i=e.closest(`.${a.slideClass}, swiper-slide`);!i&&n.isElement&&t&&t.length>1&&t.includes(e)&&[...t.slice(t.indexOf(e)+1,t.length)].forEach((e=>{!i&&e.matches&&e.matches(`.${a.slideClass}, swiper-slide`)&&(i=e)}));let r,s=!1;if(i)for(let e=0;e<n.slides.length;e+=1)if(n.slides[e]===i){s=!0,r=e;break}if(!i||!s)return n.clickedSlide=void 0,void(n.clickedIndex=void 0);n.clickedSlide=i,n.virtual&&n.params.virtual.enabled?n.clickedIndex=parseInt(i.getAttribute("data-swiper-slide-index"),10):n.clickedIndex=r,a.slideToClickedSlide&&void 0!==n.clickedIndex&&n.clickedIndex!==n.activeIndex&&n.slideToClickedSlide()}};function j(e){let{swiper:t,runCallbacks:n,direction:a,step:i}=e;const{activeIndex:r,previousIndex:s}=t;let o=a;if(o||(o=r>s?"next":r<s?"prev":"reset"),t.emit(`transition${i}`),n&&r!==s){if("reset"===o)return void t.emit(`slideResetTransition${i}`);t.emit(`slideChangeTransition${i}`),"next"===o?t.emit(`slideNextTransition${i}`):t.emit(`slidePrevTransition${i}`)}}var D={slideTo:function(e,t,n,a,i){void 0===e&&(e=0),void 0===n&&(n=!0),"string"==typeof e&&(e=parseInt(e,10));const r=this;let s=e;s<0&&(s=0);const{params:o,snapGrid:l,slidesGrid:c,previousIndex:d,activeIndex:u,rtlTranslate:f,wrapperEl:p,enabled:h}=r;if(!h&&!a&&!i||r.destroyed||r.animating&&o.preventInteractionOnTransition)return!1;void 0===t&&(t=r.params.speed);const v=Math.min(r.params.slidesPerGroupSkip,s);let g=v+Math.floor((s-v)/r.params.slidesPerGroup);g>=l.length&&(g=l.length-1);const b=-l[g];if(o.normalizeSlideIndex)for(let e=0;e<c.length;e+=1){const t=-Math.floor(100*b),n=Math.floor(100*c[e]),a=Math.floor(100*c[e+1]);void 0!==c[e+1]?t>=n&&t<a-(a-n)/2?s=e:t>=n&&t<a&&(s=e+1):t>=n&&(s=e)}if(r.initialized&&s!==u){if(!r.allowSlideNext&&(f?b>r.translate&&b>r.minTranslate():b<r.translate&&b<r.minTranslate()))return!1;if(!r.allowSlidePrev&&b>r.translate&&b>r.maxTranslate()&&(u||0)!==s)return!1}let y;if(s!==(d||0)&&n&&r.emit("beforeSlideChangeStart"),r.updateProgress(b),y=s>u?"next":s<u?"prev":"reset",f&&-b===r.translate||!f&&b===r.translate)return r.updateActiveIndex(s),o.autoHeight&&r.updateAutoHeight(),r.updateSlidesClasses(),"slide"!==o.effect&&r.setTranslate(b),"reset"!==y&&(r.transitionStart(n,y),r.transitionEnd(n,y)),!1;if(o.cssMode){const e=r.isHorizontal(),n=f?b:-b;if(0===t){const t=r.virtual&&r.params.virtual.enabled;t&&(r.wrapperEl.style.scrollSnapType="none",r._immediateVirtual=!0),t&&!r._cssModeVirtualInitialSet&&r.params.initialSlide>0?(r._cssModeVirtualInitialSet=!0,requestAnimationFrame((()=>{p[e?"scrollLeft":"scrollTop"]=n}))):p[e?"scrollLeft":"scrollTop"]=n,t&&requestAnimationFrame((()=>{r.wrapperEl.style.scrollSnapType="",r._immediateVirtual=!1}))}else{if(!r.support.smoothScroll)return m({swiper:r,targetPosition:n,side:e?"left":"top"}),!0;p.scrollTo({[e?"left":"top"]:n,behavior:"smooth"})}return!0}return r.setTransition(t),r.setTranslate(b),r.updateActiveIndex(s),r.updateSlidesClasses(),r.emit("beforeTransitionStart",t,a),r.transitionStart(n,y),0===t?r.transitionEnd(n,y):r.animating||(r.animating=!0,r.onSlideToWrapperTransitionEnd||(r.onSlideToWrapperTransitionEnd=function(e){r&&!r.destroyed&&e.target===this&&(r.wrapperEl.removeEventListener("transitionend",r.onSlideToWrapperTransitionEnd),r.onSlideToWrapperTransitionEnd=null,delete r.onSlideToWrapperTransitionEnd,r.transitionEnd(n,y))}),r.wrapperEl.addEventListener("transitionend",r.onSlideToWrapperTransitionEnd)),!0},slideToLoop:function(e,t,n,a){void 0===e&&(e=0),void 0===n&&(n=!0),"string"==typeof e&&(e=parseInt(e,10));const i=this;if(i.destroyed)return;void 0===t&&(t=i.params.speed);const r=i.grid&&i.params.grid&&i.params.grid.rows>1;let s=e;if(i.params.loop)if(i.virtual&&i.params.virtual.enabled)s+=i.virtual.slidesBefore;else{let e;if(r){const t=s*i.params.grid.rows;e=i.slides.filter((e=>1*e.getAttribute("data-swiper-slide-index")===t))[0].column}else e=i.getSlideIndexByData(s);const t=r?Math.ceil(i.slides.length/i.params.grid.rows):i.slides.length,{centeredSlides:n}=i.params;let o=i.params.slidesPerView;"auto"===o?o=i.slidesPerViewDynamic():(o=Math.ceil(parseFloat(i.params.slidesPerView,10)),n&&o%2==0&&(o+=1));let l=t-e<o;if(n&&(l=l||e<Math.ceil(o/2)),a&&n&&"auto"!==i.params.slidesPerView&&!r&&(l=!1),l){const a=n?e<i.activeIndex?"prev":"next":e-i.activeIndex-1<i.params.slidesPerView?"next":"prev";i.loopFix({direction:a,slideTo:!0,activeSlideIndex:"next"===a?e+1:e-t+1,slideRealIndex:"next"===a?i.realIndex:void 0})}if(r){const e=s*i.params.grid.rows;s=i.slides.filter((t=>1*t.getAttribute("data-swiper-slide-index")===e))[0].column}else s=i.getSlideIndexByData(s)}return requestAnimationFrame((()=>{i.slideTo(s,t,n,a)})),i},slideNext:function(e,t,n){void 0===t&&(t=!0);const a=this,{enabled:i,params:r,animating:s}=a;if(!i||a.destroyed)return a;void 0===e&&(e=a.params.speed);let o=r.slidesPerGroup;"auto"===r.slidesPerView&&1===r.slidesPerGroup&&r.slidesPerGroupAuto&&(o=Math.max(a.slidesPerViewDynamic("current",!0),1));const l=a.activeIndex<r.slidesPerGroupSkip?1:o,c=a.virtual&&r.virtual.enabled;if(r.loop){if(s&&!c&&r.loopPreventsSliding)return!1;if(a.loopFix({direction:"next"}),a._clientLeft=a.wrapperEl.clientLeft,a.activeIndex===a.slides.length-1&&r.cssMode)return requestAnimationFrame((()=>{a.slideTo(a.activeIndex+l,e,t,n)})),!0}return r.rewind&&a.isEnd?a.slideTo(0,e,t,n):a.slideTo(a.activeIndex+l,e,t,n)},slidePrev:function(e,t,n){void 0===t&&(t=!0);const a=this,{params:i,snapGrid:r,slidesGrid:s,rtlTranslate:o,enabled:l,animating:c}=a;if(!l||a.destroyed)return a;void 0===e&&(e=a.params.speed);const d=a.virtual&&i.virtual.enabled;if(i.loop){if(c&&!d&&i.loopPreventsSliding)return!1;a.loopFix({direction:"prev"}),a._clientLeft=a.wrapperEl.clientLeft}function u(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}const f=u(o?a.translate:-a.translate),p=r.map((e=>u(e)));let m=r[p.indexOf(f)-1];if(void 0===m&&i.cssMode){let e;r.forEach(((t,n)=>{f>=t&&(e=n)})),void 0!==e&&(m=r[e>0?e-1:e])}let h=0;if(void 0!==m&&(h=s.indexOf(m),h<0&&(h=a.activeIndex-1),"auto"===i.slidesPerView&&1===i.slidesPerGroup&&i.slidesPerGroupAuto&&(h=h-a.slidesPerViewDynamic("previous",!0)+1,h=Math.max(h,0))),i.rewind&&a.isBeginning){const i=a.params.virtual&&a.params.virtual.enabled&&a.virtual?a.virtual.slides.length-1:a.slides.length-1;return a.slideTo(i,e,t,n)}return i.loop&&0===a.activeIndex&&i.cssMode?(requestAnimationFrame((()=>{a.slideTo(h,e,t,n)})),!0):a.slideTo(h,e,t,n)},slideReset:function(e,t,n){void 0===t&&(t=!0);const a=this;if(!a.destroyed)return void 0===e&&(e=a.params.speed),a.slideTo(a.activeIndex,e,t,n)},slideToClosest:function(e,t,n,a){void 0===t&&(t=!0),void 0===a&&(a=.5);const i=this;if(i.destroyed)return;void 0===e&&(e=i.params.speed);let r=i.activeIndex;const s=Math.min(i.params.slidesPerGroupSkip,r),o=s+Math.floor((r-s)/i.params.slidesPerGroup),l=i.rtlTranslate?i.translate:-i.translate;if(l>=i.snapGrid[o]){const e=i.snapGrid[o];l-e>(i.snapGrid[o+1]-e)*a&&(r+=i.params.slidesPerGroup)}else{const e=i.snapGrid[o-1];l-e<=(i.snapGrid[o]-e)*a&&(r-=i.params.slidesPerGroup)}return r=Math.max(r,0),r=Math.min(r,i.slidesGrid.length-1),i.slideTo(r,e,t,n)},slideToClickedSlide:function(){const e=this;if(e.destroyed)return;const{params:t,slidesEl:n}=e,a="auto"===t.slidesPerView?e.slidesPerViewDynamic():t.slidesPerView;let i,r=e.clickedIndex;const s=e.isElement?"swiper-slide":`.${t.slideClass}`;if(t.loop){if(e.animating)return;i=parseInt(e.clickedSlide.getAttribute("data-swiper-slide-index"),10),t.centeredSlides?r<e.loopedSlides-a/2||r>e.slides.length-e.loopedSlides+a/2?(e.loopFix(),r=e.getSlideIndex(h(n,`${s}[data-swiper-slide-index="${i}"]`)[0]),c((()=>{e.slideTo(r)}))):e.slideTo(r):r>e.slides.length-a?(e.loopFix(),r=e.getSlideIndex(h(n,`${s}[data-swiper-slide-index="${i}"]`)[0]),c((()=>{e.slideTo(r)}))):e.slideTo(r)}else e.slideTo(r)}},G={loopCreate:function(e){const t=this,{params:n,slidesEl:a}=t;if(!n.loop||t.virtual&&t.params.virtual.enabled)return;const i=()=>{h(a,`.${n.slideClass}, swiper-slide`).forEach(((e,t)=>{e.setAttribute("data-swiper-slide-index",t)}))},r=t.grid&&n.grid&&n.grid.rows>1,s=n.slidesPerGroup*(r?n.grid.rows:1),o=t.slides.length%s!=0,l=r&&t.slides.length%n.grid.rows!=0,c=e=>{for(let a=0;a<e;a+=1){const e=t.isElement?g("swiper-slide",[n.slideBlankClass]):g("div",[n.slideClass,n.slideBlankClass]);t.slidesEl.append(e)}};o?(n.loopAddBlankSlides?(c(s-t.slides.length%s),t.recalcSlides(),t.updateSlides()):v("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)"),i()):l?(n.loopAddBlankSlides?(c(n.grid.rows-t.slides.length%n.grid.rows),t.recalcSlides(),t.updateSlides()):v("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)"),i()):i(),t.loopFix({slideRealIndex:e,direction:n.centeredSlides?void 0:"next"})},loopFix:function(e){let{slideRealIndex:t,slideTo:n=!0,direction:a,setTranslate:i,activeSlideIndex:r,byController:s,byMousewheel:o}=void 0===e?{}:e;const l=this;if(!l.params.loop)return;l.emit("beforeLoopFix");const{slides:c,allowSlidePrev:d,allowSlideNext:u,slidesEl:f,params:p}=l,{centeredSlides:m}=p;if(l.allowSlidePrev=!0,l.allowSlideNext=!0,l.virtual&&p.virtual.enabled)return n&&(p.centeredSlides||0!==l.snapIndex?p.centeredSlides&&l.snapIndex<p.slidesPerView?l.slideTo(l.virtual.slides.length+l.snapIndex,0,!1,!0):l.snapIndex===l.snapGrid.length-1&&l.slideTo(l.virtual.slidesBefore,0,!1,!0):l.slideTo(l.virtual.slides.length,0,!1,!0)),l.allowSlidePrev=d,l.allowSlideNext=u,void l.emit("loopFix");let h=p.slidesPerView;"auto"===h?h=l.slidesPerViewDynamic():(h=Math.ceil(parseFloat(p.slidesPerView,10)),m&&h%2==0&&(h+=1));const g=p.slidesPerGroupAuto?h:p.slidesPerGroup;let b=g;b%g!=0&&(b+=g-b%g),b+=p.loopAdditionalSlides,l.loopedSlides=b;const y=l.grid&&p.grid&&p.grid.rows>1;c.length<h+b?v("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled and not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):y&&"row"===p.grid.fill&&v("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");const w=[],x=[];let S=l.activeIndex;void 0===r?r=l.getSlideIndex(c.filter((e=>e.classList.contains(p.slideActiveClass)))[0]):S=r;const k="next"===a||!a,T="prev"===a||!a;let E=0,C=0;const P=y?Math.ceil(c.length/p.grid.rows):c.length,M=(y?c[r].column:r)+(m&&void 0===i?-h/2+.5:0);if(M<b){E=Math.max(b-M,g);for(let e=0;e<b-M;e+=1){const t=e-Math.floor(e/P)*P;if(y){const e=P-t-1;for(let t=c.length-1;t>=0;t-=1)c[t].column===e&&w.push(t)}else w.push(P-t-1)}}else if(M+h>P-b){C=Math.max(M-(P-2*b),g);for(let e=0;e<C;e+=1){const t=e-Math.floor(e/P)*P;y?c.forEach(((e,n)=>{e.column===t&&x.push(n)})):x.push(t)}}if(l.__preventObserver__=!0,requestAnimationFrame((()=>{l.__preventObserver__=!1})),T&&w.forEach((e=>{c[e].swiperLoopMoveDOM=!0,f.prepend(c[e]),c[e].swiperLoopMoveDOM=!1})),k&&x.forEach((e=>{c[e].swiperLoopMoveDOM=!0,f.append(c[e]),c[e].swiperLoopMoveDOM=!1})),l.recalcSlides(),"auto"===p.slidesPerView?l.updateSlides():y&&(w.length>0&&T||x.length>0&&k)&&l.slides.forEach(((e,t)=>{l.grid.updateSlide(t,e,l.slides)})),p.watchSlidesProgress&&l.updateSlidesOffset(),n)if(w.length>0&&T){if(void 0===t){const e=l.slidesGrid[S],t=l.slidesGrid[S+E]-e;o?l.setTranslate(l.translate-t):(l.slideTo(S+Math.ceil(E),0,!1,!0),i&&(l.touchEventsData.startTranslate=l.touchEventsData.startTranslate-t,l.touchEventsData.currentTranslate=l.touchEventsData.currentTranslate-t))}else if(i){const e=y?w.length/p.grid.rows:w.length;l.slideTo(l.activeIndex+e,0,!1,!0),l.touchEventsData.currentTranslate=l.translate}}else if(x.length>0&&k)if(void 0===t){const e=l.slidesGrid[S],t=l.slidesGrid[S-C]-e;o?l.setTranslate(l.translate-t):(l.slideTo(S-C,0,!1,!0),i&&(l.touchEventsData.startTranslate=l.touchEventsData.startTranslate-t,l.touchEventsData.currentTranslate=l.touchEventsData.currentTranslate-t))}else{const e=y?x.length/p.grid.rows:x.length;l.slideTo(l.activeIndex-e,0,!1,!0)}if(l.allowSlidePrev=d,l.allowSlideNext=u,l.controller&&l.controller.control&&!s){const e={slideRealIndex:t,direction:a,setTranslate:i,activeSlideIndex:r,byController:!0};Array.isArray(l.controller.control)?l.controller.control.forEach((t=>{!t.destroyed&&t.params.loop&&t.loopFix({...e,slideTo:t.params.slidesPerView===p.slidesPerView&&n})})):l.controller.control instanceof l.constructor&&l.controller.control.params.loop&&l.controller.control.loopFix({...e,slideTo:l.controller.control.params.slidesPerView===p.slidesPerView&&n})}l.emit("loopFix")},loopDestroy:function(){const e=this,{params:t,slidesEl:n}=e;if(!t.loop||e.virtual&&e.params.virtual.enabled)return;e.recalcSlides();const a=[];e.slides.forEach((e=>{const t=void 0===e.swiperSlideIndex?1*e.getAttribute("data-swiper-slide-index"):e.swiperSlideIndex;a[t]=e})),e.slides.forEach((e=>{e.removeAttribute("data-swiper-slide-index")})),a.forEach((e=>{n.append(e)})),e.recalcSlides(),e.slideTo(e.realIndex,0)}};function _(e,t,n){const a=l(),{params:i}=e,r=i.edgeSwipeDetection,s=i.edgeSwipeThreshold;return!r||!(n<=s||n>=a.innerWidth-s)||"prevent"===r&&(t.preventDefault(),!0)}function F(e){const t=this,n=s();let a=e;a.originalEvent&&(a=a.originalEvent);const i=t.touchEventsData;if("pointerdown"===a.type){if(null!==i.pointerId&&i.pointerId!==a.pointerId)return;i.pointerId=a.pointerId}else"touchstart"===a.type&&1===a.targetTouches.length&&(i.touchId=a.targetTouches[0].identifier);if("touchstart"===a.type)return void _(t,a,a.targetTouches[0].pageX);const{params:r,touches:o,enabled:c}=t;if(!c)return;if(!r.simulateTouch&&"mouse"===a.pointerType)return;if(t.animating&&r.preventInteractionOnTransition)return;!t.animating&&r.cssMode&&r.loop&&t.loopFix();let u=a.target;if("wrapper"===r.touchEventsTarget&&!t.wrapperEl.contains(u))return;if("which"in a&&3===a.which)return;if("button"in a&&a.button>0)return;if(i.isTouched&&i.isMoved)return;const f=!!r.noSwipingClass&&""!==r.noSwipingClass,p=a.composedPath?a.composedPath():a.path;f&&a.target&&a.target.shadowRoot&&p&&(u=p[0]);const m=r.noSwipingSelector?r.noSwipingSelector:`.${r.noSwipingClass}`,h=!(!a.target||!a.target.shadowRoot);if(r.noSwiping&&(h?function(e,t){return void 0===t&&(t=this),function t(n){if(!n||n===s()||n===l())return null;n.assignedSlot&&(n=n.assignedSlot);const a=n.closest(e);return a||n.getRootNode?a||t(n.getRootNode().host):null}(t)}(m,u):u.closest(m)))return void(t.allowClick=!0);if(r.swipeHandler&&!u.closest(r.swipeHandler))return;o.currentX=a.pageX,o.currentY=a.pageY;const v=o.currentX,g=o.currentY;if(!_(t,a,v))return;Object.assign(i,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),o.startX=v,o.startY=g,i.touchStartTime=d(),t.allowClick=!0,t.updateSize(),t.swipeDirection=void 0,r.threshold>0&&(i.allowThresholdMove=!1);let b=!0;u.matches(i.focusableElements)&&(b=!1,"SELECT"===u.nodeName&&(i.isTouched=!1)),n.activeElement&&n.activeElement.matches(i.focusableElements)&&n.activeElement!==u&&n.activeElement.blur();const y=b&&t.allowTouchMove&&r.touchStartPreventDefault;!r.touchStartForcePreventDefault&&!y||u.isContentEditable||a.preventDefault(),r.freeMode&&r.freeMode.enabled&&t.freeMode&&t.animating&&!r.cssMode&&t.freeMode.onTouchStart(),t.emit("touchStart",a)}function R(e){const t=s(),n=this,a=n.touchEventsData,{params:i,touches:r,rtlTranslate:o,enabled:l}=n;if(!l)return;if(!i.simulateTouch&&"mouse"===e.pointerType)return;let c,u=e;if(u.originalEvent&&(u=u.originalEvent),"pointermove"===u.type){if(null!==a.touchId)return;if(u.pointerId!==a.pointerId)return}if("touchmove"===u.type){if(c=[...u.changedTouches].filter((e=>e.identifier===a.touchId))[0],!c||c.identifier!==a.touchId)return}else c=u;if(!a.isTouched)return void(a.startMoving&&a.isScrolling&&n.emit("touchMoveOpposite",u));const f=c.pageX,p=c.pageY;if(u.preventedByNestedSwiper)return r.startX=f,void(r.startY=p);if(!n.allowTouchMove)return u.target.matches(a.focusableElements)||(n.allowClick=!1),void(a.isTouched&&(Object.assign(r,{startX:f,startY:p,currentX:f,currentY:p}),a.touchStartTime=d()));if(i.touchReleaseOnEdges&&!i.loop)if(n.isVertical()){if(p<r.startY&&n.translate<=n.maxTranslate()||p>r.startY&&n.translate>=n.minTranslate())return a.isTouched=!1,void(a.isMoved=!1)}else if(f<r.startX&&n.translate<=n.maxTranslate()||f>r.startX&&n.translate>=n.minTranslate())return;if(t.activeElement&&u.target===t.activeElement&&u.target.matches(a.focusableElements))return a.isMoved=!0,void(n.allowClick=!1);a.allowTouchCallbacks&&n.emit("touchMove",u),r.previousX=r.currentX,r.previousY=r.currentY,r.currentX=f,r.currentY=p;const m=r.currentX-r.startX,h=r.currentY-r.startY;if(n.params.threshold&&Math.sqrt(m**2+h**2)<n.params.threshold)return;if(void 0===a.isScrolling){let e;n.isHorizontal()&&r.currentY===r.startY||n.isVertical()&&r.currentX===r.startX?a.isScrolling=!1:m*m+h*h>=25&&(e=180*Math.atan2(Math.abs(h),Math.abs(m))/Math.PI,a.isScrolling=n.isHorizontal()?e>i.touchAngle:90-e>i.touchAngle)}if(a.isScrolling&&n.emit("touchMoveOpposite",u),void 0===a.startMoving&&(r.currentX===r.startX&&r.currentY===r.startY||(a.startMoving=!0)),a.isScrolling||"touchmove"===u.type&&a.preventTouchMoveFromPointerMove)return void(a.isTouched=!1);if(!a.startMoving)return;n.allowClick=!1,!i.cssMode&&u.cancelable&&u.preventDefault(),i.touchMoveStopPropagation&&!i.nested&&u.stopPropagation();let v=n.isHorizontal()?m:h,g=n.isHorizontal()?r.currentX-r.previousX:r.currentY-r.previousY;i.oneWayMovement&&(v=Math.abs(v)*(o?1:-1),g=Math.abs(g)*(o?1:-1)),r.diff=v,v*=i.touchRatio,o&&(v=-v,g=-g);const b=n.touchesDirection;n.swipeDirection=v>0?"prev":"next",n.touchesDirection=g>0?"prev":"next";const y=n.params.loop&&!i.cssMode,w="next"===n.touchesDirection&&n.allowSlideNext||"prev"===n.touchesDirection&&n.allowSlidePrev;if(!a.isMoved){if(y&&w&&n.loopFix({direction:n.swipeDirection}),a.startTranslate=n.getTranslate(),n.setTransition(0),n.animating){const e=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});n.wrapperEl.dispatchEvent(e)}a.allowMomentumBounce=!1,!i.grabCursor||!0!==n.allowSlideNext&&!0!==n.allowSlidePrev||n.setGrabCursor(!0),n.emit("sliderFirstMove",u)}if((new Date).getTime(),a.isMoved&&a.allowThresholdMove&&b!==n.touchesDirection&&y&&w&&Math.abs(v)>=1)return Object.assign(r,{startX:f,startY:p,currentX:f,currentY:p,startTranslate:a.currentTranslate}),a.loopSwapReset=!0,void(a.startTranslate=a.currentTranslate);n.emit("sliderMove",u),a.isMoved=!0,a.currentTranslate=v+a.startTranslate;let x=!0,S=i.resistanceRatio;if(i.touchReleaseOnEdges&&(S=0),v>0?(y&&w&&a.allowThresholdMove&&a.currentTranslate>(i.centeredSlides?n.minTranslate()-n.slidesSizesGrid[n.activeIndex+1]:n.minTranslate())&&n.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),a.currentTranslate>n.minTranslate()&&(x=!1,i.resistance&&(a.currentTranslate=n.minTranslate()-1+(-n.minTranslate()+a.startTranslate+v)**S))):v<0&&(y&&w&&a.allowThresholdMove&&a.currentTranslate<(i.centeredSlides?n.maxTranslate()+n.slidesSizesGrid[n.slidesSizesGrid.length-1]:n.maxTranslate())&&n.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:n.slides.length-("auto"===i.slidesPerView?n.slidesPerViewDynamic():Math.ceil(parseFloat(i.slidesPerView,10)))}),a.currentTranslate<n.maxTranslate()&&(x=!1,i.resistance&&(a.currentTranslate=n.maxTranslate()+1-(n.maxTranslate()-a.startTranslate-v)**S))),x&&(u.preventedByNestedSwiper=!0),!n.allowSlideNext&&"next"===n.swipeDirection&&a.currentTranslate<a.startTranslate&&(a.currentTranslate=a.startTranslate),!n.allowSlidePrev&&"prev"===n.swipeDirection&&a.currentTranslate>a.startTranslate&&(a.currentTranslate=a.startTranslate),n.allowSlidePrev||n.allowSlideNext||(a.currentTranslate=a.startTranslate),i.threshold>0){if(!(Math.abs(v)>i.threshold||a.allowThresholdMove))return void(a.currentTranslate=a.startTranslate);if(!a.allowThresholdMove)return a.allowThresholdMove=!0,r.startX=r.currentX,r.startY=r.currentY,a.currentTranslate=a.startTranslate,void(r.diff=n.isHorizontal()?r.currentX-r.startX:r.currentY-r.startY)}i.followFinger&&!i.cssMode&&((i.freeMode&&i.freeMode.enabled&&n.freeMode||i.watchSlidesProgress)&&(n.updateActiveIndex(),n.updateSlidesClasses()),i.freeMode&&i.freeMode.enabled&&n.freeMode&&n.freeMode.onTouchMove(),n.updateProgress(a.currentTranslate),n.setTranslate(a.currentTranslate))}function $(e){const t=this,n=t.touchEventsData;let a,i=e;if(i.originalEvent&&(i=i.originalEvent),"touchend"===i.type||"touchcancel"===i.type){if(a=[...i.changedTouches].filter((e=>e.identifier===n.touchId))[0],!a||a.identifier!==n.touchId)return}else{if(null!==n.touchId)return;if(i.pointerId!==n.pointerId)return;a=i}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(i.type)&&(!["pointercancel","contextmenu"].includes(i.type)||!t.browser.isSafari&&!t.browser.isWebView))return;n.pointerId=null,n.touchId=null;const{params:r,touches:s,rtlTranslate:o,slidesGrid:l,enabled:u}=t;if(!u)return;if(!r.simulateTouch&&"mouse"===i.pointerType)return;if(n.allowTouchCallbacks&&t.emit("touchEnd",i),n.allowTouchCallbacks=!1,!n.isTouched)return n.isMoved&&r.grabCursor&&t.setGrabCursor(!1),n.isMoved=!1,void(n.startMoving=!1);r.grabCursor&&n.isMoved&&n.isTouched&&(!0===t.allowSlideNext||!0===t.allowSlidePrev)&&t.setGrabCursor(!1);const f=d(),p=f-n.touchStartTime;if(t.allowClick){const e=i.path||i.composedPath&&i.composedPath();t.updateClickedSlide(e&&e[0]||i.target,e),t.emit("tap click",i),p<300&&f-n.lastClickTime<300&&t.emit("doubleTap doubleClick",i)}if(n.lastClickTime=d(),c((()=>{t.destroyed||(t.allowClick=!0)})),!n.isTouched||!n.isMoved||!t.swipeDirection||0===s.diff&&!n.loopSwapReset||n.currentTranslate===n.startTranslate&&!n.loopSwapReset)return n.isTouched=!1,n.isMoved=!1,void(n.startMoving=!1);let m;if(n.isTouched=!1,n.isMoved=!1,n.startMoving=!1,m=r.followFinger?o?t.translate:-t.translate:-n.currentTranslate,r.cssMode)return;if(r.freeMode&&r.freeMode.enabled)return void t.freeMode.onTouchEnd({currentPos:m});const h=m>=-t.maxTranslate()&&!t.params.loop;let v=0,g=t.slidesSizesGrid[0];for(let e=0;e<l.length;e+=e<r.slidesPerGroupSkip?1:r.slidesPerGroup){const t=e<r.slidesPerGroupSkip-1?1:r.slidesPerGroup;void 0!==l[e+t]?(h||m>=l[e]&&m<l[e+t])&&(v=e,g=l[e+t]-l[e]):(h||m>=l[e])&&(v=e,g=l[l.length-1]-l[l.length-2])}let b=null,y=null;r.rewind&&(t.isBeginning?y=r.virtual&&r.virtual.enabled&&t.virtual?t.virtual.slides.length-1:t.slides.length-1:t.isEnd&&(b=0));const w=(m-l[v])/g,x=v<r.slidesPerGroupSkip-1?1:r.slidesPerGroup;if(p>r.longSwipesMs){if(!r.longSwipes)return void t.slideTo(t.activeIndex);"next"===t.swipeDirection&&(w>=r.longSwipesRatio?t.slideTo(r.rewind&&t.isEnd?b:v+x):t.slideTo(v)),"prev"===t.swipeDirection&&(w>1-r.longSwipesRatio?t.slideTo(v+x):null!==y&&w<0&&Math.abs(w)>r.longSwipesRatio?t.slideTo(y):t.slideTo(v))}else{if(!r.shortSwipes)return void t.slideTo(t.activeIndex);!t.navigation||i.target!==t.navigation.nextEl&&i.target!==t.navigation.prevEl?("next"===t.swipeDirection&&t.slideTo(null!==b?b:v+x),"prev"===t.swipeDirection&&t.slideTo(null!==y?y:v)):i.target===t.navigation.nextEl?t.slideTo(v+x):t.slideTo(v)}}function B(){const e=this,{params:t,el:n}=e;if(n&&0===n.offsetWidth)return;t.breakpoints&&e.setBreakpoint();const{allowSlideNext:a,allowSlidePrev:i,snapGrid:r}=e,s=e.virtual&&e.params.virtual.enabled;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses();const o=s&&t.loop;!("auto"===t.slidesPerView||t.slidesPerView>1)||!e.isEnd||e.isBeginning||e.params.centeredSlides||o?e.params.loop&&!s?e.slideToLoop(e.realIndex,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0):e.slideTo(e.slides.length-1,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&(clearTimeout(e.autoplay.resizeTimeout),e.autoplay.resizeTimeout=setTimeout((()=>{e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.resume()}),500)),e.allowSlidePrev=i,e.allowSlideNext=a,e.params.watchOverflow&&r!==e.snapGrid&&e.checkOverflow()}function V(e){const t=this;t.enabled&&(t.allowClick||(t.params.preventClicks&&e.preventDefault(),t.params.preventClicksPropagation&&t.animating&&(e.stopPropagation(),e.stopImmediatePropagation())))}function Y(){const e=this,{wrapperEl:t,rtlTranslate:n,enabled:a}=e;if(!a)return;let i;e.previousTranslate=e.translate,e.isHorizontal()?e.translate=-t.scrollLeft:e.translate=-t.scrollTop,0===e.translate&&(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses();const r=e.maxTranslate()-e.minTranslate();i=0===r?0:(e.translate-e.minTranslate())/r,i!==e.progress&&e.updateProgress(n?-e.translate:e.translate),e.emit("setTranslate",e.translate,!1)}function H(e){const t=this;I(t,e.target),t.params.cssMode||"auto"!==t.params.slidesPerView&&!t.params.autoHeight||t.update()}function W(){const e=this;e.documentTouchHandlerProceeded||(e.documentTouchHandlerProceeded=!0,e.params.touchReleaseOnEdges&&(e.el.style.touchAction="auto"))}const q=(e,t)=>{const n=s(),{params:a,el:i,wrapperEl:r,device:o}=e,l=!!a.nested,c="on"===t?"addEventListener":"removeEventListener",d=t;i&&"string"!=typeof i&&(n[c]("touchstart",e.onDocumentTouchStart,{passive:!1,capture:l}),i[c]("touchstart",e.onTouchStart,{passive:!1}),i[c]("pointerdown",e.onTouchStart,{passive:!1}),n[c]("touchmove",e.onTouchMove,{passive:!1,capture:l}),n[c]("pointermove",e.onTouchMove,{passive:!1,capture:l}),n[c]("touchend",e.onTouchEnd,{passive:!0}),n[c]("pointerup",e.onTouchEnd,{passive:!0}),n[c]("pointercancel",e.onTouchEnd,{passive:!0}),n[c]("touchcancel",e.onTouchEnd,{passive:!0}),n[c]("pointerout",e.onTouchEnd,{passive:!0}),n[c]("pointerleave",e.onTouchEnd,{passive:!0}),n[c]("contextmenu",e.onTouchEnd,{passive:!0}),(a.preventClicks||a.preventClicksPropagation)&&i[c]("click",e.onClick,!0),a.cssMode&&r[c]("scroll",e.onScroll),a.updateOnWindowResize?e[d](o.ios||o.android?"resize orientationchange observerUpdate":"resize observerUpdate",B,!0):e[d]("observerUpdate",B,!0),i[c]("load",e.onLoad,{capture:!0}))},X=(e,t)=>e.grid&&t.grid&&t.grid.rows>1;var U={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function K(e,t){return function(n){void 0===n&&(n={});const a=Object.keys(n)[0],i=n[a];"object"==typeof i&&null!==i?(!0===e[a]&&(e[a]={enabled:!0}),"navigation"===a&&e[a]&&e[a].enabled&&!e[a].prevEl&&!e[a].nextEl&&(e[a].auto=!0),["pagination","scrollbar"].indexOf(a)>=0&&e[a]&&e[a].enabled&&!e[a].el&&(e[a].auto=!0),a in e&&"enabled"in i?("object"!=typeof e[a]||"enabled"in e[a]||(e[a].enabled=!0),e[a]||(e[a]={enabled:!1}),f(t,n)):f(t,n)):f(t,n)}}const J={eventsEmitter:M,update:N,translate:{getTranslate:function(e){void 0===e&&(e=this.isHorizontal()?"x":"y");const{params:t,rtlTranslate:n,translate:a,wrapperEl:i}=this;if(t.virtualTranslate)return n?-a:a;if(t.cssMode)return a;let r=function(e,t){void 0===t&&(t="x");const n=l();let a,i,r;const s=function(e){const t=l();let n;return t.getComputedStyle&&(n=t.getComputedStyle(e,null)),!n&&e.currentStyle&&(n=e.currentStyle),n||(n=e.style),n}(e);return n.WebKitCSSMatrix?(i=s.transform||s.webkitTransform,i.split(",").length>6&&(i=i.split(", ").map((e=>e.replace(",","."))).join(", ")),r=new n.WebKitCSSMatrix("none"===i?"":i)):(r=s.MozTransform||s.OTransform||s.MsTransform||s.msTransform||s.transform||s.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),a=r.toString().split(",")),"x"===t&&(i=n.WebKitCSSMatrix?r.m41:16===a.length?parseFloat(a[12]):parseFloat(a[4])),"y"===t&&(i=n.WebKitCSSMatrix?r.m42:16===a.length?parseFloat(a[13]):parseFloat(a[5])),i||0}(i,e);return r+=this.cssOverflowAdjustment(),n&&(r=-r),r||0},setTranslate:function(e,t){const n=this,{rtlTranslate:a,params:i,wrapperEl:r,progress:s}=n;let o,l=0,c=0;n.isHorizontal()?l=a?-e:e:c=e,i.roundLengths&&(l=Math.floor(l),c=Math.floor(c)),n.previousTranslate=n.translate,n.translate=n.isHorizontal()?l:c,i.cssMode?r[n.isHorizontal()?"scrollLeft":"scrollTop"]=n.isHorizontal()?-l:-c:i.virtualTranslate||(n.isHorizontal()?l-=n.cssOverflowAdjustment():c-=n.cssOverflowAdjustment(),r.style.transform=`translate3d(${l}px, ${c}px, 0px)`);const d=n.maxTranslate()-n.minTranslate();o=0===d?0:(e-n.minTranslate())/d,o!==s&&n.updateProgress(e),n.emit("setTranslate",n.translate,t)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(e,t,n,a,i){void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===n&&(n=!0),void 0===a&&(a=!0);const r=this,{params:s,wrapperEl:o}=r;if(r.animating&&s.preventInteractionOnTransition)return!1;const l=r.minTranslate(),c=r.maxTranslate();let d;if(d=a&&e>l?l:a&&e<c?c:e,r.updateProgress(d),s.cssMode){const e=r.isHorizontal();if(0===t)o[e?"scrollLeft":"scrollTop"]=-d;else{if(!r.support.smoothScroll)return m({swiper:r,targetPosition:-d,side:e?"left":"top"}),!0;o.scrollTo({[e?"left":"top"]:-d,behavior:"smooth"})}return!0}return 0===t?(r.setTransition(0),r.setTranslate(d),n&&(r.emit("beforeTransitionStart",t,i),r.emit("transitionEnd"))):(r.setTransition(t),r.setTranslate(d),n&&(r.emit("beforeTransitionStart",t,i),r.emit("transitionStart")),r.animating||(r.animating=!0,r.onTranslateToWrapperTransitionEnd||(r.onTranslateToWrapperTransitionEnd=function(e){r&&!r.destroyed&&e.target===this&&(r.wrapperEl.removeEventListener("transitionend",r.onTranslateToWrapperTransitionEnd),r.onTranslateToWrapperTransitionEnd=null,delete r.onTranslateToWrapperTransitionEnd,r.animating=!1,n&&r.emit("transitionEnd"))}),r.wrapperEl.addEventListener("transitionend",r.onTranslateToWrapperTransitionEnd))),!0}},transition:{setTransition:function(e,t){const n=this;n.params.cssMode||(n.wrapperEl.style.transitionDuration=`${e}ms`,n.wrapperEl.style.transitionDelay=0===e?"0ms":""),n.emit("setTransition",e,t)},transitionStart:function(e,t){void 0===e&&(e=!0);const n=this,{params:a}=n;a.cssMode||(a.autoHeight&&n.updateAutoHeight(),j({swiper:n,runCallbacks:e,direction:t,step:"Start"}))},transitionEnd:function(e,t){void 0===e&&(e=!0);const n=this,{params:a}=n;n.animating=!1,a.cssMode||(n.setTransition(0),j({swiper:n,runCallbacks:e,direction:t,step:"End"}))}},slide:D,loop:G,grabCursor:{setGrabCursor:function(e){const t=this;if(!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)return;const n="container"===t.params.touchEventsTarget?t.el:t.wrapperEl;t.isElement&&(t.__preventObserver__=!0),n.style.cursor="move",n.style.cursor=e?"grabbing":"grab",t.isElement&&requestAnimationFrame((()=>{t.__preventObserver__=!1}))},unsetGrabCursor:function(){const e=this;e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.isElement&&(e.__preventObserver__=!0),e["container"===e.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="",e.isElement&&requestAnimationFrame((()=>{e.__preventObserver__=!1})))}},events:{attachEvents:function(){const e=this,{params:t}=e;e.onTouchStart=F.bind(e),e.onTouchMove=R.bind(e),e.onTouchEnd=$.bind(e),e.onDocumentTouchStart=W.bind(e),t.cssMode&&(e.onScroll=Y.bind(e)),e.onClick=V.bind(e),e.onLoad=H.bind(e),q(e,"on")},detachEvents:function(){q(this,"off")}},breakpoints:{setBreakpoint:function(){const e=this,{realIndex:t,initialized:n,params:a,el:i}=e,r=a.breakpoints;if(!r||r&&0===Object.keys(r).length)return;const s=e.getBreakpoint(r,e.params.breakpointsBase,e.el);if(!s||e.currentBreakpoint===s)return;const o=(s in r?r[s]:void 0)||e.originalParams,l=X(e,a),c=X(e,o),d=e.params.grabCursor,u=o.grabCursor,p=a.enabled;l&&!c?(i.classList.remove(`${a.containerModifierClass}grid`,`${a.containerModifierClass}grid-column`),e.emitContainerClasses()):!l&&c&&(i.classList.add(`${a.containerModifierClass}grid`),(o.grid.fill&&"column"===o.grid.fill||!o.grid.fill&&"column"===a.grid.fill)&&i.classList.add(`${a.containerModifierClass}grid-column`),e.emitContainerClasses()),d&&!u?e.unsetGrabCursor():!d&&u&&e.setGrabCursor(),["navigation","pagination","scrollbar"].forEach((t=>{if(void 0===o[t])return;const n=a[t]&&a[t].enabled,i=o[t]&&o[t].enabled;n&&!i&&e[t].disable(),!n&&i&&e[t].enable()}));const m=o.direction&&o.direction!==a.direction,h=a.loop&&(o.slidesPerView!==a.slidesPerView||m),v=a.loop;m&&n&&e.changeDirection(),f(e.params,o);const g=e.params.enabled,b=e.params.loop;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),p&&!g?e.disable():!p&&g&&e.enable(),e.currentBreakpoint=s,e.emit("_beforeBreakpoint",o),n&&(h?(e.loopDestroy(),e.loopCreate(t),e.updateSlides()):!v&&b?(e.loopCreate(t),e.updateSlides()):v&&!b&&e.loopDestroy()),e.emit("breakpoint",o)},getBreakpoint:function(e,t,n){if(void 0===t&&(t="window"),!e||"container"===t&&!n)return;let a=!1;const i=l(),r="window"===t?i.innerHeight:n.clientHeight,s=Object.keys(e).map((e=>{if("string"==typeof e&&0===e.indexOf("@")){const t=parseFloat(e.substr(1));return{value:r*t,point:e}}return{value:e,point:e}}));s.sort(((e,t)=>parseInt(e.value,10)-parseInt(t.value,10)));for(let e=0;e<s.length;e+=1){const{point:r,value:o}=s[e];"window"===t?i.matchMedia(`(min-width: ${o}px)`).matches&&(a=r):o<=n.clientWidth&&(a=r)}return a||"max"}},checkOverflow:{checkOverflow:function(){const e=this,{isLocked:t,params:n}=e,{slidesOffsetBefore:a}=n;if(a){const t=e.slides.length-1,n=e.slidesGrid[t]+e.slidesSizesGrid[t]+2*a;e.isLocked=e.size>n}else e.isLocked=1===e.snapGrid.length;!0===n.allowSlideNext&&(e.allowSlideNext=!e.isLocked),!0===n.allowSlidePrev&&(e.allowSlidePrev=!e.isLocked),t&&t!==e.isLocked&&(e.isEnd=!1),t!==e.isLocked&&e.emit(e.isLocked?"lock":"unlock")}},classes:{addClasses:function(){const e=this,{classNames:t,params:n,rtl:a,el:i,device:r}=e,s=function(e,t){const n=[];return e.forEach((e=>{"object"==typeof e?Object.keys(e).forEach((a=>{e[a]&&n.push(t+a)})):"string"==typeof e&&n.push(t+e)})),n}(["initialized",n.direction,{"free-mode":e.params.freeMode&&n.freeMode.enabled},{autoheight:n.autoHeight},{rtl:a},{grid:n.grid&&n.grid.rows>1},{"grid-column":n.grid&&n.grid.rows>1&&"column"===n.grid.fill},{android:r.android},{ios:r.ios},{"css-mode":n.cssMode},{centered:n.cssMode&&n.centeredSlides},{"watch-progress":n.watchSlidesProgress}],n.containerModifierClass);t.push(...s),i.classList.add(...t),e.emitContainerClasses()},removeClasses:function(){const{el:e,classNames:t}=this;e&&"string"!=typeof e&&(e.classList.remove(...t),this.emitContainerClasses())}}},Q={};class Z{constructor(){let e,t;for(var n=arguments.length,a=new Array(n),i=0;i<n;i++)a[i]=arguments[i];1===a.length&&a[0].constructor&&"Object"===Object.prototype.toString.call(a[0]).slice(8,-1)?t=a[0]:[e,t]=a,t||(t={}),t=f({},t),e&&!t.el&&(t.el=e);const r=s();if(t.el&&"string"==typeof t.el&&r.querySelectorAll(t.el).length>1){const e=[];return r.querySelectorAll(t.el).forEach((n=>{const a=f({},t,{el:n});e.push(new Z(a))})),e}const o=this;o.__swiper__=!0,o.support=C(),o.device=P({userAgent:t.userAgent}),o.browser=(E||(E=function(){const e=l(),t=P();let n=!1;function a(){const t=e.navigator.userAgent.toLowerCase();return t.indexOf("safari")>=0&&t.indexOf("chrome")<0&&t.indexOf("android")<0}if(a()){const t=String(e.navigator.userAgent);if(t.includes("Version/")){const[e,a]=t.split("Version/")[1].split(" ")[0].split(".").map((e=>Number(e)));n=e<16||16===e&&a<2}}const i=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent),r=a();return{isSafari:n||r,needPerspectiveFix:n,need3dFix:r||i&&t.ios,isWebView:i}}()),E),o.eventsListeners={},o.eventsAnyListeners=[],o.modules=[...o.__modules__],t.modules&&Array.isArray(t.modules)&&o.modules.push(...t.modules);const c={};o.modules.forEach((e=>{e({params:t,swiper:o,extendParams:K(t,c),on:o.on.bind(o),once:o.once.bind(o),off:o.off.bind(o),emit:o.emit.bind(o)})}));const d=f({},U,c);return o.params=f({},d,Q,t),o.originalParams=f({},o.params),o.passedParams=f({},t),o.params&&o.params.on&&Object.keys(o.params.on).forEach((e=>{o.on(e,o.params.on[e])})),o.params&&o.params.onAny&&o.onAny(o.params.onAny),Object.assign(o,{enabled:o.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:()=>"horizontal"===o.params.direction,isVertical:()=>"vertical"===o.params.direction,activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:o.params.allowSlideNext,allowSlidePrev:o.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:o.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:o.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),o.emit("_swiper"),o.params.init&&o.init(),o}getDirectionLabel(e){return this.isHorizontal()?e:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[e]}getSlideIndex(e){const{slidesEl:t,params:n}=this,a=y(h(t,`.${n.slideClass}, swiper-slide`)[0]);return y(e)-a}getSlideIndexByData(e){return this.getSlideIndex(this.slides.filter((t=>1*t.getAttribute("data-swiper-slide-index")===e))[0])}recalcSlides(){const{slidesEl:e,params:t}=this;this.slides=h(e,`.${t.slideClass}, swiper-slide`)}enable(){const e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}disable(){const e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}setProgress(e,t){const n=this;e=Math.min(Math.max(e,0),1);const a=n.minTranslate(),i=(n.maxTranslate()-a)*e+a;n.translateTo(i,void 0===t?0:t),n.updateActiveIndex(),n.updateSlidesClasses()}emitContainerClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=e.el.className.split(" ").filter((t=>0===t.indexOf("swiper")||0===t.indexOf(e.params.containerModifierClass)));e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){const t=this;return t.destroyed?"":e.className.split(" ").filter((e=>0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass))).join(" ")}emitSlidesClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=[];e.slides.forEach((n=>{const a=e.getSlideClasses(n);t.push({slideEl:n,classNames:a}),e.emit("_slideClass",n,a)})),e.emit("_slideClasses",t)}slidesPerViewDynamic(e,t){void 0===e&&(e="current"),void 0===t&&(t=!1);const{params:n,slides:a,slidesGrid:i,slidesSizesGrid:r,size:s,activeIndex:o}=this;let l=1;if("number"==typeof n.slidesPerView)return n.slidesPerView;if(n.centeredSlides){let e,t=a[o]?Math.ceil(a[o].swiperSlideSize):0;for(let n=o+1;n<a.length;n+=1)a[n]&&!e&&(t+=Math.ceil(a[n].swiperSlideSize),l+=1,t>s&&(e=!0));for(let n=o-1;n>=0;n-=1)a[n]&&!e&&(t+=a[n].swiperSlideSize,l+=1,t>s&&(e=!0))}else if("current"===e)for(let e=o+1;e<a.length;e+=1)(t?i[e]+r[e]-i[o]<s:i[e]-i[o]<s)&&(l+=1);else for(let e=o-1;e>=0;e-=1)i[o]-i[e]<s&&(l+=1);return l}update(){const e=this;if(!e||e.destroyed)return;const{snapGrid:t,params:n}=e;function a(){const t=e.rtlTranslate?-1*e.translate:e.translate,n=Math.min(Math.max(t,e.maxTranslate()),e.minTranslate());e.setTranslate(n),e.updateActiveIndex(),e.updateSlidesClasses()}let i;if(n.breakpoints&&e.setBreakpoint(),[...e.el.querySelectorAll('[loading="lazy"]')].forEach((t=>{t.complete&&I(e,t)})),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),n.freeMode&&n.freeMode.enabled&&!n.cssMode)a(),n.autoHeight&&e.updateAutoHeight();else{if(("auto"===n.slidesPerView||n.slidesPerView>1)&&e.isEnd&&!n.centeredSlides){const t=e.virtual&&n.virtual.enabled?e.virtual.slides:e.slides;i=e.slideTo(t.length-1,0,!1,!0)}else i=e.slideTo(e.activeIndex,0,!1,!0);i||a()}n.watchOverflow&&t!==e.snapGrid&&e.checkOverflow(),e.emit("update")}changeDirection(e,t){void 0===t&&(t=!0);const n=this,a=n.params.direction;return e||(e="horizontal"===a?"vertical":"horizontal"),e===a||"horizontal"!==e&&"vertical"!==e||(n.el.classList.remove(`${n.params.containerModifierClass}${a}`),n.el.classList.add(`${n.params.containerModifierClass}${e}`),n.emitContainerClasses(),n.params.direction=e,n.slides.forEach((t=>{"vertical"===e?t.style.width="":t.style.height=""})),n.emit("changeDirection"),t&&n.update()),n}changeLanguageDirection(e){const t=this;t.rtl&&"rtl"===e||!t.rtl&&"ltr"===e||(t.rtl="rtl"===e,t.rtlTranslate="horizontal"===t.params.direction&&t.rtl,t.rtl?(t.el.classList.add(`${t.params.containerModifierClass}rtl`),t.el.dir="rtl"):(t.el.classList.remove(`${t.params.containerModifierClass}rtl`),t.el.dir="ltr"),t.update())}mount(e){const t=this;if(t.mounted)return!0;let n=e||t.params.el;if("string"==typeof n&&(n=document.querySelector(n)),!n)return!1;n.swiper=t,n.parentNode&&n.parentNode.host&&n.parentNode.host.nodeName===t.params.swiperElementNodeName.toUpperCase()&&(t.isElement=!0);const a=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`;let i=n&&n.shadowRoot&&n.shadowRoot.querySelector?n.shadowRoot.querySelector(a()):h(n,a())[0];return!i&&t.params.createElements&&(i=g("div",t.params.wrapperClass),n.append(i),h(n,`.${t.params.slideClass}`).forEach((e=>{i.append(e)}))),Object.assign(t,{el:n,wrapperEl:i,slidesEl:t.isElement&&!n.parentNode.host.slideSlots?n.parentNode.host:i,hostEl:t.isElement?n.parentNode.host:n,mounted:!0,rtl:"rtl"===n.dir.toLowerCase()||"rtl"===b(n,"direction"),rtlTranslate:"horizontal"===t.params.direction&&("rtl"===n.dir.toLowerCase()||"rtl"===b(n,"direction")),wrongRTL:"-webkit-box"===b(i,"display")}),!0}init(e){const t=this;if(t.initialized)return t;if(!1===t.mount(e))return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(),t.attachEvents();const n=[...t.el.querySelectorAll('[loading="lazy"]')];return t.isElement&&n.push(...t.hostEl.querySelectorAll('[loading="lazy"]')),n.forEach((e=>{e.complete?I(t,e):e.addEventListener("load",(e=>{I(t,e.target)}))})),z(t),t.initialized=!0,z(t),t.emit("init"),t.emit("afterInit"),t}destroy(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);const n=this,{params:a,el:i,wrapperEl:r,slides:s}=n;return void 0===n.params||n.destroyed||(n.emit("beforeDestroy"),n.initialized=!1,n.detachEvents(),a.loop&&n.loopDestroy(),t&&(n.removeClasses(),i&&"string"!=typeof i&&i.removeAttribute("style"),r&&r.removeAttribute("style"),s&&s.length&&s.forEach((e=>{e.classList.remove(a.slideVisibleClass,a.slideFullyVisibleClass,a.slideActiveClass,a.slideNextClass,a.slidePrevClass),e.removeAttribute("style"),e.removeAttribute("data-swiper-slide-index")}))),n.emit("destroy"),Object.keys(n.eventsListeners).forEach((e=>{n.off(e)})),!1!==e&&(n.el&&"string"!=typeof n.el&&(n.el.swiper=null),function(e){const t=e;Object.keys(t).forEach((e=>{try{t[e]=null}catch(e){}try{delete t[e]}catch(e){}}))}(n)),n.destroyed=!0),null}static extendDefaults(e){f(Q,e)}static get extendedDefaults(){return Q}static get defaults(){return U}static installModule(e){Z.prototype.__modules__||(Z.prototype.__modules__=[]);const t=Z.prototype.__modules__;"function"==typeof e&&t.indexOf(e)<0&&t.push(e)}static use(e){return Array.isArray(e)?(e.forEach((e=>Z.installModule(e))),Z):(Z.installModule(e),Z)}}function ee(e){return void 0===e&&(e=""),`.${e.trim().replace(/([\.:!+\/])/g,"\\$1").replace(/ /g,".")}`}Object.keys(J).forEach((e=>{Object.keys(J[e]).forEach((t=>{Z.prototype[t]=J[e][t]}))})),Z.use([function(e){let{swiper:t,on:n,emit:a}=e;const i=l();let r=null,s=null;const o=()=>{t&&!t.destroyed&&t.initialized&&(a("beforeResize"),a("resize"))},c=()=>{t&&!t.destroyed&&t.initialized&&a("orientationchange")};n("init",(()=>{t.params.resizeObserver&&void 0!==i.ResizeObserver?t&&!t.destroyed&&t.initialized&&(r=new ResizeObserver((e=>{s=i.requestAnimationFrame((()=>{const{width:n,height:a}=t;let i=n,r=a;e.forEach((e=>{let{contentBoxSize:n,contentRect:a,target:s}=e;s&&s!==t.el||(i=a?a.width:(n[0]||n).inlineSize,r=a?a.height:(n[0]||n).blockSize)})),i===n&&r===a||o()}))})),r.observe(t.el)):(i.addEventListener("resize",o),i.addEventListener("orientationchange",c))})),n("destroy",(()=>{s&&i.cancelAnimationFrame(s),r&&r.unobserve&&t.el&&(r.unobserve(t.el),r=null),i.removeEventListener("resize",o),i.removeEventListener("orientationchange",c)}))},function(e){let{swiper:t,extendParams:n,on:a,emit:i}=e;const r=[],s=l(),o=function(e,n){void 0===n&&(n={});const a=new(s.MutationObserver||s.WebkitMutationObserver)((e=>{if(t.__preventObserver__)return;if(1===e.length)return void i("observerUpdate",e[0]);const n=function(){i("observerUpdate",e[0])};s.requestAnimationFrame?s.requestAnimationFrame(n):s.setTimeout(n,0)}));a.observe(e,{attributes:void 0===n.attributes||n.attributes,childList:void 0===n.childList||n.childList,characterData:void 0===n.characterData||n.characterData}),r.push(a)};n({observer:!1,observeParents:!1,observeSlideChildren:!1}),a("init",(()=>{if(t.params.observer){if(t.params.observeParents){const e=w(t.hostEl);for(let t=0;t<e.length;t+=1)o(e[t])}o(t.hostEl,{childList:t.params.observeSlideChildren}),o(t.wrapperEl,{attributes:!1})}})),a("destroy",(()=>{r.forEach((e=>{e.disconnect()})),r.splice(0,r.length)}))}]);const te=window.wp.element;function ne(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function ae(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ne(Object(n),!0).forEach((function(t){se(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ne(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ie(e){return ie="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ie(e)}function re(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}function se(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function oe(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,i,r=[],_n=!0,s=!1;try{for(n=n.call(e);!(_n=(a=n.next()).done)&&(r.push(a.value),!t||r.length!==t);_n=!0);}catch(e){s=!0,i=e}finally{try{_n||null==n.return||n.return()}finally{if(s)throw i}}return r}}(e,t)||ce(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function le(e){return function(e){if(Array.isArray(e))return de(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||ce(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ce(e,t){if(e){if("string"==typeof e)return de(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?de(e,t):void 0}}function de(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var ue=function(){},fe={},pe={},me=null,he={mark:ue,measure:ue};try{"undefined"!=typeof window&&(fe=window),"undefined"!=typeof document&&(pe=document),"undefined"!=typeof MutationObserver&&(me=MutationObserver),"undefined"!=typeof performance&&(he=performance)}catch(e){}var ve,ge,be,ye,we,xe=(fe.navigator||{}).userAgent,Se=void 0===xe?"":xe,ke=fe,Te=pe,Ee=me,Ce=he,Pe=(ke.document,!!Te.documentElement&&!!Te.head&&"function"==typeof Te.addEventListener&&"function"==typeof Te.createElement),Me=~Se.indexOf("MSIE")||~Se.indexOf("Trident/"),Oe="___FONT_AWESOME___",Ae="fa",Ie="svg-inline--fa",Le="data-fa-i2svg",ze="data-fa-pseudo-element",Ne="data-fa-pseudo-element-pending",je="data-prefix",De="data-icon",Ge="fontawesome-i2svg",_e="async",Fe=["HTML","HEAD","STYLE","SCRIPT"],Re=function(){try{return!0}catch(e){return!1}}(),$e="classic",Be="sharp",Ve=[$e,Be];function Ye(e){return new Proxy(e,{get:function(e,t){return t in e?e[t]:e[$e]}})}var He=Ye((se(ve={},$e,{fa:"solid",fas:"solid","fa-solid":"solid",far:"regular","fa-regular":"regular",fal:"light","fa-light":"light",fat:"thin","fa-thin":"thin",fad:"duotone","fa-duotone":"duotone",fab:"brands","fa-brands":"brands",fak:"kit",fakd:"kit","fa-kit":"kit","fa-kit-duotone":"kit"}),se(ve,Be,{fa:"solid",fass:"solid","fa-solid":"solid",fasr:"regular","fa-regular":"regular",fasl:"light","fa-light":"light",fast:"thin","fa-thin":"thin"}),ve)),We=Ye((se(ge={},$e,{solid:"fas",regular:"far",light:"fal",thin:"fat",duotone:"fad",brands:"fab",kit:"fak"}),se(ge,Be,{solid:"fass",regular:"fasr",light:"fasl",thin:"fast"}),ge)),qe=Ye((se(be={},$e,{fab:"fa-brands",fad:"fa-duotone",fak:"fa-kit",fal:"fa-light",far:"fa-regular",fas:"fa-solid",fat:"fa-thin"}),se(be,Be,{fass:"fa-solid",fasr:"fa-regular",fasl:"fa-light",fast:"fa-thin"}),be)),Xe=Ye((se(ye={},$e,{"fa-brands":"fab","fa-duotone":"fad","fa-kit":"fak","fa-light":"fal","fa-regular":"far","fa-solid":"fas","fa-thin":"fat"}),se(ye,Be,{"fa-solid":"fass","fa-regular":"fasr","fa-light":"fasl","fa-thin":"fast"}),ye)),Ue=/fa(s|r|l|t|d|b|k|ss|sr|sl|st)?[\-\ ]/,Ke="fa-layers-text",Je=/Font ?Awesome ?([56 ]*)(Solid|Regular|Light|Thin|Duotone|Brands|Free|Pro|Sharp|Kit)?.*/i,Qe=Ye((se(we={},$e,{900:"fas",400:"far",normal:"far",300:"fal",100:"fat"}),se(we,Be,{900:"fass",400:"fasr",300:"fasl",100:"fast"}),we)),Ze=[1,2,3,4,5,6,7,8,9,10],et=Ze.concat([11,12,13,14,15,16,17,18,19,20]),tt=["class","data-prefix","data-icon","data-fa-transform","data-fa-mask"],nt={GROUP:"duotone-group",SWAP_OPACITY:"swap-opacity",PRIMARY:"primary",SECONDARY:"secondary"},at=new Set;Object.keys(We[$e]).map(at.add.bind(at)),Object.keys(We[Be]).map(at.add.bind(at));var it=[].concat(Ve,le(at),["2xs","xs","sm","lg","xl","2xl","beat","border","fade","beat-fade","bounce","flip-both","flip-horizontal","flip-vertical","flip","fw","inverse","layers-counter","layers-text","layers","li","pull-left","pull-right","pulse","rotate-180","rotate-270","rotate-90","rotate-by","shake","spin-pulse","spin-reverse","spin","stack-1x","stack-2x","stack","ul",nt.GROUP,nt.SWAP_OPACITY,nt.PRIMARY,nt.SECONDARY]).concat(Ze.map((function(e){return"".concat(e,"x")}))).concat(et.map((function(e){return"w-".concat(e)}))),rt=ke.FontAwesomeConfig||{};Te&&"function"==typeof Te.querySelector&&[["data-family-prefix","familyPrefix"],["data-css-prefix","cssPrefix"],["data-family-default","familyDefault"],["data-style-default","styleDefault"],["data-replacement-class","replacementClass"],["data-auto-replace-svg","autoReplaceSvg"],["data-auto-add-css","autoAddCss"],["data-auto-a11y","autoA11y"],["data-search-pseudo-elements","searchPseudoElements"],["data-observe-mutations","observeMutations"],["data-mutate-approach","mutateApproach"],["data-keep-original-source","keepOriginalSource"],["data-measure-performance","measurePerformance"],["data-show-missing-icons","showMissingIcons"]].forEach((function(e){var t=oe(e,2),n=t[0],a=t[1],i=function(e){return""===e||"false"!==e&&("true"===e||e)}(function(e){var t=Te.querySelector("script["+e+"]");if(t)return t.getAttribute(e)}(n));null!=i&&(rt[a]=i)}));var st={styleDefault:"solid",familyDefault:"classic",cssPrefix:Ae,replacementClass:Ie,autoReplaceSvg:!0,autoAddCss:!0,autoA11y:!0,searchPseudoElements:!1,observeMutations:!0,mutateApproach:"async",keepOriginalSource:!0,measurePerformance:!1,showMissingIcons:!0};rt.familyPrefix&&(rt.cssPrefix=rt.familyPrefix);var ot=ae(ae({},st),rt);ot.autoReplaceSvg||(ot.observeMutations=!1);var lt={};Object.keys(st).forEach((function(e){Object.defineProperty(lt,e,{enumerable:!0,set:function(t){ot[e]=t,ct.forEach((function(e){return e(lt)}))},get:function(){return ot[e]}})})),Object.defineProperty(lt,"familyPrefix",{enumerable:!0,set:function(e){ot.cssPrefix=e,ct.forEach((function(e){return e(lt)}))},get:function(){return ot.cssPrefix}}),ke.FontAwesomeConfig=lt;var ct=[],dt=16,ut={size:16,x:0,y:0,rotate:0,flipX:!1,flipY:!1},ft="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";function pt(){for(var e=12,t="";e-- >0;)t+=ft[62*Math.random()|0];return t}function mt(e){for(var t=[],n=(e||[]).length>>>0;n--;)t[n]=e[n];return t}function ht(e){return e.classList?mt(e.classList):(e.getAttribute("class")||"").split(" ").filter((function(e){return e}))}function vt(e){return"".concat(e).replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function gt(e){return Object.keys(e||{}).reduce((function(t,n){return t+"".concat(n,": ").concat(e[n].trim(),";")}),"")}function bt(e){return e.size!==ut.size||e.x!==ut.x||e.y!==ut.y||e.rotate!==ut.rotate||e.flipX||e.flipY}var yt=':root, :host {\n  --fa-font-solid: normal 900 1em/1 "Font Awesome 6 Solid";\n  --fa-font-regular: normal 400 1em/1 "Font Awesome 6 Regular";\n  --fa-font-light: normal 300 1em/1 "Font Awesome 6 Light";\n  --fa-font-thin: normal 100 1em/1 "Font Awesome 6 Thin";\n  --fa-font-duotone: normal 900 1em/1 "Font Awesome 6 Duotone";\n  --fa-font-sharp-solid: normal 900 1em/1 "Font Awesome 6 Sharp";\n  --fa-font-sharp-regular: normal 400 1em/1 "Font Awesome 6 Sharp";\n  --fa-font-sharp-light: normal 300 1em/1 "Font Awesome 6 Sharp";\n  --fa-font-sharp-thin: normal 100 1em/1 "Font Awesome 6 Sharp";\n  --fa-font-brands: normal 400 1em/1 "Font Awesome 6 Brands";\n}\n\nsvg:not(:root).svg-inline--fa, svg:not(:host).svg-inline--fa {\n  overflow: visible;\n  box-sizing: content-box;\n}\n\n.svg-inline--fa {\n  display: var(--fa-display, inline-block);\n  height: 1em;\n  overflow: visible;\n  vertical-align: -0.125em;\n}\n.svg-inline--fa.fa-2xs {\n  vertical-align: 0.1em;\n}\n.svg-inline--fa.fa-xs {\n  vertical-align: 0em;\n}\n.svg-inline--fa.fa-sm {\n  vertical-align: -0.0714285705em;\n}\n.svg-inline--fa.fa-lg {\n  vertical-align: -0.2em;\n}\n.svg-inline--fa.fa-xl {\n  vertical-align: -0.25em;\n}\n.svg-inline--fa.fa-2xl {\n  vertical-align: -0.3125em;\n}\n.svg-inline--fa.fa-pull-left {\n  margin-right: var(--fa-pull-margin, 0.3em);\n  width: auto;\n}\n.svg-inline--fa.fa-pull-right {\n  margin-left: var(--fa-pull-margin, 0.3em);\n  width: auto;\n}\n.svg-inline--fa.fa-li {\n  width: var(--fa-li-width, 2em);\n  top: 0.25em;\n}\n.svg-inline--fa.fa-fw {\n  width: var(--fa-fw-width, 1.25em);\n}\n\n.fa-layers svg.svg-inline--fa {\n  bottom: 0;\n  left: 0;\n  margin: auto;\n  position: absolute;\n  right: 0;\n  top: 0;\n}\n\n.fa-layers-counter, .fa-layers-text {\n  display: inline-block;\n  position: absolute;\n  text-align: center;\n}\n\n.fa-layers {\n  display: inline-block;\n  height: 1em;\n  position: relative;\n  text-align: center;\n  vertical-align: -0.125em;\n  width: 1em;\n}\n.fa-layers svg.svg-inline--fa {\n  -webkit-transform-origin: center center;\n          transform-origin: center center;\n}\n\n.fa-layers-text {\n  left: 50%;\n  top: 50%;\n  -webkit-transform: translate(-50%, -50%);\n          transform: translate(-50%, -50%);\n  -webkit-transform-origin: center center;\n          transform-origin: center center;\n}\n\n.fa-layers-counter {\n  background-color: var(--fa-counter-background-color, #ff253a);\n  border-radius: var(--fa-counter-border-radius, 1em);\n  box-sizing: border-box;\n  color: var(--fa-inverse, #fff);\n  line-height: var(--fa-counter-line-height, 1);\n  max-width: var(--fa-counter-max-width, 5em);\n  min-width: var(--fa-counter-min-width, 1.5em);\n  overflow: hidden;\n  padding: var(--fa-counter-padding, 0.25em 0.5em);\n  right: var(--fa-right, 0);\n  text-overflow: ellipsis;\n  top: var(--fa-top, 0);\n  -webkit-transform: scale(var(--fa-counter-scale, 0.25));\n          transform: scale(var(--fa-counter-scale, 0.25));\n  -webkit-transform-origin: top right;\n          transform-origin: top right;\n}\n\n.fa-layers-bottom-right {\n  bottom: var(--fa-bottom, 0);\n  right: var(--fa-right, 0);\n  top: auto;\n  -webkit-transform: scale(var(--fa-layers-scale, 0.25));\n          transform: scale(var(--fa-layers-scale, 0.25));\n  -webkit-transform-origin: bottom right;\n          transform-origin: bottom right;\n}\n\n.fa-layers-bottom-left {\n  bottom: var(--fa-bottom, 0);\n  left: var(--fa-left, 0);\n  right: auto;\n  top: auto;\n  -webkit-transform: scale(var(--fa-layers-scale, 0.25));\n          transform: scale(var(--fa-layers-scale, 0.25));\n  -webkit-transform-origin: bottom left;\n          transform-origin: bottom left;\n}\n\n.fa-layers-top-right {\n  top: var(--fa-top, 0);\n  right: var(--fa-right, 0);\n  -webkit-transform: scale(var(--fa-layers-scale, 0.25));\n          transform: scale(var(--fa-layers-scale, 0.25));\n  -webkit-transform-origin: top right;\n          transform-origin: top right;\n}\n\n.fa-layers-top-left {\n  left: var(--fa-left, 0);\n  right: auto;\n  top: var(--fa-top, 0);\n  -webkit-transform: scale(var(--fa-layers-scale, 0.25));\n          transform: scale(var(--fa-layers-scale, 0.25));\n  -webkit-transform-origin: top left;\n          transform-origin: top left;\n}\n\n.fa-1x {\n  font-size: 1em;\n}\n\n.fa-2x {\n  font-size: 2em;\n}\n\n.fa-3x {\n  font-size: 3em;\n}\n\n.fa-4x {\n  font-size: 4em;\n}\n\n.fa-5x {\n  font-size: 5em;\n}\n\n.fa-6x {\n  font-size: 6em;\n}\n\n.fa-7x {\n  font-size: 7em;\n}\n\n.fa-8x {\n  font-size: 8em;\n}\n\n.fa-9x {\n  font-size: 9em;\n}\n\n.fa-10x {\n  font-size: 10em;\n}\n\n.fa-2xs {\n  font-size: 0.625em;\n  line-height: 0.1em;\n  vertical-align: 0.225em;\n}\n\n.fa-xs {\n  font-size: 0.75em;\n  line-height: 0.0833333337em;\n  vertical-align: 0.125em;\n}\n\n.fa-sm {\n  font-size: 0.875em;\n  line-height: 0.0714285718em;\n  vertical-align: 0.0535714295em;\n}\n\n.fa-lg {\n  font-size: 1.25em;\n  line-height: 0.05em;\n  vertical-align: -0.075em;\n}\n\n.fa-xl {\n  font-size: 1.5em;\n  line-height: 0.0416666682em;\n  vertical-align: -0.125em;\n}\n\n.fa-2xl {\n  font-size: 2em;\n  line-height: 0.03125em;\n  vertical-align: -0.1875em;\n}\n\n.fa-fw {\n  text-align: center;\n  width: 1.25em;\n}\n\n.fa-ul {\n  list-style-type: none;\n  margin-left: var(--fa-li-margin, 2.5em);\n  padding-left: 0;\n}\n.fa-ul > li {\n  position: relative;\n}\n\n.fa-li {\n  left: calc(var(--fa-li-width, 2em) * -1);\n  position: absolute;\n  text-align: center;\n  width: var(--fa-li-width, 2em);\n  line-height: inherit;\n}\n\n.fa-border {\n  border-color: var(--fa-border-color, #eee);\n  border-radius: var(--fa-border-radius, 0.1em);\n  border-style: var(--fa-border-style, solid);\n  border-width: var(--fa-border-width, 0.08em);\n  padding: var(--fa-border-padding, 0.2em 0.25em 0.15em);\n}\n\n.fa-pull-left {\n  float: left;\n  margin-right: var(--fa-pull-margin, 0.3em);\n}\n\n.fa-pull-right {\n  float: right;\n  margin-left: var(--fa-pull-margin, 0.3em);\n}\n\n.fa-beat {\n  -webkit-animation-name: fa-beat;\n          animation-name: fa-beat;\n  -webkit-animation-delay: var(--fa-animation-delay, 0s);\n          animation-delay: var(--fa-animation-delay, 0s);\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\n          animation-direction: var(--fa-animation-direction, normal);\n  -webkit-animation-duration: var(--fa-animation-duration, 1s);\n          animation-duration: var(--fa-animation-duration, 1s);\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  -webkit-animation-timing-function: var(--fa-animation-timing, ease-in-out);\n          animation-timing-function: var(--fa-animation-timing, ease-in-out);\n}\n\n.fa-bounce {\n  -webkit-animation-name: fa-bounce;\n          animation-name: fa-bounce;\n  -webkit-animation-delay: var(--fa-animation-delay, 0s);\n          animation-delay: var(--fa-animation-delay, 0s);\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\n          animation-direction: var(--fa-animation-direction, normal);\n  -webkit-animation-duration: var(--fa-animation-duration, 1s);\n          animation-duration: var(--fa-animation-duration, 1s);\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  -webkit-animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));\n          animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));\n}\n\n.fa-fade {\n  -webkit-animation-name: fa-fade;\n          animation-name: fa-fade;\n  -webkit-animation-delay: var(--fa-animation-delay, 0s);\n          animation-delay: var(--fa-animation-delay, 0s);\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\n          animation-direction: var(--fa-animation-direction, normal);\n  -webkit-animation-duration: var(--fa-animation-duration, 1s);\n          animation-duration: var(--fa-animation-duration, 1s);\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  -webkit-animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\n          animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\n}\n\n.fa-beat-fade {\n  -webkit-animation-name: fa-beat-fade;\n          animation-name: fa-beat-fade;\n  -webkit-animation-delay: var(--fa-animation-delay, 0s);\n          animation-delay: var(--fa-animation-delay, 0s);\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\n          animation-direction: var(--fa-animation-direction, normal);\n  -webkit-animation-duration: var(--fa-animation-duration, 1s);\n          animation-duration: var(--fa-animation-duration, 1s);\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  -webkit-animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\n          animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\n}\n\n.fa-flip {\n  -webkit-animation-name: fa-flip;\n          animation-name: fa-flip;\n  -webkit-animation-delay: var(--fa-animation-delay, 0s);\n          animation-delay: var(--fa-animation-delay, 0s);\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\n          animation-direction: var(--fa-animation-direction, normal);\n  -webkit-animation-duration: var(--fa-animation-duration, 1s);\n          animation-duration: var(--fa-animation-duration, 1s);\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  -webkit-animation-timing-function: var(--fa-animation-timing, ease-in-out);\n          animation-timing-function: var(--fa-animation-timing, ease-in-out);\n}\n\n.fa-shake {\n  -webkit-animation-name: fa-shake;\n          animation-name: fa-shake;\n  -webkit-animation-delay: var(--fa-animation-delay, 0s);\n          animation-delay: var(--fa-animation-delay, 0s);\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\n          animation-direction: var(--fa-animation-direction, normal);\n  -webkit-animation-duration: var(--fa-animation-duration, 1s);\n          animation-duration: var(--fa-animation-duration, 1s);\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  -webkit-animation-timing-function: var(--fa-animation-timing, linear);\n          animation-timing-function: var(--fa-animation-timing, linear);\n}\n\n.fa-spin {\n  -webkit-animation-name: fa-spin;\n          animation-name: fa-spin;\n  -webkit-animation-delay: var(--fa-animation-delay, 0s);\n          animation-delay: var(--fa-animation-delay, 0s);\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\n          animation-direction: var(--fa-animation-direction, normal);\n  -webkit-animation-duration: var(--fa-animation-duration, 2s);\n          animation-duration: var(--fa-animation-duration, 2s);\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  -webkit-animation-timing-function: var(--fa-animation-timing, linear);\n          animation-timing-function: var(--fa-animation-timing, linear);\n}\n\n.fa-spin-reverse {\n  --fa-animation-direction: reverse;\n}\n\n.fa-pulse,\n.fa-spin-pulse {\n  -webkit-animation-name: fa-spin;\n          animation-name: fa-spin;\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\n          animation-direction: var(--fa-animation-direction, normal);\n  -webkit-animation-duration: var(--fa-animation-duration, 1s);\n          animation-duration: var(--fa-animation-duration, 1s);\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  -webkit-animation-timing-function: var(--fa-animation-timing, steps(8));\n          animation-timing-function: var(--fa-animation-timing, steps(8));\n}\n\n@media (prefers-reduced-motion: reduce) {\n  .fa-beat,\n.fa-bounce,\n.fa-fade,\n.fa-beat-fade,\n.fa-flip,\n.fa-pulse,\n.fa-shake,\n.fa-spin,\n.fa-spin-pulse {\n    -webkit-animation-delay: -1ms;\n            animation-delay: -1ms;\n    -webkit-animation-duration: 1ms;\n            animation-duration: 1ms;\n    -webkit-animation-iteration-count: 1;\n            animation-iteration-count: 1;\n    -webkit-transition-delay: 0s;\n            transition-delay: 0s;\n    -webkit-transition-duration: 0s;\n            transition-duration: 0s;\n  }\n}\n@-webkit-keyframes fa-beat {\n  0%, 90% {\n    -webkit-transform: scale(1);\n            transform: scale(1);\n  }\n  45% {\n    -webkit-transform: scale(var(--fa-beat-scale, 1.25));\n            transform: scale(var(--fa-beat-scale, 1.25));\n  }\n}\n@keyframes fa-beat {\n  0%, 90% {\n    -webkit-transform: scale(1);\n            transform: scale(1);\n  }\n  45% {\n    -webkit-transform: scale(var(--fa-beat-scale, 1.25));\n            transform: scale(var(--fa-beat-scale, 1.25));\n  }\n}\n@-webkit-keyframes fa-bounce {\n  0% {\n    -webkit-transform: scale(1, 1) translateY(0);\n            transform: scale(1, 1) translateY(0);\n  }\n  10% {\n    -webkit-transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);\n            transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);\n  }\n  30% {\n    -webkit-transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));\n            transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));\n  }\n  50% {\n    -webkit-transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);\n            transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);\n  }\n  57% {\n    -webkit-transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));\n            transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));\n  }\n  64% {\n    -webkit-transform: scale(1, 1) translateY(0);\n            transform: scale(1, 1) translateY(0);\n  }\n  100% {\n    -webkit-transform: scale(1, 1) translateY(0);\n            transform: scale(1, 1) translateY(0);\n  }\n}\n@keyframes fa-bounce {\n  0% {\n    -webkit-transform: scale(1, 1) translateY(0);\n            transform: scale(1, 1) translateY(0);\n  }\n  10% {\n    -webkit-transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);\n            transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);\n  }\n  30% {\n    -webkit-transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));\n            transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));\n  }\n  50% {\n    -webkit-transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);\n            transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);\n  }\n  57% {\n    -webkit-transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));\n            transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));\n  }\n  64% {\n    -webkit-transform: scale(1, 1) translateY(0);\n            transform: scale(1, 1) translateY(0);\n  }\n  100% {\n    -webkit-transform: scale(1, 1) translateY(0);\n            transform: scale(1, 1) translateY(0);\n  }\n}\n@-webkit-keyframes fa-fade {\n  50% {\n    opacity: var(--fa-fade-opacity, 0.4);\n  }\n}\n@keyframes fa-fade {\n  50% {\n    opacity: var(--fa-fade-opacity, 0.4);\n  }\n}\n@-webkit-keyframes fa-beat-fade {\n  0%, 100% {\n    opacity: var(--fa-beat-fade-opacity, 0.4);\n    -webkit-transform: scale(1);\n            transform: scale(1);\n  }\n  50% {\n    opacity: 1;\n    -webkit-transform: scale(var(--fa-beat-fade-scale, 1.125));\n            transform: scale(var(--fa-beat-fade-scale, 1.125));\n  }\n}\n@keyframes fa-beat-fade {\n  0%, 100% {\n    opacity: var(--fa-beat-fade-opacity, 0.4);\n    -webkit-transform: scale(1);\n            transform: scale(1);\n  }\n  50% {\n    opacity: 1;\n    -webkit-transform: scale(var(--fa-beat-fade-scale, 1.125));\n            transform: scale(var(--fa-beat-fade-scale, 1.125));\n  }\n}\n@-webkit-keyframes fa-flip {\n  50% {\n    -webkit-transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));\n            transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));\n  }\n}\n@keyframes fa-flip {\n  50% {\n    -webkit-transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));\n            transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));\n  }\n}\n@-webkit-keyframes fa-shake {\n  0% {\n    -webkit-transform: rotate(-15deg);\n            transform: rotate(-15deg);\n  }\n  4% {\n    -webkit-transform: rotate(15deg);\n            transform: rotate(15deg);\n  }\n  8%, 24% {\n    -webkit-transform: rotate(-18deg);\n            transform: rotate(-18deg);\n  }\n  12%, 28% {\n    -webkit-transform: rotate(18deg);\n            transform: rotate(18deg);\n  }\n  16% {\n    -webkit-transform: rotate(-22deg);\n            transform: rotate(-22deg);\n  }\n  20% {\n    -webkit-transform: rotate(22deg);\n            transform: rotate(22deg);\n  }\n  32% {\n    -webkit-transform: rotate(-12deg);\n            transform: rotate(-12deg);\n  }\n  36% {\n    -webkit-transform: rotate(12deg);\n            transform: rotate(12deg);\n  }\n  40%, 100% {\n    -webkit-transform: rotate(0deg);\n            transform: rotate(0deg);\n  }\n}\n@keyframes fa-shake {\n  0% {\n    -webkit-transform: rotate(-15deg);\n            transform: rotate(-15deg);\n  }\n  4% {\n    -webkit-transform: rotate(15deg);\n            transform: rotate(15deg);\n  }\n  8%, 24% {\n    -webkit-transform: rotate(-18deg);\n            transform: rotate(-18deg);\n  }\n  12%, 28% {\n    -webkit-transform: rotate(18deg);\n            transform: rotate(18deg);\n  }\n  16% {\n    -webkit-transform: rotate(-22deg);\n            transform: rotate(-22deg);\n  }\n  20% {\n    -webkit-transform: rotate(22deg);\n            transform: rotate(22deg);\n  }\n  32% {\n    -webkit-transform: rotate(-12deg);\n            transform: rotate(-12deg);\n  }\n  36% {\n    -webkit-transform: rotate(12deg);\n            transform: rotate(12deg);\n  }\n  40%, 100% {\n    -webkit-transform: rotate(0deg);\n            transform: rotate(0deg);\n  }\n}\n@-webkit-keyframes fa-spin {\n  0% {\n    -webkit-transform: rotate(0deg);\n            transform: rotate(0deg);\n  }\n  100% {\n    -webkit-transform: rotate(360deg);\n            transform: rotate(360deg);\n  }\n}\n@keyframes fa-spin {\n  0% {\n    -webkit-transform: rotate(0deg);\n            transform: rotate(0deg);\n  }\n  100% {\n    -webkit-transform: rotate(360deg);\n            transform: rotate(360deg);\n  }\n}\n.fa-rotate-90 {\n  -webkit-transform: rotate(90deg);\n          transform: rotate(90deg);\n}\n\n.fa-rotate-180 {\n  -webkit-transform: rotate(180deg);\n          transform: rotate(180deg);\n}\n\n.fa-rotate-270 {\n  -webkit-transform: rotate(270deg);\n          transform: rotate(270deg);\n}\n\n.fa-flip-horizontal {\n  -webkit-transform: scale(-1, 1);\n          transform: scale(-1, 1);\n}\n\n.fa-flip-vertical {\n  -webkit-transform: scale(1, -1);\n          transform: scale(1, -1);\n}\n\n.fa-flip-both,\n.fa-flip-horizontal.fa-flip-vertical {\n  -webkit-transform: scale(-1, -1);\n          transform: scale(-1, -1);\n}\n\n.fa-rotate-by {\n  -webkit-transform: rotate(var(--fa-rotate-angle, 0));\n          transform: rotate(var(--fa-rotate-angle, 0));\n}\n\n.fa-stack {\n  display: inline-block;\n  vertical-align: middle;\n  height: 2em;\n  position: relative;\n  width: 2.5em;\n}\n\n.fa-stack-1x,\n.fa-stack-2x {\n  bottom: 0;\n  left: 0;\n  margin: auto;\n  position: absolute;\n  right: 0;\n  top: 0;\n  z-index: var(--fa-stack-z-index, auto);\n}\n\n.svg-inline--fa.fa-stack-1x {\n  height: 1em;\n  width: 1.25em;\n}\n.svg-inline--fa.fa-stack-2x {\n  height: 2em;\n  width: 2.5em;\n}\n\n.fa-inverse {\n  color: var(--fa-inverse, #fff);\n}\n\n.sr-only,\n.fa-sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0;\n}\n\n.sr-only-focusable:not(:focus),\n.fa-sr-only-focusable:not(:focus) {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0;\n}\n\n.svg-inline--fa .fa-primary {\n  fill: var(--fa-primary-color, currentColor);\n  opacity: var(--fa-primary-opacity, 1);\n}\n\n.svg-inline--fa .fa-secondary {\n  fill: var(--fa-secondary-color, currentColor);\n  opacity: var(--fa-secondary-opacity, 0.4);\n}\n\n.svg-inline--fa.fa-swap-opacity .fa-primary {\n  opacity: var(--fa-secondary-opacity, 0.4);\n}\n\n.svg-inline--fa.fa-swap-opacity .fa-secondary {\n  opacity: var(--fa-primary-opacity, 1);\n}\n\n.svg-inline--fa mask .fa-primary,\n.svg-inline--fa mask .fa-secondary {\n  fill: black;\n}\n\n.fad.fa-inverse,\n.fa-duotone.fa-inverse {\n  color: var(--fa-inverse, #fff);\n}';function wt(){var e=Ae,t=Ie,n=lt.cssPrefix,a=lt.replacementClass,i=yt;if(n!==e||a!==t){var r=new RegExp("\\.".concat(e,"\\-"),"g"),s=new RegExp("\\--".concat(e,"\\-"),"g"),o=new RegExp("\\.".concat(t),"g");i=i.replace(r,".".concat(n,"-")).replace(s,"--".concat(n,"-")).replace(o,".".concat(a))}return i}var xt=!1;function St(){lt.autoAddCss&&!xt&&(function(e){if(e&&Pe){var t=Te.createElement("style");t.setAttribute("type","text/css"),t.innerHTML=e;for(var n=Te.head.childNodes,a=null,i=n.length-1;i>-1;i--){var r=n[i],s=(r.tagName||"").toUpperCase();["STYLE","LINK"].indexOf(s)>-1&&(a=r)}Te.head.insertBefore(t,a)}}(wt()),xt=!0)}var kt={mixout:function(){return{dom:{css:wt,insertCss:St}}},hooks:function(){return{beforeDOMElementCreation:function(){St()},beforeI2svg:function(){St()}}}},Tt=ke||{};Tt[Oe]||(Tt[Oe]={}),Tt[Oe].styles||(Tt[Oe].styles={}),Tt[Oe].hooks||(Tt[Oe].hooks={}),Tt[Oe].shims||(Tt[Oe].shims=[]);var Et=Tt[Oe],Ct=[],Pt=!1;function Mt(e){var t=e.tag,n=e.attributes,a=void 0===n?{}:n,i=e.children,r=void 0===i?[]:i;return"string"==typeof e?vt(e):"<".concat(t," ").concat(function(e){return Object.keys(e||{}).reduce((function(t,n){return t+"".concat(n,'="').concat(vt(e[n]),'" ')}),"").trim()}(a),">").concat(r.map(Mt).join(""),"</").concat(t,">")}function Ot(e,t,n){if(e&&e[t]&&e[t][n])return{prefix:t,iconName:n,icon:e[t][n]}}Pe&&((Pt=(Te.documentElement.doScroll?/^loaded|^c/:/^loaded|^i|^c/).test(Te.readyState))||Te.addEventListener("DOMContentLoaded",(function e(){Te.removeEventListener("DOMContentLoaded",e),Pt=1,Ct.map((function(e){return e()}))})));var At=function(e,t,n,a){var i,r,s,o=Object.keys(e),l=o.length,c=void 0!==a?function(e,t){return function(n,a,i,r){return e.call(t,n,a,i,r)}}(t,a):t;for(void 0===n?(i=1,s=e[o[0]]):(i=0,s=n);i<l;i++)s=c(s,e[r=o[i]],r,e);return s};function It(e){var t=function(e){for(var t=[],n=0,a=e.length;n<a;){var i=e.charCodeAt(n++);if(i>=55296&&i<=56319&&n<a){var r=e.charCodeAt(n++);56320==(64512&r)?t.push(((1023&i)<<10)+(1023&r)+65536):(t.push(i),n--)}else t.push(i)}return t}(e);return 1===t.length?t[0].toString(16):null}function Lt(e){return Object.keys(e).reduce((function(t,n){var a=e[n];return a.icon?t[a.iconName]=a.icon:t[n]=a,t}),{})}function zt(e,t){var n=(arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}).skipHooks,a=void 0!==n&&n,i=Lt(t);"function"!=typeof Et.hooks.addPack||a?Et.styles[e]=ae(ae({},Et.styles[e]||{}),i):Et.hooks.addPack(e,Lt(t)),"fas"===e&&zt("fa",t)}var Nt,jt,Dt,Gt=Et.styles,_t=Et.shims,Ft=(se(Nt={},$e,Object.values(qe[$e])),se(Nt,Be,Object.values(qe[Be])),Nt),Rt=null,$t={},Bt={},Vt={},Yt={},Ht={},Wt=(se(jt={},$e,Object.keys(He[$e])),se(jt,Be,Object.keys(He[Be])),jt);var qt,Xt=function(){var e=function(e){return At(Gt,(function(t,n,a){return t[a]=At(n,e,{}),t}),{})};$t=e((function(e,t,n){return t[3]&&(e[t[3]]=n),t[2]&&t[2].filter((function(e){return"number"==typeof e})).forEach((function(t){e[t.toString(16)]=n})),e})),Bt=e((function(e,t,n){return e[n]=n,t[2]&&t[2].filter((function(e){return"string"==typeof e})).forEach((function(t){e[t]=n})),e})),Ht=e((function(e,t,n){var a=t[2];return e[n]=n,a.forEach((function(t){e[t]=n})),e}));var t="far"in Gt||lt.autoFetchSvg,n=At(_t,(function(e,n){var a=n[0],i=n[1],r=n[2];return"far"!==i||t||(i="fas"),"string"==typeof a&&(e.names[a]={prefix:i,iconName:r}),"number"==typeof a&&(e.unicodes[a.toString(16)]={prefix:i,iconName:r}),e}),{names:{},unicodes:{}});Vt=n.names,Yt=n.unicodes,Rt=en(lt.styleDefault,{family:lt.familyDefault})};function Ut(e,t){return($t[e]||{})[t]}function Kt(e,t){return(Ht[e]||{})[t]}function Jt(e){return Vt[e]||{prefix:null,iconName:null}}function Qt(){return Rt}qt=function(e){Rt=en(e.styleDefault,{family:lt.familyDefault})},ct.push(qt),Xt();var Zt=function(){return{prefix:null,iconName:null,rest:[]}};function en(e){var t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).family,n=void 0===t?$e:t,a=He[n][e],i=We[n][e]||We[n][a],r=e in Et.styles?e:null;return i||r||null}var tn=(se(Dt={},$e,Object.keys(qe[$e])),se(Dt,Be,Object.keys(qe[Be])),Dt);function nn(e){var t,n=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).skipLookups,a=void 0!==n&&n,i=(se(t={},$e,"".concat(lt.cssPrefix,"-").concat($e)),se(t,Be,"".concat(lt.cssPrefix,"-").concat(Be)),t),r=null,s=$e;(e.includes(i[$e])||e.some((function(e){return tn[$e].includes(e)})))&&(s=$e),(e.includes(i[Be])||e.some((function(e){return tn[Be].includes(e)})))&&(s=Be);var o=e.reduce((function(e,t){var n=function(e,t){var n,a=t.split("-"),i=a[0],r=a.slice(1).join("-");return i!==e||""===r||(n=r,~it.indexOf(n))?null:r}(lt.cssPrefix,t);if(Gt[t]?(t=Ft[s].includes(t)?Xe[s][t]:t,r=t,e.prefix=t):Wt[s].indexOf(t)>-1?(r=t,e.prefix=en(t,{family:s})):n?e.iconName=n:t!==lt.replacementClass&&t!==i[$e]&&t!==i[Be]&&e.rest.push(t),!a&&e.prefix&&e.iconName){var o="fa"===r?Jt(e.iconName):{},l=Kt(e.prefix,e.iconName);o.prefix&&(r=null),e.iconName=o.iconName||l||e.iconName,e.prefix=o.prefix||e.prefix,"far"!==e.prefix||Gt.far||!Gt.fas||lt.autoFetchSvg||(e.prefix="fas")}return e}),Zt());return(e.includes("fa-brands")||e.includes("fab"))&&(o.prefix="fab"),(e.includes("fa-duotone")||e.includes("fad"))&&(o.prefix="fad"),o.prefix||s!==Be||!Gt.fass&&!lt.autoFetchSvg||(o.prefix="fass",o.iconName=Kt(o.prefix,o.iconName)||o.iconName),"fa"!==o.prefix&&"fa"!==r||(o.prefix=Qt()||"fas"),o}var an=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.definitions={}}var t,n;return t=e,n=[{key:"add",value:function(){for(var e=this,t=arguments.length,n=new Array(t),a=0;a<t;a++)n[a]=arguments[a];var i=n.reduce(this._pullDefinitions,{});Object.keys(i).forEach((function(t){e.definitions[t]=ae(ae({},e.definitions[t]||{}),i[t]),zt(t,i[t]);var n=qe[$e][t];n&&zt(n,i[t]),Xt()}))}},{key:"reset",value:function(){this.definitions={}}},{key:"_pullDefinitions",value:function(e,t){var n=t.prefix&&t.iconName&&t.icon?{0:t}:t;return Object.keys(n).map((function(t){var a=n[t],i=a.prefix,r=a.iconName,s=a.icon,o=s[2];e[i]||(e[i]={}),o.length>0&&o.forEach((function(t){"string"==typeof t&&(e[i][t]=s)})),e[i][r]=s})),e}}],n&&re(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),e}(),rn=[],sn={},on={},ln=Object.keys(on);function cn(e,t){for(var n=arguments.length,a=new Array(n>2?n-2:0),i=2;i<n;i++)a[i-2]=arguments[i];return(sn[e]||[]).forEach((function(e){t=e.apply(null,[t].concat(a))})),t}function dn(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),a=1;a<t;a++)n[a-1]=arguments[a];(sn[e]||[]).forEach((function(e){e.apply(null,n)}))}function un(){var e=arguments[0],t=Array.prototype.slice.call(arguments,1);return on[e]?on[e].apply(null,t):void 0}function fn(e){"fa"===e.prefix&&(e.prefix="fas");var t=e.iconName,n=e.prefix||Qt();if(t)return t=Kt(n,t)||t,Ot(pn.definitions,n,t)||Ot(Et.styles,n,t)}var pn=new an,mn={i2svg:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Pe?(dn("beforeI2svg",e),un("pseudoElements2svg",e),un("i2svg",e)):Promise.reject("Operation requires a DOM of some kind.")},watch:function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.autoReplaceSvgRoot;!1===lt.autoReplaceSvg&&(lt.autoReplaceSvg=!0),lt.observeMutations=!0,e=function(){vn({autoReplaceSvgRoot:n}),dn("watch",t)},Pe&&(Pt?setTimeout(e,0):Ct.push(e))}},hn={noAuto:function(){lt.autoReplaceSvg=!1,lt.observeMutations=!1,dn("noAuto")},config:lt,dom:mn,parse:{icon:function(e){if(null===e)return null;if("object"===ie(e)&&e.prefix&&e.iconName)return{prefix:e.prefix,iconName:Kt(e.prefix,e.iconName)||e.iconName};if(Array.isArray(e)&&2===e.length){var t=0===e[1].indexOf("fa-")?e[1].slice(3):e[1],n=en(e[0]);return{prefix:n,iconName:Kt(n,t)||t}}if("string"==typeof e&&(e.indexOf("".concat(lt.cssPrefix,"-"))>-1||e.match(Ue))){var a=nn(e.split(" "),{skipLookups:!0});return{prefix:a.prefix||Qt(),iconName:Kt(a.prefix,a.iconName)||a.iconName}}if("string"==typeof e){var i=Qt();return{prefix:i,iconName:Kt(i,e)||e}}}},library:pn,findIconDefinition:fn,toHtml:Mt},vn=function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).autoReplaceSvgRoot,t=void 0===e?Te:e;(Object.keys(Et.styles).length>0||lt.autoFetchSvg)&&Pe&&lt.autoReplaceSvg&&hn.dom.i2svg({node:t})};function gn(e,t){return Object.defineProperty(e,"abstract",{get:t}),Object.defineProperty(e,"html",{get:function(){return e.abstract.map((function(e){return Mt(e)}))}}),Object.defineProperty(e,"node",{get:function(){if(Pe){var t=Te.createElement("div");return t.innerHTML=e.html,t.children}}}),e}function bn(e){var t=e.icons,n=t.main,a=t.mask,i=e.prefix,r=e.iconName,s=e.transform,o=e.symbol,l=e.title,c=e.maskId,d=e.titleId,u=e.extra,f=e.watchable,p=void 0!==f&&f,m=a.found?a:n,h=m.width,v=m.height,g="fak"===i,b=[lt.replacementClass,r?"".concat(lt.cssPrefix,"-").concat(r):""].filter((function(e){return-1===u.classes.indexOf(e)})).filter((function(e){return""!==e||!!e})).concat(u.classes).join(" "),y={children:[],attributes:ae(ae({},u.attributes),{},{"data-prefix":i,"data-icon":r,class:b,role:u.attributes.role||"img",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 ".concat(h," ").concat(v)})},w=g&&!~u.classes.indexOf("fa-fw")?{width:"".concat(h/v*16*.0625,"em")}:{};p&&(y.attributes[Le]=""),l&&(y.children.push({tag:"title",attributes:{id:y.attributes["aria-labelledby"]||"title-".concat(d||pt())},children:[l]}),delete y.attributes.title);var x=ae(ae({},y),{},{prefix:i,iconName:r,main:n,mask:a,maskId:c,transform:s,symbol:o,styles:ae(ae({},w),u.styles)}),S=a.found&&n.found?un("generateAbstractMask",x)||{children:[],attributes:{}}:un("generateAbstractIcon",x)||{children:[],attributes:{}},k=S.children,T=S.attributes;return x.children=k,x.attributes=T,o?function(e){var t=e.prefix,n=e.iconName,a=e.children,i=e.attributes,r=e.symbol,s=!0===r?"".concat(t,"-").concat(lt.cssPrefix,"-").concat(n):r;return[{tag:"svg",attributes:{style:"display: none;"},children:[{tag:"symbol",attributes:ae(ae({},i),{},{id:s}),children:a}]}]}(x):function(e){var t=e.children,n=e.main,a=e.mask,i=e.attributes,r=e.styles,s=e.transform;if(bt(s)&&n.found&&!a.found){var o={x:n.width/n.height/2,y:.5};i.style=gt(ae(ae({},r),{},{"transform-origin":"".concat(o.x+s.x/16,"em ").concat(o.y+s.y/16,"em")}))}return[{tag:"svg",attributes:i,children:t}]}(x)}function yn(e){var t=e.content,n=e.width,a=e.height,i=e.transform,r=e.title,s=e.extra,o=e.watchable,l=void 0!==o&&o,c=ae(ae(ae({},s.attributes),r?{title:r}:{}),{},{class:s.classes.join(" ")});l&&(c[Le]="");var d=ae({},s.styles);bt(i)&&(d.transform=function(e){var t=e.transform,n=e.width,a=void 0===n?16:n,i=e.height,r=void 0===i?16:i,s=e.startCentered,o=void 0!==s&&s,l="";return l+=o&&Me?"translate(".concat(t.x/dt-a/2,"em, ").concat(t.y/dt-r/2,"em) "):o?"translate(calc(-50% + ".concat(t.x/dt,"em), calc(-50% + ").concat(t.y/dt,"em)) "):"translate(".concat(t.x/dt,"em, ").concat(t.y/dt,"em) "),(l+="scale(".concat(t.size/dt*(t.flipX?-1:1),", ").concat(t.size/dt*(t.flipY?-1:1),") "))+"rotate(".concat(t.rotate,"deg) ")}({transform:i,startCentered:!0,width:n,height:a}),d["-webkit-transform"]=d.transform);var u=gt(d);u.length>0&&(c.style=u);var f=[];return f.push({tag:"span",attributes:c,children:[t]}),r&&f.push({tag:"span",attributes:{class:"sr-only"},children:[r]}),f}var wn=Et.styles;function xn(e){var t=e[0],n=e[1],a=oe(e.slice(4),1)[0];return{found:!0,width:t,height:n,icon:Array.isArray(a)?{tag:"g",attributes:{class:"".concat(lt.cssPrefix,"-").concat(nt.GROUP)},children:[{tag:"path",attributes:{class:"".concat(lt.cssPrefix,"-").concat(nt.SECONDARY),fill:"currentColor",d:a[0]}},{tag:"path",attributes:{class:"".concat(lt.cssPrefix,"-").concat(nt.PRIMARY),fill:"currentColor",d:a[1]}}]}:{tag:"path",attributes:{fill:"currentColor",d:a}}}}var Sn={found:!1,width:512,height:512};function kn(e,t){var n=t;return"fa"===t&&null!==lt.styleDefault&&(t=Qt()),new Promise((function(a,i){if(un("missingIconAbstract"),"fa"===n){var r=Jt(e)||{};e=r.iconName||e,t=r.prefix||t}if(e&&t&&wn[t]&&wn[t][e])return a(xn(wn[t][e]));!function(e,t){Re||lt.showMissingIcons||!e||console.error('Icon with name "'.concat(e,'" and prefix "').concat(t,'" is missing.'))}(e,t),a(ae(ae({},Sn),{},{icon:lt.showMissingIcons&&e&&un("missingIconAbstract")||{}}))}))}var Tn=function(){},En=lt.measurePerformance&&Ce&&Ce.mark&&Ce.measure?Ce:{mark:Tn,measure:Tn},Cn='FA "6.5.2"',Pn=function(e){En.mark("".concat(Cn," ").concat(e," ends")),En.measure("".concat(Cn," ").concat(e),"".concat(Cn," ").concat(e," begins"),"".concat(Cn," ").concat(e," ends"))},Mn={begin:function(e){return En.mark("".concat(Cn," ").concat(e," begins")),function(){return Pn(e)}},end:Pn},On=function(){};function An(e){return"string"==typeof(e.getAttribute?e.getAttribute(Le):null)}function In(e){return Te.createElementNS("http://www.w3.org/2000/svg",e)}function Ln(e){return Te.createElement(e)}function zn(e){var t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).ceFn,n=void 0===t?"svg"===e.tag?In:Ln:t;if("string"==typeof e)return Te.createTextNode(e);var a=n(e.tag);return Object.keys(e.attributes||[]).forEach((function(t){a.setAttribute(t,e.attributes[t])})),(e.children||[]).forEach((function(e){a.appendChild(zn(e,{ceFn:n}))})),a}var Nn={replace:function(e){var t=e[0];if(t.parentNode)if(e[1].forEach((function(e){t.parentNode.insertBefore(zn(e),t)})),null===t.getAttribute(Le)&&lt.keepOriginalSource){var n=Te.createComment(function(e){var t=" ".concat(e.outerHTML," ");return"".concat(t,"Font Awesome fontawesome.com ")}(t));t.parentNode.replaceChild(n,t)}else t.remove()},nest:function(e){var t=e[0],n=e[1];if(~ht(t).indexOf(lt.replacementClass))return Nn.replace(e);var a=new RegExp("".concat(lt.cssPrefix,"-.*"));if(delete n[0].attributes.id,n[0].attributes.class){var i=n[0].attributes.class.split(" ").reduce((function(e,t){return t===lt.replacementClass||t.match(a)?e.toSvg.push(t):e.toNode.push(t),e}),{toNode:[],toSvg:[]});n[0].attributes.class=i.toSvg.join(" "),0===i.toNode.length?t.removeAttribute("class"):t.setAttribute("class",i.toNode.join(" "))}var r=n.map((function(e){return Mt(e)})).join("\n");t.setAttribute(Le,""),t.innerHTML=r}};function jn(e){e()}function Dn(e,t){var n="function"==typeof t?t:On;if(0===e.length)n();else{var a=jn;lt.mutateApproach===_e&&(a=ke.requestAnimationFrame||jn),a((function(){var t=!0===lt.autoReplaceSvg?Nn.replace:Nn[lt.autoReplaceSvg]||Nn.replace,a=Mn.begin("mutate");e.map(t),a(),n()}))}}var Gn=!1;function Fn(){Gn=!0}function Rn(){Gn=!1}var $n=null;function Bn(e){if(Ee&&lt.observeMutations){var t=e.treeCallback,n=void 0===t?On:t,a=e.nodeCallback,i=void 0===a?On:a,r=e.pseudoElementsCallback,s=void 0===r?On:r,o=e.observeMutationsRoot,l=void 0===o?Te:o;$n=new Ee((function(e){if(!Gn){var t=Qt();mt(e).forEach((function(e){if("childList"===e.type&&e.addedNodes.length>0&&!An(e.addedNodes[0])&&(lt.searchPseudoElements&&s(e.target),n(e.target)),"attributes"===e.type&&e.target.parentNode&&lt.searchPseudoElements&&s(e.target.parentNode),"attributes"===e.type&&An(e.target)&&~tt.indexOf(e.attributeName))if("class"===e.attributeName&&function(e){var t=e.getAttribute?e.getAttribute(je):null,n=e.getAttribute?e.getAttribute(De):null;return t&&n}(e.target)){var a=nn(ht(e.target)),r=a.prefix,o=a.iconName;e.target.setAttribute(je,r||t),o&&e.target.setAttribute(De,o)}else(l=e.target)&&l.classList&&l.classList.contains&&l.classList.contains(lt.replacementClass)&&i(e.target);var l}))}})),Pe&&$n.observe(l,{childList:!0,attributes:!0,characterData:!0,subtree:!0})}}function Vn(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{styleParser:!0},n=function(e){var t,n,a=e.getAttribute("data-prefix"),i=e.getAttribute("data-icon"),r=void 0!==e.innerText?e.innerText.trim():"",s=nn(ht(e));return s.prefix||(s.prefix=Qt()),a&&i&&(s.prefix=a,s.iconName=i),s.iconName&&s.prefix||(s.prefix&&r.length>0&&(s.iconName=(t=s.prefix,n=e.innerText,(Bt[t]||{})[n]||Ut(s.prefix,It(e.innerText)))),!s.iconName&&lt.autoFetchSvg&&e.firstChild&&e.firstChild.nodeType===Node.TEXT_NODE&&(s.iconName=e.firstChild.data)),s}(e),a=n.iconName,i=n.prefix,r=n.rest,s=function(e){var t=mt(e.attributes).reduce((function(e,t){return"class"!==e.name&&"style"!==e.name&&(e[t.name]=t.value),e}),{}),n=e.getAttribute("title"),a=e.getAttribute("data-fa-title-id");return lt.autoA11y&&(n?t["aria-labelledby"]="".concat(lt.replacementClass,"-title-").concat(a||pt()):(t["aria-hidden"]="true",t.focusable="false")),t}(e),o=cn("parseNodeAttributes",{},e),l=t.styleParser?function(e){var t=e.getAttribute("style"),n=[];return t&&(n=t.split(";").reduce((function(e,t){var n=t.split(":"),a=n[0],i=n.slice(1);return a&&i.length>0&&(e[a]=i.join(":").trim()),e}),{})),n}(e):[];return ae({iconName:a,title:e.getAttribute("title"),titleId:e.getAttribute("data-fa-title-id"),prefix:i,transform:ut,mask:{iconName:null,prefix:null,rest:[]},maskId:null,symbol:!1,extra:{classes:r,styles:l,attributes:s}},o)}var Yn=Et.styles;function Hn(e){var t="nest"===lt.autoReplaceSvg?Vn(e,{styleParser:!1}):Vn(e);return~t.extra.classes.indexOf(Ke)?un("generateLayersText",e,t):un("generateSvgReplacementMutation",e,t)}var Wn=new Set;function qn(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(!Pe)return Promise.resolve();var n=Te.documentElement.classList,a=function(e){return n.add("".concat(Ge,"-").concat(e))},i=function(e){return n.remove("".concat(Ge,"-").concat(e))},r=lt.autoFetchSvg?Wn:Ve.map((function(e){return"fa-".concat(e)})).concat(Object.keys(Yn));r.includes("fa")||r.push("fa");var s=[".".concat(Ke,":not([").concat(Le,"])")].concat(r.map((function(e){return".".concat(e,":not([").concat(Le,"])")}))).join(", ");if(0===s.length)return Promise.resolve();var o=[];try{o=mt(e.querySelectorAll(s))}catch(e){}if(!(o.length>0))return Promise.resolve();a("pending"),i("complete");var l=Mn.begin("onTree"),c=o.reduce((function(e,t){try{var n=Hn(t);n&&e.push(n)}catch(e){Re||"MissingIcon"===e.name&&console.error(e)}return e}),[]);return new Promise((function(e,n){Promise.all(c).then((function(n){Dn(n,(function(){a("active"),a("complete"),i("pending"),"function"==typeof t&&t(),l(),e()}))})).catch((function(e){l(),n(e)}))}))}function Xn(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;Hn(e).then((function(e){e&&Dn([e],t)}))}Ve.map((function(e){Wn.add("fa-".concat(e))})),Object.keys(He[$e]).map(Wn.add.bind(Wn)),Object.keys(He[Be]).map(Wn.add.bind(Wn)),Wn=le(Wn);var Un=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.transform,a=void 0===n?ut:n,i=t.symbol,r=void 0!==i&&i,s=t.mask,o=void 0===s?null:s,l=t.maskId,c=void 0===l?null:l,d=t.title,u=void 0===d?null:d,f=t.titleId,p=void 0===f?null:f,m=t.classes,h=void 0===m?[]:m,v=t.attributes,g=void 0===v?{}:v,b=t.styles,y=void 0===b?{}:b;if(e){var w=e.prefix,x=e.iconName,S=e.icon;return gn(ae({type:"icon"},e),(function(){return dn("beforeDOMElementCreation",{iconDefinition:e,params:t}),lt.autoA11y&&(u?g["aria-labelledby"]="".concat(lt.replacementClass,"-title-").concat(p||pt()):(g["aria-hidden"]="true",g.focusable="false")),bn({icons:{main:xn(S),mask:o?xn(o.icon):{found:!1,width:null,height:null,icon:{}}},prefix:w,iconName:x,transform:ae(ae({},ut),a),symbol:r,title:u,maskId:c,titleId:p,extra:{attributes:g,styles:y,classes:h}})}))}},Kn={mixout:function(){return{icon:(e=Un,function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=(t||{}).icon?t:fn(t||{}),i=n.mask;return i&&(i=(i||{}).icon?i:fn(i||{})),e(a,ae(ae({},n),{},{mask:i}))})};var e},hooks:function(){return{mutationObserverCallbacks:function(e){return e.treeCallback=qn,e.nodeCallback=Xn,e}}},provides:function(e){e.i2svg=function(e){var t=e.node,n=void 0===t?Te:t,a=e.callback;return qn(n,void 0===a?function(){}:a)},e.generateSvgReplacementMutation=function(e,t){var n=t.iconName,a=t.title,i=t.titleId,r=t.prefix,s=t.transform,o=t.symbol,l=t.mask,c=t.maskId,d=t.extra;return new Promise((function(t,u){Promise.all([kn(n,r),l.iconName?kn(l.iconName,l.prefix):Promise.resolve({found:!1,width:512,height:512,icon:{}})]).then((function(l){var u=oe(l,2),f=u[0],p=u[1];t([e,bn({icons:{main:f,mask:p},prefix:r,iconName:n,transform:s,symbol:o,maskId:c,title:a,titleId:i,extra:d,watchable:!0})])})).catch(u)}))},e.generateAbstractIcon=function(e){var t,n=e.children,a=e.attributes,i=e.main,r=e.transform,s=gt(e.styles);return s.length>0&&(a.style=s),bt(r)&&(t=un("generateAbstractTransformGrouping",{main:i,transform:r,containerWidth:i.width,iconWidth:i.width})),n.push(t||i.icon),{children:n,attributes:a}}}},Jn={mixout:function(){return{layer:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.classes,a=void 0===n?[]:n;return gn({type:"layer"},(function(){dn("beforeDOMElementCreation",{assembler:e,params:t});var n=[];return e((function(e){Array.isArray(e)?e.map((function(e){n=n.concat(e.abstract)})):n=n.concat(e.abstract)})),[{tag:"span",attributes:{class:["".concat(lt.cssPrefix,"-layers")].concat(le(a)).join(" ")},children:n}]}))}}}},Qn={mixout:function(){return{counter:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.title,a=void 0===n?null:n,i=t.classes,r=void 0===i?[]:i,s=t.attributes,o=void 0===s?{}:s,l=t.styles,c=void 0===l?{}:l;return gn({type:"counter",content:e},(function(){return dn("beforeDOMElementCreation",{content:e,params:t}),function(e){var t=e.content,n=e.title,a=e.extra,i=ae(ae(ae({},a.attributes),n?{title:n}:{}),{},{class:a.classes.join(" ")}),r=gt(a.styles);r.length>0&&(i.style=r);var s=[];return s.push({tag:"span",attributes:i,children:[t]}),n&&s.push({tag:"span",attributes:{class:"sr-only"},children:[n]}),s}({content:e.toString(),title:a,extra:{attributes:o,styles:c,classes:["".concat(lt.cssPrefix,"-layers-counter")].concat(le(r))}})}))}}}},Zn={mixout:function(){return{text:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.transform,a=void 0===n?ut:n,i=t.title,r=void 0===i?null:i,s=t.classes,o=void 0===s?[]:s,l=t.attributes,c=void 0===l?{}:l,d=t.styles,u=void 0===d?{}:d;return gn({type:"text",content:e},(function(){return dn("beforeDOMElementCreation",{content:e,params:t}),yn({content:e,transform:ae(ae({},ut),a),title:r,extra:{attributes:c,styles:u,classes:["".concat(lt.cssPrefix,"-layers-text")].concat(le(o))}})}))}}},provides:function(e){e.generateLayersText=function(e,t){var n=t.title,a=t.transform,i=t.extra,r=null,s=null;if(Me){var o=parseInt(getComputedStyle(e).fontSize,10),l=e.getBoundingClientRect();r=l.width/o,s=l.height/o}return lt.autoA11y&&!n&&(i.attributes["aria-hidden"]="true"),Promise.resolve([e,yn({content:e.innerHTML,width:r,height:s,transform:a,title:n,extra:i,watchable:!0})])}}},ea=new RegExp('"',"ug"),ta=[1105920,1112319];function na(e,t){var n="".concat(Ne).concat(t.replace(":","-"));return new Promise((function(a,i){if(null!==e.getAttribute(n))return a();var r,s,o,l=mt(e.children).filter((function(e){return e.getAttribute(ze)===t}))[0],c=ke.getComputedStyle(e,t),d=c.getPropertyValue("font-family").match(Je),u=c.getPropertyValue("font-weight"),f=c.getPropertyValue("content");if(l&&!d)return e.removeChild(l),a();if(d&&"none"!==f&&""!==f){var p=c.getPropertyValue("content"),m=~["Sharp"].indexOf(d[2])?Be:$e,h=~["Solid","Regular","Light","Thin","Duotone","Brands","Kit"].indexOf(d[2])?We[m][d[2].toLowerCase()]:Qe[m][u],v=function(e){var t,n,a,i,r=e.replace(ea,""),s=(a=(t=r).length,(i=t.charCodeAt(0))>=55296&&i<=56319&&a>1&&(n=t.charCodeAt(1))>=56320&&n<=57343?1024*(i-55296)+n-56320+65536:i),o=s>=ta[0]&&s<=ta[1],l=2===r.length&&r[0]===r[1];return{value:It(l?r[0]:r),isSecondary:o||l}}(p),g=v.value,b=v.isSecondary,y=d[0].startsWith("FontAwesome"),w=Ut(h,g),x=w;if(y){var S=(s=Yt[r=g],o=Ut("fas",r),s||(o?{prefix:"fas",iconName:o}:null)||{prefix:null,iconName:null});S.iconName&&S.prefix&&(w=S.iconName,h=S.prefix)}if(!w||b||l&&l.getAttribute(je)===h&&l.getAttribute(De)===x)a();else{e.setAttribute(n,x),l&&e.removeChild(l);var k={iconName:null,title:null,titleId:null,prefix:null,transform:ut,symbol:!1,mask:{iconName:null,prefix:null,rest:[]},maskId:null,extra:{classes:[],styles:{},attributes:{}}},T=k.extra;T.attributes[ze]=t,kn(w,h).then((function(i){var r=bn(ae(ae({},k),{},{icons:{main:i,mask:Zt()},prefix:h,iconName:x,extra:T,watchable:!0})),s=Te.createElementNS("http://www.w3.org/2000/svg","svg");"::before"===t?e.insertBefore(s,e.firstChild):e.appendChild(s),s.outerHTML=r.map((function(e){return Mt(e)})).join("\n"),e.removeAttribute(n),a()})).catch(i)}}else a()}))}function aa(e){return Promise.all([na(e,"::before"),na(e,"::after")])}function ia(e){return!(e.parentNode===document.head||~Fe.indexOf(e.tagName.toUpperCase())||e.getAttribute(ze)||e.parentNode&&"svg"===e.parentNode.tagName)}function ra(e){if(Pe)return new Promise((function(t,n){var a=mt(e.querySelectorAll("*")).filter(ia).map(aa),i=Mn.begin("searchPseudoElements");Fn(),Promise.all(a).then((function(){i(),Rn(),t()})).catch((function(){i(),Rn(),n()}))}))}var sa=!1,oa=function(e){return e.toLowerCase().split(" ").reduce((function(e,t){var n=t.toLowerCase().split("-"),a=n[0],i=n.slice(1).join("-");if(a&&"h"===i)return e.flipX=!0,e;if(a&&"v"===i)return e.flipY=!0,e;if(i=parseFloat(i),isNaN(i))return e;switch(a){case"grow":e.size=e.size+i;break;case"shrink":e.size=e.size-i;break;case"left":e.x=e.x-i;break;case"right":e.x=e.x+i;break;case"up":e.y=e.y-i;break;case"down":e.y=e.y+i;break;case"rotate":e.rotate=e.rotate+i}return e}),{size:16,x:0,y:0,flipX:!1,flipY:!1,rotate:0})},la={mixout:function(){return{parse:{transform:function(e){return oa(e)}}}},hooks:function(){return{parseNodeAttributes:function(e,t){var n=t.getAttribute("data-fa-transform");return n&&(e.transform=oa(n)),e}}},provides:function(e){e.generateAbstractTransformGrouping=function(e){var t=e.main,n=e.transform,a=e.containerWidth,i=e.iconWidth,r={transform:"translate(".concat(a/2," 256)")},s="translate(".concat(32*n.x,", ").concat(32*n.y,") "),o="scale(".concat(n.size/16*(n.flipX?-1:1),", ").concat(n.size/16*(n.flipY?-1:1),") "),l="rotate(".concat(n.rotate," 0 0)"),c={outer:r,inner:{transform:"".concat(s," ").concat(o," ").concat(l)},path:{transform:"translate(".concat(i/2*-1," -256)")}};return{tag:"g",attributes:ae({},c.outer),children:[{tag:"g",attributes:ae({},c.inner),children:[{tag:t.icon.tag,children:t.icon.children,attributes:ae(ae({},t.icon.attributes),c.path)}]}]}}}},ca={x:0,y:0,width:"100%",height:"100%"};function da(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return e.attributes&&(e.attributes.fill||t)&&(e.attributes.fill="black"),e}var ua,fa={hooks:function(){return{parseNodeAttributes:function(e,t){var n=t.getAttribute("data-fa-mask"),a=n?nn(n.split(" ").map((function(e){return e.trim()}))):Zt();return a.prefix||(a.prefix=Qt()),e.mask=a,e.maskId=t.getAttribute("data-fa-mask-id"),e}}},provides:function(e){e.generateAbstractMask=function(e){var t,n=e.children,a=e.attributes,i=e.main,r=e.mask,s=e.maskId,o=e.transform,l=i.width,c=i.icon,d=r.width,u=r.icon,f=function(e){var t=e.transform,n=e.iconWidth,a={transform:"translate(".concat(e.containerWidth/2," 256)")},i="translate(".concat(32*t.x,", ").concat(32*t.y,") "),r="scale(".concat(t.size/16*(t.flipX?-1:1),", ").concat(t.size/16*(t.flipY?-1:1),") "),s="rotate(".concat(t.rotate," 0 0)");return{outer:a,inner:{transform:"".concat(i," ").concat(r," ").concat(s)},path:{transform:"translate(".concat(n/2*-1," -256)")}}}({transform:o,containerWidth:d,iconWidth:l}),p={tag:"rect",attributes:ae(ae({},ca),{},{fill:"white"})},m=c.children?{children:c.children.map(da)}:{},h={tag:"g",attributes:ae({},f.inner),children:[da(ae({tag:c.tag,attributes:ae(ae({},c.attributes),f.path)},m))]},v={tag:"g",attributes:ae({},f.outer),children:[h]},g="mask-".concat(s||pt()),b="clip-".concat(s||pt()),y={tag:"mask",attributes:ae(ae({},ca),{},{id:g,maskUnits:"userSpaceOnUse",maskContentUnits:"userSpaceOnUse"}),children:[p,v]},w={tag:"defs",children:[{tag:"clipPath",attributes:{id:b},children:(t=u,"g"===t.tag?t.children:[t])},y]};return n.push(w,{tag:"rect",attributes:ae({fill:"currentColor","clip-path":"url(#".concat(b,")"),mask:"url(#".concat(g,")")},ca)}),{children:n,attributes:a}}}},pa={provides:function(e){var t=!1;ke.matchMedia&&(t=ke.matchMedia("(prefers-reduced-motion: reduce)").matches),e.missingIconAbstract=function(){var e=[],n={fill:"currentColor"},a={attributeType:"XML",repeatCount:"indefinite",dur:"2s"};e.push({tag:"path",attributes:ae(ae({},n),{},{d:"M156.5,447.7l-12.6,29.5c-18.7-9.5-35.9-21.2-51.5-34.9l22.7-22.7C127.6,430.5,141.5,440,156.5,447.7z M40.6,272H8.5 c1.4,21.2,5.4,41.7,11.7,61.1L50,321.2C45.1,305.5,41.8,289,40.6,272z M40.6,240c1.4-18.8,5.2-37,11.1-54.1l-29.5-12.6 C14.7,194.3,10,216.7,8.5,240H40.6z M64.3,156.5c7.8-14.9,17.2-28.8,28.1-41.5L69.7,92.3c-13.7,15.6-25.5,32.8-34.9,51.5 L64.3,156.5z M397,419.6c-13.9,12-29.4,22.3-46.1,30.4l11.9,29.8c20.7-9.9,39.8-22.6,56.9-37.6L397,419.6z M115,92.4 c13.9-12,29.4-22.3,46.1-30.4l-11.9-29.8c-20.7,9.9-39.8,22.6-56.8,37.6L115,92.4z M447.7,355.5c-7.8,14.9-17.2,28.8-28.1,41.5 l22.7,22.7c13.7-15.6,25.5-32.9,34.9-51.5L447.7,355.5z M471.4,272c-1.4,18.8-5.2,37-11.1,54.1l29.5,12.6 c7.5-21.1,12.2-43.5,13.6-66.8H471.4z M321.2,462c-15.7,5-32.2,8.2-49.2,9.4v32.1c21.2-1.4,41.7-5.4,61.1-11.7L321.2,462z M240,471.4c-18.8-1.4-37-5.2-54.1-11.1l-12.6,29.5c21.1,7.5,43.5,12.2,66.8,13.6V471.4z M462,190.8c5,15.7,8.2,32.2,9.4,49.2h32.1 c-1.4-21.2-5.4-41.7-11.7-61.1L462,190.8z M92.4,397c-12-13.9-22.3-29.4-30.4-46.1l-29.8,11.9c9.9,20.7,22.6,39.8,37.6,56.9 L92.4,397z M272,40.6c18.8,1.4,36.9,5.2,54.1,11.1l12.6-29.5C317.7,14.7,295.3,10,272,8.5V40.6z M190.8,50 c15.7-5,32.2-8.2,49.2-9.4V8.5c-21.2,1.4-41.7,5.4-61.1,11.7L190.8,50z M442.3,92.3L419.6,115c12,13.9,22.3,29.4,30.5,46.1 l29.8-11.9C470,128.5,457.3,109.4,442.3,92.3z M397,92.4l22.7-22.7c-15.6-13.7-32.8-25.5-51.5-34.9l-12.6,29.5 C370.4,72.1,384.4,81.5,397,92.4z"})});var i=ae(ae({},a),{},{attributeName:"opacity"}),r={tag:"circle",attributes:ae(ae({},n),{},{cx:"256",cy:"364",r:"28"}),children:[]};return t||r.children.push({tag:"animate",attributes:ae(ae({},a),{},{attributeName:"r",values:"28;14;28;28;14;28;"})},{tag:"animate",attributes:ae(ae({},i),{},{values:"1;0;1;1;0;1;"})}),e.push(r),e.push({tag:"path",attributes:ae(ae({},n),{},{opacity:"1",d:"M263.7,312h-16c-6.6,0-12-5.4-12-12c0-71,77.4-63.9,77.4-107.8c0-20-17.8-40.2-57.4-40.2c-29.1,0-44.3,9.6-59.2,28.7 c-3.9,5-11.1,6-16.2,2.4l-13.1-9.2c-5.6-3.9-6.9-11.8-2.6-17.2c21.2-27.2,46.4-44.7,91.2-44.7c52.3,0,97.4,29.8,97.4,80.2 c0,67.6-77.4,63.5-77.4,107.8C275.7,306.6,270.3,312,263.7,312z"}),children:t?[]:[{tag:"animate",attributes:ae(ae({},i),{},{values:"1;0;0;0;0;1;"})}]}),t||e.push({tag:"path",attributes:ae(ae({},n),{},{opacity:"0",d:"M232.5,134.5l7,168c0.3,6.4,5.6,11.5,12,11.5h9c6.4,0,11.7-5.1,12-11.5l7-168c0.3-6.8-5.2-12.5-12-12.5h-23 C237.7,122,232.2,127.7,232.5,134.5z"}),children:[{tag:"animate",attributes:ae(ae({},i),{},{values:"0;0;1;1;0;0;"})}]}),{tag:"g",attributes:{class:"missing"},children:e}}}};ua={mixoutsTo:hn}.mixoutsTo,rn=[kt,Kn,Jn,Qn,Zn,{hooks:function(){return{mutationObserverCallbacks:function(e){return e.pseudoElementsCallback=ra,e}}},provides:function(e){e.pseudoElements2svg=function(e){var t=e.node,n=void 0===t?Te:t;lt.searchPseudoElements&&ra(n)}}},{mixout:function(){return{dom:{unwatch:function(){Fn(),sa=!0}}}},hooks:function(){return{bootstrap:function(){Bn(cn("mutationObserverCallbacks",{}))},noAuto:function(){$n&&$n.disconnect()},watch:function(e){var t=e.observeMutationsRoot;sa?Rn():Bn(cn("mutationObserverCallbacks",{observeMutationsRoot:t}))}}}},la,fa,pa,{hooks:function(){return{parseNodeAttributes:function(e,t){var n=t.getAttribute("data-fa-symbol"),a=null!==n&&(""===n||n);return e.symbol=a,e}}}}],sn={},Object.keys(on).forEach((function(e){-1===ln.indexOf(e)&&delete on[e]})),rn.forEach((function(e){var t=e.mixout?e.mixout():{};if(Object.keys(t).forEach((function(e){"function"==typeof t[e]&&(ua[e]=t[e]),"object"===ie(t[e])&&Object.keys(t[e]).forEach((function(n){ua[e]||(ua[e]={}),ua[e][n]=t[e][n]}))})),e.hooks){var n=e.hooks();Object.keys(n).forEach((function(e){sn[e]||(sn[e]=[]),sn[e].push(n[e])}))}e.provides&&e.provides(on)}));var ma=hn.parse,ha=hn.icon,va=n(5556),ga=n.n(va);function ba(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function ya(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ba(Object(n),!0).forEach((function(t){xa(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ba(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function wa(e){return wa="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},wa(e)}function xa(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Sa(e){return function(e){if(Array.isArray(e))return ka(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return ka(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ka(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ka(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}function Ta(e){return t=e,(t-=0)==t?e:(e=e.replace(/[\-_\s]+(.)?/g,(function(e,t){return t?t.toUpperCase():""}))).substr(0,1).toLowerCase()+e.substr(1);var t}var Ea=["style"],Ca=!1;try{Ca=!0}catch(e){}function Pa(e){return e&&"object"===wa(e)&&e.prefix&&e.iconName&&e.icon?e:ma.icon?ma.icon(e):null===e?null:e&&"object"===wa(e)&&e.prefix&&e.iconName?e:Array.isArray(e)&&2===e.length?{prefix:e[0],iconName:e[1]}:"string"==typeof e?{prefix:"fas",iconName:e}:void 0}function Ma(e,t){return Array.isArray(t)&&t.length>0||!Array.isArray(t)&&t?xa({},e,t):{}}var Oa={border:!1,className:"",mask:null,maskId:null,fixedWidth:!1,inverse:!1,flip:!1,icon:null,listItem:!1,pull:null,pulse:!1,rotation:null,size:null,spin:!1,spinPulse:!1,spinReverse:!1,beat:!1,fade:!1,beatFade:!1,bounce:!1,shake:!1,symbol:!1,title:"",titleId:null,transform:null,swapOpacity:!1},Aa=t().forwardRef((function(e,t){var n=ya(ya({},Oa),e),a=n.icon,i=n.mask,r=n.symbol,s=n.className,o=n.title,l=n.titleId,c=n.maskId,d=Pa(a),u=Ma("classes",[].concat(Sa(function(e){var t,n=e.beat,a=e.fade,i=e.beatFade,r=e.bounce,s=e.shake,o=e.flash,l=e.spin,c=e.spinPulse,d=e.spinReverse,u=e.pulse,f=e.fixedWidth,p=e.inverse,m=e.border,h=e.listItem,v=e.flip,g=e.size,b=e.rotation,y=e.pull,w=(xa(t={"fa-beat":n,"fa-fade":a,"fa-beat-fade":i,"fa-bounce":r,"fa-shake":s,"fa-flash":o,"fa-spin":l,"fa-spin-reverse":d,"fa-spin-pulse":c,"fa-pulse":u,"fa-fw":f,"fa-inverse":p,"fa-border":m,"fa-li":h,"fa-flip":!0===v,"fa-flip-horizontal":"horizontal"===v||"both"===v,"fa-flip-vertical":"vertical"===v||"both"===v},"fa-".concat(g),null!=g),xa(t,"fa-rotate-".concat(b),null!=b&&0!==b),xa(t,"fa-pull-".concat(y),null!=y),xa(t,"fa-swap-opacity",e.swapOpacity),t);return Object.keys(w).map((function(e){return w[e]?e:null})).filter((function(e){return e}))}(n)),Sa((s||"").split(" ")))),f=Ma("transform","string"==typeof n.transform?ma.transform(n.transform):n.transform),p=Ma("mask",Pa(i)),m=ha(d,ya(ya(ya(ya({},u),f),p),{},{symbol:r,title:o,titleId:l,maskId:c}));if(!m)return function(){var e;!Ca&&console&&"function"==typeof console.error&&(e=console).error.apply(e,arguments)}("Could not find icon",d),null;var h=m.abstract,v={ref:t};return Object.keys(n).forEach((function(e){Oa.hasOwnProperty(e)||(v[e]=n[e])})),Ia(h[0],v)}));Aa.displayName="FontAwesomeIcon",Aa.propTypes={beat:ga().bool,border:ga().bool,beatFade:ga().bool,bounce:ga().bool,className:ga().string,fade:ga().bool,flash:ga().bool,mask:ga().oneOfType([ga().object,ga().array,ga().string]),maskId:ga().string,fixedWidth:ga().bool,inverse:ga().bool,flip:ga().oneOf([!0,!1,"horizontal","vertical","both"]),icon:ga().oneOfType([ga().object,ga().array,ga().string]),listItem:ga().bool,pull:ga().oneOf(["right","left"]),pulse:ga().bool,rotation:ga().oneOf([0,90,180,270]),shake:ga().bool,size:ga().oneOf(["2xs","xs","sm","lg","xl","2xl","1x","2x","3x","4x","5x","6x","7x","8x","9x","10x"]),spin:ga().bool,spinPulse:ga().bool,spinReverse:ga().bool,symbol:ga().oneOfType([ga().bool,ga().string]),title:ga().string,titleId:ga().string,transform:ga().oneOfType([ga().string,ga().object]),swapOpacity:ga().bool};var Ia=function e(t,n){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if("string"==typeof n)return n;var i=(n.children||[]).map((function(n){return e(t,n)})),r=Object.keys(n.attributes||{}).reduce((function(e,t){var a=n.attributes[t];switch(t){case"class":e.attrs.className=a,delete n.attributes.class;break;case"style":e.attrs.style=a.split(";").map((function(e){return e.trim()})).filter((function(e){return e})).reduce((function(e,t){var n,a=t.indexOf(":"),i=Ta(t.slice(0,a)),r=t.slice(a+1).trim();return i.startsWith("webkit")?e[(n=i,n.charAt(0).toUpperCase()+n.slice(1))]=r:e[i]=r,e}),{});break;default:0===t.indexOf("aria-")||0===t.indexOf("data-")?e.attrs[t.toLowerCase()]=a:e.attrs[Ta(t)]=a}return e}),{attrs:{}}),s=a.style,o=void 0===s?{}:s,l=function(e,t){if(null==e)return{};var n,a,i=function(e,t){if(null==e)return{};var n,a,i={},r=Object.keys(e);for(a=0;a<r.length;a++)n=r[a],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(a=0;a<r.length;a++)n=r[a],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}(a,Ea);return r.attrs.style=ya(ya({},r.attrs.style),o),t.apply(void 0,[n.tag,ya(ya({},r.attrs),l)].concat(Sa(i)))}.bind(null,t().createElement),La={prefix:"fas",iconName:"user",icon:[448,512,[128100,62144],"f007","M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-45.7 48C79.8 304 0 383.8 0 482.3C0 498.7 13.3 512 29.7 512H418.3c16.4 0 29.7-13.3 29.7-29.7C448 383.8 368.2 304 269.7 304H178.3z"]};new Z(".swiper",{modules:[function(e){let{swiper:t,extendParams:n,on:a,emit:i}=e;const r="swiper-pagination";let s;n({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:e=>e,formatFractionTotal:e=>e,bulletClass:`${r}-bullet`,bulletActiveClass:`${r}-bullet-active`,modifierClass:`${r}-`,currentClass:`${r}-current`,totalClass:`${r}-total`,hiddenClass:`${r}-hidden`,progressbarFillClass:`${r}-progressbar-fill`,progressbarOppositeClass:`${r}-progressbar-opposite`,clickableClass:`${r}-clickable`,lockClass:`${r}-lock`,horizontalClass:`${r}-horizontal`,verticalClass:`${r}-vertical`,paginationDisabledClass:`${r}-disabled`}}),t.pagination={el:null,bullets:[]};let o=0;function l(){return!t.params.pagination.el||!t.pagination.el||Array.isArray(t.pagination.el)&&0===t.pagination.el.length}function c(e,n){const{bulletActiveClass:a}=t.params.pagination;e&&(e=e[("prev"===n?"previous":"next")+"ElementSibling"])&&(e.classList.add(`${a}-${n}`),(e=e[("prev"===n?"previous":"next")+"ElementSibling"])&&e.classList.add(`${a}-${n}-${n}`))}function d(e){const n=e.target.closest(ee(t.params.pagination.bulletClass));if(!n)return;e.preventDefault();const a=y(n)*t.params.slidesPerGroup;if(t.params.loop){if(t.realIndex===a)return;t.slideToLoop(a)}else t.slideTo(a)}function u(){const e=t.rtl,n=t.params.pagination;if(l())return;let a,r,d=t.pagination.el;d=S(d);const u=t.virtual&&t.params.virtual.enabled?t.virtual.slides.length:t.slides.length,f=t.params.loop?Math.ceil(u/t.params.slidesPerGroup):t.snapGrid.length;if(t.params.loop?(r=t.previousRealIndex||0,a=t.params.slidesPerGroup>1?Math.floor(t.realIndex/t.params.slidesPerGroup):t.realIndex):void 0!==t.snapIndex?(a=t.snapIndex,r=t.previousSnapIndex):(r=t.previousIndex||0,a=t.activeIndex||0),"bullets"===n.type&&t.pagination.bullets&&t.pagination.bullets.length>0){const i=t.pagination.bullets;let l,u,f;if(n.dynamicBullets&&(s=x(i[0],t.isHorizontal()?"width":"height",!0),d.forEach((e=>{e.style[t.isHorizontal()?"width":"height"]=s*(n.dynamicMainBullets+4)+"px"})),n.dynamicMainBullets>1&&void 0!==r&&(o+=a-(r||0),o>n.dynamicMainBullets-1?o=n.dynamicMainBullets-1:o<0&&(o=0)),l=Math.max(a-o,0),u=l+(Math.min(i.length,n.dynamicMainBullets)-1),f=(u+l)/2),i.forEach((e=>{const t=[...["","-next","-next-next","-prev","-prev-prev","-main"].map((e=>`${n.bulletActiveClass}${e}`))].map((e=>"string"==typeof e&&e.includes(" ")?e.split(" "):e)).flat();e.classList.remove(...t)})),d.length>1)i.forEach((e=>{const i=y(e);i===a?e.classList.add(...n.bulletActiveClass.split(" ")):t.isElement&&e.setAttribute("part","bullet"),n.dynamicBullets&&(i>=l&&i<=u&&e.classList.add(...`${n.bulletActiveClass}-main`.split(" ")),i===l&&c(e,"prev"),i===u&&c(e,"next"))}));else{const e=i[a];if(e&&e.classList.add(...n.bulletActiveClass.split(" ")),t.isElement&&i.forEach(((e,t)=>{e.setAttribute("part",t===a?"bullet-active":"bullet")})),n.dynamicBullets){const e=i[l],t=i[u];for(let e=l;e<=u;e+=1)i[e]&&i[e].classList.add(...`${n.bulletActiveClass}-main`.split(" "));c(e,"prev"),c(t,"next")}}if(n.dynamicBullets){const a=Math.min(i.length,n.dynamicMainBullets+4),r=(s*a-s)/2-f*s,o=e?"right":"left";i.forEach((e=>{e.style[t.isHorizontal()?o:"top"]=`${r}px`}))}}d.forEach(((e,r)=>{if("fraction"===n.type&&(e.querySelectorAll(ee(n.currentClass)).forEach((e=>{e.textContent=n.formatFractionCurrent(a+1)})),e.querySelectorAll(ee(n.totalClass)).forEach((e=>{e.textContent=n.formatFractionTotal(f)}))),"progressbar"===n.type){let i;i=n.progressbarOpposite?t.isHorizontal()?"vertical":"horizontal":t.isHorizontal()?"horizontal":"vertical";const r=(a+1)/f;let s=1,o=1;"horizontal"===i?s=r:o=r,e.querySelectorAll(ee(n.progressbarFillClass)).forEach((e=>{e.style.transform=`translate3d(0,0,0) scaleX(${s}) scaleY(${o})`,e.style.transitionDuration=`${t.params.speed}ms`}))}"custom"===n.type&&n.renderCustom?(e.innerHTML=n.renderCustom(t,a+1,f),0===r&&i("paginationRender",e)):(0===r&&i("paginationRender",e),i("paginationUpdate",e)),t.params.watchOverflow&&t.enabled&&e.classList[t.isLocked?"add":"remove"](n.lockClass)}))}function f(){const e=t.params.pagination;if(l())return;const n=t.virtual&&t.params.virtual.enabled?t.virtual.slides.length:t.grid&&t.params.grid.rows>1?t.slides.length/Math.ceil(t.params.grid.rows):t.slides.length;let a=t.pagination.el;a=S(a);let r="";if("bullets"===e.type){let a=t.params.loop?Math.ceil(n/t.params.slidesPerGroup):t.snapGrid.length;t.params.freeMode&&t.params.freeMode.enabled&&a>n&&(a=n);for(let n=0;n<a;n+=1)e.renderBullet?r+=e.renderBullet.call(t,n,e.bulletClass):r+=`<${e.bulletElement} ${t.isElement?'part="bullet"':""} class="${e.bulletClass}"></${e.bulletElement}>`}"fraction"===e.type&&(r=e.renderFraction?e.renderFraction.call(t,e.currentClass,e.totalClass):`<span class="${e.currentClass}"></span> / <span class="${e.totalClass}"></span>`),"progressbar"===e.type&&(r=e.renderProgressbar?e.renderProgressbar.call(t,e.progressbarFillClass):`<span class="${e.progressbarFillClass}"></span>`),t.pagination.bullets=[],a.forEach((n=>{"custom"!==e.type&&(n.innerHTML=r||""),"bullets"===e.type&&t.pagination.bullets.push(...n.querySelectorAll(ee(e.bulletClass)))})),"custom"!==e.type&&i("paginationRender",a[0])}function p(){t.params.pagination=function(e,t,n,a){return e.params.createElements&&Object.keys(a).forEach((i=>{if(!n[i]&&!0===n.auto){let r=h(e.el,`.${a[i]}`)[0];r||(r=g("div",a[i]),r.className=a[i],e.el.append(r)),n[i]=r,t[i]=r}})),n}(t,t.originalParams.pagination,t.params.pagination,{el:"swiper-pagination"});const e=t.params.pagination;if(!e.el)return;let n;"string"==typeof e.el&&t.isElement&&(n=t.el.querySelector(e.el)),n||"string"!=typeof e.el||(n=[...document.querySelectorAll(e.el)]),n||(n=e.el),n&&0!==n.length&&(t.params.uniqueNavElements&&"string"==typeof e.el&&Array.isArray(n)&&n.length>1&&(n=[...t.el.querySelectorAll(e.el)],n.length>1&&(n=n.filter((e=>w(e,".swiper")[0]===t.el))[0])),Array.isArray(n)&&1===n.length&&(n=n[0]),Object.assign(t.pagination,{el:n}),n=S(n),n.forEach((n=>{"bullets"===e.type&&e.clickable&&n.classList.add(...(e.clickableClass||"").split(" ")),n.classList.add(e.modifierClass+e.type),n.classList.add(t.isHorizontal()?e.horizontalClass:e.verticalClass),"bullets"===e.type&&e.dynamicBullets&&(n.classList.add(`${e.modifierClass}${e.type}-dynamic`),o=0,e.dynamicMainBullets<1&&(e.dynamicMainBullets=1)),"progressbar"===e.type&&e.progressbarOpposite&&n.classList.add(e.progressbarOppositeClass),e.clickable&&n.addEventListener("click",d),t.enabled||n.classList.add(e.lockClass)})))}function m(){const e=t.params.pagination;if(l())return;let n=t.pagination.el;n&&(n=S(n),n.forEach((n=>{n.classList.remove(e.hiddenClass),n.classList.remove(e.modifierClass+e.type),n.classList.remove(t.isHorizontal()?e.horizontalClass:e.verticalClass),e.clickable&&(n.classList.remove(...(e.clickableClass||"").split(" ")),n.removeEventListener("click",d))}))),t.pagination.bullets&&t.pagination.bullets.forEach((t=>t.classList.remove(...e.bulletActiveClass.split(" "))))}a("changeDirection",(()=>{if(!t.pagination||!t.pagination.el)return;const e=t.params.pagination;let{el:n}=t.pagination;n=S(n),n.forEach((n=>{n.classList.remove(e.horizontalClass,e.verticalClass),n.classList.add(t.isHorizontal()?e.horizontalClass:e.verticalClass)}))})),a("init",(()=>{!1===t.params.pagination.enabled?v():(p(),f(),u())})),a("activeIndexChange",(()=>{void 0===t.snapIndex&&u()})),a("snapIndexChange",(()=>{u()})),a("snapGridLengthChange",(()=>{f(),u()})),a("destroy",(()=>{m()})),a("enable disable",(()=>{let{el:e}=t.pagination;e&&(e=S(e),e.forEach((e=>e.classList[t.enabled?"remove":"add"](t.params.pagination.lockClass))))})),a("lock unlock",(()=>{u()})),a("click",((e,n)=>{const a=n.target,r=S(t.pagination.el);if(t.params.pagination.el&&t.params.pagination.hideOnClick&&r&&r.length>0&&!a.classList.contains(t.params.pagination.bulletClass)){if(t.navigation&&(t.navigation.nextEl&&a===t.navigation.nextEl||t.navigation.prevEl&&a===t.navigation.prevEl))return;const e=r[0].classList.contains(t.params.pagination.hiddenClass);i(!0===e?"paginationShow":"paginationHide"),r.forEach((e=>e.classList.toggle(t.params.pagination.hiddenClass)))}}));const v=()=>{t.el.classList.add(t.params.pagination.paginationDisabledClass);let{el:e}=t.pagination;e&&(e=S(e),e.forEach((e=>e.classList.add(t.params.pagination.paginationDisabledClass)))),m()};Object.assign(t.pagination,{enable:()=>{t.el.classList.remove(t.params.pagination.paginationDisabledClass);let{el:e}=t.pagination;e&&(e=S(e),e.forEach((e=>e.classList.remove(t.params.pagination.paginationDisabledClass)))),p(),f(),u()},disable:v,render:f,update:u,init:p,destroy:m})}],grabCursor:!0,keyboard:!0,slidesPerView:1,initialSlide:0,freeMode:!0,slidesPerColumn:1,pagination:{el:".wp-block-masterstudy-testimonials__swiper .swiper-pagination",clickable:!0,renderBullet(t,n){const a=masterstudyTestimonialsSlides||[];if(a[t]){const i=a[t]?a[t].imgUrl:"";return(0,te.renderToString)((0,e.createElement)(e.Fragment,null,(0,e.createElement)("span",{className:n,style:{display:"flex"}},i&&(0,e.createElement)("img",{src:i,alt:`reviewer-avatar-${t}`}),!i&&(0,e.createElement)(Aa,{icon:La})),1===a.length&&(0,e.createElement)("span",{className:n,style:{display:"none"}})))}return`<span class="${n}"></span>`}}})})()})();