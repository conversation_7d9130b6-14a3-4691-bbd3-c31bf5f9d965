(()=>{var e,t={1720:(e,t,n)=>{"use strict";const a=window.React,l=window.wp.blocks,r=window.wp.blockEditor;var s=n(6942),o=n.n(s);const i=window.wp.i18n,c=[["core/group",{className:"lms-courses-group-header",layout:{type:"flex",orientation:"vertical",justifyContent:"stretch"},templateLock:!0},[["core/group",{layout:{type:"constrained"},templateLock:"insert"},[["core/heading",{textAlign:"center",style:{typography:{fontSize:"48px"},color:{text:"#001931"}},content:i.__("Courses Archive","masterstudy-lms-learning-management-system"),placeholder:i.__("Courses Container Title","masterstudy-lms-learning-management-system")}]]],["core/group",{layout:{type:"constrained"},templateLock:"insert"},[["masterstudy/courses-tab-options"]]]]],["core/group",{className:"lms-courses-group-presets",templateLock:"all",allowedBlocks:["masterstudy/courses-filter","masterstudy/courses-preset"]},[["masterstudy/archive-container-columns"]]]],m=window.wp.element,d=(0,m.createContext)(null),u=({children:e,...t})=>(0,a.createElement)(d.Provider,{value:{...t}},e),p=window.wp.components,h=({condition:e,fallback:t=null,children:n})=>(0,a.createElement)(a.Fragment,null,e?n:t),g=(e,t)=>{if(typeof e!=typeof t)return!1;if(Number.isNaN(e)&&Number.isNaN(t))return!0;if("object"!=typeof e||null===e||null===t)return e===t;if(Object.keys(e).length!==Object.keys(t).length)return!1;if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;const n=e.slice().sort(),a=t.slice().sort();return n.every(((e,t)=>g(e,a[t])))}for(const n of Object.keys(e))if(!g(e[n],t[n]))return!1;return!0};let v=function(e){return e.ALL="all",e.SOME="some",e}({}),y=function(e){return e.NORMAL="Normal",e.HOVER="Hover",e.ACTIVE="Active",e.FOCUS="Focus",e}({}),_=function(e){return e.DESKTOP="Desktop",e.TABLET="Tablet",e.MOBILE="Mobile",e}({}),b=function(e){return e.TOP_lEFT="top-left",e.TOP_CENTER="top-center",e.TOP_RIGHT="top-right",e.BOTTOM_lEFT="bottom-left",e.BOTTOM_CENTER="bottom-center",e.BOTTOM_RIGHT="bottom-right",e}({});const E=["",null,void 0,"null","undefined"],C=[".jpg",".jpeg",".png",".gif"],f=e=>E.includes(e),N=(e,t,n="")=>{const a=e[t];return"object"==typeof a&&null!==a?((e,t)=>{return n=e,Object.values(n).every((e=>E.includes(e)))?null:((e,t="")=>{const n=Object.entries(e).reduce(((e,[n,a])=>(e[n]=(a||"0")+t,e)),{});return`${n.top} ${n.right} ${n.bottom} ${n.left}`})(e,t);var n})(a,n):((e,t)=>f(e)?e:(e=>{if("string"!=typeof e)return!1;const t=e.slice(e.lastIndexOf("."));return C.includes(t)||e.startsWith("blob")})(e)?`url('${e}')`:String(e+t))(a,n)},w=(e,t,n)=>{const a={};return n.forEach((({isAdaptive:n,hasHover:l,unit:r},s)=>{if(t.hasOwnProperty(s)){const{unitMeasureDesktop:i,unitMeasureTablet:c,unitMeasureMobile:m}=((e,t)=>{var n;return{unitMeasureDesktop:null!==(n=e[t])&&void 0!==n?n:"",unitMeasureTablet:t?e[t+"Tablet"]:"",unitMeasureMobile:t?e[t+"Mobile"]:""}})(t,r);if(n&&l){const{desktopHoverPropertyName:n,mobileHoverPropertyName:l,tabletHoverPropertyName:r}=(e=>{const t=e+y.HOVER;return{desktopHoverPropertyName:t,tabletHoverPropertyName:t+"Tablet",mobileHoverPropertyName:t+"Mobile"}})(s),o=N(t,n,i);f(o)||(a[`--lms-${e}-${n}`]=o);const d=N(t,r,c);f(d)||(a[`--lms-${e}-${r}`]=d);const u=N(t,l,m);f(u)||(a[`--lms-${e}-${l}`]=u)}if(l){const n=s+y.HOVER,l=N(t,n,i);f(l)||(a[`--lms-${e}-${n}`]=l)}if(n){const{desktopPropertyName:n,mobilePropertyName:l,tabletPropertyName:r}={desktopPropertyName:o=s,tabletPropertyName:o+"Tablet",mobilePropertyName:o+"Mobile"},d=N(t,n,i);f(d)||(a[`--lms-${e}-${n}`]=d);const u=N(t,r,c);f(u)||(a[`--lms-${e}-${r}`]=u);const p=N(t,l,m);f(p)||(a[`--lms-${e}-${l}`]=p)}const d=N(t,s,i);f(d)||(a[`--lms-${e}-${s}`]=d)}var o})),a},M=(i.__("Small","masterstudy-lms-learning-management-system"),i.__("Normal","masterstudy-lms-learning-management-system"),i.__("Large","masterstudy-lms-learning-management-system"),i.__("Extra Large","masterstudy-lms-learning-management-system"),"wp-block-masterstudy-settings__"),k={top:"",right:"",bottom:"",left:""},x=(b.TOP_lEFT,b.TOP_CENTER,b.TOP_RIGHT,b.BOTTOM_lEFT,b.BOTTOM_CENTER,b.BOTTOM_RIGHT,[{label:i.__("Newest","masterstudy-lms-learning-management-system"),value:"date_high"},{label:i.__("Oldest","masterstudy-lms-learning-management-system"),value:"date_low"},{label:i.__("Overall rating","masterstudy-lms-learning-management-system"),value:"rating"},{label:i.__("Popular","masterstudy-lms-learning-management-system"),value:"popular"},{label:i.__("Price low","masterstudy-lms-learning-management-system"),value:"price_low"},{label:i.__("Price high","masterstudy-lms-learning-management-system"),value:"price_high"}]);function A(e){return Array.isArray(e)?e.map((e=>M+e)):M+e}const O=window.wp.data,T=()=>(0,O.useSelect)((e=>e("core/editor").getDeviceType()||e("core/edit-site")?.__experimentalGetPreviewDeviceType()||e("core/edit-post")?.__experimentalGetPreviewDeviceType()||e("masterstudy/store")?.getDeviceType()),[])||"Desktop",S=(e=!1)=>{const[t,n]=(0,m.useState)(e),a=(0,m.useCallback)((()=>{n(!0)}),[]);return{isOpen:t,onClose:(0,m.useCallback)((()=>{n(!1)}),[]),onOpen:a,onToggle:(0,m.useCallback)((()=>{n((e=>!e))}),[])}},H=()=>{const e=(0,m.useContext)(d);if(!e)throw new Error("No settings context provided");return e},R=(e="")=>{const{attributes:t,setAttributes:n,onResetByFieldName:a,changedFieldsByName:l}=H();return{value:t[e],onChange:t=>n({[e]:t}),onReset:a.get(e),isChanged:l.get(e)}},B=(e,t=!1,n=!1)=>{const{hoverName:a,onChangeHoverName:l}=(()=>{const[e,t]=(0,m.useState)(y.NORMAL);return{hoverName:e,onChangeHoverName:(0,m.useCallback)((e=>{t(e)}),[])}})(),r=T();return{fieldName:(0,m.useMemo)((()=>{const l=a===y.HOVER?a:"",s=r===_.DESKTOP?"":r;return n&&t?e+l+s:n&&!t?e+l:t&&!n?e+s:e}),[e,n,t,a,r]),hoverName:a,onChangeHoverName:l}},V=(e,t=!1,n="Normal")=>{const a=T(),l=(0,m.useMemo)((()=>{const l=n===y.NORMAL?"":n,r=a===_.DESKTOP?"":a;return l&&t?e+l+r:l&&!t?e+l:t&&!l?e+r:e}),[e,t,n,a]),{value:r,isChanged:s,onReset:o}=R(l);return{fieldName:l,value:r,isChanged:s,onReset:o}},P=(e=[],t=v.ALL)=>{const{attributes:n}=H();return!e.length||(t===v.ALL?e.every((({name:e,value:t})=>{const a=n[e];return Array.isArray(t)?Array.isArray(a)?g(t,a):t.includes(a):t===a})):t!==v.SOME||e.some((({name:e,value:t})=>{const a=n[e];return Array.isArray(t)?Array.isArray(a)?g(t,a):t.includes(a):t===a})))},D=e=>{const t=(0,m.useRef)(null);return(0,m.useEffect)((()=>{const n=n=>{t.current&&!t.current.contains(n.target)&&e()};return document.addEventListener("click",n),()=>{document.removeEventListener("click",n)}}),[t,e]),t},I=e=>(0,a.createElement)(p.SVG,{width:"16",height:"16",viewBox:"0 0 12 13",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(p.Path,{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})),L=e=>(0,a.createElement)(p.SVG,{width:"16",height:"16",viewBox:"0 0 14 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(p.G,{"clip-path":"url(#clip0_1068_38993)"},(0,a.createElement)(p.Path,{d:"M11.1973 8.60005L8.74967 8.11005V5.67171C8.74967 5.517 8.68822 5.36863 8.57882 5.25923C8.46942 5.14984 8.32105 5.08838 8.16634 5.08838H6.99967C6.84496 5.08838 6.69659 5.14984 6.5872 5.25923C6.4778 5.36863 6.41634 5.517 6.41634 5.67171V8.58838H5.24967C5.15021 8.58844 5.05241 8.61391 4.96555 8.66238C4.87869 8.71084 4.80565 8.78068 4.75336 8.86529C4.70106 8.9499 4.67125 9.04646 4.66674 9.14582C4.66223 9.24518 4.68317 9.34405 4.72759 9.43305L6.47759 12.933C6.57676 13.1302 6.77859 13.255 6.99967 13.255H11.083C11.2377 13.255 11.3861 13.1936 11.4955 13.0842C11.6049 12.9748 11.6663 12.8264 11.6663 12.6717V9.17171C11.6665 9.03685 11.6198 8.90612 11.5343 8.80186C11.4487 8.69759 11.3296 8.62626 11.1973 8.60005Z"}),(0,a.createElement)(p.Path,{d:"M10.4997 1.58838H3.49967C2.85626 1.58838 2.33301 2.11221 2.33301 2.75505V6.83838C2.33301 7.4818 2.85626 8.00505 3.49967 8.00505H5.24967V6.83838H3.49967V2.75505H10.4997V6.83838H11.6663V2.75505C11.6663 2.11221 11.1431 1.58838 10.4997 1.58838Z"})),(0,a.createElement)("defs",null,(0,a.createElement)("clipPath",{id:"clip0_1068_38993"},(0,a.createElement)(p.Rect,{width:"14",height:"14",transform:"translate(0 0.421875)"})))),F=[{value:y.NORMAL,label:i.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(I,{onClick:e})},{value:y.HOVER,label:i.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(L,{onClick:e})},{value:y.ACTIVE,label:i.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(L,{onClick:e})},{value:y.FOCUS,label:i.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(L,{onClick:e})}],Z={[y.NORMAL]:{icon:(0,a.createElement)(I,null),label:i.__("Normal State","masterstudy-lms-learning-management-system")},[y.HOVER]:{icon:(0,a.createElement)(L,null),label:i.__("Hovered State","masterstudy-lms-learning-management-system")},[y.ACTIVE]:{icon:(0,a.createElement)(L,null),label:i.__("Active State","masterstudy-lms-learning-management-system")},[y.FOCUS]:{icon:(0,a.createElement)(L,null),label:i.__("Focus State","masterstudy-lms-learning-management-system")}},U=(e,t)=>{let n=[];return n=e.length?F.filter((t=>e.includes(t.value))):F,n=n.filter((e=>e.value!==t)),{ICONS_MAP:Z,options:n}},[j,W,z,$,G]=A(["hover-state","hover-state__selected","hover-state__selected__opened-menu","hover-state__menu","hover-state__menu__item"]),K=({stateOptions:e,currentState:t,onSelect:n})=>{const{isOpen:l,onOpen:r,onClose:s}=S(),i=D(s),{ICONS_MAP:c,options:m}=U(e,t);return(0,a.createElement)("div",{className:j,ref:i},(0,a.createElement)("div",{className:o()([W],{[z]:l}),onClick:r,title:c[t]?.label},c[t]?.icon),(0,a.createElement)(h,{condition:l},(0,a.createElement)("div",{className:$},m.map((({value:e,icon:t,label:l})=>(0,a.createElement)("div",{key:e,className:G,title:l},t((()=>n(e)))))))))},X=A("color-indicator"),Y=(0,m.memo)((({color:e,onChange:t})=>(0,a.createElement)("div",{className:X},(0,a.createElement)(r.PanelColorSettings,{enableAlpha:!0,disableCustomColors:!1,__experimentalHasMultipleOrigins:!0,__experimentalIsRenderedInSidebar:!0,colorSettings:[{label:"",value:e,onChange:t}]}))));var q;function J(){return J=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},J.apply(null,arguments)}var Q,ee,te=function(e){return a.createElement("svg",J({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 12 13"},e),q||(q=a.createElement("path",{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})))};function ne(){return ne=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},ne.apply(null,arguments)}var ae=function(e){return a.createElement("svg",ne({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 14 15"},e),Q||(Q=a.createElement("g",{clipPath:"url(#state-hover_svg__a)"},a.createElement("path",{d:"M11.197 8.6 8.75 8.11V5.672a.583.583 0 0 0-.584-.584H7a.583.583 0 0 0-.584.584v2.916H5.25a.584.584 0 0 0-.522.845l1.75 3.5c.099.197.3.322.522.322h4.083a.583.583 0 0 0 .583-.583v-3.5a.58.58 0 0 0-.469-.572"}),a.createElement("path",{d:"M10.5 1.588h-7c-.644 0-1.167.524-1.167 1.167v4.083c0 .644.523 1.167 1.167 1.167h1.75V6.838H3.5V2.755h7v4.083h1.166V2.755c0-.643-.523-1.167-1.166-1.167"}))),ee||(ee=a.createElement("defs",null,a.createElement("clipPath",{id:"state-hover_svg__a"},a.createElement("path",{d:"M0 .422h14v14H0z"})))))};const le=[{value:y.NORMAL,label:i.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(te,{onClick:e})},{value:y.HOVER,label:i.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(ae,{onClick:e})}],re={[y.NORMAL]:{icon:(0,a.createElement)(te,null),label:i.__("Normal State","masterstudy-lms-learning-management-system")},[y.HOVER]:{icon:(0,a.createElement)(ae,null),label:i.__("Hovered State","masterstudy-lms-learning-management-system")}},se=A("hover-state"),oe=A("hover-state__selected"),ie=A("hover-state__selected__opened-menu"),ce=A("has-changes"),me=A("hover-state__menu"),de=A("hover-state__menu__item"),ue=(0,m.memo)((e=>{const{hoverName:t,onChangeHoverName:n,fieldName:l}=e,{changedFieldsByName:r}=H(),s=r.get(l),{isOpen:i,onOpen:c,onClose:d}=S(),u=D(d),{ICONS_MAP:p,options:g}=(e=>{const t=(0,m.useMemo)((()=>le.filter((t=>t.value!==e))),[e]);return{ICONS_MAP:re,options:t}})(t),v=(0,m.useCallback)((e=>{n(e),d()}),[n,d]);return(0,a.createElement)("div",{className:se,ref:u},(0,a.createElement)("div",{className:o()([oe],{[ie]:i,[ce]:s}),onClick:c,title:p[t]?.label},p[t]?.icon),(0,a.createElement)(h,{condition:i},(0,a.createElement)("div",{className:me},g.map((({value:e,icon:t,label:n})=>(0,a.createElement)("div",{key:e,className:de,title:n},t((()=>v(e)))))))))})),pe={Desktop:{icon:"desktop",label:i.__("Desktop","masterstudy-lms-learning-management-system")},Tablet:{icon:"tablet",label:i.__("Tablet","masterstudy-lms-learning-management-system")},Mobile:{icon:"smartphone",label:i.__("Mobile","masterstudy-lms-learning-management-system")}},he=[{value:_.DESKTOP,icon:"desktop",label:i.__("Desktop","masterstudy-lms-learning-management-system")},{value:_.TABLET,icon:"tablet",label:i.__("Tablet","masterstudy-lms-learning-management-system")},{value:_.MOBILE,icon:"smartphone",label:i.__("Mobile","masterstudy-lms-learning-management-system")}],ge=A("device-picker"),ve=A("device-picker__selected"),ye=A("device-picker__selected__opened-menu"),_e=A("device-picker__menu"),be=A("device-picker__menu__item"),Ee=()=>{const{isOpen:e,onOpen:t,onClose:n}=S(),{value:l,onChange:r}=(e=>{const t=T(),n=(0,O.useDispatch)();return{value:(0,m.useMemo)((()=>pe[t]),[t]),onChange:t=>{n("core/edit-site")&&n("core/edit-site").__experimentalSetPreviewDeviceType?n("core/edit-site").__experimentalSetPreviewDeviceType(t):n("core/edit-post")&&n("core/edit-post").__experimentalSetPreviewDeviceType?n("core/edit-post").__experimentalSetPreviewDeviceType(t):n("masterstudy/store").setDeviceType(t),e()}}})(n),s=(e=>(0,m.useMemo)((()=>he.filter((t=>t.icon!==e))),[e]))(l.icon),i=D(n);return(0,a.createElement)("div",{className:ge,ref:i},(0,a.createElement)(p.Dashicon,{className:o()([ve],{[ye]:e}),icon:l.icon,size:16,onClick:t,title:l.label}),(0,a.createElement)(h,{condition:e},(0,a.createElement)("div",{className:_e},s.map((e=>(0,a.createElement)(p.Dashicon,{key:e.value,icon:e.icon,size:16,onClick:()=>r(e.value),className:be,title:e.label}))))))},Ce=A("reset-button"),fe=({onReset:e})=>(0,a.createElement)(p.Dashicon,{icon:"undo",onClick:e,className:Ce,size:16}),Ne=[{label:"PX",value:"px"},{label:"%",value:"%"},{label:"EM",value:"em"},{label:"REM",value:"rem"},{label:"VW",value:"vw"},{label:"VH",value:"vh"}],we=A("unit"),Me=A("unit__single"),ke=A("unit__list"),xe=({name:e,isAdaptive:t})=>{const{isOpen:n,onOpen:l,onClose:r}=S(),{fieldName:s}=B(e,t),{value:o,onChange:i}=R(s),c=D(r);return(0,a.createElement)("div",{className:we,ref:c},(0,a.createElement)("div",{className:Me,onClick:l},o),(0,a.createElement)(h,{condition:n},(0,a.createElement)("div",{className:ke},Ne.map((({value:e,label:t})=>(0,a.createElement)("div",{key:e,onClick:()=>(i(e),void r())},t))))))},Ae=A("popover-modal"),Oe=A("popover-modal__close dashicon dashicons dashicons-no-alt"),Te=e=>{const{isOpen:t,onClose:n,popoverContent:l}=e;return(0,a.createElement)(h,{condition:t},(0,a.createElement)(p.Popover,{position:"middle left",onClose:n,className:Ae},l,(0,a.createElement)("span",{onClick:n,className:Oe})))},Se=A("setting-label"),He=A("setting-label__content"),Re=e=>{const{label:t,isChanged:n=!1,onReset:l,showDevicePicker:r=!0,HoverStateControl:s=null,unitName:o,popoverContent:i=null,dependencies:c}=e,{isOpen:m,onClose:d,onToggle:u}=S();return P(c)?(0,a.createElement)("div",{className:Se},(0,a.createElement)("div",{className:He},(0,a.createElement)("div",{onClick:u},t),(0,a.createElement)(h,{condition:Boolean(i)},(0,a.createElement)(Te,{isOpen:m,onClose:d,popoverContent:i})),(0,a.createElement)(h,{condition:r},(0,a.createElement)(Ee,null)),(0,a.createElement)(h,{condition:Boolean(s)},s)),(0,a.createElement)(h,{condition:Boolean(o)},(0,a.createElement)(xe,{name:o,isAdaptive:r})),(0,a.createElement)(h,{condition:n},(0,a.createElement)(fe,{onReset:l}))):null},Be=A("suffix"),Ve=()=>(0,a.createElement)("div",{className:Be},(0,a.createElement)(p.Dashicon,{icon:"color-picker",size:16})),Pe=A("color-picker"),De=e=>{const{name:t,label:n,placeholder:l,dependencyMode:r,dependencies:s,isAdaptive:o=!1,hasHover:i=!1}=e,{fieldName:c,hoverName:m,onChangeHoverName:d}=B(t,o,i),{value:u,isChanged:g,onChange:v,onReset:y}=R(c);return P(s,r)?(0,a.createElement)("div",{className:Pe},(0,a.createElement)(h,{condition:Boolean(n)},(0,a.createElement)(Re,{label:n,isChanged:g,onReset:y,showDevicePicker:o,HoverStateControl:(0,a.createElement)(h,{condition:i},(0,a.createElement)(ue,{hoverName:m,onChangeHoverName:d,fieldName:c}))})),(0,a.createElement)(p.__experimentalInputControl,{prefix:(0,a.createElement)(Y,{color:u,onChange:v}),suffix:(0,a.createElement)(Ve,null),onChange:v,value:u,placeholder:l})):null},Ie=A("number-steppers"),Le=A("indent-steppers"),Fe=A("indent-stepper-plus"),Ze=A("indent-stepper-minus"),Ue=({onIncrement:e,onDecrement:t,withArrows:n=!1})=>n?(0,a.createElement)("span",{className:Le},(0,a.createElement)("button",{onClick:e,className:Fe}),(0,a.createElement)("button",{onClick:t,className:Ze})):(0,a.createElement)("span",{className:Ie},(0,a.createElement)("button",{onClick:e},"+"),(0,a.createElement)("button",{onClick:t},"-")),[je,We]=A(["indents","indents-control"]),ze=({name:e,label:t,unitName:n,popoverContent:l,dependencyMode:r,dependencies:s,isAdaptive:o=!1})=>{const{fieldName:c}=B(e,o),{value:d,onResetSegmentedBox:u,hasChanges:g,handleInputIncrement:v,handleInputDecrement:y,updateDirectionsValues:_,lastFieldValue:b}=((e,t)=>{const{value:n,isChanged:a,onChange:l,onReset:r}=R(e),{onResetByFieldName:s,changedFieldsByName:o}=H(),i=a||o.get(t),c=e=>{l({...n,...e})},[d,u]=(0,m.useState)(!1);return{value:n,onResetSegmentedBox:()=>{r(),s.get(t)()},hasChanges:i,handleInputIncrement:e=>Number(n[e])+1,handleInputDecrement:e=>Number(n[e])-1,updateDirectionsValues:(e,t,n)=>{e?(u(!1),c({top:n,right:n,bottom:n,left:n})):(u(n),c({[t]:n}))},lastFieldValue:d}})(c,n),[E,C]=(0,m.useState)((()=>{const{left:e,right:t,top:n,bottom:a}=d;return""!==e&&e===t&&t===n&&n===a})),f=e=>{const[t,n]=Object.entries(e)[0];_(E,t,n)},N=e=>()=>{const t=v(e);_(E,e,String(t))},w=e=>()=>{const t=y(e);_(E,e,String(t))};return P(s,r)?(0,a.createElement)("div",{className:je},(0,a.createElement)(h,{condition:Boolean(t)},(0,a.createElement)(Re,{label:null!=t?t:"",isChanged:g,onReset:u,unitName:n,popoverContent:l,showDevicePicker:o})),(0,a.createElement)("div",{className:`${We} ${E?"active":""}`},(0,a.createElement)("div",null,(0,a.createElement)(p.__experimentalNumberControl,{value:d.top,onChange:e=>{f({top:e})},spinControls:"none",suffix:(0,a.createElement)(Ue,{onIncrement:N("top"),onDecrement:w("top"),withArrows:!0})}),(0,a.createElement)("div",null,i.__("Top","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(p.__experimentalNumberControl,{value:d.right,onChange:e=>{f({right:e})},spinControls:"none",suffix:(0,a.createElement)(Ue,{onIncrement:N("right"),onDecrement:w("right"),withArrows:!0})}),(0,a.createElement)("div",null,i.__("Right","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(p.__experimentalNumberControl,{value:d.bottom,onChange:e=>{f({bottom:e})},spinControls:"none",suffix:(0,a.createElement)(Ue,{onIncrement:N("bottom"),onDecrement:w("bottom"),withArrows:!0})}),(0,a.createElement)("div",null,i.__("Bottom","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(p.__experimentalNumberControl,{value:d.left,onChange:e=>{f({left:e})},spinControls:"none",suffix:(0,a.createElement)(Ue,{onIncrement:N("left"),onDecrement:w("left"),withArrows:!0})}),(0,a.createElement)("div",null,i.__("Left","masterstudy-lms-learning-management-system"))),(0,a.createElement)(p.Dashicon,{icon:"dashicons "+(E?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{E||!1===b||_(!0,"left",b),C((e=>!e))}}))):null},[$e,Ge,Ke,Xe]=A(["toggle-group-wrapper","toggle-group","toggle-group__toggle","toggle-group__active-toggle"]),Ye=e=>{const{name:t,options:n,label:l,isAdaptive:r=!1,dependencyMode:s,dependencies:i}=e,{fieldName:c}=B(t,r),{value:m,isChanged:d,onChange:u,onReset:p}=R(c);return P(i,s)?(0,a.createElement)("div",{className:$e},(0,a.createElement)(h,{condition:Boolean(l)},(0,a.createElement)(Re,{label:l,isChanged:d,onReset:p,showDevicePicker:r})),(0,a.createElement)("div",{className:Ge},n.map((e=>(0,a.createElement)("div",{key:e.value,className:o()([Ke],{[Xe]:e.value===m}),onClick:()=>u(e.value)},e.label))))):null},[qe,Je,Qe,et]=A(["border-control","border-control-solid","border-control-dashed","border-control-dotted"]),tt=e=>{const{label:t,borderStyleName:n,borderColorName:l,borderWidthName:r,dependencyMode:s,dependencies:c,isAdaptive:d=!1,hasHover:u=!1}=e,[p,g]=(0,m.useState)("Normal"),{fieldName:v,value:y,isChanged:_,onReset:b}=V(n,d,p),{fieldName:E,isChanged:C,onReset:f}=V(l,d,p),{fieldName:N,isChanged:w,onReset:M}=V(r,d,p);if(!P(c,s))return null;const k=_||C||w;return(0,a.createElement)("div",{className:o()([qe],{"has-reset-button":k})},(0,a.createElement)(Re,{label:t,isChanged:k,onReset:()=>{b(),f(),M()},showDevicePicker:d,HoverStateControl:(0,a.createElement)(h,{condition:u},(0,a.createElement)(K,{stateOptions:["Normal","Hover"],currentState:p,onSelect:g}))}),(0,a.createElement)(Ye,{options:[{label:(0,a.createElement)("span",null,i.__("None","masterstudy-lms-learning-management-system")),value:"none"},{label:(0,a.createElement)("span",{className:Je}),value:"solid"},{label:(0,a.createElement)("span",{className:Qe},(0,a.createElement)("span",null)),value:"dashed"},{label:(0,a.createElement)("span",{className:et},(0,a.createElement)("span",null,(0,a.createElement)("span",null))),value:"dotted"}],name:v}),(0,a.createElement)(h,{condition:"none"!==y},(0,a.createElement)(De,{name:E,placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(ze,{name:N})))},nt=A("border-radius"),at=A("border-radius-control"),lt=({name:e,label:t,unitName:n,popoverContent:l,dependencyMode:r,dependencies:s,isAdaptive:i=!1,hasHover:c=!1})=>{const{fieldName:d}=B(e,i,c),{value:u,onResetBorderRadius:h,hasChanges:g,handleInputIncrement:v,handleInputDecrement:y,updateDirectionsValues:_,lastFieldValue:b}=((e,t)=>{const[n,a]=(0,m.useState)(!1),{value:l,isChanged:r,onChange:s,onReset:o}=R(e),{onResetByFieldName:i,changedFieldsByName:c}=H(),d=r||c.get(t),u=e=>{s({...l,...e})};return{value:l,onResetBorderRadius:()=>{o(),i.get(t)()},hasChanges:d,handleInputIncrement:e=>Number(l[e])+1,handleInputDecrement:e=>Number(l[e])-1,updateDirectionsValues:(e,t,n)=>{e?(u({top:n,right:n,bottom:n,left:n}),a(!1)):(u({[t]:n}),a(n))},lastFieldValue:n}})(d,n),[E,C]=(0,m.useState)((()=>{const{left:e,right:t,top:n,bottom:a}=u;return""!==e&&e===t&&t===n&&n===a})),f=e=>{const[t,n]=Object.entries(e)[0];_(E,t,n)},N=e=>()=>{const t=v(e);_(E,e,String(t))},w=e=>()=>{const t=y(e);_(E,e,String(t))};return P(s,r)?(0,a.createElement)("div",{className:nt},(0,a.createElement)(Re,{label:t,isChanged:g,onReset:h,unitName:n,popoverContent:l,showDevicePicker:i}),(0,a.createElement)("div",{className:o()([at],{"has-reset-button":g,active:E})},(0,a.createElement)("div",{className:"number-control-top"},(0,a.createElement)(p.__experimentalNumberControl,{value:u.top,onChange:e=>{f({top:e})},spinControls:"none",suffix:(0,a.createElement)(Ue,{onIncrement:N("top"),onDecrement:w("top"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-right"},(0,a.createElement)(p.__experimentalNumberControl,{value:u.right,onChange:e=>{f({right:e})},spinControls:"none",suffix:(0,a.createElement)(Ue,{onIncrement:N("right"),onDecrement:w("right"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-left"},(0,a.createElement)(p.__experimentalNumberControl,{value:u.left,onChange:e=>{f({left:e})},spinControls:"none",suffix:(0,a.createElement)(Ue,{onIncrement:N("left"),onDecrement:w("left"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-bottom"},(0,a.createElement)(p.__experimentalNumberControl,{value:u.bottom,onChange:e=>{f({bottom:e})},spinControls:"none",suffix:(0,a.createElement)(Ue,{onIncrement:N("bottom"),onDecrement:w("bottom"),withArrows:!0})})),(0,a.createElement)(p.Dashicon,{icon:"dashicons "+(E?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{E||!1===b||_(!0,"left",b),C((e=>!e))}}))):null},rt=(A("box-shadow-preset"),A("presets")),st=A("presets__item-wrapper"),ot=A("presets__item-wrapper__preset"),it=A("presets__item-wrapper__name"),ct=((0,m.memo)((e=>{const{presets:t,activePreset:n,onSelectPreset:l,PresetItem:r,detectIsActive:s,detectByIndex:i=!1}=e;return(0,a.createElement)("div",{className:rt},t.map((({name:e,...t},c)=>(0,a.createElement)("div",{key:c,className:o()([st],{active:s(n,i?c:t)}),onClick:()=>l(t)},(0,a.createElement)("div",{className:ot},(0,a.createElement)(r,{preset:t})),(0,a.createElement)("span",{className:it},e)))))})),A("range-control")),mt=e=>{const{name:t,label:n,min:l,max:r,unitName:s,dependencyMode:o,dependencies:i,isAdaptive:c=!1}=e,{fieldName:m}=B(t,c),{value:d,onChange:u,onResetNumberField:g,hasChanges:v}=((e,t)=>{const{value:n,isChanged:a,onChange:l,onReset:r}=R(e),{onResetByFieldName:s,changedFieldsByName:o}=H();return{value:n,onChange:l,onResetNumberField:()=>{r(),s.get(t)()},hasChanges:a||o.get(t)}})(m,s);return P(i,o)?(0,a.createElement)("div",{className:ct},(0,a.createElement)(h,{condition:Boolean(n)},(0,a.createElement)(Re,{label:n,isChanged:v,onReset:g,unitName:s,showDevicePicker:c})),(0,a.createElement)(p.RangeControl,{value:d,onChange:u,min:l,max:r})):null},dt=A("switch"),ut=e=>{const{name:t,label:n,dependencyMode:l,dependencies:r,isAdaptive:s=!1}=e,{fieldName:o}=B(t,s),{value:i,onChange:c}=R(o);return P(r,l)?(0,a.createElement)("div",{className:dt,"data-has-label":Boolean(n).toString()},(0,a.createElement)(p.ToggleControl,{label:n,checked:i,onChange:c}),(0,a.createElement)(h,{condition:s},(0,a.createElement)(Ee,null))):null},pt=(A("box-shadow-settings"),A("box-shadow-presets-title"),A("input-field"),A("input-field-control"),A("number-field")),ht=A("number-field-control"),gt=e=>{const{name:t,label:n,unitName:l,help:r,popoverContent:s,dependencyMode:o,dependencies:i,isAdaptive:c=!1}=e,{fieldName:m}=B(t,c),{value:d,onResetNumberField:u,hasChanges:h,handleIncrement:g,handleDecrement:v,handleInputChange:y}=((e,t)=>{const{value:n,isChanged:a,onChange:l,onReset:r}=R(e),{onResetByFieldName:s,changedFieldsByName:o}=H(),i=a||o.get(t);return{value:n,onResetNumberField:()=>{r(),s.get(t)()},hasChanges:i,handleIncrement:()=>{l(n+1)},handleDecrement:()=>{l(n-1)},handleInputChange:e=>{const t=Number(""===e?0:e);l(t)}}})(m,l);return P(i,o)?(0,a.createElement)("div",{className:pt},(0,a.createElement)(Re,{label:n,isChanged:h,onReset:u,unitName:l,showDevicePicker:c,popoverContent:s}),(0,a.createElement)("div",{className:ht},(0,a.createElement)(p.__experimentalNumberControl,{value:d,onChange:y,spinControls:"none",suffix:(0,a.createElement)(Ue,{onIncrement:g,onDecrement:v})})),r&&(0,a.createElement)("small",null,r)):null},vt=({className:e})=>(0,a.createElement)("div",{className:e},i.__("No options","masterstudy-lms-learning-management-system")),yt=A("select__single-item"),_t=A("select__container"),bt=A("select__container__multi-item"),Et=({multiple:e,value:t,options:n,onChange:l})=>{const{singleValue:r,multipleValue:s}=((e,t,n)=>({singleValue:(0,m.useMemo)((()=>t?null:n.find((t=>t.value===e))?.label),[t,e,n]),multipleValue:(0,m.useMemo)((()=>t?e:null),[t,e])}))(t,e,n);return(0,a.createElement)(h,{condition:e,fallback:(0,a.createElement)("div",{className:yt},r)},(0,a.createElement)("div",{className:_t},s?.map((e=>{const t=n.find((t=>t.value===e));return t?(0,a.createElement)("div",{key:t.value,className:bt},(0,a.createElement)("div",null,t.label),(0,a.createElement)(p.Dashicon,{icon:"no-alt",onClick:()=>l(t.value),size:16})):null}))))},Ct=A("select"),ft=A("select__select-box"),Nt=A("select__placeholder"),wt=A("select__select-box-multiple"),Mt=A("select__menu"),kt=A("select__menu__options-container"),xt=A("select__menu__item"),At=e=>{const{options:t,multiple:n=!1,placeholder:l="Select",value:r,onSelect:s}=e,{isOpen:i,onToggle:c,onClose:d}=S(),u=D(d),g=((e,t,n)=>(0,m.useMemo)((()=>n&&Array.isArray(e)?t.filter((t=>!e.includes(t.value))):t.filter((t=>t.value!==e))),[e,t,n]))(r,t,n),v=((e,t,n,a)=>(0,m.useCallback)((l=>{if(t&&Array.isArray(e)){const t=e.includes(l)?e.filter((e=>e!==l)):[...e,l];n(t)}else n(l),a()}),[t,e,n,a]))(r,n,s,d),y=((e,t)=>(0,m.useMemo)((()=>t&&Array.isArray(e)?Boolean(e.length):Boolean(e)),[t,e]))(r,n),_=n&&Array.isArray(r)&&r?.length>0;return(0,a.createElement)("div",{className:Ct,ref:u},(0,a.createElement)("div",{className:o()([ft],{[wt]:_}),onClick:c},(0,a.createElement)(h,{condition:y,fallback:(0,a.createElement)("div",{className:Nt},l)},(0,a.createElement)(Et,{onChange:v,options:t,multiple:n,value:r})),(0,a.createElement)(p.Dashicon,{icon:i?"arrow-up-alt2":"arrow-down-alt2",size:16,style:{color:"#4D5E6F"}})),(0,a.createElement)(h,{condition:i},(0,a.createElement)("div",{className:Mt},(0,a.createElement)(h,{condition:Boolean(g.length),fallback:(0,a.createElement)(vt,{className:xt})},(0,a.createElement)("div",{className:kt},g.map((e=>(0,a.createElement)("div",{key:e.value,onClick:()=>v(e.value),className:xt},e.label))))))))},Ot=A("setting-select"),Tt=e=>{const{name:t,options:n,label:l,multiple:r=!1,placeholder:s,isAdaptive:o=!1,dependencyMode:i,dependencies:c}=e,{fieldName:m}=B(t,o),{value:d,isChanged:u,onChange:p,onReset:g}=R(m);return P(c,i)?(0,a.createElement)("div",{className:Ot},(0,a.createElement)(h,{condition:Boolean(l)},(0,a.createElement)(Re,{label:l,isChanged:u,onReset:g,showDevicePicker:o})),(0,a.createElement)(At,{options:n,value:d,onSelect:p,multiple:r,placeholder:s})):null},St=A("row-select"),Ht=A("row-select__label"),Rt=A("row-select__control"),Bt=e=>{const{name:t,label:n,options:l,isAdaptive:r=!1}=e,{fieldName:s}=B(t,r),{isChanged:o,onReset:i}=R(s);return(0,a.createElement)("div",{className:St},(0,a.createElement)("div",{className:Ht},(0,a.createElement)("div",null,n),(0,a.createElement)(h,{condition:r},(0,a.createElement)(Ee,null))),(0,a.createElement)("div",{className:Rt},(0,a.createElement)(Tt,{name:t,options:l,isAdaptive:r}),(0,a.createElement)(h,{condition:o},(0,a.createElement)(fe,{onReset:i}))))},Vt=(A("typography-select"),A("typography-select-label"),A("typography"),A("file-upload"),A("file-upload__wrap"),A("file-upload__image"),A("file-upload__remove"),A("file-upload__replace"),(0,m.createContext)({activeTab:0,setActiveTab:()=>{}})),Pt=()=>{const e=(0,m.useContext)(Vt);if(!e)throw new Error("useTabs should be used inside Tabs");return e},Dt=({children:e})=>{const[t,n]=(0,m.useState)(0);return(0,a.createElement)(Vt.Provider,{value:{activeTab:t,setActiveTab:n}},(0,a.createElement)("div",{className:`active-tab-${t}`},e))},It=A("tab-list"),Lt=({children:e})=>(0,a.createElement)("div",{className:It},m.Children.map(e,((e,t)=>(0,m.cloneElement)(e,{index:t})))),Ft=A("tab"),Zt=A("tab-active"),Ut=A("content"),jt=({index:e,title:t,icon:n})=>{const{activeTab:l,setActiveTab:r}=Pt();return(0,a.createElement)("div",{className:o()([Ft],{[Zt]:l===e}),onClick:()=>r(e)},(0,a.createElement)("div",{className:Ut},(0,a.createElement)("div",null,n),(0,a.createElement)("div",null,t)))},Wt=({children:e})=>(0,a.createElement)("div",null,m.Children.map(e,((e,t)=>(0,m.cloneElement)(e,{index:t})))),zt=A("tab-panel"),$t=({index:e,children:t})=>{const{activeTab:n}=Pt();return n===e?(0,a.createElement)("div",{className:zt},t):null},Gt=({generalTab:e,styleTab:t,advancedTab:n})=>(0,a.createElement)(Dt,null,(0,a.createElement)(Lt,null,(0,a.createElement)(jt,{title:i.__("General","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(p.Dashicon,{icon:"layout"})}),(0,a.createElement)(jt,{title:i.__("Style","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(p.Dashicon,{icon:"admin-appearance"})}),(0,a.createElement)(jt,{title:i.__("Advanced","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(p.Dashicon,{icon:"admin-settings"})})),(0,a.createElement)(Wt,null,(0,a.createElement)($t,null,e),(0,a.createElement)($t,null,t),(0,a.createElement)($t,null,n)));window.ReactDOM;"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement;function Kt(e){const t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function Xt(e){return"nodeType"in e}function Yt(e){var t,n;return e?Kt(e)?e:Xt(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function qt(e){const{Document:t}=Yt(e);return e instanceof t}function Jt(e){return!Kt(e)&&e instanceof Yt(e).HTMLElement}function Qt(e){return e instanceof Yt(e).SVGElement}function en(e){return e?Kt(e)?e.document:Xt(e)?qt(e)?e:Jt(e)||Qt(e)?e.ownerDocument:document:document:document}function tn(e){return function(t){for(var n=arguments.length,a=new Array(n>1?n-1:0),l=1;l<n;l++)a[l-1]=arguments[l];return a.reduce(((t,n)=>{const a=Object.entries(n);for(const[n,l]of a){const a=t[n];null!=a&&(t[n]=a+e*l)}return t}),{...t})}}const nn=tn(-1);function an(e){if(function(e){if(!e)return!1;const{TouchEvent:t}=Yt(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return function(e){return"clientX"in e&&"clientY"in e}(e)?{x:e.clientX,y:e.clientY}:null}var ln;!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(ln||(ln={}));const rn=Object.freeze({x:0,y:0});var sn,on,cn,mn;!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(sn||(sn={}));class dn{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach((e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)}))},this.target=e}add(e,t,n){var a;null==(a=this.target)||a.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function un(e,t){const n=Math.abs(e.x),a=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+a**2)>t:"x"in t&&"y"in t?n>t.x&&a>t.y:"x"in t?n>t.x:"y"in t&&a>t.y}function pn(e){e.preventDefault()}function hn(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(on||(on={})),(mn=cn||(cn={})).Space="Space",mn.Down="ArrowDown",mn.Right="ArrowRight",mn.Left="ArrowLeft",mn.Up="ArrowUp",mn.Esc="Escape",mn.Enter="Enter";cn.Space,cn.Enter,cn.Esc,cn.Space,cn.Enter;function gn(e){return Boolean(e&&"distance"in e)}function vn(e){return Boolean(e&&"delay"in e)}class yn{constructor(e,t,n){var a;void 0===n&&(n=function(e){const{EventTarget:t}=Yt(e);return e instanceof t?e:en(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;const{event:l}=e,{target:r}=l;this.props=e,this.events=t,this.document=en(r),this.documentListeners=new dn(this.document),this.listeners=new dn(n),this.windowListeners=new dn(Yt(r)),this.initialCoordinates=null!=(a=an(l))?a:rn,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),this.windowListeners.add(on.Resize,this.handleCancel),this.windowListeners.add(on.DragStart,pn),this.windowListeners.add(on.VisibilityChange,this.handleCancel),this.windowListeners.add(on.ContextMenu,pn),this.documentListeners.add(on.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(vn(t))return void(this.timeoutId=setTimeout(this.handleStart,t.delay));if(gn(t))return}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handleStart(){const{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(on.Click,hn,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(on.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;const{activated:n,initialCoordinates:a,props:l}=this,{onMove:r,options:{activationConstraint:s}}=l;if(!a)return;const o=null!=(t=an(e))?t:rn,i=nn(a,o);if(!n&&s){if(gn(s)){if(null!=s.tolerance&&un(i,s.tolerance))return this.handleCancel();if(un(i,s.distance))return this.handleStart()}return vn(s)&&un(i,s.tolerance)?this.handleCancel():void 0}e.cancelable&&e.preventDefault(),r(o)}handleEnd(){const{onEnd:e}=this.props;this.detach(),e()}handleCancel(){const{onCancel:e}=this.props;this.detach(),e()}handleKeydown(e){e.code===cn.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}const bn={move:{name:"pointermove"},end:{name:"pointerup"}};(class extends yn{constructor(e){const{event:t}=e,n=en(t.target);super(e,bn,n)}}).activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;return!(!n.isPrimary||0!==n.button||(null==a||a({event:n}),0))}}];const En={move:{name:"mousemove"},end:{name:"mouseup"}};var Cn;!function(e){e[e.RightClick=2]="RightClick"}(Cn||(Cn={})),class extends yn{constructor(e){super(e,En,en(e.event.target))}}.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;return n.button!==Cn.RightClick&&(null==a||a({event:n}),!0)}}];const fn={move:{name:"touchmove"},end:{name:"touchend"}};var Nn,wn,Mn,kn,xn;(class extends yn{constructor(e){super(e,fn)}static setup(){return window.addEventListener(fn.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(fn.move.name,e)};function e(){}}}).activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;const{touches:l}=n;return!(l.length>1||(null==a||a({event:n}),0))}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(Nn||(Nn={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(wn||(wn={})),sn.Backward,sn.Forward,sn.Backward,sn.Forward,function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(Mn||(Mn={})),function(e){e.Optimized="optimized"}(kn||(kn={})),Mn.WhileDragging,kn.Optimized,Map,function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(xn||(xn={})),cn.Down,cn.Right,cn.Up,cn.Left,i.__("Lectures","masterstudy-lms-learning-management-system"),i.__("Duration","masterstudy-lms-learning-management-system"),i.__("Views","masterstudy-lms-learning-management-system"),i.__("Level","masterstudy-lms-learning-management-system"),i.__("Members","masterstudy-lms-learning-management-system"),i.__("Empty","masterstudy-lms-learning-management-system"),A("sortable__item"),A("sortable__item__disabled"),A("sortable__item__content"),A("sortable__item__content__drag-item"),A("sortable__item__content__drag-item__disabled"),A("sortable__item__content__title"),A("sortable__item__control"),A("sortable__item__icon"),A("nested-sortable"),A("nested-sortable__item"),A("sortable");const An=A("accordion"),On=A("accordion__header"),Tn=A("accordion__header-flex"),Sn=A("accordion__content"),Hn=A("accordion__icon"),Rn=A("accordion__title"),Bn=A("accordion__title-disabled"),Vn=A("accordion__indicator"),Pn=A("accordion__controls"),Dn=A("accordion__controls-disabled"),In=({title:e,children:t,accordionFields:n,switchName:l,visible:r=!0,isDefaultOpen:s=!1})=>{const{isOpen:i,onToggle:c,disabled:d,onReset:u,hasChanges:v,onClose:y}=((e,t,n)=>{var a;const{isOpen:l,onToggle:r,onClose:s}=S(t),{defaultValues:o,attributes:i,setAttributes:c}=H(),m=((e,t,n)=>{for(const a of n)if(!g(e[a],t[a]))return!0;return!1})(o,i,e);return{isOpen:l,onToggle:r,disabled:!(null===(a=i[n])||void 0===a||a),hasChanges:m,onReset:t=>{t.stopPropagation(),c(e.reduce(((e,t)=>(e[t]=o[t],e)),{}))},onClose:s}})(n,s,l);return((e,t)=>{const{attributes:n}=H(),a=!n[t];(0,m.useEffect)((()=>{a&&e()}),[a,e])})(y,l),r?(0,a.createElement)("div",{className:An},(0,a.createElement)("div",{className:On},(0,a.createElement)("div",{className:Tn,onClick:d?null:c},(0,a.createElement)("div",{className:o()(Rn,{[Bn]:d,"with-switch":Boolean(l)})},(0,a.createElement)("div",null,e),(0,a.createElement)(h,{condition:v&&!d},(0,a.createElement)("div",{className:Vn}))),(0,a.createElement)("div",{className:o()(Pn,{[Dn]:d})},(0,a.createElement)(p.Dashicon,{icon:i?"arrow-up-alt2":"arrow-down-alt2",className:Hn,size:16}))),(0,a.createElement)(h,{condition:Boolean(l)},(0,a.createElement)(ut,{name:l})),(0,a.createElement)(h,{condition:v&&!d},(0,a.createElement)(fe,{onReset:u}))),i&&(0,a.createElement)("div",{className:Sn},t)):null};A("preset-picker"),A("preset-picker__label"),A("preset-picker__remove"),A("preset-picker__presets-list"),A("preset-picker__presets-list__item"),A("preset-picker__presets-list__item__preset"),A("preset-picker__presets-list__item__preset-active");const Ln={coursesPerPage:9,coursesPerRow:3,coursesPerRowTablet:2,coursesPerRowMobile:1,coursesOrderBy:"date_high"},Fn=Object.keys(Ln),Zn={layoutMargin:k,layoutMarginTablet:k,layoutMarginMobile:k,layoutMarginUnit:"px",layoutMarginUnitTablet:"px",layoutMarginUnitMobile:"px",layoutPadding:{top:"100",right:"80",bottom:"100",left:"80"},layoutPaddingTablet:k,layoutPaddingMobile:{top:"50",right:"50",bottom:"50",left:"50"},layoutPaddingUnit:"px",layoutPaddingUnitTablet:"px",layoutPaddingUnitMobile:"px",layoutBackground:"#EEF1F7",layoutBorderStyle:"none",layoutBorderStyleTablet:"",layoutBorderStyleMobile:"",layoutBorderColor:"",layoutBorderColorTablet:"",layoutBorderColorMobile:"",layoutBorderWidth:k,layoutBorderWidthTablet:k,layoutBorderWidthMobile:k,layoutBorderWidthUnit:"px",layoutBorderWidthUnitTablet:"px",layoutBorderWidthUnitMobile:"px",layoutBorderRadius:k,layoutBorderRadiusTablet:k,layoutBorderRadiusMobile:k,layoutBorderRadiusUnit:"px",layoutBorderRadiusUnitTablet:"px",layoutBorderRadiusUnitMobile:"px",layoutWidth:"alignfull",layoutContentWidth:1170,layoutContentWidthUnit:"px",layoutZIndex:null,layoutZIndexTablet:null,layoutZIndexMobile:null},Un=Object.keys(Zn),jn={...{...Ln},...{...Zn}},Wn=new Map([["layoutMargin",{unit:"layoutMarginUnit",isAdaptive:!0}],["layoutPadding",{unit:"layoutPaddingUnit",isAdaptive:!0}],["layoutBackground",{}],["layoutBorderStyle",{isAdaptive:!0}],["layoutBorderColor",{isAdaptive:!0}],["layoutBorderWidth",{isAdaptive:!0,unit:"layoutBorderWidthUnit"}],["layoutBorderRadius",{isAdaptive:!0,unit:"layoutBorderRadiusUnit"}],["layoutZIndex",{isAdaptive:!0}],["layoutContentWidth",{unit:"layoutContentWidthUnit"}]]),zn=()=>{const{min:e,max:t}=((e,t=!1)=>{const n=T(),[a,l]=(0,m.useState)(e.default||{min:3,max:6});return(0,m.useEffect)((()=>{if(n===_.DESKTOP){const n=e.desktop||{min:t?2:3,max:6};l(n)}if(n===_.TABLET){const n=e.tablet||{min:t?1:2,max:3};l(n)}if(n===_.MOBILE){const t=e.mobile||{min:1,max:1};l(t)}}),[n,t,e]),a})({default:{min:3,max:6}});return(0,a.createElement)(a.Fragment,null,(0,a.createElement)(In,{title:i.__("Layout","masterstudy-lms-learning-management-system"),accordionFields:Fn},(0,a.createElement)(mt,{name:"coursesPerPage",label:i.__("Courses per page","masterstudy-lms-learning-management-system"),min:2,max:24}),(0,a.createElement)(mt,{name:"coursesPerRow",label:i.__("Courses per row","masterstudy-lms-learning-management-system"),min:e,max:t,isAdaptive:!0})))},$n=()=>{const{widthOptions:e}=(()=>{const e=[{label:i.__("Auto","masterstudy-lms-learning-management-system"),value:"alignauto"},{label:i.__("Full width","masterstudy-lms-learning-management-system"),value:"alignfull"}],t=[{label:i.__("Cover","masterstudy-lms-learning-management-system"),value:"cover"},{label:i.__("Contain","masterstudy-lms-learning-management-system"),value:"contain"},{label:i.__("Inherit","masterstudy-lms-learning-management-system"),value:"inherit"},{label:i.__("Initial","masterstudy-lms-learning-management-system"),value:"initial"},{label:i.__("Revert","masterstudy-lms-learning-management-system"),value:"revert"},{label:i.__("Revert-layer","masterstudy-lms-learning-management-system"),value:"revert-layer"},{label:i.__("Unset","masterstudy-lms-learning-management-system"),value:"unset"}],n=[{label:i.__("Center center","masterstudy-lms-learning-management-system"),value:"center center"},{label:i.__("Center left","masterstudy-lms-learning-management-system"),value:"center left"},{label:i.__("Center right","masterstudy-lms-learning-management-system"),value:"center right"},{label:i.__("Top center","masterstudy-lms-learning-management-system"),value:"top center"},{label:i.__("Top left","masterstudy-lms-learning-management-system"),value:"top left"},{label:i.__("Top right","masterstudy-lms-learning-management-system"),value:"top right"},{label:i.__("Bottom center","masterstudy-lms-learning-management-system"),value:"bottom center"},{label:i.__("Bottom left","masterstudy-lms-learning-management-system"),value:"bottom left"},{label:i.__("Bottom right","masterstudy-lms-learning-management-system"),value:"bottom right"}],a=[{label:i.__("Center","masterstudy-lms-learning-management-system"),value:"center"},{label:i.__("Start","masterstudy-lms-learning-management-system"),value:"flex-start"},{label:i.__("End","masterstudy-lms-learning-management-system"),value:"flex-end"},{label:i.__("Space Between","masterstudy-lms-learning-management-system"),value:"space-between"},{label:i.__("Space Around","masterstudy-lms-learning-management-system"),value:"space-around"},{label:i.__("Space Evenly","masterstudy-lms-learning-management-system"),value:"space-evenly"}];return{filterOptions:x,widthOptions:e,backgroundSizeOptions:t,backgroundPositionOptions:n,alignContentOptions:a}})();return(0,a.createElement)(In,{title:i.__("Layout","masterstudy-lms-learning-management-system"),accordionFields:Un},(0,a.createElement)(ze,{name:"layoutMargin",label:i.__("Margin","masterstudy-lms-learning-management-system"),unitName:"layoutMarginUnit",isAdaptive:!0,dependencies:[{name:"layoutWidth",value:"alignauto"}]}),(0,a.createElement)(ze,{name:"layoutPadding",label:i.__("Padding","masterstudy-lms-learning-management-system"),unitName:"layoutPaddingUnit",isAdaptive:!0}),(0,a.createElement)(De,{name:"layoutBackground",label:i.__("Background","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(tt,{label:i.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"layoutBorderStyle",borderColorName:"layoutBorderColor",borderWidthName:"layoutBorderWidth",isAdaptive:!0}),(0,a.createElement)(lt,{name:"layoutBorderRadius",label:i.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,a.createElement)(Bt,{name:"layoutWidth",label:i.__("Container Width","masterstudy-lms-learning-management-system"),options:e}),(0,a.createElement)(gt,{name:"layoutContentWidth",label:i.__("Content Width","masterstudy-lms-learning-management-system"),unitName:"layoutContentWidthUnit"}),(0,a.createElement)(mt,{name:"layoutZIndex",label:i.__("Z-Index","masterstudy-lms-learning-management-system"),min:0,max:100,isAdaptive:!0}))},Gn=({attributes:e,setAttributes:t})=>{const{onResetByFieldName:n,changedFieldsByName:l}=((e,t,n,a=[])=>{const l=(e=>{const t={};return Object.entries(e).forEach((([e,n])=>{e.includes("UAG")||(t[e]=n)})),t})(t),r=!g(e,l),s=((e,t,n)=>{const a=new Map;return n.forEach((n=>{a.has(n)||a.set(n,(()=>t({[n]:e[n]})))})),a})(e,n,a),o=((e,t,n)=>{const a=new Map;return n.forEach((n=>{a.has(n)?a.set(n,!1):a.set(n,!g(e[n],t[n]))})),a})(e,l,a);return{hasChanges:r,onResetByFieldName:s,changedFieldsByName:o}})(jn,e,t,Object.keys(jn));return(0,a.createElement)(r.InspectorControls,null,(0,a.createElement)(u,{attributes:e,setAttributes:t,defaultValues:jn,onResetByFieldName:n,changedFieldsByName:l},(0,a.createElement)(Gt,{generalTab:(0,a.createElement)(zn,null),styleTab:(0,a.createElement)($n,null),advancedTab:(0,a.createElement)(a.Fragment,null)})))},Kn=[{name:"start",title:i.__("Archive Courses container","masterstudy-lms-learning-management-system"),description:i.__("Add and customize an Archive courses page with this block","masterstudy-lms-learning-management-system"),attributes:{filterPosition:"start"},isDefault:!0,scope:["transform"],isActive:e=>"start"===e.filterPosition,icon:"align-right"},{name:"end",title:i.__("Archive Courses container","masterstudy-lms-learning-management-system"),description:i.__("Add and customize an Archive courses page with this block","masterstudy-lms-learning-management-system"),attributes:{filterPosition:"end"},scope:["transform"],isActive:e=>"end"===e.filterPosition,icon:"align-left"}],Xn=JSON.parse('{"UU":"masterstudy/archive-container"}');(0,l.registerBlockType)(Xn.UU,{icon:{src:(0,a.createElement)("svg",{width:"513",height:"513",viewBox:"0 0 513 513",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,a.createElement)("g",{clipPath:"url(#clip0_3567_45417)"},(0,a.createElement)("path",{d:"M472.275 1.93774H40.7307C19.0753 1.95767 1.52383 19.5091 1.50391 41.1645V472.709C1.52383 494.364 19.0753 511.916 40.7307 511.946H472.275C493.93 511.926 511.482 494.374 511.512 472.709V41.1645C511.492 19.5091 493.94 1.95767 472.275 1.93774ZM40.7307 21.5511H472.275C483.103 21.5611 491.878 30.3368 491.888 41.1645V80.3913H21.1173V41.1645C21.1273 30.3368 29.903 21.5611 40.7307 21.5511ZM472.275 492.332H40.7307C29.903 492.322 21.1273 483.546 21.1173 472.719V100.015H491.898V472.709C491.888 483.546 483.113 492.322 472.275 492.332Z",fill:"black"}),(0,a.createElement)("path",{d:"M472.285 512.942H40.7307C18.5772 512.922 0.527735 494.872 0.507812 472.719V41.1645C0.527735 19.0111 18.5772 0.961573 40.7307 0.94165H472.275C494.438 0.961573 512.478 19.0111 512.498 41.1645V472.709C512.488 494.872 494.438 512.922 472.285 512.942ZM40.7307 2.93387C19.673 2.95379 2.51995 20.1068 2.50003 41.1745V472.719C2.51995 493.777 19.673 510.93 40.7407 510.949H472.285C493.343 510.93 510.496 493.777 510.516 472.709V41.1645C510.496 20.1068 493.343 2.95379 472.275 2.93387H40.7307ZM472.285 493.328H40.7307C29.3751 493.318 20.1312 484.074 20.1212 472.719V99.0185H492.884V472.709C492.884 484.074 483.641 493.318 472.285 493.328ZM22.1134 101.011V472.709C22.1234 482.969 30.4807 491.316 40.7307 491.326H472.275C482.535 491.316 490.882 482.969 490.892 472.709V101.011H22.1134ZM492.894 81.3974H20.1212V41.1645C20.1312 29.8089 29.3751 20.565 40.7307 20.555H472.275C483.631 20.565 492.874 29.8089 492.884 41.1645V81.3974H492.894ZM22.1134 79.4052H490.892V41.1645C490.882 30.9046 482.525 22.5572 472.275 22.5473H40.7307C30.4708 22.5572 22.1234 30.9146 22.1134 41.1645V79.4052Z",fill:"black"}),(0,a.createElement)("path",{d:"M129.325 60.9272C134.755 60.9272 139.156 56.5255 139.156 51.0956C139.156 45.6658 134.755 41.264 129.325 41.264C123.895 41.264 119.493 45.6658 119.493 51.0956C119.493 56.5255 123.895 60.9272 129.325 60.9272Z",fill:"black"}),(0,a.createElement)("path",{d:"M129.325 61.9334C123.358 61.9334 118.497 57.0724 118.497 51.1057C118.497 45.139 123.358 40.278 129.325 40.278C135.291 40.278 140.152 45.139 140.152 51.1057C140.152 57.0724 135.291 61.9334 129.325 61.9334ZM129.325 42.2602C124.454 42.2602 120.489 46.2247 120.489 51.0957C120.489 55.9667 124.454 59.9312 129.325 59.9312C134.196 59.9312 138.16 55.9667 138.16 51.0957C138.16 46.2247 134.196 42.2602 129.325 42.2602Z",fill:"black"}),(0,a.createElement)("path",{d:"M89.9986 60.9272C95.4284 60.9272 99.8302 56.5255 99.8302 51.0956C99.8302 45.6658 95.4284 41.264 89.9986 41.264C84.5687 41.264 80.167 45.6658 80.167 51.0956C80.167 56.5255 84.5687 60.9272 89.9986 60.9272Z",fill:"black"}),(0,a.createElement)("path",{d:"M89.9986 61.9334C84.0319 61.9334 79.1709 57.0724 79.1709 51.1057C79.1709 45.139 84.0319 40.278 89.9986 40.278C95.9653 40.278 100.826 45.139 100.826 51.1057C100.826 57.0724 95.9653 61.9334 89.9986 61.9334ZM89.9986 42.2602C85.1276 42.2602 81.1631 46.2247 81.1631 51.0957C81.1631 55.9667 85.1276 59.9312 89.9986 59.9312C94.8696 59.9312 98.8341 55.9667 98.8341 51.0957C98.8341 46.2247 94.8696 42.2602 89.9986 42.2602Z",fill:"black"}),(0,a.createElement)("path",{d:"M50.6617 60.9272C56.0915 60.9272 60.4933 56.5255 60.4933 51.0956C60.4933 45.6658 56.0915 41.264 50.6617 41.264C45.2318 41.264 40.8301 45.6658 40.8301 51.0956C40.8301 56.5255 45.2318 60.9272 50.6617 60.9272Z",fill:"black"}),(0,a.createElement)("path",{d:"M50.6617 61.9334C44.695 61.9334 39.834 57.0724 39.834 51.1057C39.834 45.139 44.695 40.278 50.6617 40.278C56.6284 40.278 61.4894 45.139 61.4894 51.1057C61.4894 57.0724 56.6383 61.9334 50.6617 61.9334ZM50.6617 42.2602C45.7907 42.2602 41.8262 46.2247 41.8262 51.0957C41.8262 55.9667 45.7907 59.9312 50.6617 59.9312C55.5327 59.9312 59.4972 55.9667 59.4972 51.0957C59.4972 46.2247 55.5426 42.2602 50.6617 42.2602Z",fill:"black"}),(0,a.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M173.508 168.942C173.508 159.553 181.119 151.942 190.508 151.942H246.508C255.897 151.942 263.508 159.553 263.508 168.942V216.942C263.508 226.33 255.897 233.942 246.508 233.942H190.508C181.119 233.942 173.508 226.33 173.508 216.942V168.942ZM191.508 169.942V215.942H245.508V169.942H191.508ZM277.508 168.942C277.508 159.553 285.119 151.942 294.508 151.942H342.508C351.897 151.942 359.508 159.553 359.508 168.942V216.942C359.508 226.33 351.897 233.942 342.508 233.942H294.508C285.119 233.942 277.508 226.33 277.508 216.942V168.942ZM295.508 169.942V215.942H341.508V169.942H295.508ZM373.508 168.942C373.508 159.553 381.119 151.942 390.508 151.942H438.508C447.897 151.942 455.508 159.553 455.508 168.942V216.942C455.508 226.33 447.897 233.942 438.508 233.942H390.508C381.119 233.942 373.508 226.33 373.508 216.942V168.942ZM391.508 169.942V215.942H437.508V169.942H391.508ZM173.508 272.942C173.508 263.553 181.119 255.942 190.508 255.942H246.508C255.897 255.942 263.508 263.553 263.508 272.942V320.942C263.508 330.33 255.897 337.942 246.508 337.942H190.508C181.119 337.942 173.508 330.33 173.508 320.942V272.942ZM191.508 273.942V319.942H245.508V273.942H191.508ZM277.508 272.942C277.508 263.553 285.119 255.942 294.508 255.942H342.508C351.897 255.942 359.508 263.553 359.508 272.942V320.942C359.508 330.33 351.897 337.942 342.508 337.942H294.508C285.119 337.942 277.508 330.33 277.508 320.942V272.942ZM295.508 273.942V319.942H341.508V273.942H295.508ZM373.508 272.942C373.508 263.553 381.119 255.942 390.508 255.942H438.508C447.897 255.942 455.508 263.553 455.508 272.942V320.942C455.508 330.33 447.897 337.942 438.508 337.942H390.508C381.119 337.942 373.508 330.33 373.508 320.942V272.942ZM391.508 273.942V319.942H437.508V273.942H391.508ZM173.508 376.942C173.508 367.553 181.119 359.942 190.508 359.942H246.508C255.897 359.942 263.508 367.553 263.508 376.942V424.942C263.508 434.331 255.897 441.942 246.508 441.942H190.508C181.119 441.942 173.508 434.331 173.508 424.942V376.942ZM191.508 377.942V423.942H245.508V377.942H191.508ZM277.508 376.942C277.508 367.553 285.119 359.942 294.508 359.942H342.508C351.897 359.942 359.508 367.553 359.508 376.942V424.942C359.508 434.331 351.897 441.942 342.508 441.942H294.508C285.119 441.942 277.508 434.331 277.508 424.942V376.942ZM295.508 377.942V423.942H341.508V377.942H295.508ZM373.508 376.942C373.508 367.553 381.119 359.942 390.508 359.942H438.508C447.897 359.942 455.508 367.553 455.508 376.942V424.942C455.508 434.331 447.897 441.942 438.508 441.942H390.508C381.119 441.942 373.508 434.331 373.508 424.942V376.942ZM391.508 377.942V423.942H437.508V377.942H391.508Z",fill:"black"}),(0,a.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M64.5078 168.942C64.5078 159.553 72.119 151.942 81.5078 151.942H137.508C146.897 151.942 154.508 159.553 154.508 168.942V372.942C154.508 382.33 146.897 389.942 137.508 389.942H81.5078C72.119 389.942 64.5078 382.33 64.5078 372.942V168.942ZM82.5078 169.942V371.942H136.508V169.942H82.5078Z",fill:"black"}),(0,a.createElement)("rect",{opacity:"0.3",x:"167.119",y:"256.942",width:"336.66",height:"247.271",rx:"30",fill:"#227AFF"})),(0,a.createElement)("defs",null,(0,a.createElement)("clipPath",{id:"clip0_3567_45417"},(0,a.createElement)("rect",{width:"512",height:"512",fill:"white",transform:"translate(0.507812 0.94165)"}))))},edit:({attributes:e,setAttributes:t})=>{const{filterPosition:n,layoutWidth:l}=e,s=(0,r.useBlockProps)({className:o()("lms-archive-courses","is-loaded",[l],{"lms-archive-courses--filter-end":"end"===n}),style:w("archive-courses",e,Wn)}),i=(0,r.useInnerBlocksProps)({className:"lms-archive-courses__inner"},{template:c,templateLock:"all"});return(0,a.createElement)(a.Fragment,null,(0,a.createElement)(Gn,{attributes:e,setAttributes:t}),(0,a.createElement)("div",{...s},(0,a.createElement)("div",{...i})))},save:({attributes:e})=>{const{filterPosition:t,layoutWidth:n}=e,l=r.useBlockProps.save({className:o()("lms-archive-courses",[n],{"lms-archive-courses--filter-end":"end"===t}),style:w("archive-courses",e,Wn)}),s=r.useInnerBlocksProps.save({className:"lms-archive-courses__inner"});return(0,a.createElement)("div",{...l},(0,a.createElement)("div",{...s}))},variations:Kn})},6942:(e,t)=>{var n;!function(){"use strict";var a={}.hasOwnProperty;function l(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=s(e,r(n)))}return e}function r(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return l.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)a.call(e,n)&&e[n]&&(t=s(t,n));return t}function s(e,t){return t?e?e+" "+t:e+t:e}e.exports?(l.default=l,e.exports=l):void 0===(n=function(){return l}.apply(t,[]))||(e.exports=n)}()}},n={};function a(e){var l=n[e];if(void 0!==l)return l.exports;var r=n[e]={exports:{}};return t[e](r,r.exports,a),r.exports}a.m=t,e=[],a.O=(t,n,l,r)=>{if(!n){var s=1/0;for(m=0;m<e.length;m++){for(var[n,l,r]=e[m],o=!0,i=0;i<n.length;i++)(!1&r||s>=r)&&Object.keys(a.O).every((e=>a.O[e](n[i])))?n.splice(i--,1):(o=!1,r<s&&(s=r));if(o){e.splice(m--,1);var c=l();void 0!==c&&(t=c)}}return t}r=r||0;for(var m=e.length;m>0&&e[m-1][2]>r;m--)e[m]=e[m-1];e[m]=[n,l,r]},a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={1625:0,8845:0};a.O.j=t=>0===e[t];var t=(t,n)=>{var l,r,[s,o,i]=n,c=0;if(s.some((t=>0!==e[t]))){for(l in o)a.o(o,l)&&(a.m[l]=o[l]);if(i)var m=i(a)}for(t&&t(n);c<s.length;c++)r=s[c],a.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return a.O(m)},n=globalThis.webpackChunkmasterstudy_lms_learning_management_system=globalThis.webpackChunkmasterstudy_lms_learning_management_system||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})();var l=a.O(void 0,[8845],(()=>a(1720)));l=a.O(l)})();