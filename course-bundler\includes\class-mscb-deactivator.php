<?php

if (!defined('ABSPATH')) {
    exit;
}

/*
 * MSCB_Deactivator handles plugin deactivation and cleanup.
 */
class MSCB_Deactivator {
    /**
     * Drops all custom tables and removes plugin options on deactivation.
     */
    public static function deactivate() {
        // Clear any scheduled events
        wp_clear_scheduled_hook('mscb_daily_cleanup');

        // Remove plugin options
        delete_option('mscb_version');
        delete_option('mscb_db_version');
        delete_option('mscb_settings');
        delete_option('mscb_settings_version');

        /* Drop all plugin custom tables for a clean uninstall */
        global $wpdb;
        $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}mscb_bundles"); //* Drop bundles table
        $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}mscb_bundle_courses"); //* Drop bundle-courses table
        $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}mscb_bundle_enrollments"); //* Drop enrollments table
    }
} 