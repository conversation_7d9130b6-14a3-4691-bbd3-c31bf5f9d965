(()=>{var e,t={1666:(e,t,n)=>{"use strict";const a=window.React,l=window.wp.blocks,r=window.wp.blockEditor;var i=n(6942),o=n.n(i);const s=window.wp.i18n,m=()=>(0,a.createElement)("div",{className:"courses-load-more"},(0,a.createElement)("button",{type:"button",className:"courses-load-more__button"},s.__("Load More","masterstudy-lms-learning-management-system"))),c=()=>(0,a.createElement)("ul",{className:"lms-courses-pagination-list"},(0,a.createElement)("li",{className:"lms-courses-pagination-list__item"},(0,a.createElement)("div",{className:"lms-courses-pagination-list__item-start"})),(0,a.createElement)("li",{className:"lms-courses-pagination-list__item"},(0,a.createElement)("div",{className:"lms-courses-pagination-list__item-link"},"1")),(0,a.createElement)("li",{className:"lms-courses-pagination-list__item"},(0,a.createElement)("div",{className:"lms-courses-pagination-list__item-link"},"2")),(0,a.createElement)("li",{className:"lms-courses-pagination-list__item is-current"},(0,a.createElement)("span",null,"3")),(0,a.createElement)("li",{className:"lms-courses-pagination-list__item"},(0,a.createElement)("div",{className:"lms-courses-pagination-list__item-link"},"4")),(0,a.createElement)("li",{className:"lms-courses-pagination-list__item"},(0,a.createElement)("div",{className:"lms-courses-pagination-list__item-link"},"5")),(0,a.createElement)("li",{className:"lms-courses-pagination-list__item"},(0,a.createElement)("div",{className:"lms-courses-pagination-list__item-end"}))),d=window.wp.components,u=({condition:e,fallback:t=null,children:n})=>(0,a.createElement)(a.Fragment,null,e?n:t),p=(e,t)=>{if(typeof e!=typeof t)return!1;if(Number.isNaN(e)&&Number.isNaN(t))return!0;if("object"!=typeof e||null===e||null===t)return e===t;if(Object.keys(e).length!==Object.keys(t).length)return!1;if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;const n=e.slice().sort(),a=t.slice().sort();return n.every(((e,t)=>p(e,a[t])))}for(const n of Object.keys(e))if(!p(e[n],t[n]))return!1;return!0};let g=function(e){return e.ALL="all",e.SOME="some",e}({}),v=function(e){return e.NORMAL="Normal",e.HOVER="Hover",e.ACTIVE="Active",e.FOCUS="Focus",e}({}),h=function(e){return e.DESKTOP="Desktop",e.TABLET="Tablet",e.MOBILE="Mobile",e}({}),b=function(e){return e.TOP_lEFT="top-left",e.TOP_CENTER="top-center",e.TOP_RIGHT="top-right",e.BOTTOM_lEFT="bottom-left",e.BOTTOM_CENTER="bottom-center",e.BOTTOM_RIGHT="bottom-right",e}({});const f=["",null,void 0,"null","undefined"],_=[".jpg",".jpeg",".png",".gif"],y=e=>f.includes(e),E=(e,t,n="")=>{const a=e[t];return"object"==typeof a&&null!==a?((e,t)=>{return n=e,Object.values(n).every((e=>f.includes(e)))?null:((e,t="")=>{const n=Object.entries(e).reduce(((e,[n,a])=>(e[n]=(a||"0")+t,e)),{});return`${n.top} ${n.right} ${n.bottom} ${n.left}`})(e,t);var n})(a,n):((e,t)=>y(e)?e:(e=>{if("string"!=typeof e)return!1;const t=e.slice(e.lastIndexOf("."));return _.includes(t)||e.startsWith("blob")})(e)?`url('${e}')`:String(e+t))(a,n)},C=(e,t,n)=>{const a={};return n.forEach((({isAdaptive:n,hasHover:l,unit:r},i)=>{if(t.hasOwnProperty(i)){const{unitMeasureDesktop:s,unitMeasureTablet:m,unitMeasureMobile:c}=((e,t)=>{var n;return{unitMeasureDesktop:null!==(n=e[t])&&void 0!==n?n:"",unitMeasureTablet:t?e[t+"Tablet"]:"",unitMeasureMobile:t?e[t+"Mobile"]:""}})(t,r);if(n&&l){const{desktopHoverPropertyName:n,mobileHoverPropertyName:l,tabletHoverPropertyName:r}=(e=>{const t=e+v.HOVER;return{desktopHoverPropertyName:t,tabletHoverPropertyName:t+"Tablet",mobileHoverPropertyName:t+"Mobile"}})(i),o=E(t,n,s);y(o)||(a[`--lms-${e}-${n}`]=o);const d=E(t,r,m);y(d)||(a[`--lms-${e}-${r}`]=d);const u=E(t,l,c);y(u)||(a[`--lms-${e}-${l}`]=u)}if(l){const n=i+v.HOVER,l=E(t,n,s);y(l)||(a[`--lms-${e}-${n}`]=l)}if(n){const{desktopPropertyName:n,mobilePropertyName:l,tabletPropertyName:r}={desktopPropertyName:o=i,tabletPropertyName:o+"Tablet",mobilePropertyName:o+"Mobile"},d=E(t,n,s);y(d)||(a[`--lms-${e}-${n}`]=d);const u=E(t,r,m);y(u)||(a[`--lms-${e}-${r}`]=u);const p=E(t,l,c);y(p)||(a[`--lms-${e}-${l}`]=p)}const d=E(t,i,s);y(d)||(a[`--lms-${e}-${i}`]=d)}var o})),a},N=(s.__("Small","masterstudy-lms-learning-management-system"),s.__("Normal","masterstudy-lms-learning-management-system"),s.__("Large","masterstudy-lms-learning-management-system"),s.__("Extra Large","masterstudy-lms-learning-management-system"),"wp-block-masterstudy-settings__"),w={top:"",right:"",bottom:"",left:""};function S(e){return Array.isArray(e)?e.map((e=>N+e)):N+e}b.TOP_lEFT,b.TOP_CENTER,b.TOP_RIGHT,b.BOTTOM_lEFT,b.BOTTOM_CENTER,b.BOTTOM_RIGHT,s.__("Newest","masterstudy-lms-learning-management-system"),s.__("Oldest","masterstudy-lms-learning-management-system"),s.__("Overall rating","masterstudy-lms-learning-management-system"),s.__("Popular","masterstudy-lms-learning-management-system"),s.__("Price low","masterstudy-lms-learning-management-system"),s.__("Price high","masterstudy-lms-learning-management-system");const M=window.wp.element,x=window.wp.data,T=()=>(0,x.useSelect)((e=>e("core/editor").getDeviceType()||e("core/edit-site")?.__experimentalGetPreviewDeviceType()||e("core/edit-post")?.__experimentalGetPreviewDeviceType()||e("masterstudy/store")?.getDeviceType()),[])||"Desktop",k=(e=!1)=>{const[t,n]=(0,M.useState)(e),a=(0,M.useCallback)((()=>{n(!0)}),[]);return{isOpen:t,onClose:(0,M.useCallback)((()=>{n(!1)}),[]),onOpen:a,onToggle:(0,M.useCallback)((()=>{n((e=>!e))}),[])}},A=(0,M.createContext)(null),H=({children:e,...t})=>(0,a.createElement)(A.Provider,{value:{...t}},e),R=()=>{const e=(0,M.useContext)(A);if(!e)throw new Error("No settings context provided");return e},O=(e="")=>{const{attributes:t,setAttributes:n,onResetByFieldName:a,changedFieldsByName:l}=R();return{value:t[e],onChange:t=>n({[e]:t}),onReset:a.get(e),isChanged:l.get(e)}},D=(e,t=!1,n=!1)=>{const{hoverName:a,onChangeHoverName:l}=(()=>{const[e,t]=(0,M.useState)(v.NORMAL);return{hoverName:e,onChangeHoverName:(0,M.useCallback)((e=>{t(e)}),[])}})(),r=T();return{fieldName:(0,M.useMemo)((()=>{const l=a===v.HOVER?a:"",i=r===h.DESKTOP?"":r;return n&&t?e+l+i:n&&!t?e+l:t&&!n?e+i:e}),[e,n,t,a,r]),hoverName:a,onChangeHoverName:l}},P=(e,t=!1,n="Normal")=>{const a=T(),l=(0,M.useMemo)((()=>{const l=n===v.NORMAL?"":n,r=a===h.DESKTOP?"":a;return l&&t?e+l+r:l&&!t?e+l:t&&!l?e+r:e}),[e,t,n,a]),{value:r,isChanged:i,onReset:o}=O(l);return{fieldName:l,value:r,isChanged:i,onReset:o}},U=(e=[],t=g.ALL)=>{const{attributes:n}=R();return!e.length||(t===g.ALL?e.every((({name:e,value:t})=>{const a=n[e];return Array.isArray(t)?Array.isArray(a)?p(t,a):t.includes(a):t===a})):t!==g.SOME||e.some((({name:e,value:t})=>{const a=n[e];return Array.isArray(t)?Array.isArray(a)?p(t,a):t.includes(a):t===a})))},L=e=>{const t=(0,M.useRef)(null);return(0,M.useEffect)((()=>{const n=n=>{t.current&&!t.current.contains(n.target)&&e()};return document.addEventListener("click",n),()=>{document.removeEventListener("click",n)}}),[t,e]),t},B=e=>(0,a.createElement)(d.SVG,{width:"16",height:"16",viewBox:"0 0 12 13",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(d.Path,{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})),F=e=>(0,a.createElement)(d.SVG,{width:"16",height:"16",viewBox:"0 0 14 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(d.G,{"clip-path":"url(#clip0_1068_38993)"},(0,a.createElement)(d.Path,{d:"M11.1973 8.60005L8.74967 8.11005V5.67171C8.74967 5.517 8.68822 5.36863 8.57882 5.25923C8.46942 5.14984 8.32105 5.08838 8.16634 5.08838H6.99967C6.84496 5.08838 6.69659 5.14984 6.5872 5.25923C6.4778 5.36863 6.41634 5.517 6.41634 5.67171V8.58838H5.24967C5.15021 8.58844 5.05241 8.61391 4.96555 8.66238C4.87869 8.71084 4.80565 8.78068 4.75336 8.86529C4.70106 8.9499 4.67125 9.04646 4.66674 9.14582C4.66223 9.24518 4.68317 9.34405 4.72759 9.43305L6.47759 12.933C6.57676 13.1302 6.77859 13.255 6.99967 13.255H11.083C11.2377 13.255 11.3861 13.1936 11.4955 13.0842C11.6049 12.9748 11.6663 12.8264 11.6663 12.6717V9.17171C11.6665 9.03685 11.6198 8.90612 11.5343 8.80186C11.4487 8.69759 11.3296 8.62626 11.1973 8.60005Z"}),(0,a.createElement)(d.Path,{d:"M10.4997 1.58838H3.49967C2.85626 1.58838 2.33301 2.11221 2.33301 2.75505V6.83838C2.33301 7.4818 2.85626 8.00505 3.49967 8.00505H5.24967V6.83838H3.49967V2.75505H10.4997V6.83838H11.6663V2.75505C11.6663 2.11221 11.1431 1.58838 10.4997 1.58838Z"})),(0,a.createElement)("defs",null,(0,a.createElement)("clipPath",{id:"clip0_1068_38993"},(0,a.createElement)(d.Rect,{width:"14",height:"14",transform:"translate(0 0.421875)"})))),I=[{value:v.NORMAL,label:s.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(B,{onClick:e})},{value:v.HOVER,label:s.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(F,{onClick:e})},{value:v.ACTIVE,label:s.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(F,{onClick:e})},{value:v.FOCUS,label:s.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(F,{onClick:e})}],V={[v.NORMAL]:{icon:(0,a.createElement)(B,null),label:s.__("Normal State","masterstudy-lms-learning-management-system")},[v.HOVER]:{icon:(0,a.createElement)(F,null),label:s.__("Hovered State","masterstudy-lms-learning-management-system")},[v.ACTIVE]:{icon:(0,a.createElement)(F,null),label:s.__("Active State","masterstudy-lms-learning-management-system")},[v.FOCUS]:{icon:(0,a.createElement)(F,null),label:s.__("Focus State","masterstudy-lms-learning-management-system")}},z=(e,t)=>{let n=[];return n=e.length?I.filter((t=>e.includes(t.value))):I,n=n.filter((e=>e.value!==t)),{ICONS_MAP:V,options:n}},[j,W,$,Z,G]=S(["hover-state","hover-state__selected","hover-state__selected__opened-menu","hover-state__menu","hover-state__menu__item"]),K=({stateOptions:e,currentState:t,onSelect:n})=>{const{isOpen:l,onOpen:r,onClose:i}=k(),s=L(i),{ICONS_MAP:m,options:c}=z(e,t);return(0,a.createElement)("div",{className:j,ref:s},(0,a.createElement)("div",{className:o()([W],{[$]:l}),onClick:r,title:m[t]?.label},m[t]?.icon),(0,a.createElement)(u,{condition:l},(0,a.createElement)("div",{className:Z},c.map((({value:e,icon:t,label:l})=>(0,a.createElement)("div",{key:e,className:G,title:l},t((()=>n(e)))))))))},X=S("color-indicator"),Y=(0,M.memo)((({color:e,onChange:t})=>(0,a.createElement)("div",{className:X},(0,a.createElement)(r.PanelColorSettings,{enableAlpha:!0,disableCustomColors:!1,__experimentalHasMultipleOrigins:!0,__experimentalIsRenderedInSidebar:!0,colorSettings:[{label:"",value:e,onChange:t}]}))));var q;function J(){return J=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},J.apply(null,arguments)}var Q,ee,te=function(e){return a.createElement("svg",J({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 12 13"},e),q||(q=a.createElement("path",{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})))};function ne(){return ne=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},ne.apply(null,arguments)}var ae=function(e){return a.createElement("svg",ne({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 14 15"},e),Q||(Q=a.createElement("g",{clipPath:"url(#state-hover_svg__a)"},a.createElement("path",{d:"M11.197 8.6 8.75 8.11V5.672a.583.583 0 0 0-.584-.584H7a.583.583 0 0 0-.584.584v2.916H5.25a.584.584 0 0 0-.522.845l1.75 3.5c.099.197.3.322.522.322h4.083a.583.583 0 0 0 .583-.583v-3.5a.58.58 0 0 0-.469-.572"}),a.createElement("path",{d:"M10.5 1.588h-7c-.644 0-1.167.524-1.167 1.167v4.083c0 .644.523 1.167 1.167 1.167h1.75V6.838H3.5V2.755h7v4.083h1.166V2.755c0-.643-.523-1.167-1.166-1.167"}))),ee||(ee=a.createElement("defs",null,a.createElement("clipPath",{id:"state-hover_svg__a"},a.createElement("path",{d:"M0 .422h14v14H0z"})))))};const le=[{value:v.NORMAL,label:s.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(te,{onClick:e})},{value:v.HOVER,label:s.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(ae,{onClick:e})}],re={[v.NORMAL]:{icon:(0,a.createElement)(te,null),label:s.__("Normal State","masterstudy-lms-learning-management-system")},[v.HOVER]:{icon:(0,a.createElement)(ae,null),label:s.__("Hovered State","masterstudy-lms-learning-management-system")}},ie=S("hover-state"),oe=S("hover-state__selected"),se=S("hover-state__selected__opened-menu"),me=S("has-changes"),ce=S("hover-state__menu"),de=S("hover-state__menu__item"),ue=(0,M.memo)((e=>{const{hoverName:t,onChangeHoverName:n,fieldName:l}=e,{changedFieldsByName:r}=R(),i=r.get(l),{isOpen:s,onOpen:m,onClose:c}=k(),d=L(c),{ICONS_MAP:p,options:g}=(e=>{const t=(0,M.useMemo)((()=>le.filter((t=>t.value!==e))),[e]);return{ICONS_MAP:re,options:t}})(t),v=(0,M.useCallback)((e=>{n(e),c()}),[n,c]);return(0,a.createElement)("div",{className:ie,ref:d},(0,a.createElement)("div",{className:o()([oe],{[se]:s,[me]:i}),onClick:m,title:p[t]?.label},p[t]?.icon),(0,a.createElement)(u,{condition:s},(0,a.createElement)("div",{className:ce},g.map((({value:e,icon:t,label:n})=>(0,a.createElement)("div",{key:e,className:de,title:n},t((()=>v(e)))))))))})),pe={Desktop:{icon:"desktop",label:s.__("Desktop","masterstudy-lms-learning-management-system")},Tablet:{icon:"tablet",label:s.__("Tablet","masterstudy-lms-learning-management-system")},Mobile:{icon:"smartphone",label:s.__("Mobile","masterstudy-lms-learning-management-system")}},ge=[{value:h.DESKTOP,icon:"desktop",label:s.__("Desktop","masterstudy-lms-learning-management-system")},{value:h.TABLET,icon:"tablet",label:s.__("Tablet","masterstudy-lms-learning-management-system")},{value:h.MOBILE,icon:"smartphone",label:s.__("Mobile","masterstudy-lms-learning-management-system")}],ve=S("device-picker"),he=S("device-picker__selected"),be=S("device-picker__selected__opened-menu"),fe=S("device-picker__menu"),_e=S("device-picker__menu__item"),ye=()=>{const{isOpen:e,onOpen:t,onClose:n}=k(),{value:l,onChange:r}=(e=>{const t=T(),n=(0,x.useDispatch)();return{value:(0,M.useMemo)((()=>pe[t]),[t]),onChange:t=>{n("core/edit-site")&&n("core/edit-site").__experimentalSetPreviewDeviceType?n("core/edit-site").__experimentalSetPreviewDeviceType(t):n("core/edit-post")&&n("core/edit-post").__experimentalSetPreviewDeviceType?n("core/edit-post").__experimentalSetPreviewDeviceType(t):n("masterstudy/store").setDeviceType(t),e()}}})(n),i=(e=>(0,M.useMemo)((()=>ge.filter((t=>t.icon!==e))),[e]))(l.icon),s=L(n);return(0,a.createElement)("div",{className:ve,ref:s},(0,a.createElement)(d.Dashicon,{className:o()([he],{[be]:e}),icon:l.icon,size:16,onClick:t,title:l.label}),(0,a.createElement)(u,{condition:e},(0,a.createElement)("div",{className:fe},i.map((e=>(0,a.createElement)(d.Dashicon,{key:e.value,icon:e.icon,size:16,onClick:()=>r(e.value),className:_e,title:e.label}))))))},Ee=S("reset-button"),Ce=({onReset:e})=>(0,a.createElement)(d.Dashicon,{icon:"undo",onClick:e,className:Ee,size:16}),Ne=[{label:"PX",value:"px"},{label:"%",value:"%"},{label:"EM",value:"em"},{label:"REM",value:"rem"},{label:"VW",value:"vw"},{label:"VH",value:"vh"}],we=S("unit"),Se=S("unit__single"),Me=S("unit__list"),xe=({name:e,isAdaptive:t})=>{const{isOpen:n,onOpen:l,onClose:r}=k(),{fieldName:i}=D(e,t),{value:o,onChange:s}=O(i),m=L(r);return(0,a.createElement)("div",{className:we,ref:m},(0,a.createElement)("div",{className:Se,onClick:l},o),(0,a.createElement)(u,{condition:n},(0,a.createElement)("div",{className:Me},Ne.map((({value:e,label:t})=>(0,a.createElement)("div",{key:e,onClick:()=>(s(e),void r())},t))))))},Te=S("popover-modal"),ke=S("popover-modal__close dashicon dashicons dashicons-no-alt"),Ae=e=>{const{isOpen:t,onClose:n,popoverContent:l}=e;return(0,a.createElement)(u,{condition:t},(0,a.createElement)(d.Popover,{position:"middle left",onClose:n,className:Te},l,(0,a.createElement)("span",{onClick:n,className:ke})))},He=S("setting-label"),Re=S("setting-label__content"),Oe=e=>{const{label:t,isChanged:n=!1,onReset:l,showDevicePicker:r=!0,HoverStateControl:i=null,unitName:o,popoverContent:s=null,dependencies:m}=e,{isOpen:c,onClose:d,onToggle:p}=k();return U(m)?(0,a.createElement)("div",{className:He},(0,a.createElement)("div",{className:Re},(0,a.createElement)("div",{onClick:p},t),(0,a.createElement)(u,{condition:Boolean(s)},(0,a.createElement)(Ae,{isOpen:c,onClose:d,popoverContent:s})),(0,a.createElement)(u,{condition:r},(0,a.createElement)(ye,null)),(0,a.createElement)(u,{condition:Boolean(i)},i)),(0,a.createElement)(u,{condition:Boolean(o)},(0,a.createElement)(xe,{name:o,isAdaptive:r})),(0,a.createElement)(u,{condition:n},(0,a.createElement)(Ce,{onReset:l}))):null},De=S("suffix"),Pe=()=>(0,a.createElement)("div",{className:De},(0,a.createElement)(d.Dashicon,{icon:"color-picker",size:16})),Ue=S("color-picker"),Le=e=>{const{name:t,label:n,placeholder:l,dependencyMode:r,dependencies:i,isAdaptive:o=!1,hasHover:s=!1}=e,{fieldName:m,hoverName:c,onChangeHoverName:p}=D(t,o,s),{value:g,isChanged:v,onChange:h,onReset:b}=O(m);return U(i,r)?(0,a.createElement)("div",{className:Ue},(0,a.createElement)(u,{condition:Boolean(n)},(0,a.createElement)(Oe,{label:n,isChanged:v,onReset:b,showDevicePicker:o,HoverStateControl:(0,a.createElement)(u,{condition:s},(0,a.createElement)(ue,{hoverName:c,onChangeHoverName:p,fieldName:m}))})),(0,a.createElement)(d.__experimentalInputControl,{prefix:(0,a.createElement)(Y,{color:g,onChange:h}),suffix:(0,a.createElement)(Pe,null),onChange:h,value:g,placeholder:l})):null},Be=S("number-steppers"),Fe=S("indent-steppers"),Ie=S("indent-stepper-plus"),Ve=S("indent-stepper-minus"),ze=({onIncrement:e,onDecrement:t,withArrows:n=!1})=>n?(0,a.createElement)("span",{className:Fe},(0,a.createElement)("button",{onClick:e,className:Ie}),(0,a.createElement)("button",{onClick:t,className:Ve})):(0,a.createElement)("span",{className:Be},(0,a.createElement)("button",{onClick:e},"+"),(0,a.createElement)("button",{onClick:t},"-")),[je,We]=S(["indents","indents-control"]),$e=({name:e,label:t,unitName:n,popoverContent:l,dependencyMode:r,dependencies:i,isAdaptive:o=!1})=>{const{fieldName:m}=D(e,o),{value:c,onResetSegmentedBox:p,hasChanges:g,handleInputIncrement:v,handleInputDecrement:h,updateDirectionsValues:b,lastFieldValue:f}=((e,t)=>{const{value:n,isChanged:a,onChange:l,onReset:r}=O(e),{onResetByFieldName:i,changedFieldsByName:o}=R(),s=a||o.get(t),m=e=>{l({...n,...e})},[c,d]=(0,M.useState)(!1);return{value:n,onResetSegmentedBox:()=>{r(),i.get(t)()},hasChanges:s,handleInputIncrement:e=>Number(n[e])+1,handleInputDecrement:e=>Number(n[e])-1,updateDirectionsValues:(e,t,n)=>{e?(d(!1),m({top:n,right:n,bottom:n,left:n})):(d(n),m({[t]:n}))},lastFieldValue:c}})(m,n),[_,y]=(0,M.useState)((()=>{const{left:e,right:t,top:n,bottom:a}=c;return""!==e&&e===t&&t===n&&n===a})),E=e=>{const[t,n]=Object.entries(e)[0];b(_,t,n)},C=e=>()=>{const t=v(e);b(_,e,String(t))},N=e=>()=>{const t=h(e);b(_,e,String(t))};return U(i,r)?(0,a.createElement)("div",{className:je},(0,a.createElement)(u,{condition:Boolean(t)},(0,a.createElement)(Oe,{label:null!=t?t:"",isChanged:g,onReset:p,unitName:n,popoverContent:l,showDevicePicker:o})),(0,a.createElement)("div",{className:`${We} ${_?"active":""}`},(0,a.createElement)("div",null,(0,a.createElement)(d.__experimentalNumberControl,{value:c.top,onChange:e=>{E({top:e})},spinControls:"none",suffix:(0,a.createElement)(ze,{onIncrement:C("top"),onDecrement:N("top"),withArrows:!0})}),(0,a.createElement)("div",null,s.__("Top","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(d.__experimentalNumberControl,{value:c.right,onChange:e=>{E({right:e})},spinControls:"none",suffix:(0,a.createElement)(ze,{onIncrement:C("right"),onDecrement:N("right"),withArrows:!0})}),(0,a.createElement)("div",null,s.__("Right","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(d.__experimentalNumberControl,{value:c.bottom,onChange:e=>{E({bottom:e})},spinControls:"none",suffix:(0,a.createElement)(ze,{onIncrement:C("bottom"),onDecrement:N("bottom"),withArrows:!0})}),(0,a.createElement)("div",null,s.__("Bottom","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(d.__experimentalNumberControl,{value:c.left,onChange:e=>{E({left:e})},spinControls:"none",suffix:(0,a.createElement)(ze,{onIncrement:C("left"),onDecrement:N("left"),withArrows:!0})}),(0,a.createElement)("div",null,s.__("Left","masterstudy-lms-learning-management-system"))),(0,a.createElement)(d.Dashicon,{icon:"dashicons "+(_?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{_||!1===f||b(!0,"left",f),y((e=>!e))}}))):null},[Ze,Ge,Ke,Xe]=S(["toggle-group-wrapper","toggle-group","toggle-group__toggle","toggle-group__active-toggle"]),Ye=e=>{const{name:t,options:n,label:l,isAdaptive:r=!1,dependencyMode:i,dependencies:s}=e,{fieldName:m}=D(t,r),{value:c,isChanged:d,onChange:p,onReset:g}=O(m);return U(s,i)?(0,a.createElement)("div",{className:Ze},(0,a.createElement)(u,{condition:Boolean(l)},(0,a.createElement)(Oe,{label:l,isChanged:d,onReset:g,showDevicePicker:r})),(0,a.createElement)("div",{className:Ge},n.map((e=>(0,a.createElement)("div",{key:e.value,className:o()([Ke],{[Xe]:e.value===c}),onClick:()=>p(e.value)},e.label))))):null},[qe,Je,Qe,et]=S(["border-control","border-control-solid","border-control-dashed","border-control-dotted"]),tt=e=>{const{label:t,borderStyleName:n,borderColorName:l,borderWidthName:r,dependencyMode:i,dependencies:m,isAdaptive:c=!1,hasHover:d=!1}=e,[p,g]=(0,M.useState)("Normal"),{fieldName:v,value:h,isChanged:b,onReset:f}=P(n,c,p),{fieldName:_,isChanged:y,onReset:E}=P(l,c,p),{fieldName:C,isChanged:N,onReset:w}=P(r,c,p);if(!U(m,i))return null;const S=b||y||N;return(0,a.createElement)("div",{className:o()([qe],{"has-reset-button":S})},(0,a.createElement)(Oe,{label:t,isChanged:S,onReset:()=>{f(),E(),w()},showDevicePicker:c,HoverStateControl:(0,a.createElement)(u,{condition:d},(0,a.createElement)(K,{stateOptions:["Normal","Hover"],currentState:p,onSelect:g}))}),(0,a.createElement)(Ye,{options:[{label:(0,a.createElement)("span",null,s.__("None","masterstudy-lms-learning-management-system")),value:"none"},{label:(0,a.createElement)("span",{className:Je}),value:"solid"},{label:(0,a.createElement)("span",{className:Qe},(0,a.createElement)("span",null)),value:"dashed"},{label:(0,a.createElement)("span",{className:et},(0,a.createElement)("span",null,(0,a.createElement)("span",null))),value:"dotted"}],name:v}),(0,a.createElement)(u,{condition:"none"!==h},(0,a.createElement)(Le,{name:_,placeholder:s.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)($e,{name:C})))},nt=S("border-radius"),at=S("border-radius-control"),lt=({name:e,label:t,unitName:n,popoverContent:l,dependencyMode:r,dependencies:i,isAdaptive:s=!1,hasHover:m=!1})=>{const{fieldName:c}=D(e,s,m),{value:u,onResetBorderRadius:p,hasChanges:g,handleInputIncrement:v,handleInputDecrement:h,updateDirectionsValues:b,lastFieldValue:f}=((e,t)=>{const[n,a]=(0,M.useState)(!1),{value:l,isChanged:r,onChange:i,onReset:o}=O(e),{onResetByFieldName:s,changedFieldsByName:m}=R(),c=r||m.get(t),d=e=>{i({...l,...e})};return{value:l,onResetBorderRadius:()=>{o(),s.get(t)()},hasChanges:c,handleInputIncrement:e=>Number(l[e])+1,handleInputDecrement:e=>Number(l[e])-1,updateDirectionsValues:(e,t,n)=>{e?(d({top:n,right:n,bottom:n,left:n}),a(!1)):(d({[t]:n}),a(n))},lastFieldValue:n}})(c,n),[_,y]=(0,M.useState)((()=>{const{left:e,right:t,top:n,bottom:a}=u;return""!==e&&e===t&&t===n&&n===a})),E=e=>{const[t,n]=Object.entries(e)[0];b(_,t,n)},C=e=>()=>{const t=v(e);b(_,e,String(t))},N=e=>()=>{const t=h(e);b(_,e,String(t))};return U(i,r)?(0,a.createElement)("div",{className:nt},(0,a.createElement)(Oe,{label:t,isChanged:g,onReset:p,unitName:n,popoverContent:l,showDevicePicker:s}),(0,a.createElement)("div",{className:o()([at],{"has-reset-button":g,active:_})},(0,a.createElement)("div",{className:"number-control-top"},(0,a.createElement)(d.__experimentalNumberControl,{value:u.top,onChange:e=>{E({top:e})},spinControls:"none",suffix:(0,a.createElement)(ze,{onIncrement:C("top"),onDecrement:N("top"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-right"},(0,a.createElement)(d.__experimentalNumberControl,{value:u.right,onChange:e=>{E({right:e})},spinControls:"none",suffix:(0,a.createElement)(ze,{onIncrement:C("right"),onDecrement:N("right"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-left"},(0,a.createElement)(d.__experimentalNumberControl,{value:u.left,onChange:e=>{E({left:e})},spinControls:"none",suffix:(0,a.createElement)(ze,{onIncrement:C("left"),onDecrement:N("left"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-bottom"},(0,a.createElement)(d.__experimentalNumberControl,{value:u.bottom,onChange:e=>{E({bottom:e})},spinControls:"none",suffix:(0,a.createElement)(ze,{onIncrement:C("bottom"),onDecrement:N("bottom"),withArrows:!0})})),(0,a.createElement)(d.Dashicon,{icon:"dashicons "+(_?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{_||!1===f||b(!0,"left",f),y((e=>!e))}}))):null},rt=(S("box-shadow-preset"),S("presets")),it=S("presets__item-wrapper"),ot=S("presets__item-wrapper__preset"),st=S("presets__item-wrapper__name"),mt=((0,M.memo)((e=>{const{presets:t,activePreset:n,onSelectPreset:l,PresetItem:r,detectIsActive:i,detectByIndex:s=!1}=e;return(0,a.createElement)("div",{className:rt},t.map((({name:e,...t},m)=>(0,a.createElement)("div",{key:m,className:o()([it],{active:i(n,s?m:t)}),onClick:()=>l(t)},(0,a.createElement)("div",{className:ot},(0,a.createElement)(r,{preset:t})),(0,a.createElement)("span",{className:st},e)))))})),S("range-control")),ct=e=>{const{name:t,label:n,min:l,max:r,unitName:i,dependencyMode:o,dependencies:s,isAdaptive:m=!1}=e,{fieldName:c}=D(t,m),{value:p,onChange:g,onResetNumberField:v,hasChanges:h}=((e,t)=>{const{value:n,isChanged:a,onChange:l,onReset:r}=O(e),{onResetByFieldName:i,changedFieldsByName:o}=R();return{value:n,onChange:l,onResetNumberField:()=>{r(),i.get(t)()},hasChanges:a||o.get(t)}})(c,i);return U(s,o)?(0,a.createElement)("div",{className:mt},(0,a.createElement)(u,{condition:Boolean(n)},(0,a.createElement)(Oe,{label:n,isChanged:h,onReset:v,unitName:i,showDevicePicker:m})),(0,a.createElement)(d.RangeControl,{value:p,onChange:g,min:l,max:r})):null},dt=S("switch"),ut=e=>{const{name:t,label:n,dependencyMode:l,dependencies:r,isAdaptive:i=!1}=e,{fieldName:o}=D(t,i),{value:s,onChange:m}=O(o);return U(r,l)?(0,a.createElement)("div",{className:dt,"data-has-label":Boolean(n).toString()},(0,a.createElement)(d.ToggleControl,{label:n,checked:s,onChange:m}),(0,a.createElement)(u,{condition:i},(0,a.createElement)(ye,null))):null},pt=(S("box-shadow-settings"),S("box-shadow-presets-title"),S("input-field"),S("input-field-control"),S("number-field")),gt=S("number-field-control"),vt=e=>{const{name:t,label:n,unitName:l,help:r,popoverContent:i,dependencyMode:o,dependencies:s,isAdaptive:m=!1}=e,{fieldName:c}=D(t,m),{value:u,onResetNumberField:p,hasChanges:g,handleIncrement:v,handleDecrement:h,handleInputChange:b}=((e,t)=>{const{value:n,isChanged:a,onChange:l,onReset:r}=O(e),{onResetByFieldName:i,changedFieldsByName:o}=R(),s=a||o.get(t);return{value:n,onResetNumberField:()=>{r(),i.get(t)()},hasChanges:s,handleIncrement:()=>{l(n+1)},handleDecrement:()=>{l(n-1)},handleInputChange:e=>{const t=Number(""===e?0:e);l(t)}}})(c,l);return U(s,o)?(0,a.createElement)("div",{className:pt},(0,a.createElement)(Oe,{label:n,isChanged:g,onReset:p,unitName:l,showDevicePicker:m,popoverContent:i}),(0,a.createElement)("div",{className:gt},(0,a.createElement)(d.__experimentalNumberControl,{value:u,onChange:b,spinControls:"none",suffix:(0,a.createElement)(ze,{onIncrement:v,onDecrement:h})})),r&&(0,a.createElement)("small",null,r)):null},ht=({className:e})=>(0,a.createElement)("div",{className:e},s.__("No options","masterstudy-lms-learning-management-system")),bt=S("select__single-item"),ft=S("select__container"),_t=S("select__container__multi-item"),yt=({multiple:e,value:t,options:n,onChange:l})=>{const{singleValue:r,multipleValue:i}=((e,t,n)=>({singleValue:(0,M.useMemo)((()=>t?null:n.find((t=>t.value===e))?.label),[t,e,n]),multipleValue:(0,M.useMemo)((()=>t?e:null),[t,e])}))(t,e,n);return(0,a.createElement)(u,{condition:e,fallback:(0,a.createElement)("div",{className:bt},r)},(0,a.createElement)("div",{className:ft},i?.map((e=>{const t=n.find((t=>t.value===e));return t?(0,a.createElement)("div",{key:t.value,className:_t},(0,a.createElement)("div",null,t.label),(0,a.createElement)(d.Dashicon,{icon:"no-alt",onClick:()=>l(t.value),size:16})):null}))))},Et=S("select"),Ct=S("select__select-box"),Nt=S("select__placeholder"),wt=S("select__select-box-multiple"),St=S("select__menu"),Mt=S("select__menu__options-container"),xt=S("select__menu__item"),Tt=e=>{const{options:t,multiple:n=!1,placeholder:l="Select",value:r,onSelect:i}=e,{isOpen:s,onToggle:m,onClose:c}=k(),p=L(c),g=((e,t,n)=>(0,M.useMemo)((()=>n&&Array.isArray(e)?t.filter((t=>!e.includes(t.value))):t.filter((t=>t.value!==e))),[e,t,n]))(r,t,n),v=((e,t,n,a)=>(0,M.useCallback)((l=>{if(t&&Array.isArray(e)){const t=e.includes(l)?e.filter((e=>e!==l)):[...e,l];n(t)}else n(l),a()}),[t,e,n,a]))(r,n,i,c),h=((e,t)=>(0,M.useMemo)((()=>t&&Array.isArray(e)?Boolean(e.length):Boolean(e)),[t,e]))(r,n),b=n&&Array.isArray(r)&&r?.length>0;return(0,a.createElement)("div",{className:Et,ref:p},(0,a.createElement)("div",{className:o()([Ct],{[wt]:b}),onClick:m},(0,a.createElement)(u,{condition:h,fallback:(0,a.createElement)("div",{className:Nt},l)},(0,a.createElement)(yt,{onChange:v,options:t,multiple:n,value:r})),(0,a.createElement)(d.Dashicon,{icon:s?"arrow-up-alt2":"arrow-down-alt2",size:16,style:{color:"#4D5E6F"}})),(0,a.createElement)(u,{condition:s},(0,a.createElement)("div",{className:St},(0,a.createElement)(u,{condition:Boolean(g.length),fallback:(0,a.createElement)(ht,{className:xt})},(0,a.createElement)("div",{className:Mt},g.map((e=>(0,a.createElement)("div",{key:e.value,onClick:()=>v(e.value),className:xt},e.label))))))))},kt=S("setting-select"),At=e=>{const{name:t,options:n,label:l,multiple:r=!1,placeholder:i,isAdaptive:o=!1,dependencyMode:s,dependencies:m}=e,{fieldName:c}=D(t,o),{value:d,isChanged:p,onChange:g,onReset:v}=O(c);return U(m,s)?(0,a.createElement)("div",{className:kt},(0,a.createElement)(u,{condition:Boolean(l)},(0,a.createElement)(Oe,{label:l,isChanged:p,onReset:v,showDevicePicker:o})),(0,a.createElement)(Tt,{options:n,value:d,onSelect:g,multiple:r,placeholder:i})):null},Ht=(S("row-select"),S("row-select__label"),S("row-select__control"),S("typography-select")),Rt=S("typography-select-label"),Ot=e=>{const{name:t,label:n,options:l,isAdaptive:r=!1}=e,{fieldName:i}=D(t,r),{isChanged:o,onReset:s}=O(i);return(0,a.createElement)("div",{className:Ht},(0,a.createElement)("div",{className:Rt},(0,a.createElement)("div",null,n),(0,a.createElement)(u,{condition:r},(0,a.createElement)(ye,null))),(0,a.createElement)(At,{name:t,options:l,isAdaptive:r}),(0,a.createElement)(u,{condition:o},(0,a.createElement)(Ce,{onReset:s})))},Dt=S("typography"),Pt=e=>{const{fontSizeName:t,fontWeightName:n,textTransformName:l,fontStyleName:r,textDecorationName:i,lineHeightName:o,letterSpacingName:m,wordSpacingName:c,fontSizeUnitName:d,lineHeightUnitName:u,letterSpacingUnitName:p,wordSpacingUnitName:g,dependencyMode:v,dependencies:h,isAdaptive:b=!1}=e,{fontWeightOptions:f,textTransformOptions:_,fontStyleOptions:y,textDecorationOptions:E}={fontWeightOptions:[{label:s.__("100 (Thin)","masterstudy-lms-learning-management-system"),value:"100"},{label:s.__("200 (Extra Light)","masterstudy-lms-learning-management-system"),value:"200"},{label:s.__("300 (Light)","masterstudy-lms-learning-management-system"),value:"300"},{label:s.__("400 (Normal)","masterstudy-lms-learning-management-system"),value:"400"},{label:s.__("500 (Medium)","masterstudy-lms-learning-management-system"),value:"500"},{label:s.__("600 (Semi Bold)","masterstudy-lms-learning-management-system"),value:"600"},{label:s.__("700 (Bold)","masterstudy-lms-learning-management-system"),value:"700"},{label:s.__("800 (Extra Bold)","masterstudy-lms-learning-management-system"),value:"800"},{label:s.__("900 (Extra)","masterstudy-lms-learning-management-system"),value:"900"}],textTransformOptions:[{label:s.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:s.__("Uppercase","masterstudy-lms-learning-management-system"),value:"uppercase"},{label:s.__("Lowercase","masterstudy-lms-learning-management-system"),value:"lowercase"},{label:s.__("Capitalize","masterstudy-lms-learning-management-system"),value:"capitalize"},{label:s.__("Normal","masterstudy-lms-learning-management-system"),value:"none"}],fontStyleOptions:[{label:s.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:s.__("Normal","masterstudy-lms-learning-management-system"),value:"none"},{label:s.__("Italic","masterstudy-lms-learning-management-system"),value:"italic"},{label:s.__("Oblique","masterstudy-lms-learning-management-system"),value:"oblique"}],textDecorationOptions:[{label:s.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:s.__("Underline","masterstudy-lms-learning-management-system"),value:"underline"},{label:s.__("Line Through","masterstudy-lms-learning-management-system"),value:"line-through"},{label:s.__("None","masterstudy-lms-learning-management-system"),value:"none"}]};return U(h,v)?(0,a.createElement)("div",{className:Dt},(0,a.createElement)(ct,{name:t,label:s.__("Size","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:d,isAdaptive:b}),(0,a.createElement)(Ot,{name:n,label:s.__("Weight","masterstudy-lms-learning-management-system"),options:f}),(0,a.createElement)(Ot,{name:l,label:s.__("Transform","masterstudy-lms-learning-management-system"),options:_}),(0,a.createElement)(Ot,{name:r,label:s.__("Style","masterstudy-lms-learning-management-system"),options:y}),(0,a.createElement)(Ot,{name:i,label:s.__("Decoration","masterstudy-lms-learning-management-system"),options:E}),(0,a.createElement)(ct,{name:o,label:s.__("Line Height","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:u,isAdaptive:b}),(0,a.createElement)(ct,{name:m,label:s.__("Letter Spacing","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:p,isAdaptive:b}),c&&(0,a.createElement)(ct,{name:c,label:s.__("Word Spacing","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:g,isAdaptive:b})):null},Ut=(S("file-upload"),S("file-upload__wrap"),S("file-upload__image"),S("file-upload__remove"),S("file-upload__replace"),(0,M.createContext)({activeTab:0,setActiveTab:()=>{}})),Lt=()=>{const e=(0,M.useContext)(Ut);if(!e)throw new Error("useTabs should be used inside Tabs");return e},Bt=({children:e})=>{const[t,n]=(0,M.useState)(0);return(0,a.createElement)(Ut.Provider,{value:{activeTab:t,setActiveTab:n}},(0,a.createElement)("div",{className:`active-tab-${t}`},e))},Ft=S("tab-list"),It=({children:e})=>(0,a.createElement)("div",{className:Ft},M.Children.map(e,((e,t)=>(0,M.cloneElement)(e,{index:t})))),Vt=S("tab"),zt=S("tab-active"),jt=S("content"),Wt=({index:e,title:t,icon:n})=>{const{activeTab:l,setActiveTab:r}=Lt();return(0,a.createElement)("div",{className:o()([Vt],{[zt]:l===e}),onClick:()=>r(e)},(0,a.createElement)("div",{className:jt},(0,a.createElement)("div",null,n),(0,a.createElement)("div",null,t)))},$t=({children:e})=>(0,a.createElement)("div",null,M.Children.map(e,((e,t)=>(0,M.cloneElement)(e,{index:t})))),Zt=S("tab-panel"),Gt=({index:e,children:t})=>{const{activeTab:n}=Lt();return n===e?(0,a.createElement)("div",{className:Zt},t):null},Kt=({generalTab:e,styleTab:t,advancedTab:n})=>(0,a.createElement)(Bt,null,(0,a.createElement)(It,null,(0,a.createElement)(Wt,{title:s.__("General","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(d.Dashicon,{icon:"layout"})}),(0,a.createElement)(Wt,{title:s.__("Style","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(d.Dashicon,{icon:"admin-appearance"})}),(0,a.createElement)(Wt,{title:s.__("Advanced","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(d.Dashicon,{icon:"admin-settings"})})),(0,a.createElement)($t,null,(0,a.createElement)(Gt,null,e),(0,a.createElement)(Gt,null,t),(0,a.createElement)(Gt,null,n)));window.ReactDOM;"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement;function Xt(e){const t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function Yt(e){return"nodeType"in e}function qt(e){var t,n;return e?Xt(e)?e:Yt(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function Jt(e){const{Document:t}=qt(e);return e instanceof t}function Qt(e){return!Xt(e)&&e instanceof qt(e).HTMLElement}function en(e){return e instanceof qt(e).SVGElement}function tn(e){return e?Xt(e)?e.document:Yt(e)?Jt(e)?e:Qt(e)||en(e)?e.ownerDocument:document:document:document}function nn(e){return function(t){for(var n=arguments.length,a=new Array(n>1?n-1:0),l=1;l<n;l++)a[l-1]=arguments[l];return a.reduce(((t,n)=>{const a=Object.entries(n);for(const[n,l]of a){const a=t[n];null!=a&&(t[n]=a+e*l)}return t}),{...t})}}const an=nn(-1);function ln(e){if(function(e){if(!e)return!1;const{TouchEvent:t}=qt(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return function(e){return"clientX"in e&&"clientY"in e}(e)?{x:e.clientX,y:e.clientY}:null}var rn;!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(rn||(rn={}));const on=Object.freeze({x:0,y:0});var sn,mn,cn,dn;!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(sn||(sn={}));class un{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach((e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)}))},this.target=e}add(e,t,n){var a;null==(a=this.target)||a.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function pn(e,t){const n=Math.abs(e.x),a=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+a**2)>t:"x"in t&&"y"in t?n>t.x&&a>t.y:"x"in t?n>t.x:"y"in t&&a>t.y}function gn(e){e.preventDefault()}function vn(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(mn||(mn={})),(dn=cn||(cn={})).Space="Space",dn.Down="ArrowDown",dn.Right="ArrowRight",dn.Left="ArrowLeft",dn.Up="ArrowUp",dn.Esc="Escape",dn.Enter="Enter";cn.Space,cn.Enter,cn.Esc,cn.Space,cn.Enter;function hn(e){return Boolean(e&&"distance"in e)}function bn(e){return Boolean(e&&"delay"in e)}class fn{constructor(e,t,n){var a;void 0===n&&(n=function(e){const{EventTarget:t}=qt(e);return e instanceof t?e:tn(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;const{event:l}=e,{target:r}=l;this.props=e,this.events=t,this.document=tn(r),this.documentListeners=new un(this.document),this.listeners=new un(n),this.windowListeners=new un(qt(r)),this.initialCoordinates=null!=(a=ln(l))?a:on,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),this.windowListeners.add(mn.Resize,this.handleCancel),this.windowListeners.add(mn.DragStart,gn),this.windowListeners.add(mn.VisibilityChange,this.handleCancel),this.windowListeners.add(mn.ContextMenu,gn),this.documentListeners.add(mn.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(bn(t))return void(this.timeoutId=setTimeout(this.handleStart,t.delay));if(hn(t))return}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handleStart(){const{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(mn.Click,vn,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(mn.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;const{activated:n,initialCoordinates:a,props:l}=this,{onMove:r,options:{activationConstraint:i}}=l;if(!a)return;const o=null!=(t=ln(e))?t:on,s=an(a,o);if(!n&&i){if(hn(i)){if(null!=i.tolerance&&pn(s,i.tolerance))return this.handleCancel();if(pn(s,i.distance))return this.handleStart()}return bn(i)&&pn(s,i.tolerance)?this.handleCancel():void 0}e.cancelable&&e.preventDefault(),r(o)}handleEnd(){const{onEnd:e}=this.props;this.detach(),e()}handleCancel(){const{onCancel:e}=this.props;this.detach(),e()}handleKeydown(e){e.code===cn.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}const yn={move:{name:"pointermove"},end:{name:"pointerup"}};(class extends fn{constructor(e){const{event:t}=e,n=tn(t.target);super(e,yn,n)}}).activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;return!(!n.isPrimary||0!==n.button||(null==a||a({event:n}),0))}}];const En={move:{name:"mousemove"},end:{name:"mouseup"}};var Cn;!function(e){e[e.RightClick=2]="RightClick"}(Cn||(Cn={})),class extends fn{constructor(e){super(e,En,tn(e.event.target))}}.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;return n.button!==Cn.RightClick&&(null==a||a({event:n}),!0)}}];const Nn={move:{name:"touchmove"},end:{name:"touchend"}};var wn,Sn,Mn,xn,Tn;(class extends fn{constructor(e){super(e,Nn)}static setup(){return window.addEventListener(Nn.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(Nn.move.name,e)};function e(){}}}).activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;const{touches:l}=n;return!(l.length>1||(null==a||a({event:n}),0))}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(wn||(wn={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(Sn||(Sn={})),sn.Backward,sn.Forward,sn.Backward,sn.Forward,function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(Mn||(Mn={})),function(e){e.Optimized="optimized"}(xn||(xn={})),Mn.WhileDragging,xn.Optimized,Map,function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(Tn||(Tn={})),cn.Down,cn.Right,cn.Up,cn.Left,s.__("Lectures","masterstudy-lms-learning-management-system"),s.__("Duration","masterstudy-lms-learning-management-system"),s.__("Views","masterstudy-lms-learning-management-system"),s.__("Level","masterstudy-lms-learning-management-system"),s.__("Members","masterstudy-lms-learning-management-system"),s.__("Empty","masterstudy-lms-learning-management-system"),S("sortable__item"),S("sortable__item__disabled"),S("sortable__item__content"),S("sortable__item__content__drag-item"),S("sortable__item__content__drag-item__disabled"),S("sortable__item__content__title"),S("sortable__item__control"),S("sortable__item__icon"),S("nested-sortable"),S("nested-sortable__item"),S("sortable");const kn=S("accordion"),An=S("accordion__header"),Hn=S("accordion__header-flex"),Rn=S("accordion__content"),On=S("accordion__icon"),Dn=S("accordion__title"),Pn=S("accordion__title-disabled"),Un=S("accordion__indicator"),Ln=S("accordion__controls"),Bn=S("accordion__controls-disabled"),Fn=({title:e,children:t,accordionFields:n,switchName:l,visible:r=!0,isDefaultOpen:i=!1})=>{const{isOpen:s,onToggle:m,disabled:c,onReset:g,hasChanges:v,onClose:h}=((e,t,n)=>{var a;const{isOpen:l,onToggle:r,onClose:i}=k(t),{defaultValues:o,attributes:s,setAttributes:m}=R(),c=((e,t,n)=>{for(const a of n)if(!p(e[a],t[a]))return!0;return!1})(o,s,e);return{isOpen:l,onToggle:r,disabled:!(null===(a=s[n])||void 0===a||a),hasChanges:c,onReset:t=>{t.stopPropagation(),m(e.reduce(((e,t)=>(e[t]=o[t],e)),{}))},onClose:i}})(n,i,l);return((e,t)=>{const{attributes:n}=R(),a=!n[t];(0,M.useEffect)((()=>{a&&e()}),[a,e])})(h,l),r?(0,a.createElement)("div",{className:kn},(0,a.createElement)("div",{className:An},(0,a.createElement)("div",{className:Hn,onClick:c?null:m},(0,a.createElement)("div",{className:o()(Dn,{[Pn]:c,"with-switch":Boolean(l)})},(0,a.createElement)("div",null,e),(0,a.createElement)(u,{condition:v&&!c},(0,a.createElement)("div",{className:Un}))),(0,a.createElement)("div",{className:o()(Ln,{[Bn]:c})},(0,a.createElement)(d.Dashicon,{icon:s?"arrow-up-alt2":"arrow-down-alt2",className:On,size:16}))),(0,a.createElement)(u,{condition:Boolean(l)},(0,a.createElement)(ut,{name:l})),(0,a.createElement)(u,{condition:v&&!c},(0,a.createElement)(Ce,{onReset:g}))),s&&(0,a.createElement)("div",{className:Rn},t)):null};S("preset-picker"),S("preset-picker__label"),S("preset-picker__remove"),S("preset-picker__presets-list"),S("preset-picker__presets-list__item"),S("preset-picker__presets-list__item__preset"),S("preset-picker__presets-list__item__preset-active");const In={loadMoreIncluded:!0,loadMorePosition:"center"},Vn=Object.keys(In),zn={...In},jn={fontSize:14,fontSizeTablet:null,fontSizeMobile:null,fontSizeUnit:"px",fontSizeUnitTablet:"px",fontSizeUnitMobile:"px",fontWeight:"500",textTransform:"inherit",fontStyle:"inherit",textDecoration:"inherit",lineHeight:14,lineHeightTablet:null,lineHeightMobile:null,lineHeightUnit:"px",lineHeightUnitTablet:"px",lineHeightUnitMobile:"px",letterSpacing:0,letterSpacingTablet:null,letterSpacingMobile:null,letterSpacingUnit:"px",letterSpacingUnitTablet:"px",letterSpacingUnitMobile:"px",wordSpacing:0,wordSpacingTablet:null,wordSpacingMobile:null,wordSpacingUnit:"px",wordSpacingUnitTablet:"px",wordSpacingUnitMobile:"px",background:"#227AFF",backgroundHover:"#438EFF",color:"#ffffff",colorHover:"#ffffff",activeBackground:"#227AFF",activeColor:"#ffffff",borderStyle:"none",borderStyleHover:"",borderStyleTablet:"",borderStyleHoverTablet:"",borderStyleMobile:"",borderStyleHoverMobile:"",borderColor:"",borderColorHover:"",borderColorTablet:"",borderColorHoverTablet:"",borderColorMobile:"",borderColorHoverMobile:"",borderWidth:w,borderWidthHover:w,borderWidthTablet:w,borderWidthHoverTablet:w,borderWidthMobile:w,borderWidthHoverMobile:w,borderWidthUnit:"px",borderWidthUnitTablet:"px",borderWidthUnitMobile:"px",borderRadius:w,borderRadiusTablet:w,borderRadiusMobile:w,borderRadiusUnit:"px",borderRadiusUnitTablet:"px",borderRadiusUnitMobile:"px",margin:{top:"50",right:"",bottom:"",left:""},marginTablet:w,marginMobile:w,marginUnit:"px",marginUnitTablet:"px",marginUnitMobile:"px",padding:w,paddingTablet:w,paddingMobile:w,paddingUnit:"px",paddingUnitTablet:"px",paddingUnitMobile:"px",gap:null,gapTablet:null,gapMobile:null,gapUnit:"px",gapUnitTablet:"px",gapUnitMobile:"px"},Wn=Object.keys(jn),$n={...zn,...{...jn}},Zn=new Map([["fontSize",{unit:"fontSizeUnit",isAdaptive:!0}],["fontWeight",{}],["textTransform",{}],["fontStyle",{}],["textDecoration",{}],["lineHeight",{unit:"lineHeightUnit",isAdaptive:!0}],["letterSpacing",{unit:"letterSpacingUnit",isAdaptive:!0}],["wordSpacing",{unit:"wordSpacingUnit",isAdaptive:!0}],["background",{hasHover:!0}],["color",{hasHover:!0}],["activeBackground",{}],["activeColor",{}],["borderStyle",{isAdaptive:!0,hasHover:!0}],["borderColor",{isAdaptive:!0,hasHover:!0}],["borderWidth",{isAdaptive:!0,hasHover:!0,unit:"borderWidthUnit"}],["borderRadius",{isAdaptive:!0,unit:"borderRadiusUnit"}],["margin",{unit:"marginUnit",isAdaptive:!0}],["padding",{unit:"paddingUnit",isAdaptive:!0}],["gap",{unit:"gapUnit",isAdaptive:!0}]]),Gn=({accortionTitle:e})=>(0,a.createElement)(Fn,{title:e,accordionFields:Vn,isDefaultOpen:!0,switchName:"loadMoreIncluded"},(0,a.createElement)(Ye,{name:"loadMorePosition",label:s.__("Position","masterstudy-lms-learning-management-system"),options:[{label:(0,a.createElement)(d.Dashicon,{icon:"align-pull-left"}),value:"start"},{label:(0,a.createElement)(d.Dashicon,{icon:"align-center"}),value:"center"},{label:(0,a.createElement)(d.Dashicon,{icon:"align-pull-right"}),value:"end"}]})),Kn=({accortionTitle:e})=>(0,a.createElement)(Fn,{title:e,accordionFields:Wn},(0,a.createElement)(Pt,{fontSizeName:"fontSize",fontSizeUnitName:"fontSizeUnit",fontWeightName:"fontWeight",textTransformName:"textTransform",fontStyleName:"fontStyle",textDecorationName:"textDecoration",lineHeightName:"lineHeight",lineHeightUnitName:"lineHeightUnit",letterSpacingName:"letterSpacing",letterSpacingUnitName:"letterSpacingUnit",wordSpacingName:"wordSpacing",wordSpacingUnitName:"wordSpacingUnit",isAdaptive:!0}),(0,a.createElement)(Le,{name:"background",label:s.__("Background","masterstudy-lms-learning-management-system"),placeholder:s.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,a.createElement)(Le,{name:"color",label:s.__("Color","masterstudy-lms-learning-management-system"),placeholder:s.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,a.createElement)(Le,{name:"activeBackground",label:s.__("Active Background","masterstudy-lms-learning-management-system"),placeholder:s.__("Select color","masterstudy-lms-learning-management-system"),dependencies:[{name:"loadMorePreset",value:"pagination"}]}),(0,a.createElement)(Le,{name:"activeColor",label:s.__("Active Color","masterstudy-lms-learning-management-system"),placeholder:s.__("Select color","masterstudy-lms-learning-management-system"),dependencies:[{name:"loadMorePreset",value:"pagination"}]}),(0,a.createElement)(tt,{label:s.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"borderStyle",borderColorName:"borderColor",borderWidthName:"borderWidth",hasHover:!0,isAdaptive:!0}),(0,a.createElement)(lt,{name:"borderRadius",label:s.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,a.createElement)($e,{name:"margin",label:s.__("Margin","masterstudy-lms-learning-management-system"),unitName:"marginUnit",isAdaptive:!0}),(0,a.createElement)($e,{name:"padding",label:s.__("Padding","masterstudy-lms-learning-management-system"),unitName:"paddingUnit",isAdaptive:!0}),(0,a.createElement)(vt,{name:"gap",label:s.__("Space between links","masterstudy-lms-learning-management-system"),unitName:"gapUnit",dependencies:[{name:"loadMorePreset",value:"pagination"}],isAdaptive:!0})),Xn=({attributes:e,setAttributes:t})=>{const{onResetByFieldName:n,changedFieldsByName:l}=((e,t,n,a=[])=>{const l=(e=>{const t={};return Object.entries(e).forEach((([e,n])=>{e.includes("UAG")||(t[e]=n)})),t})(t),r=!p(e,l),i=((e,t,n)=>{const a=new Map;return n.forEach((n=>{a.has(n)||a.set(n,(()=>t({[n]:e[n]})))})),a})(e,n,a),o=((e,t,n)=>{const a=new Map;return n.forEach((n=>{a.has(n)?a.set(n,!1):a.set(n,!p(e[n],t[n]))})),a})(e,l,a);return{hasChanges:r,onResetByFieldName:i,changedFieldsByName:o}})($n,e,t,Object.keys($n)),i=(0,M.useMemo)((()=>"pagination"===e.loadMorePreset?s.__("Pagination","masterstudy-lms-learning-management-system"):s.__("Load more","masterstudy-lms-learning-management-system")),[e.loadMorePreset]);return(0,a.createElement)(r.InspectorControls,null,(0,a.createElement)(H,{attributes:e,setAttributes:t,defaultValues:$n,onResetByFieldName:n,changedFieldsByName:l},(0,a.createElement)(Kt,{generalTab:(0,a.createElement)(Gn,{accortionTitle:i}),styleTab:(0,a.createElement)(Kn,{accortionTitle:i}),advancedTab:(0,a.createElement)(a.Fragment,null)})))},Yn=window.wp.primitives,qn=(0,a.createElement)(Yn.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,a.createElement)(Yn.Path,{d:"M8 12.5h8V11H8v1.5Z M19 6.5H5a2 2 0 0 0-2 2V15a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8.5a2 2 0 0 0-2-2ZM5 8h14a.5.5 0 0 1 .5.5V15a.5.5 0 0 1-.5.5H5a.5.5 0 0 1-.5-.5V8.5A.5.5 0 0 1 5 8Z"})),Jn=(0,a.createElement)(Yn.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,a.createElement)(Yn.Path,{d:"M4 13.5h6v-3H4v3zm8 0h3v-3h-3v3zm5-3v3h3v-3h-3z"})),Qn=[{name:"button",title:s.__("Load more button","masterstudy-lms-learning-management-system"),description:s.__("Load more course cards button","masterstudy-lms-learning-management-system"),attributes:{loadMorePreset:"button",background:"#227AFF",backgroundHover:"#438EFF",color:"#ffffff",colorHover:"#ffffff"},isDefault:!0,scope:["transform"],isActive:e=>"button"===e.loadMorePreset,icon:qn},{name:"pagination",title:s.__("Pagination","masterstudy-lms-learning-management-system"),description:s.__("Page navigation","masterstudy-lms-learning-management-system"),attributes:{loadMorePreset:"pagination",background:"#ffffff",backgroundHover:"#227AFF",color:"#4D5E6F",colorHover:"#ffffff"},scope:["transform"],isActive:e=>"pagination"===e.loadMorePreset,icon:Jn}],ea=window.wp.compose,ta=[{attributes:{loadMoreIncluded:{type:"boolean",default:!0},loadMorePreset:{type:"string",enum:["button","pagination"],default:"button"},loadMorePosition:{type:"string",enum:["start","center","end"],default:"center"},fontSize:{type:"number",default:14},fontSizeTablet:{type:"number",default:null},fontSizeMobile:{type:"number",default:null},fontSizeUnit:{type:"string",default:"px"},fontSizeUnitTablet:{type:"string",default:"px"},fontSizeUnitMobile:{type:"string",default:"px"},fontWeight:{type:"string",default:"500"},textTransform:{type:"string",default:"inherit"},fontStyle:{type:"string",default:"inherit"},textDecoration:{type:"string",default:"inherit"},lineHeight:{type:"number",default:14},lineHeightTablet:{type:"number",default:null},lineHeightMobile:{type:"number",default:null},lineHeightUnit:{type:"string",default:"px"},lineHeightUnitTablet:{type:"string",default:"px"},lineHeightUnitMobile:{type:"string",default:"px"},letterSpacing:{type:"number",default:0},letterSpacingTablet:{type:"number",default:null},letterSpacingMobile:{type:"number",default:null},letterSpacingUnit:{type:"string",default:"px"},letterSpacingUnitTablet:{type:"string",default:"px"},letterSpacingUnitMobile:{type:"string",default:"px"},wordSpacing:{type:"number",default:0},wordSpacingTablet:{type:"number",default:null},wordSpacingMobile:{type:"number",default:null},wordSpacingUnit:{type:"string",default:"px"},wordSpacingUnitTablet:{type:"string",default:"px"},wordSpacingUnitMobile:{type:"string",default:"px"},background:{type:"string",default:"#227AFF"},backgroundHover:{type:"string",default:"#438EFF"},color:{type:"string",default:"#ffffff"},colorHover:{type:"string",default:"#ffffff"},activeBackground:{type:"string",default:"#227AFF"},activeColor:{type:"string",default:"#ffffff"},borderStyle:{type:"string",default:"none"},borderStyleHover:{type:"string",default:""},borderStyleTablet:{type:"string",default:""},borderStyleHoverTablet:{type:"string",default:""},borderStyleMobile:{type:"string",default:""},borderStyleHoverMobile:{type:"string",default:""},borderColor:{type:"string",default:""},borderColorHover:{type:"string",default:""},borderColorTablet:{type:"string",default:""},borderColorHoverTablet:{type:"string",default:""},borderColorMobile:{type:"string",default:""},borderColorHoverMobile:{type:"string",default:""},borderWidth:{type:"object",default:{top:"",right:"",bottom:"",left:""}},borderWidthHover:{type:"object",default:{top:"",right:"",bottom:"",left:""}},borderWidthTablet:{type:"object",default:{top:"",right:"",bottom:"",left:""}},borderWidthHoverTablet:{type:"object",default:{top:"",right:"",bottom:"",left:""}},borderWidthMobile:{type:"object",default:{top:"",right:"",bottom:"",left:""}},borderWidthHoverMobile:{type:"object",default:{top:"",right:"",bottom:"",left:""}},borderWidthUnit:{type:"string",default:"px"},borderWidthUnitTablet:{type:"string",default:"px"},borderWidthUnitMobile:{type:"string",default:"px"},borderRadius:{type:"object",default:{top:"20",right:"20",bottom:"20",left:"20"}},borderRadiusTablet:{type:"object",default:{top:"",right:"",bottom:"",left:""}},borderRadiusMobile:{type:"object",default:{top:"",right:"",bottom:"",left:""}},borderRadiusUnit:{type:"string",default:"px"},borderRadiusUnitTablet:{type:"string",default:"px"},borderRadiusUnitMobile:{type:"string",default:"px"},margin:{type:"object",default:{top:"50",right:"",bottom:"",left:""}},marginTablet:{type:"object",default:{top:"",right:"",bottom:"",left:""}},marginMobile:{type:"object",default:{top:"",right:"",bottom:"",left:""}},marginUnit:{type:"string",default:"px"},marginUnitTablet:{type:"string",default:"px"},marginUnitMobile:{type:"string",default:"px"},padding:{type:"object",default:{top:"11",right:"20",bottom:"11",left:"20"}},paddingTablet:{type:"object",default:{top:"",right:"",bottom:"",left:""}},paddingMobile:{type:"object",default:{top:"",right:"",bottom:"",left:""}},paddingUnit:{type:"string",default:"px"},paddingUnitTablet:{type:"string",default:"px"},paddingUnitMobile:{type:"string",default:"px"}},supports:{html:!1},save({attributes:e}){if(!e.loadMoreIncluded)return null;const t=r.useBlockProps.save({className:`lms-courses-load-more-container align-items-${e.loadMorePosition||"center"}`,style:C("lm",e,Zn)});return(0,a.createElement)("div",{...t},(0,a.createElement)("div",{className:"courses-load-more"},(0,a.createElement)("button",{type:"button",className:"courses-load-more__button"},s.__("Load More","masterstudy-lms-learning-management-system"))))},migrate:(0,ea.compose)((e=>{const{borderRadius:t,...n}=e;return{...n,borderRadius:{...t,default:{top:"",left:"",bottom:"",right:""}}}}),(e=>{const{padding:t,...n}=e;return{...n,padding:{...t,default:{top:"",left:"",bottom:"",right:""}}}}))}],na=JSON.parse('{"UU":"masterstudy/courses-load-more"}');(0,l.registerBlockType)(na.UU,{icon:{src:(0,a.createElement)("svg",{width:"512",height:"513",viewBox:"0 0 512 513",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,a.createElement)("path",{opacity:"0.3",d:"M46 114.942C46 103.896 54.9543 94.9418 66 94.9418H381.94C392.986 94.9418 401.94 103.896 401.94 114.942V292.57L243.867 269.434L252.014 338.942H66C54.9543 338.942 46 329.987 46 318.942V114.942Z",fill:"#227AFF"}),(0,a.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M377.888 64.9418C377.888 59.4189 382.365 54.9418 387.888 54.9418H451.888C466.239 54.9418 480.003 60.6428 490.151 70.7908C500.299 80.9388 506 94.7024 506 109.054V324.83C506 331.936 504.6 338.972 501.881 345.538C499.162 352.103 495.176 358.068 490.151 363.093C485.126 368.117 479.161 372.103 472.596 374.823C466.031 377.542 458.994 378.942 451.888 378.942H432C426.477 378.942 422 374.465 422 368.942C422 363.419 426.477 358.942 432 358.942H451.888C456.368 358.942 460.803 358.059 464.942 356.345C469.081 354.631 472.841 352.118 476.009 348.951C479.176 345.783 481.689 342.023 483.403 337.884C485.118 333.745 486 329.309 486 324.83V109.054C486 100.007 482.406 91.3302 476.009 84.9329C469.612 78.5357 460.935 74.9418 451.888 74.9418H387.888C382.365 74.9418 377.888 70.4646 377.888 64.9418Z",fill:"black"}),(0,a.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M60.112 74.9418C51.0649 74.9418 42.3884 78.5357 35.9912 84.9329C29.5939 91.3302 26 100.007 26 109.054V324.83C26 329.309 26.8823 333.745 28.5966 337.884C30.3109 342.023 32.8236 345.783 35.9912 348.951C39.1588 352.118 42.9193 354.631 47.0579 356.345C51.1965 358.059 55.6323 358.942 60.112 358.942H247.408C252.931 358.942 257.408 363.419 257.408 368.942C257.408 374.465 252.931 378.942 247.408 378.942H60.112C53.0059 378.942 45.9694 377.542 39.4042 374.823C32.8391 372.103 26.8738 368.117 21.849 363.093C16.8243 358.068 12.8384 352.103 10.119 345.538C7.39965 338.972 6 331.936 6 324.83V109.054C6 94.7024 11.7011 80.9388 21.849 70.7908C31.997 60.6428 45.7606 54.9418 60.112 54.9418H387.888C402.239 54.9418 416.003 60.6428 426.151 70.7908C436.299 80.9388 442 94.7024 442 109.054V324.825C442 324.826 442 324.828 442 324.83C442.003 333.434 439.954 341.915 436.022 349.568C432.089 357.222 426.386 363.827 419.387 368.835C414.895 372.048 408.649 371.012 405.435 366.52C402.222 362.029 403.258 355.782 407.749 352.569C412.16 349.413 415.754 345.251 418.233 340.427C420.711 335.603 422.003 330.258 422 324.835V109.054C422 100.007 418.406 91.3302 412.009 84.9329C405.612 78.5357 396.935 74.9418 387.888 74.9418H60.112Z",fill:"black"}),(0,a.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M232.769 257.711C235.076 255.404 238.364 254.367 241.577 254.934L433.897 288.854C437.569 289.501 440.576 292.139 441.697 295.695C442.818 299.251 441.868 303.136 439.231 305.773L401.054 343.95L445.487 388.383C450.053 392.948 453.674 398.369 456.145 404.334C458.616 410.299 459.888 416.693 459.888 423.15C459.888 429.607 458.616 436 456.145 441.966C453.674 447.931 450.053 453.351 445.487 457.917L432.991 470.413C428.425 474.978 423.005 478.6 417.04 481.071C411.074 483.542 404.681 484.814 398.224 484.814C391.767 484.814 385.374 483.542 379.408 481.071C373.443 478.6 368.023 474.979 363.458 470.414C363.458 470.414 363.457 470.413 363.457 470.413L319.025 425.995L280.847 464.173C278.21 466.809 274.326 467.76 270.77 466.639C267.213 465.518 264.576 462.512 263.928 458.839L229.992 266.519C229.425 263.306 230.461 260.018 232.769 257.711ZM252.17 277.111L280.286 436.449L311.953 404.783C315.858 400.878 322.189 400.877 326.094 404.782L377.599 456.271C380.308 458.979 383.523 461.128 387.062 462.594C390.601 464.059 394.394 464.814 398.224 464.814C402.054 464.814 405.847 464.059 409.386 462.594C412.925 461.128 416.14 458.979 418.849 456.271L431.345 443.775C434.053 441.066 436.202 437.851 437.668 434.312C439.134 430.773 439.888 426.98 439.888 423.15C439.888 419.319 439.134 415.526 437.668 411.988C436.202 408.449 434.053 405.233 431.345 402.525L379.841 351.021C377.966 349.145 376.912 346.602 376.912 343.95C376.912 341.298 377.966 338.754 379.841 336.879L411.506 305.213L252.17 277.111Z",fill:"black"}),(0,a.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M224 118.942C229.523 118.942 234 123.419 234 128.942V176.942C234 182.465 229.523 186.942 224 186.942C218.477 186.942 214 182.465 214 176.942V128.942C214 123.419 218.477 118.942 224 118.942Z",fill:"black"}),(0,a.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M118 208.942C118 203.419 122.477 198.942 128 198.942H176C181.523 198.942 186 203.419 186 208.942C186 214.465 181.523 218.942 176 218.942H128C122.477 218.942 118 214.465 118 208.942Z",fill:"black"}),(0,a.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M262 208.942C262 203.419 266.477 198.942 272 198.942H320C325.523 198.942 330 203.419 330 208.942C330 214.465 325.523 218.942 320 218.942H272C266.477 218.942 262 214.465 262 208.942Z",fill:"black"}),(0,a.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M295.071 137.871C298.976 141.776 298.976 148.108 295.071 152.013L279.071 168.013C275.166 171.918 268.834 171.918 264.929 168.013C261.024 164.108 261.024 157.776 264.929 153.871L280.929 137.871C284.834 133.965 291.166 133.965 295.071 137.871Z",fill:"black"}),(0,a.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M152.929 137.871C156.834 133.965 163.166 133.965 167.071 137.871L183.071 153.871C186.976 157.776 186.976 164.108 183.071 168.013C179.166 171.918 172.834 171.918 168.929 168.013L152.929 152.013C149.024 148.108 149.024 141.776 152.929 137.871Z",fill:"black"}),(0,a.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M183.071 249.871C186.976 253.776 186.976 260.108 183.071 264.013L167.071 280.013C163.166 283.918 156.834 283.918 152.929 280.013C149.024 276.108 149.024 269.776 152.929 265.871L168.929 249.871C172.834 245.965 179.166 245.965 183.071 249.871Z",fill:"black"}))},edit:({attributes:e,setAttributes:t})=>{const n=(0,r.useBlockProps)({className:`lms-courses-load-more-container align-items-${e.loadMorePosition||"center"}`,style:C("lm",e,Zn)});return(0,a.createElement)(a.Fragment,null,(0,a.createElement)(Xn,{attributes:e,setAttributes:t}),e.loadMoreIncluded&&(0,a.createElement)("div",{...n},"pagination"===e.loadMorePreset?(0,a.createElement)("div",{className:"lms-courses-pagination is-loaded"},(0,a.createElement)(c,null)):(0,a.createElement)(m,null)))},save:({attributes:e})=>{if(!e.loadMoreIncluded)return null;const t=r.useBlockProps.save({className:`lms-courses-load-more-container align-items-${e.loadMorePosition||"center"}`,style:C("lm",e,Zn)});return(0,a.createElement)("div",{...t},"pagination"===e.loadMorePreset?(0,a.createElement)("div",{className:"lms-courses-pagination"},(0,a.createElement)("ul",{className:"lms-courses-pagination-list"})):(0,a.createElement)(m,null))},variations:Qn,deprecated:ta})},6942:(e,t)=>{var n;!function(){"use strict";var a={}.hasOwnProperty;function l(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=i(e,r(n)))}return e}function r(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return l.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)a.call(e,n)&&e[n]&&(t=i(t,n));return t}function i(e,t){return t?e?e+" "+t:e+t:e}e.exports?(l.default=l,e.exports=l):void 0===(n=function(){return l}.apply(t,[]))||(e.exports=n)}()}},n={};function a(e){var l=n[e];if(void 0!==l)return l.exports;var r=n[e]={exports:{}};return t[e](r,r.exports,a),r.exports}a.m=t,e=[],a.O=(t,n,l,r)=>{if(!n){var i=1/0;for(c=0;c<e.length;c++){for(var[n,l,r]=e[c],o=!0,s=0;s<n.length;s++)(!1&r||i>=r)&&Object.keys(a.O).every((e=>a.O[e](n[s])))?n.splice(s--,1):(o=!1,r<i&&(i=r));if(o){e.splice(c--,1);var m=l();void 0!==m&&(t=m)}}return t}r=r||0;for(var c=e.length;c>0&&e[c-1][2]>r;c--)e[c]=e[c-1];e[c]=[n,l,r]},a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={3568:0,7288:0};a.O.j=t=>0===e[t];var t=(t,n)=>{var l,r,[i,o,s]=n,m=0;if(i.some((t=>0!==e[t]))){for(l in o)a.o(o,l)&&(a.m[l]=o[l]);if(s)var c=s(a)}for(t&&t(n);m<i.length;m++)r=i[m],a.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return a.O(c)},n=globalThis.webpackChunkmasterstudy_lms_learning_management_system=globalThis.webpackChunkmasterstudy_lms_learning_management_system||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})();var l=a.O(void 0,[7288],(()=>a(1666)));l=a.O(l)})();