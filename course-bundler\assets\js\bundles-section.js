document.addEventListener('DOMContentLoaded', function() {
    // Initialize bundle cards
    initBundleCards();

    // Handle add to cart functionality
    initAddToCart();
});

function initBundleCards() {
    const bundleCards = document.querySelectorAll('.bundle-card');
    
    bundleCards.forEach(card => {
        // Add hover effect for images
        const image = card.querySelector('.bundle-card__image img');
        if (image) {
            image.addEventListener('load', function() {
                this.classList.add('loaded');
            });
        }

        // Initialize course list toggle if there are many courses
        const coursesList = card.querySelector('.bundle-card__courses ul');
        if (coursesList && coursesList.children.length > 3) {
            const hiddenCourses = Array.from(coursesList.children).slice(3);
            hiddenCourses.forEach(course => course.style.display = 'none');

            const toggleButton = document.createElement('button');
            toggleButton.className = 'bundle-card__courses-toggle';
            toggleButton.textContent = 'Show more courses';
            
            toggleButton.addEventListener('click', function() {
                const isExpanded = this.classList.contains('expanded');
                hiddenCourses.forEach(course => {
                    course.style.display = isExpanded ? 'none' : 'block';
                });
                this.textContent = isExpanded ? 'Show more courses' : 'Show less';
                this.classList.toggle('expanded');
            });

            coursesList.parentNode.insertBefore(toggleButton, coursesList.nextSibling);
        }
    });
}

function initAddToCart() {
    const addToCartButtons = document.querySelectorAll('.bundle-card__button');
    
    addToCartButtons.forEach(button => {
        button.addEventListener('click', async function(e) {
            e.preventDefault();
            
            const bundleId = this.dataset.bundleId;
            if (!bundleId) return;

            try {
                this.classList.add('loading');
                this.textContent = 'Adding to cart...';

                const response = await fetch(mscb_ajax.ajax_url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'mscb_add_bundle_to_cart',
                        bundle_id: bundleId,
                        nonce: mscb_ajax.nonce
                    })
                });

                const data = await response.json();

                if (data.success) {
                    this.textContent = 'Added to cart!';
                    setTimeout(() => {
                        this.textContent = 'Add to cart';
                        this.classList.remove('loading');
                    }, 2000);

                    // Update cart fragments if WooCommerce is present
                    if (typeof wc_cart_fragments_params !== 'undefined') {
                        jQuery(document.body).trigger('wc_fragment_refresh');
                    }
                } else {
                    throw new Error(data.data || 'Error adding to cart');
                }
            } catch (error) {
                console.error('Error:', error);
                this.textContent = 'Error - Try again';
                this.classList.remove('loading');
                this.classList.add('error');
                
                setTimeout(() => {
                    this.textContent = 'Add to cart';
                    this.classList.remove('error');
                }, 3000);
            }
        });
    });
} 