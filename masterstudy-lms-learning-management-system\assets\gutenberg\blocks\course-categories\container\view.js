(()=>{"use strict";var s={n:e=>{var n=e&&e.__esModule?()=>e.default:()=>e;return s.d(n,{a:n}),n},d:(e,n)=>{for(var t in n)s.o(n,t)&&!s.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:n[t]})},o:(s,e)=>Object.prototype.hasOwnProperty.call(s,e)};const e=window.wp.apiFetch;var n=s.n(e);(()=>{function s(){const s=document.body.clientWidth+"px";document.documentElement.style.setProperty("--body-width",s)}s(),window.addEventListener("resize",s)})();const t=(s,e,n,t)=>{const a=n.querySelector(".lms-courses-category-container__wrap");if(!a)return;const c=document.createElement("div");if(c.className=`lms-courses-category-list lms-courses-category-list__${t}`,"dynamic"===t||"dynamic2"===t){const n=[];for(let e=0;e<s.length;e+=3)n.push(s.slice(e,e+3));n.forEach(((s,n)=>{const t=document.createElement("div");t.className="row",s.forEach(((a,c)=>{if(n%2==0&&2===c)return;const r=document.createElement("div");r.className="lms-courses-category-list__item";const o=`\n          <a href="${e}?terms[]=${a.id}&category[]=${a.id}" class="lms-courses-category-list__link">\n            <span class="lms-courses-category-list__link-image" style="background-color: ${a.color}">\n              ${a.image?`<img src="${a.image}" alt="${a.name}" />`:""}\n            </span>\n            <span class="lms-courses-category-list__link-info-wrap">\n                <span class="lms-courses-category-list__link-info">\n                <span class="lms-courses-category-list__link-title">${a.name}</span>\n                <span class="lms-courses-category-list__link-posts">${a.courses} Courses</span>\n                </span>\n            </span>\n          </a>\n        `;if(n%2==0&&1===c){const n=document.createElement("div");if(n.className="two-columns",r.innerHTML=o,n.appendChild(r),s[c+1]){const t=s[c+1],r=document.createElement("div");r.className="lms-courses-category-list__item";const o=`\n              <a href="${e}?terms[]=${a.id}&category[]=${a.id}" class="lms-courses-category-list__link">\n                <span class="lms-courses-category-list__link-image" style="background-color: ${t.color}">\n                  ${t.image?`<img src="${t.image}" alt="${t.name}" />`:""}\n                </span>\n                <span class="lms-courses-category-list__link-info-wrap">\n                    <span class="lms-courses-category-list__link-info">\n                    <span class="lms-courses-category-list__link-title">${t.name}</span>\n                    <span class="lms-courses-category-list__link-posts">${t.courses} Courses</span>\n                    </span>\n                </span>\n              </a>\n            `;r.innerHTML=o,n.appendChild(r)}t.appendChild(n)}else r.innerHTML=o,t.appendChild(r)})),c.appendChild(t)}))}else s.forEach((s=>{const n=document.createElement("div");n.className="lms-courses-category-list__item";let a="";a="colorful"===t?`\n          <a href="${e}?terms[]=${s.id}&category[]=${s.id}" class="link" style="background-color: ${s.color}">\n            ${s.icon?`<i class="${s.icon}"></i>`:""}\n            <span>${s.name}</span>\n          </a>\n        `:"sleek"===t?`\n          <a href="${e}?terms[]=${s.id}&category[]=${s.id}" class="lms-courses-category-list__link">\n            ${s.image?`<img src="${s.image}" alt="${s.name}" />`:""}\n            <span>${s.name}</span>\n          </a>\n        `:`\n          <a href="${e}?terms[]=${s.id}&category[]=${s.id}" class="lms-courses-category-list__link">\n            ${s.icon?`<i class="${s.icon}"></i>`:""}\n            <span>${s.name}</span>\n          </a>\n        `,n.innerHTML=a,c.appendChild(n)}));a.appendChild(c);const r=n.querySelector(".lms-course-preloader");r&&r.remove()};document.addEventListener("DOMContentLoaded",(async()=>{document.querySelectorAll(".lms-courses-category-container").forEach((async s=>{const e=s.querySelector(".lms-courses-category-list-data");if(!e)return;const a=e.dataset.categories?.split(",").map(Number),c=e.dataset.style;let r=[],o="";try{const s=await(async(s=!1)=>{try{let e="?children=true";return s&&(e+="&details=true"),await n()({path:`masterstudy-lms/v2/course-categories${e}`})}catch(s){throw new Error(s)}})(!0);r=s.categories||[],o=s.course_url}catch(s){return}if(a&&a.length>1){const e=r.filter((s=>a.includes(s.id)));e.sort(((s,e)=>a.indexOf(s.id)-a.indexOf(e.id))),t(e,o,s,c)}else t(r,o,s,c)}))}))})();