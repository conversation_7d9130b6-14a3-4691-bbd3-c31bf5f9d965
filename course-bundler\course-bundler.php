<?php
/**
 * Plugin Name: MasterStudy Course Bundler
 * Plugin URI: https://yourwebsite.com/masterstudy-course-bundler
 * Description: Create and manage course bundles in MasterStudy LMS
 * Version: 1.0.0
 * Author: kush<PERSON>ra mishra
 * Author URI: http://vedmg.com
 * Text Domain: masterstudy-course-bundler
 * Domain Path: /languages
 * Requires at least: 5.8
 * Requires PHP: 7.4
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('MSCB_VERSION', '1.0.0');
define('MSCB_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('MSCB_PLUGIN_URL', plugin_dir_url(__FILE__));
define('MSCB_PLUGIN_BASENAME', plugin_basename(__FILE__));

// Check if MasterStudy LMS is active
function mscb_check_required_plugins() {
    if (!class_exists('STM_LMS_User')) {
        add_action('admin_notices', 'mscb_admin_notice_missing_masterstudy');
        return false;
    }
    return true;
}

function mscb_admin_notice_missing_masterstudy() {
    ?>
    <div class="notice notice-error">
        <p><?php esc_html_e('MasterStudy Course Bundler requires MasterStudy LMS plugin to be installed and activated.', 'masterstudy-course-bundler'); ?></p>
    </div>
    <?php
}

// Initialize plugin
function mscb_init() {
    if (!mscb_check_required_plugins()) {
        return;
    }

    // Load plugin text domain
    load_plugin_textdomain('masterstudy-course-bundler', false, dirname(MSCB_PLUGIN_BASENAME) . '/languages');

    // Include required files
    require_once MSCB_PLUGIN_DIR . 'includes/class-mscb-activator.php';
    require_once MSCB_PLUGIN_DIR . 'includes/class-mscb-deactivator.php';
    require_once MSCB_PLUGIN_DIR . 'includes/class-mscb-bundle.php';
    require_once MSCB_PLUGIN_DIR . 'includes/class-mscb-admin.php';
    require_once MSCB_PLUGIN_DIR . 'includes/class-mscb-frontend.php';
    require_once MSCB_PLUGIN_DIR . 'includes/class-mscb-enrollment.php';

    // Initialize main classes
    new MSCB_Bundle();
    new MSCB_Admin();
    new MSCB_Frontend();
    new MSCB_Enrollment();
}

add_action('plugins_loaded', 'mscb_init');

// Activation hook
register_activation_hook(__FILE__, 'mscb_activate');
function mscb_activate() {
    require_once MSCB_PLUGIN_DIR . 'includes/class-mscb-activator.php';
    MSCB_Activator::activate();
}

// Deactivation hook
register_deactivation_hook(__FILE__, 'mscb_deactivate');
function mscb_deactivate() {
    require_once MSCB_PLUGIN_DIR . 'includes/class-mscb-deactivator.php';
    MSCB_Deactivator::deactivate();
} 