# MasterStudy Course Bundler

A WordPress plugin that extends MasterStudy LMS to allow course bundling functionality.

## Description

MasterStudy Course Bundler allows administrators to create and sell bundles of courses from the MasterStudy LMS plugin. With this plugin, you can:

- Create custom course bundles with special pricing
- Display bundles in a grid or individual pages
- Allow students to purchase bundles with a single payment
- Automatically enroll students in all courses within a bundle
- Track bundle enrollments and completions

## Requirements

- WordPress 5.0 or higher
- MasterStudy LMS plugin (latest version recommended)
- PHP 7.2 or higher

## Installation

1. Upload the `course-bundler` folder to the `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Navigate to 'Course Bundles' in the WordPress admin menu to start creating bundles

## Usage

### Creating Course Bundles

1. Go to **Course Bundles > Add New** in your WordPress admin menu
2. Enter a title and description for your bundle
3. Set the regular price and optional sale price
4. Upload a featured image for the bundle
5. In the "Bundle Courses" meta box, search for and select courses to include in the bundle
6. Arrange the courses in the desired order using drag and drop
7. Check the "Featured Bundle" option if you want to highlight this bundle
8. Publish the bundle

### Displaying Bundles

#### Using Shortcodes

The plugin provides two main shortcodes for displaying bundles:

**Display a grid of all bundles:**
```
[mscb_bundles]
```

**Optional parameters:**
- `limit` - Number of bundles to display (default: 10)
- `columns` - Number of columns in the grid (default: 3)
- `featured` - Show only featured bundles (default: false)

Example with parameters:
```
[mscb_bundles limit="6" columns="2" featured="true"]
```

**Display a single bundle:**
```
[mscb_bundle id="123"]
```
Replace `123` with the actual bundle ID.

#### Adding to Pages

1. Create a new page or edit an existing one
2. Add a shortcode block and insert one of the shortcodes above
3. Publish or update the page

### Bundle Purchase Process

When a student purchases a bundle:

1. They click the "Purchase Bundle" button on the bundle page
2. The bundle is added to their cart
3. They are redirected to the checkout page
4. After completing payment, they are automatically enrolled in all courses in the bundle
5. The courses appear in their student dashboard, accessible through the standard MasterStudy LMS interface

## Customization

### Templates

The plugin includes two main templates that can be overridden in your theme:

- `bundles-grid.php` - Template for displaying a grid of bundles
- `single-bundle.php` - Template for displaying a single bundle

To override these templates, create a `course-bundler` folder in your theme directory and copy the template files there. You can then modify them as needed.

### CSS

The plugin includes a CSS file (`assets/css/mscb-frontend.css`) that styles the bundle display. You can override these styles in your theme's stylesheet.

## Hooks and Filters

The plugin provides several hooks and filters for developers to extend its functionality:

### Actions

- `mscb_before_bundle_grid` - Fires before the bundle grid is displayed
- `mscb_after_bundle_grid` - Fires after the bundle grid is displayed
- `mscb_before_single_bundle` - Fires before a single bundle is displayed
- `mscb_after_single_bundle` - Fires after a single bundle is displayed
- `mscb_bundle_purchased` - Fires when a bundle is purchased

### Filters

- `mscb_bundle_price` - Filter the bundle price
- `mscb_bundle_courses` - Filter the courses in a bundle
- `mscb_bundle_data` - Filter the bundle data for API responses

## Troubleshooting

### Common Issues

**Bundles not displaying:**
- Make sure the shortcode is correctly formatted
- Check that you have created and published at least one bundle
- Verify that the bundle has courses assigned to it

**Purchase button not working:**
- Ensure MasterStudy LMS is up to date
- Check that the checkout page is properly configured in MasterStudy LMS settings
- Verify that the courses in the bundle are set as purchasable in MasterStudy LMS

**Students not enrolled in courses after purchase:**
- Check that the courses in the bundle are published and available
- Verify that the user has completed the checkout process
- Check the server error logs for any PHP errors

## Support

For support, feature requests, or bug reports, please contact the plugin developer or submit an issue on the plugin's GitHub repository.

## License

This plugin is licensed under the GPL v2 or later.

## Credits

Developed by [Your Name/Company]

---

Thank you for using MasterStudy Course Bundler!
