<?php
/**
 * Template for displaying single course bundle
 */

defined('ABSPATH') || exit;

get_header();

// Get bundle data
global $post;
$bundle_id = $post->ID;
$bundle = new MSCB_Bundle();
$price = get_post_meta($bundle_id, '_mscb_price', true);
$sale_price = get_post_meta($bundle_id, '_mscb_sale_price', true);
$courses = $bundle->get_bundle_courses($bundle_id);
$total_courses = count($courses);
$featured = get_post_meta($bundle_id, '_mscb_featured', true);

// Calculate savings based on regular price vs sale price
$bundle_regular_price = $price;
$bundle_sale_price = (!empty($sale_price)) ? $sale_price : $price;

// Calculate discount percentage
$savings = $bundle_regular_price - $bundle_sale_price;
$savings_percentage = ($bundle_regular_price > 0) ? round(($savings / $bundle_regular_price) * 100) : 0;

// Set currency symbol
$currency = '₹';
?>

<div class="mscb-single-bundle">
    <div class="mscb-single-bundle__header">
        <div class="mscb-single-bundle__header-content">
            <h1 class="mscb-single-bundle__title"><?php the_title(); ?></h1>
            
            <?php if ($featured) : ?>
                <span class="mscb-single-bundle__featured"><?php esc_html_e('Featured Bundle', 'masterstudy-course-bundler'); ?></span>
            <?php endif; ?>
            
            <div class="mscb-single-bundle__meta">
                <div class="mscb-single-bundle__courses-count">
                    <span class="mscb-single-bundle__courses-icon">
                        <i class="fa fa-book"></i>
                    </span>
                    <span class="mscb-single-bundle__courses-text">
                        <?php 
                        printf(
                            _n('%s Course', '%s Courses', $total_courses, 'masterstudy-course-bundler'),
                            number_format_i18n($total_courses)
                        ); 
                        ?>
                    </span>
                </div>
                
                <?php if ($savings > 0) : ?>
                    <div class="mscb-single-bundle__savings">
                        <span class="mscb-single-bundle__savings-icon">
                            <i class="fa fa-tags"></i>
                        </span>
                        <span class="mscb-single-bundle__savings-text">
                            <?php printf(esc_html__('Save %s (%d%%)', 'masterstudy-course-bundler'), $currency . number_format((float)$savings, 2), $savings_percentage); ?>
                        </span>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="mscb-single-bundle__header-image">
            <?php if (has_post_thumbnail()) : ?>
                <?php the_post_thumbnail('large', array('class' => 'mscb-single-bundle__thumbnail')); ?>
            <?php else : ?>
                <img src="<?php echo MSCB_PLUGIN_URL; ?>assets/images/default-bundle.png" alt="<?php echo esc_attr(get_the_title()); ?>" class="mscb-single-bundle__thumbnail">
            <?php endif; ?>
        </div>
    </div>
    
    <div class="mscb-single-bundle__description">
        <h3><?php esc_html_e('Bundle Description', 'masterstudy-course-bundler'); ?></h3>
        <?php the_content(); ?>
    </div>
    
    <div class="mscb-single-bundle__content">
        <div class="mscb-single-bundle__main">
            <div class="mscb-single-bundle__courses">
                <h3><?php esc_html_e('Courses Included', 'masterstudy-course-bundler'); ?></h3>
                
                <?php if (!empty($courses)) : ?>
                    <div class="mscb-single-bundle__courses-list">
                        <?php foreach ($courses as $index => $course) : 
                            $course_id = $course['course_id'];
                            $course_post = get_post($course_id);
                            
                            if (!$course_post) {
                                continue;
                            }
                            
                            $course_price = get_post_meta($course_id, 'price', true);
                            $course_sale_price = get_post_meta($course_id, 'sale_price', true);
                            $course_price_display = (!empty($course_sale_price)) ? $course_sale_price : $course_price;
                        ?>
                            <div class="mscb-single-bundle__course-item">
                                <div class="mscb-single-bundle__course-number"><?php echo esc_html($index + 1); ?></div>
                                
                                <div class="mscb-single-bundle__course-image">
                                    <a href="<?php echo esc_url(get_permalink($course_id)); ?>">
                                        <?php if (has_post_thumbnail($course_id)) : ?>
                                            <?php echo get_the_post_thumbnail($course_id, 'thumbnail', array('class' => 'mscb-single-bundle__course-thumbnail')); ?>
                                        <?php else : ?>
                                            <img src="<?php echo MSCB_PLUGIN_URL; ?>assets/images/default-course.png" alt="<?php echo esc_attr($course_post->post_title); ?>" class="mscb-single-bundle__course-thumbnail">
                                        <?php endif; ?>
                                    </a>
                                </div>
                                
                                <div class="mscb-single-bundle__course-content">
                                    <h4 class="mscb-single-bundle__course-title">
                                        <a href="<?php echo esc_url(get_permalink($course_id)); ?>"><?php echo esc_html($course_post->post_title); ?></a>
                                    </h4>
                                </div>
                                
                                <!-- Course price removed as per requirement -->
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else : ?>
                    <p><?php esc_html_e('No courses found in this bundle.', 'masterstudy-course-bundler'); ?></p>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="mscb-single-bundle__sidebar">
            <div class="mscb-single-bundle__purchase-box">
                <div class="mscb-single-bundle__price">
                    <?php if ($sale_price && $sale_price < $price) : ?>
                        <span class="mscb-single-bundle__price-regular">
                            <del><?php echo esc_html($currency . number_format((float)$price, 2)); ?></del>
                        </span>
                        <span class="mscb-single-bundle__price-sale">
                            <?php echo esc_html($currency . number_format((float)$sale_price, 2)); ?>
                        </span>
                    <?php else : ?>
                        <span class="mscb-single-bundle__price-regular">
                            <?php echo esc_html($currency . number_format((float)$price, 2)); ?>
                        </span>
                    <?php endif; ?>
                </div>
                
                <?php if ($savings > 0) : ?>
                    <div class="mscb-single-bundle__savings-box">
                        <div class="mscb-single-bundle__savings-label"><?php esc_html_e('You Save:', 'masterstudy-course-bundler'); ?></div>
                        <div class="mscb-single-bundle__savings-value">
                            <?php echo esc_html($currency . number_format((float)$savings, 2)); ?> (<?php echo esc_html($savings_percentage); ?>%)
                        </div>
                    </div>
                <?php endif; ?>
                
                <div class="mscb-single-bundle__courses-count-box">
                    <div class="mscb-single-bundle__courses-count-label"><?php esc_html_e('Courses Included:', 'masterstudy-course-bundler'); ?></div>
                    <div class="mscb-single-bundle__courses-count-value">
                        <?php echo esc_html($total_courses); ?>
                    </div>
                </div>
                
                <a href="#" class="mscb-single-bundle__purchase-button" data-bundle-id="<?php echo esc_attr($bundle_id); ?>">
                    <?php esc_html_e('Purchase Bundle', 'masterstudy-course-bundler'); ?>
                </a>
                
                <script>
                jQuery(document).ready(function($) {
                    $('.mscb-single-bundle__purchase-button').on('click', function(e) {
                        e.preventDefault();
                        
                        const bundleId = $(this).data('bundle-id');
                        if (!bundleId) return;
                        
                        // Show loading state
                        const $button = $(this);
                        const originalText = $button.text();
                        $button.text('Processing...').addClass('loading').prop('disabled', true);
                        
                        // Use our custom AJAX handler to add the bundle to cart
                        $.ajax({
                            url: mscb_data.ajax_url,
                            type: 'POST',
                            dataType: 'json',
                            data: {
                                action: 'mscb_add_bundle_to_cart',
                                nonce: mscb_data.nonce,
                                bundle_id: bundleId
                            },
                            success: function(response) {
                                if (response.success) {
                                    // Redirect to checkout page
                                    window.location.href = '<?php echo esc_url(site_url("/checkout-2/")); ?>';
                                } else {
                                    // Show error message
                                    alert(response.data.message || '<?php esc_html_e("Error adding bundle to cart. Please try again.", "masterstudy-course-bundler"); ?>');
                                    $button.text(originalText).removeClass('loading').prop('disabled', false);
                                }
                            },
                            error: function() {
                                alert('<?php esc_html_e("Error adding bundle to cart. Please try again.", "masterstudy-course-bundler"); ?>');
                                $button.text(originalText).removeClass('loading').prop('disabled', false);
                            }
                        });
                    });
                });
                </script>
            </div>
        </div>
    </div>
</div>

<?php
get_footer();
