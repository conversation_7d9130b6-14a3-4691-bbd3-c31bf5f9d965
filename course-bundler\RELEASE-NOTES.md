# MasterStudy Course Bundler - Release Notes

## Version 1.0.0 - Initial Release

MasterStudy Course Bundler is a powerful extension for the MasterStudy LMS plugin that allows administrators to create and sell bundles of courses with special pricing. This initial release provides a comprehensive set of features for course bundling, enrollment management, and integration with the MasterStudy LMS ecosystem.

## Key Features

- **Course Bundle Creation**: Create custom bundles containing multiple MasterStudy LMS courses
- **Special Bundle Pricing**: Set regular and sale prices for course bundles
- **Automatic Enrollment**: Students are automatically enrolled in all courses within a purchased bundle
- **Bundle Management**: Admin interface for creating, editing, and managing course bundles
- **Frontend Display**: Shortcodes and templates for displaying bundles on your site
- **Purchase Integration**: Seamless integration with MasterStudy LMS cart and checkout systems
- **Enrollment Tracking**: Track bundle enrollments and completions

## Technical Requirements

- WordPress 5.8 or higher
- PHP 7.4 or higher
- MasterStudy LMS plugin (latest version recommended)

## Database Tables

The plugin creates the following custom database tables:

1. **{prefix}_mscb_bundles**: Stores bundle information
   - id (primary key)
   - title
   - description
   - price
   - sale_price
   - status
   - created_at
   - updated_at

2. **{prefix}_mscb_bundle_courses**: Manages the relationship between bundles and courses
   - id (primary key)
   - bundle_id
   - course_id
   - order_index
   - created_at

3. **{prefix}_mscb_bundle_enrollments**: Tracks user enrollments in bundles
   - id (primary key)
   - bundle_id
   - user_id
   - status
   - enrolled_at
   - completed_at

## Security Features

- **Direct Access Prevention**: All PHP files include ABSPATH checks to prevent direct file access
- **Nonce Verification**: AJAX requests are secured with WordPress nonces
- **Data Sanitization**: User input is properly sanitized using WordPress sanitization functions
- **SQL Preparation**: Database queries use prepared statements to prevent SQL injection
- **Capability Checks**: Admin functions verify user capabilities before execution
- **XSS Prevention**: Output is escaped using WordPress escaping functions
- **Error Logging**: Comprehensive error logging for troubleshooting

## Frontend Features

- **Bundle Grid Display**: Display bundles in a responsive grid layout
- **Single Bundle Display**: Detailed view of individual bundles
- **Purchase Integration**: "Add to Cart" functionality for bundles
- **Shortcodes**: Easy embedding of bundle displays on any page
- **Responsive Design**: Mobile-friendly layouts for all bundle displays
- **Custom Templates**: Overridable templates for theme customization

## Admin Features

- **Bundle Management**: Create, edit, and delete course bundles
- **Course Selection**: Add multiple courses to a bundle with drag-and-drop ordering
- **Pricing Controls**: Set regular and sale prices for bundles
- **Featured Bundles**: Mark bundles as featured for special highlighting
- **Bulk Actions**: Duplicate, activate, or deactivate multiple bundles at once
- **Bundle Statistics**: View enrollment data for each bundle

## Integration Points

- **MasterStudy LMS Cart**: Bundles can be added to the MasterStudy LMS cart
- **Enrollment System**: Integration with MasterStudy LMS enrollment system
- **Order Processing**: Hooks into order completion for automatic enrollment
- **WooCommerce Support**: Optional integration with WooCommerce (if used with MasterStudy LMS)

## Developer Features

- **Action Hooks**: Multiple action hooks for extending functionality
- **Filter Hooks**: Filter hooks for modifying bundle data and display
- **REST API Endpoints**: API endpoints for accessing bundle data
- **Template Overrides**: Customizable templates for theme developers
- **Modular Architecture**: Well-organized code for easy extension

## What's New in 1.0.0

This is the initial release of the MasterStudy Course Bundler plugin with the following features:

- Complete bundle management system
- Frontend display with shortcodes
- Integration with MasterStudy LMS cart and checkout
- Automatic enrollment in bundle courses
- Bundle enrollment tracking
- Admin interface for bundle management
- REST API endpoints for bundle data
- Responsive frontend design
- Security features and data validation

## Step-by-Step Usage Guide

### Installation and Setup

1. **Install the Plugin**
   - Upload the `course-bundler` folder to the `/wp-content/plugins/` directory
   - Navigate to Plugins > Installed Plugins in your WordPress admin
   - Find "MasterStudy Course Bundler" and click "Activate"
   - Verify that MasterStudy LMS is installed and activated

2. **Initial Configuration**
   - After activation, a new menu item "Course Bundles" will appear in your WordPress admin menu
   - No additional configuration is required as the plugin automatically creates necessary database tables

### Creating Your First Bundle

1. **Create a New Bundle**
   - Go to Course Bundles > Add New in your WordPress admin menu
   - Enter a title for your bundle (e.g., "Web Development Essentials")
   - Add a detailed description explaining what's included in the bundle

2. **Add Bundle Details**
   - Scroll down to the "Bundle Information" meta box
   - Set the regular price for the bundle
   - Optionally, set a sale price if you want to offer a discount
   - Check the "Featured Bundle" option if you want to highlight this bundle

3. **Select Courses**
   - In the "Bundle Courses" meta box, click the "Add Courses" button
   - Search for and select the courses you want to include in the bundle
   - Arrange the courses in the desired order using drag and drop
   - Click "Update" or "Publish" to save your bundle

### Displaying Bundles on Your Site

1. **Using Shortcodes**
   - Create a new page or edit an existing one
   - Add a shortcode block and insert one of the following shortcodes:
     - `[mscb_bundles]` - Displays a grid of all bundles
     - `[mscb_bundles limit="6" columns="3" featured="true"]` - Customized display
     - `[mscb_bundle id="123"]` - Displays a specific bundle (replace 123 with your bundle ID)
   - Publish or update the page

2. **Customizing the Display**
   - The shortcode accepts several parameters:
     - `limit` - Number of bundles to display (default: 10)
     - `columns` - Number of columns in the grid (default: 3)
     - `featured` - Show only featured bundles (default: false)

### Managing Student Enrollments

1. **Purchase Process**
   - When a student purchases a bundle:
     - They click the "Purchase Bundle" button on the bundle page
     - The bundle is added to their cart
     - They complete the checkout process
     - They are automatically enrolled in all courses in the bundle

2. **Tracking Enrollments**
   - Go to Course Bundles > All Bundles in your WordPress admin
   - Click on a bundle to edit it
   - Scroll down to the "Enrollments" section to view all students enrolled in the bundle

3. **Managing Access**
   - Students access the courses through their standard MasterStudy LMS dashboard
   - Each course from the bundle appears individually in their course list
   - Progress is tracked separately for each course

### Troubleshooting Common Issues

1. **Bundles Not Displaying**
   - Ensure you've published at least one bundle
   - Check that the shortcode is correctly formatted
   - Verify that the bundle has courses assigned to it

2. **Purchase Button Not Working**
   - Ensure MasterStudy LMS is up to date
   - Check that the checkout page is properly configured in MasterStudy LMS settings
   - Verify that the courses in the bundle are set as purchasable in MasterStudy LMS

3. **Enrollment Issues**
   - Check that the courses in the bundle are published and available
   - Verify that the user has completed the checkout process
   - Check the server error logs for any PHP errors



Thank you for using MasterStudy Course Bundler!

For support, feature requests, or bug reports, please contact the plugin developer.

© 2025 MasterStudy Course Bundler - Developed by kushagra mishra
