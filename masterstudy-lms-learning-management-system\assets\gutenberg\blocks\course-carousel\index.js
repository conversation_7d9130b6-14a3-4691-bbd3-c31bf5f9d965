(()=>{var e,t={2760:(e,t,n)=>{"use strict";const a=window.React,l=window.wp.blocks,i=window.wp.blockEditor;var r=n(6942),s=n.n(r);const o=window.wp.components,m=({condition:e,fallback:t=null,children:n})=>(0,a.createElement)(a.Fragment,null,e?n:t),c=(e,t)=>{if(typeof e!=typeof t)return!1;if(Number.isNaN(e)&&Number.isNaN(t))return!0;if("object"!=typeof e||null===e||null===t)return e===t;if(Object.keys(e).length!==Object.keys(t).length)return!1;if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;const n=e.slice().sort(),a=t.slice().sort();return n.every(((e,t)=>c(e,a[t])))}for(const n of Object.keys(e))if(!c(e[n],t[n]))return!1;return!0},d=(e=[])=>e.map((e=>({label:e.name,value:e.term_id})));let u=function(e){return e.ALL="all",e.SOME="some",e}({}),g=function(e){return e.NORMAL="Normal",e.HOVER="Hover",e.ACTIVE="Active",e.FOCUS="Focus",e}({}),p=function(e){return e.DESKTOP="Desktop",e.TABLET="Tablet",e.MOBILE="Mobile",e}({}),v=function(e){return e.TOP_lEFT="top-left",e.TOP_CENTER="top-center",e.TOP_RIGHT="top-right",e.BOTTOM_lEFT="bottom-left",e.BOTTOM_CENTER="bottom-center",e.BOTTOM_RIGHT="bottom-right",e}({});const h=["",null,void 0,"null","undefined"],_=[".jpg",".jpeg",".png",".gif"],b=e=>h.includes(e),y=(e,t,n="")=>{const a=e[t];return"object"==typeof a&&null!==a?((e,t)=>{return n=e,Object.values(n).every((e=>h.includes(e)))?null:((e,t="")=>{const n=Object.entries(e).reduce(((e,[n,a])=>(e[n]=(a||"0")+t,e)),{});return`${n.top} ${n.right} ${n.bottom} ${n.left}`})(e,t);var n})(a,n):((e,t)=>b(e)?e:(e=>{if("string"!=typeof e)return!1;const t=e.slice(e.lastIndexOf("."));return _.includes(t)||e.startsWith("blob")})(e)?`url('${e}')`:String(e+t))(a,n)},E=(e,t,n)=>{const a={};return n.forEach((({isAdaptive:n,hasHover:l,unit:i},r)=>{if(t.hasOwnProperty(r)){const{unitMeasureDesktop:o,unitMeasureTablet:m,unitMeasureMobile:c}=((e,t)=>{var n;return{unitMeasureDesktop:null!==(n=e[t])&&void 0!==n?n:"",unitMeasureTablet:t?e[t+"Tablet"]:"",unitMeasureMobile:t?e[t+"Mobile"]:""}})(t,i);if(n&&l){const{desktopHoverPropertyName:n,mobileHoverPropertyName:l,tabletHoverPropertyName:i}=(e=>{const t=e+g.HOVER;return{desktopHoverPropertyName:t,tabletHoverPropertyName:t+"Tablet",mobileHoverPropertyName:t+"Mobile"}})(r),s=y(t,n,o);b(s)||(a[`--lms-${e}-${n}`]=s);const d=y(t,i,m);b(d)||(a[`--lms-${e}-${i}`]=d);const u=y(t,l,c);b(u)||(a[`--lms-${e}-${l}`]=u)}if(l){const n=r+g.HOVER,l=y(t,n,o);b(l)||(a[`--lms-${e}-${n}`]=l)}if(n){const{desktopPropertyName:n,mobilePropertyName:l,tabletPropertyName:i}={desktopPropertyName:s=r,tabletPropertyName:s+"Tablet",mobilePropertyName:s+"Mobile"},d=y(t,n,o);b(d)||(a[`--lms-${e}-${n}`]=d);const u=y(t,i,m);b(u)||(a[`--lms-${e}-${i}`]=u);const g=y(t,l,c);b(g)||(a[`--lms-${e}-${l}`]=g)}const d=y(t,r,o);b(d)||(a[`--lms-${e}-${r}`]=d)}var s})),a},C=window.wp.i18n,f=(C.__("Small","masterstudy-lms-learning-management-system"),C.__("Normal","masterstudy-lms-learning-management-system"),C.__("Large","masterstudy-lms-learning-management-system"),C.__("Extra Large","masterstudy-lms-learning-management-system"),"wp-block-masterstudy-settings__"),N={top:"",right:"",bottom:"",left:""},T=[v.TOP_lEFT,v.TOP_CENTER,v.TOP_RIGHT],w=[v.BOTTOM_lEFT,v.BOTTOM_CENTER,v.BOTTOM_RIGHT],S=[{label:C.__("Newest","masterstudy-lms-learning-management-system"),value:"date_high"},{label:C.__("Oldest","masterstudy-lms-learning-management-system"),value:"date_low"},{label:C.__("Overall rating","masterstudy-lms-learning-management-system"),value:"rating"},{label:C.__("Popular","masterstudy-lms-learning-management-system"),value:"popular"},{label:C.__("Price low","masterstudy-lms-learning-management-system"),value:"price_low"},{label:C.__("Price high","masterstudy-lms-learning-management-system"),value:"price_high"}];function x(e){return Array.isArray(e)?e.map((e=>f+e)):f+e}const M=window.wp.element,O=window.wp.data,B=()=>(0,O.useSelect)((e=>e("core/editor").getDeviceType()||e("core/edit-site")?.__experimentalGetPreviewDeviceType()||e("core/edit-post")?.__experimentalGetPreviewDeviceType()||e("masterstudy/store")?.getDeviceType()),[])||"Desktop",k=(e,t,n)=>{const a=B();return a===p.TABLET?t||e:a===p.MOBILE&&(n||t)||e},A=(e=!1)=>{const[t,n]=(0,M.useState)(e),a=(0,M.useCallback)((()=>{n(!0)}),[]);return{isOpen:t,onClose:(0,M.useCallback)((()=>{n(!1)}),[]),onOpen:a,onToggle:(0,M.useCallback)((()=>{n((e=>!e))}),[])}},R=(0,M.createContext)(null),P=({children:e,...t})=>(0,a.createElement)(R.Provider,{value:{...t}},e),H=()=>{const e=(0,M.useContext)(R);if(!e)throw new Error("No settings context provided");return e},D=(e="")=>{const{attributes:t,setAttributes:n,onResetByFieldName:a,changedFieldsByName:l}=H();return{value:t[e],onChange:t=>n({[e]:t}),onReset:a.get(e),isChanged:l.get(e)}},L=(e,t=!1,n=!1)=>{const{hoverName:a,onChangeHoverName:l}=(()=>{const[e,t]=(0,M.useState)(g.NORMAL);return{hoverName:e,onChangeHoverName:(0,M.useCallback)((e=>{t(e)}),[])}})(),i=B();return{fieldName:(0,M.useMemo)((()=>{const l=a===g.HOVER?a:"",r=i===p.DESKTOP?"":i;return n&&t?e+l+r:n&&!t?e+l:t&&!n?e+r:e}),[e,n,t,a,i]),hoverName:a,onChangeHoverName:l}},F=(e,t=!1,n="Normal")=>{const a=B(),l=(0,M.useMemo)((()=>{const l=n===g.NORMAL?"":n,i=a===p.DESKTOP?"":a;return l&&t?e+l+i:l&&!t?e+l:t&&!l?e+i:e}),[e,t,n,a]),{value:i,isChanged:r,onReset:s}=D(l);return{fieldName:l,value:i,isChanged:r,onReset:s}},I=(e=[],t=u.ALL)=>{const{attributes:n}=H();return!e.length||(t===u.ALL?e.every((({name:e,value:t})=>{const a=n[e];return Array.isArray(t)?Array.isArray(a)?c(t,a):t.includes(a):t===a})):t!==u.SOME||e.some((({name:e,value:t})=>{const a=n[e];return Array.isArray(t)?Array.isArray(a)?c(t,a):t.includes(a):t===a})))},U=e=>{const t=(0,M.useRef)(null);return(0,M.useEffect)((()=>{const n=n=>{t.current&&!t.current.contains(n.target)&&e()};return document.addEventListener("click",n),()=>{document.removeEventListener("click",n)}}),[t,e]),t},V=e=>(0,a.createElement)(o.SVG,{width:"16",height:"16",viewBox:"0 0 12 13",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(o.Path,{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})),W=e=>(0,a.createElement)(o.SVG,{width:"16",height:"16",viewBox:"0 0 14 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(o.G,{"clip-path":"url(#clip0_1068_38993)"},(0,a.createElement)(o.Path,{d:"M11.1973 8.60005L8.74967 8.11005V5.67171C8.74967 5.517 8.68822 5.36863 8.57882 5.25923C8.46942 5.14984 8.32105 5.08838 8.16634 5.08838H6.99967C6.84496 5.08838 6.69659 5.14984 6.5872 5.25923C6.4778 5.36863 6.41634 5.517 6.41634 5.67171V8.58838H5.24967C5.15021 8.58844 5.05241 8.61391 4.96555 8.66238C4.87869 8.71084 4.80565 8.78068 4.75336 8.86529C4.70106 8.9499 4.67125 9.04646 4.66674 9.14582C4.66223 9.24518 4.68317 9.34405 4.72759 9.43305L6.47759 12.933C6.57676 13.1302 6.77859 13.255 6.99967 13.255H11.083C11.2377 13.255 11.3861 13.1936 11.4955 13.0842C11.6049 12.9748 11.6663 12.8264 11.6663 12.6717V9.17171C11.6665 9.03685 11.6198 8.90612 11.5343 8.80186C11.4487 8.69759 11.3296 8.62626 11.1973 8.60005Z"}),(0,a.createElement)(o.Path,{d:"M10.4997 1.58838H3.49967C2.85626 1.58838 2.33301 2.11221 2.33301 2.75505V6.83838C2.33301 7.4818 2.85626 8.00505 3.49967 8.00505H5.24967V6.83838H3.49967V2.75505H10.4997V6.83838H11.6663V2.75505C11.6663 2.11221 11.1431 1.58838 10.4997 1.58838Z"})),(0,a.createElement)("defs",null,(0,a.createElement)("clipPath",{id:"clip0_1068_38993"},(0,a.createElement)(o.Rect,{width:"14",height:"14",transform:"translate(0 0.421875)"})))),G=[{value:g.NORMAL,label:C.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(V,{onClick:e})},{value:g.HOVER,label:C.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(W,{onClick:e})},{value:g.ACTIVE,label:C.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(W,{onClick:e})},{value:g.FOCUS,label:C.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(W,{onClick:e})}],z={[g.NORMAL]:{icon:(0,a.createElement)(V,null),label:C.__("Normal State","masterstudy-lms-learning-management-system")},[g.HOVER]:{icon:(0,a.createElement)(W,null),label:C.__("Hovered State","masterstudy-lms-learning-management-system")},[g.ACTIVE]:{icon:(0,a.createElement)(W,null),label:C.__("Active State","masterstudy-lms-learning-management-system")},[g.FOCUS]:{icon:(0,a.createElement)(W,null),label:C.__("Focus State","masterstudy-lms-learning-management-system")}},j=(e,t)=>{let n=[];return n=e.length?G.filter((t=>e.includes(t.value))):G,n=n.filter((e=>e.value!==t)),{ICONS_MAP:z,options:n}},[$,Z,K,X,q]=x(["hover-state","hover-state__selected","hover-state__selected__opened-menu","hover-state__menu","hover-state__menu__item"]),Y=({stateOptions:e,currentState:t,onSelect:n})=>{const{isOpen:l,onOpen:i,onClose:r}=A(),o=U(r),{ICONS_MAP:c,options:d}=j(e,t);return(0,a.createElement)("div",{className:$,ref:o},(0,a.createElement)("div",{className:s()([Z],{[K]:l}),onClick:i,title:c[t]?.label},c[t]?.icon),(0,a.createElement)(m,{condition:l},(0,a.createElement)("div",{className:X},d.map((({value:e,icon:t,label:l})=>(0,a.createElement)("div",{key:e,className:q,title:l},t((()=>n(e)))))))))},J=x("color-indicator"),Q=(0,M.memo)((({color:e,onChange:t})=>(0,a.createElement)("div",{className:J},(0,a.createElement)(i.PanelColorSettings,{enableAlpha:!0,disableCustomColors:!1,__experimentalHasMultipleOrigins:!0,__experimentalIsRenderedInSidebar:!0,colorSettings:[{label:"",value:e,onChange:t}]}))));var ee;function te(){return te=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},te.apply(null,arguments)}var ne,ae,le=function(e){return a.createElement("svg",te({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 12 13"},e),ee||(ee=a.createElement("path",{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})))};function ie(){return ie=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},ie.apply(null,arguments)}var re=function(e){return a.createElement("svg",ie({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 14 15"},e),ne||(ne=a.createElement("g",{clipPath:"url(#state-hover_svg__a)"},a.createElement("path",{d:"M11.197 8.6 8.75 8.11V5.672a.583.583 0 0 0-.584-.584H7a.583.583 0 0 0-.584.584v2.916H5.25a.584.584 0 0 0-.522.845l1.75 3.5c.099.197.3.322.522.322h4.083a.583.583 0 0 0 .583-.583v-3.5a.58.58 0 0 0-.469-.572"}),a.createElement("path",{d:"M10.5 1.588h-7c-.644 0-1.167.524-1.167 1.167v4.083c0 .644.523 1.167 1.167 1.167h1.75V6.838H3.5V2.755h7v4.083h1.166V2.755c0-.643-.523-1.167-1.166-1.167"}))),ae||(ae=a.createElement("defs",null,a.createElement("clipPath",{id:"state-hover_svg__a"},a.createElement("path",{d:"M0 .422h14v14H0z"})))))};const se=[{value:g.NORMAL,label:C.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(le,{onClick:e})},{value:g.HOVER,label:C.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(re,{onClick:e})}],oe={[g.NORMAL]:{icon:(0,a.createElement)(le,null),label:C.__("Normal State","masterstudy-lms-learning-management-system")},[g.HOVER]:{icon:(0,a.createElement)(re,null),label:C.__("Hovered State","masterstudy-lms-learning-management-system")}},me=x("hover-state"),ce=x("hover-state__selected"),de=x("hover-state__selected__opened-menu"),ue=x("has-changes"),ge=x("hover-state__menu"),pe=x("hover-state__menu__item"),ve=(0,M.memo)((e=>{const{hoverName:t,onChangeHoverName:n,fieldName:l}=e,{changedFieldsByName:i}=H(),r=i.get(l),{isOpen:o,onOpen:c,onClose:d}=A(),u=U(d),{ICONS_MAP:g,options:p}=(e=>{const t=(0,M.useMemo)((()=>se.filter((t=>t.value!==e))),[e]);return{ICONS_MAP:oe,options:t}})(t),v=(0,M.useCallback)((e=>{n(e),d()}),[n,d]);return(0,a.createElement)("div",{className:me,ref:u},(0,a.createElement)("div",{className:s()([ce],{[de]:o,[ue]:r}),onClick:c,title:g[t]?.label},g[t]?.icon),(0,a.createElement)(m,{condition:o},(0,a.createElement)("div",{className:ge},p.map((({value:e,icon:t,label:n})=>(0,a.createElement)("div",{key:e,className:pe,title:n},t((()=>v(e)))))))))})),he={Desktop:{icon:"desktop",label:C.__("Desktop","masterstudy-lms-learning-management-system")},Tablet:{icon:"tablet",label:C.__("Tablet","masterstudy-lms-learning-management-system")},Mobile:{icon:"smartphone",label:C.__("Mobile","masterstudy-lms-learning-management-system")}},_e=[{value:p.DESKTOP,icon:"desktop",label:C.__("Desktop","masterstudy-lms-learning-management-system")},{value:p.TABLET,icon:"tablet",label:C.__("Tablet","masterstudy-lms-learning-management-system")},{value:p.MOBILE,icon:"smartphone",label:C.__("Mobile","masterstudy-lms-learning-management-system")}],be=x("device-picker"),ye=x("device-picker__selected"),Ee=x("device-picker__selected__opened-menu"),Ce=x("device-picker__menu"),fe=x("device-picker__menu__item"),Ne=()=>{const{isOpen:e,onOpen:t,onClose:n}=A(),{value:l,onChange:i}=(e=>{const t=B(),n=(0,O.useDispatch)();return{value:(0,M.useMemo)((()=>he[t]),[t]),onChange:t=>{n("core/edit-site")&&n("core/edit-site").__experimentalSetPreviewDeviceType?n("core/edit-site").__experimentalSetPreviewDeviceType(t):n("core/edit-post")&&n("core/edit-post").__experimentalSetPreviewDeviceType?n("core/edit-post").__experimentalSetPreviewDeviceType(t):n("masterstudy/store").setDeviceType(t),e()}}})(n),r=(e=>(0,M.useMemo)((()=>_e.filter((t=>t.icon!==e))),[e]))(l.icon),c=U(n);return(0,a.createElement)("div",{className:be,ref:c},(0,a.createElement)(o.Dashicon,{className:s()([ye],{[Ee]:e}),icon:l.icon,size:16,onClick:t,title:l.label}),(0,a.createElement)(m,{condition:e},(0,a.createElement)("div",{className:Ce},r.map((e=>(0,a.createElement)(o.Dashicon,{key:e.value,icon:e.icon,size:16,onClick:()=>i(e.value),className:fe,title:e.label}))))))},Te=x("reset-button"),we=({onReset:e})=>(0,a.createElement)(o.Dashicon,{icon:"undo",onClick:e,className:Te,size:16}),Se=[{label:"PX",value:"px"},{label:"%",value:"%"},{label:"EM",value:"em"},{label:"REM",value:"rem"},{label:"VW",value:"vw"},{label:"VH",value:"vh"}],xe=x("unit"),Me=x("unit__single"),Oe=x("unit__list"),Be=({name:e,isAdaptive:t})=>{const{isOpen:n,onOpen:l,onClose:i}=A(),{fieldName:r}=L(e,t),{value:s,onChange:o}=D(r),c=U(i);return(0,a.createElement)("div",{className:xe,ref:c},(0,a.createElement)("div",{className:Me,onClick:l},s),(0,a.createElement)(m,{condition:n},(0,a.createElement)("div",{className:Oe},Se.map((({value:e,label:t})=>(0,a.createElement)("div",{key:e,onClick:()=>(o(e),void i())},t))))))},ke=x("popover-modal"),Ae=x("popover-modal__close dashicon dashicons dashicons-no-alt"),Re=e=>{const{isOpen:t,onClose:n,popoverContent:l}=e;return(0,a.createElement)(m,{condition:t},(0,a.createElement)(o.Popover,{position:"middle left",onClose:n,className:ke},l,(0,a.createElement)("span",{onClick:n,className:Ae})))},Pe=x("setting-label"),He=x("setting-label__content"),De=e=>{const{label:t,isChanged:n=!1,onReset:l,showDevicePicker:i=!0,HoverStateControl:r=null,unitName:s,popoverContent:o=null,dependencies:c}=e,{isOpen:d,onClose:u,onToggle:g}=A();return I(c)?(0,a.createElement)("div",{className:Pe},(0,a.createElement)("div",{className:He},(0,a.createElement)("div",{onClick:g},t),(0,a.createElement)(m,{condition:Boolean(o)},(0,a.createElement)(Re,{isOpen:d,onClose:u,popoverContent:o})),(0,a.createElement)(m,{condition:i},(0,a.createElement)(Ne,null)),(0,a.createElement)(m,{condition:Boolean(r)},r)),(0,a.createElement)(m,{condition:Boolean(s)},(0,a.createElement)(Be,{name:s,isAdaptive:i})),(0,a.createElement)(m,{condition:n},(0,a.createElement)(we,{onReset:l}))):null},Le=x("suffix"),Fe=()=>(0,a.createElement)("div",{className:Le},(0,a.createElement)(o.Dashicon,{icon:"color-picker",size:16})),Ie=x("color-picker"),Ue=e=>{const{name:t,label:n,placeholder:l,dependencyMode:i,dependencies:r,isAdaptive:s=!1,hasHover:c=!1}=e,{fieldName:d,hoverName:u,onChangeHoverName:g}=L(t,s,c),{value:p,isChanged:v,onChange:h,onReset:_}=D(d);return I(r,i)?(0,a.createElement)("div",{className:Ie},(0,a.createElement)(m,{condition:Boolean(n)},(0,a.createElement)(De,{label:n,isChanged:v,onReset:_,showDevicePicker:s,HoverStateControl:(0,a.createElement)(m,{condition:c},(0,a.createElement)(ve,{hoverName:u,onChangeHoverName:g,fieldName:d}))})),(0,a.createElement)(o.__experimentalInputControl,{prefix:(0,a.createElement)(Q,{color:p,onChange:h}),suffix:(0,a.createElement)(Fe,null),onChange:h,value:p,placeholder:l})):null},Ve=x("number-steppers"),We=x("indent-steppers"),Ge=x("indent-stepper-plus"),ze=x("indent-stepper-minus"),je=({onIncrement:e,onDecrement:t,withArrows:n=!1})=>n?(0,a.createElement)("span",{className:We},(0,a.createElement)("button",{onClick:e,className:Ge}),(0,a.createElement)("button",{onClick:t,className:ze})):(0,a.createElement)("span",{className:Ve},(0,a.createElement)("button",{onClick:e},"+"),(0,a.createElement)("button",{onClick:t},"-")),[$e,Ze]=x(["indents","indents-control"]),Ke=({name:e,label:t,unitName:n,popoverContent:l,dependencyMode:i,dependencies:r,isAdaptive:s=!1})=>{const{fieldName:c}=L(e,s),{value:d,onResetSegmentedBox:u,hasChanges:g,handleInputIncrement:p,handleInputDecrement:v,updateDirectionsValues:h,lastFieldValue:_}=((e,t)=>{const{value:n,isChanged:a,onChange:l,onReset:i}=D(e),{onResetByFieldName:r,changedFieldsByName:s}=H(),o=a||s.get(t),m=e=>{l({...n,...e})},[c,d]=(0,M.useState)(!1);return{value:n,onResetSegmentedBox:()=>{i(),r.get(t)()},hasChanges:o,handleInputIncrement:e=>Number(n[e])+1,handleInputDecrement:e=>Number(n[e])-1,updateDirectionsValues:(e,t,n)=>{e?(d(!1),m({top:n,right:n,bottom:n,left:n})):(d(n),m({[t]:n}))},lastFieldValue:c}})(c,n),[b,y]=(0,M.useState)((()=>{const{left:e,right:t,top:n,bottom:a}=d;return""!==e&&e===t&&t===n&&n===a})),E=e=>{const[t,n]=Object.entries(e)[0];h(b,t,n)},f=e=>()=>{const t=p(e);h(b,e,String(t))},N=e=>()=>{const t=v(e);h(b,e,String(t))};return I(r,i)?(0,a.createElement)("div",{className:$e},(0,a.createElement)(m,{condition:Boolean(t)},(0,a.createElement)(De,{label:null!=t?t:"",isChanged:g,onReset:u,unitName:n,popoverContent:l,showDevicePicker:s})),(0,a.createElement)("div",{className:`${Ze} ${b?"active":""}`},(0,a.createElement)("div",null,(0,a.createElement)(o.__experimentalNumberControl,{value:d.top,onChange:e=>{E({top:e})},spinControls:"none",suffix:(0,a.createElement)(je,{onIncrement:f("top"),onDecrement:N("top"),withArrows:!0})}),(0,a.createElement)("div",null,C.__("Top","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(o.__experimentalNumberControl,{value:d.right,onChange:e=>{E({right:e})},spinControls:"none",suffix:(0,a.createElement)(je,{onIncrement:f("right"),onDecrement:N("right"),withArrows:!0})}),(0,a.createElement)("div",null,C.__("Right","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(o.__experimentalNumberControl,{value:d.bottom,onChange:e=>{E({bottom:e})},spinControls:"none",suffix:(0,a.createElement)(je,{onIncrement:f("bottom"),onDecrement:N("bottom"),withArrows:!0})}),(0,a.createElement)("div",null,C.__("Bottom","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(o.__experimentalNumberControl,{value:d.left,onChange:e=>{E({left:e})},spinControls:"none",suffix:(0,a.createElement)(je,{onIncrement:f("left"),onDecrement:N("left"),withArrows:!0})}),(0,a.createElement)("div",null,C.__("Left","masterstudy-lms-learning-management-system"))),(0,a.createElement)(o.Dashicon,{icon:"dashicons "+(b?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{b||!1===_||h(!0,"left",_),y((e=>!e))}}))):null},[Xe,qe,Ye,Je]=x(["toggle-group-wrapper","toggle-group","toggle-group__toggle","toggle-group__active-toggle"]),Qe=e=>{const{name:t,options:n,label:l,isAdaptive:i=!1,dependencyMode:r,dependencies:o}=e,{fieldName:c}=L(t,i),{value:d,isChanged:u,onChange:g,onReset:p}=D(c);return I(o,r)?(0,a.createElement)("div",{className:Xe},(0,a.createElement)(m,{condition:Boolean(l)},(0,a.createElement)(De,{label:l,isChanged:u,onReset:p,showDevicePicker:i})),(0,a.createElement)("div",{className:qe},n.map((e=>(0,a.createElement)("div",{key:e.value,className:s()([Ye],{[Je]:e.value===d}),onClick:()=>g(e.value)},e.label))))):null},[et,tt,nt,at]=x(["border-control","border-control-solid","border-control-dashed","border-control-dotted"]),lt=e=>{const{label:t,borderStyleName:n,borderColorName:l,borderWidthName:i,dependencyMode:r,dependencies:o,isAdaptive:c=!1,hasHover:d=!1}=e,[u,g]=(0,M.useState)("Normal"),{fieldName:p,value:v,isChanged:h,onReset:_}=F(n,c,u),{fieldName:b,isChanged:y,onReset:E}=F(l,c,u),{fieldName:f,isChanged:N,onReset:T}=F(i,c,u);if(!I(o,r))return null;const w=h||y||N;return(0,a.createElement)("div",{className:s()([et],{"has-reset-button":w})},(0,a.createElement)(De,{label:t,isChanged:w,onReset:()=>{_(),E(),T()},showDevicePicker:c,HoverStateControl:(0,a.createElement)(m,{condition:d},(0,a.createElement)(Y,{stateOptions:["Normal","Hover"],currentState:u,onSelect:g}))}),(0,a.createElement)(Qe,{options:[{label:(0,a.createElement)("span",null,C.__("None","masterstudy-lms-learning-management-system")),value:"none"},{label:(0,a.createElement)("span",{className:tt}),value:"solid"},{label:(0,a.createElement)("span",{className:nt},(0,a.createElement)("span",null)),value:"dashed"},{label:(0,a.createElement)("span",{className:at},(0,a.createElement)("span",null,(0,a.createElement)("span",null))),value:"dotted"}],name:p}),(0,a.createElement)(m,{condition:"none"!==v},(0,a.createElement)(Ue,{name:b,placeholder:C.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Ke,{name:f})))},it=x("border-radius"),rt=x("border-radius-control"),st=({name:e,label:t,unitName:n,popoverContent:l,dependencyMode:i,dependencies:r,isAdaptive:m=!1,hasHover:c=!1})=>{const{fieldName:d}=L(e,m,c),{value:u,onResetBorderRadius:g,hasChanges:p,handleInputIncrement:v,handleInputDecrement:h,updateDirectionsValues:_,lastFieldValue:b}=((e,t)=>{const[n,a]=(0,M.useState)(!1),{value:l,isChanged:i,onChange:r,onReset:s}=D(e),{onResetByFieldName:o,changedFieldsByName:m}=H(),c=i||m.get(t),d=e=>{r({...l,...e})};return{value:l,onResetBorderRadius:()=>{s(),o.get(t)()},hasChanges:c,handleInputIncrement:e=>Number(l[e])+1,handleInputDecrement:e=>Number(l[e])-1,updateDirectionsValues:(e,t,n)=>{e?(d({top:n,right:n,bottom:n,left:n}),a(!1)):(d({[t]:n}),a(n))},lastFieldValue:n}})(d,n),[y,E]=(0,M.useState)((()=>{const{left:e,right:t,top:n,bottom:a}=u;return""!==e&&e===t&&t===n&&n===a})),C=e=>{const[t,n]=Object.entries(e)[0];_(y,t,n)},f=e=>()=>{const t=v(e);_(y,e,String(t))},N=e=>()=>{const t=h(e);_(y,e,String(t))};return I(r,i)?(0,a.createElement)("div",{className:it},(0,a.createElement)(De,{label:t,isChanged:p,onReset:g,unitName:n,popoverContent:l,showDevicePicker:m}),(0,a.createElement)("div",{className:s()([rt],{"has-reset-button":p,active:y})},(0,a.createElement)("div",{className:"number-control-top"},(0,a.createElement)(o.__experimentalNumberControl,{value:u.top,onChange:e=>{C({top:e})},spinControls:"none",suffix:(0,a.createElement)(je,{onIncrement:f("top"),onDecrement:N("top"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-right"},(0,a.createElement)(o.__experimentalNumberControl,{value:u.right,onChange:e=>{C({right:e})},spinControls:"none",suffix:(0,a.createElement)(je,{onIncrement:f("right"),onDecrement:N("right"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-left"},(0,a.createElement)(o.__experimentalNumberControl,{value:u.left,onChange:e=>{C({left:e})},spinControls:"none",suffix:(0,a.createElement)(je,{onIncrement:f("left"),onDecrement:N("left"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-bottom"},(0,a.createElement)(o.__experimentalNumberControl,{value:u.bottom,onChange:e=>{C({bottom:e})},spinControls:"none",suffix:(0,a.createElement)(je,{onIncrement:f("bottom"),onDecrement:N("bottom"),withArrows:!0})})),(0,a.createElement)(o.Dashicon,{icon:"dashicons "+(y?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{y||!1===b||_(!0,"left",b),E((e=>!e))}}))):null},ot=(x("box-shadow-preset"),x("presets")),mt=x("presets__item-wrapper"),ct=x("presets__item-wrapper__preset"),dt=x("presets__item-wrapper__name"),ut=((0,M.memo)((e=>{const{presets:t,activePreset:n,onSelectPreset:l,PresetItem:i,detectIsActive:r,detectByIndex:o=!1}=e;return(0,a.createElement)("div",{className:ot},t.map((({name:e,...t},m)=>(0,a.createElement)("div",{key:m,className:s()([mt],{active:r(n,o?m:t)}),onClick:()=>l(t)},(0,a.createElement)("div",{className:ct},(0,a.createElement)(i,{preset:t})),(0,a.createElement)("span",{className:dt},e)))))})),x("range-control")),gt=e=>{const{name:t,label:n,min:l,max:i,unitName:r,dependencyMode:s,dependencies:c,isAdaptive:d=!1}=e,{fieldName:u}=L(t,d),{value:g,onChange:p,onResetNumberField:v,hasChanges:h}=((e,t)=>{const{value:n,isChanged:a,onChange:l,onReset:i}=D(e),{onResetByFieldName:r,changedFieldsByName:s}=H();return{value:n,onChange:l,onResetNumberField:()=>{i(),r.get(t)()},hasChanges:a||s.get(t)}})(u,r);return I(c,s)?(0,a.createElement)("div",{className:ut},(0,a.createElement)(m,{condition:Boolean(n)},(0,a.createElement)(De,{label:n,isChanged:h,onReset:v,unitName:r,showDevicePicker:d})),(0,a.createElement)(o.RangeControl,{value:g,onChange:p,min:l,max:i})):null},pt=x("switch"),vt=e=>{const{name:t,label:n,dependencyMode:l,dependencies:i,isAdaptive:r=!1}=e,{fieldName:s}=L(t,r),{value:c,onChange:d}=D(s);return I(i,l)?(0,a.createElement)("div",{className:pt,"data-has-label":Boolean(n).toString()},(0,a.createElement)(o.ToggleControl,{label:n,checked:c,onChange:d}),(0,a.createElement)(m,{condition:r},(0,a.createElement)(Ne,null))):null},ht=(x("box-shadow-settings"),x("box-shadow-presets-title"),x("input-field"),x("input-field-control"),x("number-field")),_t=x("number-field-control"),bt=e=>{const{name:t,label:n,unitName:l,help:i,popoverContent:r,dependencyMode:s,dependencies:m,isAdaptive:c=!1}=e,{fieldName:d}=L(t,c),{value:u,onResetNumberField:g,hasChanges:p,handleIncrement:v,handleDecrement:h,handleInputChange:_}=((e,t)=>{const{value:n,isChanged:a,onChange:l,onReset:i}=D(e),{onResetByFieldName:r,changedFieldsByName:s}=H(),o=a||s.get(t);return{value:n,onResetNumberField:()=>{i(),r.get(t)()},hasChanges:o,handleIncrement:()=>{l(n+1)},handleDecrement:()=>{l(n-1)},handleInputChange:e=>{const t=Number(""===e?0:e);l(t)}}})(d,l);return I(m,s)?(0,a.createElement)("div",{className:ht},(0,a.createElement)(De,{label:n,isChanged:p,onReset:g,unitName:l,showDevicePicker:c,popoverContent:r}),(0,a.createElement)("div",{className:_t},(0,a.createElement)(o.__experimentalNumberControl,{value:u,onChange:_,spinControls:"none",suffix:(0,a.createElement)(je,{onIncrement:v,onDecrement:h})})),i&&(0,a.createElement)("small",null,i)):null},yt=({className:e})=>(0,a.createElement)("div",{className:e},C.__("No options","masterstudy-lms-learning-management-system")),Et=x("select__single-item"),Ct=x("select__container"),ft=x("select__container__multi-item"),Nt=({multiple:e,value:t,options:n,onChange:l})=>{const{singleValue:i,multipleValue:r}=((e,t,n)=>({singleValue:(0,M.useMemo)((()=>t?null:n.find((t=>t.value===e))?.label),[t,e,n]),multipleValue:(0,M.useMemo)((()=>t?e:null),[t,e])}))(t,e,n);return(0,a.createElement)(m,{condition:e,fallback:(0,a.createElement)("div",{className:Et},i)},(0,a.createElement)("div",{className:Ct},r?.map((e=>{const t=n.find((t=>t.value===e));return t?(0,a.createElement)("div",{key:t.value,className:ft},(0,a.createElement)("div",null,t.label),(0,a.createElement)(o.Dashicon,{icon:"no-alt",onClick:()=>l(t.value),size:16})):null}))))},Tt=x("select"),wt=x("select__select-box"),St=x("select__placeholder"),xt=x("select__select-box-multiple"),Mt=x("select__menu"),Ot=x("select__menu__options-container"),Bt=x("select__menu__item"),kt=e=>{const{options:t,multiple:n=!1,placeholder:l="Select",value:i,onSelect:r}=e,{isOpen:c,onToggle:d,onClose:u}=A(),g=U(u),p=((e,t,n)=>(0,M.useMemo)((()=>n&&Array.isArray(e)?t.filter((t=>!e.includes(t.value))):t.filter((t=>t.value!==e))),[e,t,n]))(i,t,n),v=((e,t,n,a)=>(0,M.useCallback)((l=>{if(t&&Array.isArray(e)){const t=e.includes(l)?e.filter((e=>e!==l)):[...e,l];n(t)}else n(l),a()}),[t,e,n,a]))(i,n,r,u),h=((e,t)=>(0,M.useMemo)((()=>t&&Array.isArray(e)?Boolean(e.length):Boolean(e)),[t,e]))(i,n),_=n&&Array.isArray(i)&&i?.length>0;return(0,a.createElement)("div",{className:Tt,ref:g},(0,a.createElement)("div",{className:s()([wt],{[xt]:_}),onClick:d},(0,a.createElement)(m,{condition:h,fallback:(0,a.createElement)("div",{className:St},l)},(0,a.createElement)(Nt,{onChange:v,options:t,multiple:n,value:i})),(0,a.createElement)(o.Dashicon,{icon:c?"arrow-up-alt2":"arrow-down-alt2",size:16,style:{color:"#4D5E6F"}})),(0,a.createElement)(m,{condition:c},(0,a.createElement)("div",{className:Mt},(0,a.createElement)(m,{condition:Boolean(p.length),fallback:(0,a.createElement)(yt,{className:Bt})},(0,a.createElement)("div",{className:Ot},p.map((e=>(0,a.createElement)("div",{key:e.value,onClick:()=>v(e.value),className:Bt},e.label))))))))},At=x("setting-select"),Rt=e=>{const{name:t,options:n,label:l,multiple:i=!1,placeholder:r,isAdaptive:s=!1,dependencyMode:o,dependencies:c}=e,{fieldName:d}=L(t,s),{value:u,isChanged:g,onChange:p,onReset:v}=D(d);return I(c,o)?(0,a.createElement)("div",{className:At},(0,a.createElement)(m,{condition:Boolean(l)},(0,a.createElement)(De,{label:l,isChanged:g,onReset:v,showDevicePicker:s})),(0,a.createElement)(kt,{options:n,value:u,onSelect:p,multiple:i,placeholder:r})):null},Pt=x("row-select"),Ht=x("row-select__label"),Dt=x("row-select__control"),Lt=e=>{const{name:t,label:n,options:l,isAdaptive:i=!1}=e,{fieldName:r}=L(t,i),{isChanged:s,onReset:o}=D(r);return(0,a.createElement)("div",{className:Pt},(0,a.createElement)("div",{className:Ht},(0,a.createElement)("div",null,n),(0,a.createElement)(m,{condition:i},(0,a.createElement)(Ne,null))),(0,a.createElement)("div",{className:Dt},(0,a.createElement)(Rt,{name:t,options:l,isAdaptive:i}),(0,a.createElement)(m,{condition:s},(0,a.createElement)(we,{onReset:o}))))},Ft=x("typography-select"),It=x("typography-select-label"),Ut=e=>{const{name:t,label:n,options:l,isAdaptive:i=!1}=e,{fieldName:r}=L(t,i),{isChanged:s,onReset:o}=D(r);return(0,a.createElement)("div",{className:Ft},(0,a.createElement)("div",{className:It},(0,a.createElement)("div",null,n),(0,a.createElement)(m,{condition:i},(0,a.createElement)(Ne,null))),(0,a.createElement)(Rt,{name:t,options:l,isAdaptive:i}),(0,a.createElement)(m,{condition:s},(0,a.createElement)(we,{onReset:o})))},Vt=x("typography"),Wt=e=>{const{fontSizeName:t,fontWeightName:n,textTransformName:l,fontStyleName:i,textDecorationName:r,lineHeightName:s,letterSpacingName:o,wordSpacingName:m,fontSizeUnitName:c,lineHeightUnitName:d,letterSpacingUnitName:u,wordSpacingUnitName:g,dependencyMode:p,dependencies:v,isAdaptive:h=!1}=e,{fontWeightOptions:_,textTransformOptions:b,fontStyleOptions:y,textDecorationOptions:E}={fontWeightOptions:[{label:C.__("100 (Thin)","masterstudy-lms-learning-management-system"),value:"100"},{label:C.__("200 (Extra Light)","masterstudy-lms-learning-management-system"),value:"200"},{label:C.__("300 (Light)","masterstudy-lms-learning-management-system"),value:"300"},{label:C.__("400 (Normal)","masterstudy-lms-learning-management-system"),value:"400"},{label:C.__("500 (Medium)","masterstudy-lms-learning-management-system"),value:"500"},{label:C.__("600 (Semi Bold)","masterstudy-lms-learning-management-system"),value:"600"},{label:C.__("700 (Bold)","masterstudy-lms-learning-management-system"),value:"700"},{label:C.__("800 (Extra Bold)","masterstudy-lms-learning-management-system"),value:"800"},{label:C.__("900 (Extra)","masterstudy-lms-learning-management-system"),value:"900"}],textTransformOptions:[{label:C.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:C.__("Uppercase","masterstudy-lms-learning-management-system"),value:"uppercase"},{label:C.__("Lowercase","masterstudy-lms-learning-management-system"),value:"lowercase"},{label:C.__("Capitalize","masterstudy-lms-learning-management-system"),value:"capitalize"},{label:C.__("Normal","masterstudy-lms-learning-management-system"),value:"none"}],fontStyleOptions:[{label:C.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:C.__("Normal","masterstudy-lms-learning-management-system"),value:"none"},{label:C.__("Italic","masterstudy-lms-learning-management-system"),value:"italic"},{label:C.__("Oblique","masterstudy-lms-learning-management-system"),value:"oblique"}],textDecorationOptions:[{label:C.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:C.__("Underline","masterstudy-lms-learning-management-system"),value:"underline"},{label:C.__("Line Through","masterstudy-lms-learning-management-system"),value:"line-through"},{label:C.__("None","masterstudy-lms-learning-management-system"),value:"none"}]};return I(v,p)?(0,a.createElement)("div",{className:Vt},(0,a.createElement)(gt,{name:t,label:C.__("Size","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:c,isAdaptive:h}),(0,a.createElement)(Ut,{name:n,label:C.__("Weight","masterstudy-lms-learning-management-system"),options:_}),(0,a.createElement)(Ut,{name:l,label:C.__("Transform","masterstudy-lms-learning-management-system"),options:b}),(0,a.createElement)(Ut,{name:i,label:C.__("Style","masterstudy-lms-learning-management-system"),options:y}),(0,a.createElement)(Ut,{name:r,label:C.__("Decoration","masterstudy-lms-learning-management-system"),options:E}),(0,a.createElement)(gt,{name:s,label:C.__("Line Height","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:d,isAdaptive:h}),(0,a.createElement)(gt,{name:o,label:C.__("Letter Spacing","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:u,isAdaptive:h}),m&&(0,a.createElement)(gt,{name:m,label:C.__("Word Spacing","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:g,isAdaptive:h})):null},Gt=(x("file-upload"),x("file-upload__wrap"),x("file-upload__image"),x("file-upload__remove"),x("file-upload__replace"),(0,M.createContext)({activeTab:0,setActiveTab:()=>{}})),zt=()=>{const e=(0,M.useContext)(Gt);if(!e)throw new Error("useTabs should be used inside Tabs");return e},jt=({children:e})=>{const[t,n]=(0,M.useState)(0);return(0,a.createElement)(Gt.Provider,{value:{activeTab:t,setActiveTab:n}},(0,a.createElement)("div",{className:`active-tab-${t}`},e))},$t=x("tab-list"),Zt=({children:e})=>(0,a.createElement)("div",{className:$t},M.Children.map(e,((e,t)=>(0,M.cloneElement)(e,{index:t})))),Kt=x("tab"),Xt=x("tab-active"),qt=x("content"),Yt=({index:e,title:t,icon:n})=>{const{activeTab:l,setActiveTab:i}=zt();return(0,a.createElement)("div",{className:s()([Kt],{[Xt]:l===e}),onClick:()=>i(e)},(0,a.createElement)("div",{className:qt},(0,a.createElement)("div",null,n),(0,a.createElement)("div",null,t)))},Jt=({children:e})=>(0,a.createElement)("div",null,M.Children.map(e,((e,t)=>(0,M.cloneElement)(e,{index:t})))),Qt=x("tab-panel"),en=({index:e,children:t})=>{const{activeTab:n}=zt();return n===e?(0,a.createElement)("div",{className:Qt},t):null},tn=({generalTab:e,styleTab:t,advancedTab:n})=>(0,a.createElement)(jt,null,(0,a.createElement)(Zt,null,(0,a.createElement)(Yt,{title:C.__("General","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(o.Dashicon,{icon:"layout"})}),(0,a.createElement)(Yt,{title:C.__("Style","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(o.Dashicon,{icon:"admin-appearance"})}),(0,a.createElement)(Yt,{title:C.__("Advanced","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(o.Dashicon,{icon:"admin-settings"})})),(0,a.createElement)(Jt,null,(0,a.createElement)(en,null,e),(0,a.createElement)(en,null,t),(0,a.createElement)(en,null,n)));window.ReactDOM;"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement;function nn(e){const t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function an(e){return"nodeType"in e}function ln(e){var t,n;return e?nn(e)?e:an(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function rn(e){const{Document:t}=ln(e);return e instanceof t}function sn(e){return!nn(e)&&e instanceof ln(e).HTMLElement}function on(e){return e instanceof ln(e).SVGElement}function mn(e){return e?nn(e)?e.document:an(e)?rn(e)?e:sn(e)||on(e)?e.ownerDocument:document:document:document}function cn(e){return function(t){for(var n=arguments.length,a=new Array(n>1?n-1:0),l=1;l<n;l++)a[l-1]=arguments[l];return a.reduce(((t,n)=>{const a=Object.entries(n);for(const[n,l]of a){const a=t[n];null!=a&&(t[n]=a+e*l)}return t}),{...t})}}const dn=cn(-1);function un(e){if(function(e){if(!e)return!1;const{TouchEvent:t}=ln(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return function(e){return"clientX"in e&&"clientY"in e}(e)?{x:e.clientX,y:e.clientY}:null}var gn;!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(gn||(gn={}));const pn=Object.freeze({x:0,y:0});var vn,hn,bn,yn;!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(vn||(vn={}));class En{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach((e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)}))},this.target=e}add(e,t,n){var a;null==(a=this.target)||a.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function Cn(e,t){const n=Math.abs(e.x),a=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+a**2)>t:"x"in t&&"y"in t?n>t.x&&a>t.y:"x"in t?n>t.x:"y"in t&&a>t.y}function fn(e){e.preventDefault()}function Nn(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(hn||(hn={})),(yn=bn||(bn={})).Space="Space",yn.Down="ArrowDown",yn.Right="ArrowRight",yn.Left="ArrowLeft",yn.Up="ArrowUp",yn.Esc="Escape",yn.Enter="Enter";bn.Space,bn.Enter,bn.Esc,bn.Space,bn.Enter;function Tn(e){return Boolean(e&&"distance"in e)}function wn(e){return Boolean(e&&"delay"in e)}class Sn{constructor(e,t,n){var a;void 0===n&&(n=function(e){const{EventTarget:t}=ln(e);return e instanceof t?e:mn(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;const{event:l}=e,{target:i}=l;this.props=e,this.events=t,this.document=mn(i),this.documentListeners=new En(this.document),this.listeners=new En(n),this.windowListeners=new En(ln(i)),this.initialCoordinates=null!=(a=un(l))?a:pn,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),this.windowListeners.add(hn.Resize,this.handleCancel),this.windowListeners.add(hn.DragStart,fn),this.windowListeners.add(hn.VisibilityChange,this.handleCancel),this.windowListeners.add(hn.ContextMenu,fn),this.documentListeners.add(hn.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(wn(t))return void(this.timeoutId=setTimeout(this.handleStart,t.delay));if(Tn(t))return}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handleStart(){const{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(hn.Click,Nn,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(hn.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;const{activated:n,initialCoordinates:a,props:l}=this,{onMove:i,options:{activationConstraint:r}}=l;if(!a)return;const s=null!=(t=un(e))?t:pn,o=dn(a,s);if(!n&&r){if(Tn(r)){if(null!=r.tolerance&&Cn(o,r.tolerance))return this.handleCancel();if(Cn(o,r.distance))return this.handleStart()}return wn(r)&&Cn(o,r.tolerance)?this.handleCancel():void 0}e.cancelable&&e.preventDefault(),i(s)}handleEnd(){const{onEnd:e}=this.props;this.detach(),e()}handleCancel(){const{onCancel:e}=this.props;this.detach(),e()}handleKeydown(e){e.code===bn.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}const xn={move:{name:"pointermove"},end:{name:"pointerup"}};(class extends Sn{constructor(e){const{event:t}=e,n=mn(t.target);super(e,xn,n)}}).activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;return!(!n.isPrimary||0!==n.button||(null==a||a({event:n}),0))}}];const Mn={move:{name:"mousemove"},end:{name:"mouseup"}};var On;!function(e){e[e.RightClick=2]="RightClick"}(On||(On={})),class extends Sn{constructor(e){super(e,Mn,mn(e.event.target))}}.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;return n.button!==On.RightClick&&(null==a||a({event:n}),!0)}}];const Bn={move:{name:"touchmove"},end:{name:"touchend"}};var kn,An,Rn,Pn,Hn;(class extends Sn{constructor(e){super(e,Bn)}static setup(){return window.addEventListener(Bn.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(Bn.move.name,e)};function e(){}}}).activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;const{touches:l}=n;return!(l.length>1||(null==a||a({event:n}),0))}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(kn||(kn={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(An||(An={})),vn.Backward,vn.Forward,vn.Backward,vn.Forward,function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(Rn||(Rn={})),function(e){e.Optimized="optimized"}(Pn||(Pn={})),Rn.WhileDragging,Pn.Optimized,Map,function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(Hn||(Hn={})),bn.Down,bn.Right,bn.Up,bn.Left,C.__("Lectures","masterstudy-lms-learning-management-system"),C.__("Duration","masterstudy-lms-learning-management-system"),C.__("Views","masterstudy-lms-learning-management-system"),C.__("Level","masterstudy-lms-learning-management-system"),C.__("Members","masterstudy-lms-learning-management-system"),C.__("Empty","masterstudy-lms-learning-management-system"),x("sortable__item"),x("sortable__item__disabled"),x("sortable__item__content"),x("sortable__item__content__drag-item"),x("sortable__item__content__drag-item__disabled"),x("sortable__item__content__title"),x("sortable__item__control"),x("sortable__item__icon"),x("nested-sortable"),x("nested-sortable__item"),x("sortable");const Dn=x("accordion"),Ln=x("accordion__header"),Fn=x("accordion__header-flex"),In=x("accordion__content"),Un=x("accordion__icon"),Vn=x("accordion__title"),Wn=x("accordion__title-disabled"),Gn=x("accordion__indicator"),zn=x("accordion__controls"),jn=x("accordion__controls-disabled"),$n=({title:e,children:t,accordionFields:n,switchName:l,visible:i=!0,isDefaultOpen:r=!1})=>{const{isOpen:d,onToggle:u,disabled:g,onReset:p,hasChanges:v,onClose:h}=((e,t,n)=>{var a;const{isOpen:l,onToggle:i,onClose:r}=A(t),{defaultValues:s,attributes:o,setAttributes:m}=H(),d=((e,t,n)=>{for(const a of n)if(!c(e[a],t[a]))return!0;return!1})(s,o,e);return{isOpen:l,onToggle:i,disabled:!(null===(a=o[n])||void 0===a||a),hasChanges:d,onReset:t=>{t.stopPropagation(),m(e.reduce(((e,t)=>(e[t]=s[t],e)),{}))},onClose:r}})(n,r,l);return((e,t)=>{const{attributes:n}=H(),a=!n[t];(0,M.useEffect)((()=>{a&&e()}),[a,e])})(h,l),i?(0,a.createElement)("div",{className:Dn},(0,a.createElement)("div",{className:Ln},(0,a.createElement)("div",{className:Fn,onClick:g?null:u},(0,a.createElement)("div",{className:s()(Vn,{[Wn]:g,"with-switch":Boolean(l)})},(0,a.createElement)("div",null,e),(0,a.createElement)(m,{condition:v&&!g},(0,a.createElement)("div",{className:Gn}))),(0,a.createElement)("div",{className:s()(zn,{[jn]:g})},(0,a.createElement)(o.Dashicon,{icon:d?"arrow-up-alt2":"arrow-down-alt2",className:Un,size:16}))),(0,a.createElement)(m,{condition:Boolean(l)},(0,a.createElement)(vt,{name:l})),(0,a.createElement)(m,{condition:v&&!g},(0,a.createElement)(we,{onReset:p}))),d&&(0,a.createElement)("div",{className:In},t)):null},Zn=({type:e})=>(0,a.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",width:"8",height:"14",viewBox:"0 0 8 14",fill:"none"},(0,a.createElement)(o.Rect,{width:"40",height:"40"}),(0,a.createElement)(o.Path,{d:e,stroke:"currentColor","stroke-width":"2","stroke-miterlimit":"10","stroke-linecap":"round","stroke-linejoin":"round"}));let Kn=function(e){return e.LEFT="M7 1L1 7L7 13",e.RIGHT="M0.999999 1L7 7L1 13",e}({});const Xn=({position:e,prevSlide:t,nextSlide:n,isVisible:l=!0,disabledNext:i,disabledPrev:r,containerClassName:o,navItemClassName:m,disabledNavItemClassName:c})=>l?(0,a.createElement)("div",{className:s()(o,{"bottom-left":e===v.BOTTOM_lEFT,"bottom-right":e===v.BOTTOM_RIGHT,"bottom-center":e===v.BOTTOM_CENTER,"top-left":e===v.TOP_lEFT,"top-right":e===v.TOP_RIGHT})},(0,a.createElement)("div",{className:s()(m,{[c]:r}),onClick:r?null:t},(0,a.createElement)(Zn,{type:Kn.LEFT})),(0,a.createElement)("div",{className:s()(m,{[c]:i}),onClick:i?null:n},(0,a.createElement)(Zn,{type:Kn.RIGHT}))):null,qn=(x("preset-picker"),x("preset-picker__label"),x("preset-picker__remove"),x("preset-picker__presets-list"),x("preset-picker__presets-list__item"),x("preset-picker__presets-list__item__preset"),x("preset-picker__presets-list__item__preset-active"),({value:e,onChange:t,navigationPosition:n,prevSlide:l,nextSlide:r,disabledNext:o,disabledPrev:m,titleTag:c})=>(0,a.createElement)("div",{className:s()("lms-course-carousel-header",{"top-center":n===v.TOP_CENTER,"space-between":[v.TOP_RIGHT,v.TOP_lEFT].includes(n)})},(0,a.createElement)(i.RichText,{tagName:c,className:s()("lms-course-carousel-title","wp-block-heading",{"top-left":n===v.TOP_RIGHT,"top-right":n===v.TOP_lEFT}),value:e,onChange:t,placeholder:C.__("Courses Carousel","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Xn,{position:n,isVisible:T.includes(n),nextSlide:r,prevSlide:l,disabledNext:o,disabledPrev:m,containerClassName:"lms-course-carousel-navigations",navItemClassName:"lms-course-carousel-navigations_item",disabledNavItemClassName:"lms-course-carousel-navigations_item_disabled"}))),Yn={slidesToShow:4,slidesToShowTablet:2,slidesToShowMobile:1,slidesCount:8,orderBy:"date_high",categories:[],autoplay:!1,loop:!0,navigationPosition:v.BOTTOM_CENTER},Jn=Object.keys(Yn),Qn={layoutMargin:N,layoutMarginTablet:N,layoutMarginMobile:N,layoutMarginUnit:"px",layoutMarginUnitTablet:"px",layoutMarginUnitMobile:"px",layoutPadding:{top:"100",right:"40",bottom:"100",left:"40"},layoutPaddingTablet:{top:"80",right:"60",bottom:"80",left:"60"},layoutPaddingMobile:{top:"80",right:"40",bottom:"80",left:"40"},layoutPaddingUnit:"px",layoutPaddingUnitTablet:"px",layoutPaddingUnitMobile:"px",cardGap:30,cardGapTablet:null,cardGapMobile:null,cardGapUnit:"px",cardGapUnitTablet:"px",cardGapUnitMobile:"px",layoutBackground:"",layoutBorderStyle:"none",layoutBorderStyleTablet:"",layoutBorderStyleMobile:"",layoutBorderColor:"",layoutBorderColorTablet:"",layoutBorderColorMobile:"",layoutBorderWidth:N,layoutBorderWidthTablet:N,layoutBorderWidthMobile:N,layoutBorderWidthUnit:"px",layoutBorderWidthUnitTablet:"px",layoutBorderWidthUnitMobile:"px",layoutBorderRadius:N,layoutBorderRadiusTablet:N,layoutBorderRadiusMobile:N,layoutBorderRadiusUnit:"px",layoutBorderRadiusUnitTablet:"px",layoutBorderRadiusUnitMobile:"px",layoutWidth:"alignfull",layoutZIndex:null,layoutZIndexTablet:null,layoutZIndexMobile:null},ea=Object.keys(Qn),ta={titleTag:"h1",titleFontSize:36,titleFontSizeTablet:null,titleFontSizeMobile:null,titleFontSizeUnit:"px",titleFontSizeUnitTablet:"px",titleFontSizeUnitMobile:"px",titleFontWeight:"700",titleTextTransform:"none",titleFontStyle:"inherit",titleTextDecoration:"inherit",titleLineHeight:null,titleLineHeightTablet:null,titleLineHeightMobile:null,titleLineHeightUnit:"px",titleLineHeightUnitTablet:"px",titleLineHeightUnitMobile:"px",titleLetterSpacing:0,titleLetterSpacingTablet:null,titleLetterSpacingMobile:null,titleLetterSpacingUnit:"px",titleLetterSpacingUnitTablet:"px",titleLetterSpacingUnitMobile:"px",titleWordSpacing:0,titleWordSpacingTablet:null,titleWordSpacingMobile:null,titleWordSpacingUnit:"px",titleWordSpacingUnitTablet:"px",titleWordSpacingUnitMobile:"px",titleMargin:N,titleMarginTablet:N,titleMarginMobile:N,titleMarginUnit:"px",titleMarginUnitTablet:"px",titleMarginUnitMobile:"px"},na=Object.keys(ta),aa={navigationBackground:"#EEF1F7",navigationBackgroundHover:"",navigationColor:"#4D5E6F",navigationColorHover:"",navigationBorderStyle:"none",navigationBorderStyleTablet:"",navigationBorderStyleMobile:"",navigationBorderStyleHover:"",navigationBorderStyleHoverTablet:"",navigationBorderStyleHoverMobile:"",navigationBorderColor:"",navigationBorderColorTablet:"",navigationBorderColorMobile:"",navigationBorderColorHover:"",navigationBorderColorHoverTablet:"",navigationBorderColorHoverMobile:"",navigationBorderWidth:N,navigationBorderWidthTablet:N,navigationBorderWidthMobile:N,navigationBorderWidthHover:N,navigationBorderWidthHoverTablet:N,navigationBorderWidthHoverMobile:N,navigationBorderWidthUnit:"px",navigationBorderWidthUnitTablet:"px",navigationBorderWidthUnitMobile:"px",navigationBorderRadius:{top:"4",right:"4",bottom:"4",left:"4"},navigationBorderRadiusTablet:N,navigationBorderRadiusMobile:N,navigationBorderRadiusUnit:"px",navigationBorderRadiusUnitTablet:"px",navigationBorderRadiusUnitMobile:"px",navigationMargin:N,navigationMarginTablet:N,navigationMarginMobile:N,navigationMarginUnit:"px",navigationMarginUnitTablet:"px",navigationMarginUnitMobile:"px",navigationPadding:{top:"13",right:"16",bottom:"13",left:"16"},navigationPaddingTablet:N,navigationPaddingMobile:N,navigationPaddingUnit:"px",navigationPaddingUnitTablet:"px",navigationPaddingUnitMobile:"px"},la=Object.keys(aa),ia={...aa,...ta,...Qn},ra={...Yn,...ia},sa=new Map([["layoutMargin",{unit:"layoutMarginUnit",isAdaptive:!0}],["layoutPadding",{unit:"layoutPaddingUnit",isAdaptive:!0}],["cardGap",{unit:"cardGapUnit",isAdaptive:!0}],["slidesToShow",{isAdaptive:!0}],["slidesCount",{}],["layoutBackground",{}],["layoutBorderStyle",{isAdaptive:!0}],["layoutBorderColor",{isAdaptive:!0}],["layoutBorderWidth",{isAdaptive:!0,unit:"layoutBorderWidthUnit"}],["layoutBorderRadius",{isAdaptive:!0,unit:"layoutBorderRadiusUnit"}],["layoutZIndex",{isAdaptive:!0}],["titleFontSize",{unit:"titleFontSizeUnit",isAdaptive:!0}],["titleFontWeight",{}],["titleTextTransform",{}],["titleFontStyle",{}],["titleTextDecoration",{}],["titleLineHeight",{unit:"titleLineHeightUnit",isAdaptive:!0}],["titleLetterSpacing",{unit:"titleLetterSpacingUnit",isAdaptive:!0}],["titleWordSpacing",{unit:"titleWordSpacingUnit",isAdaptive:!0}],["titleMargin",{unit:"titleMarginUnit",isAdaptive:!0}],["navigationBackground",{hasHover:!0}],["navigationColor",{hasHover:!0}],["navigationBorderStyle",{isAdaptive:!0,hasHover:!0}],["navigationBorderColor",{isAdaptive:!0,hasHover:!0}],["navigationBorderWidth",{isAdaptive:!0,unit:"navigationBorderWidthUnit",hasHover:!0}],["navigationBorderRadius",{isAdaptive:!0,unit:"navigationBorderRadiusUnit"}],["navigationMargin",{unit:"navigationMarginUnit",isAdaptive:!0}],["navigationPadding",{unit:"navigationPaddingUnit",isAdaptive:!0}]]),oa=({categories:e})=>{const t=[{label:C.__("Top-Left","masterstudy-lms-learning-management-system"),value:v.TOP_lEFT},{label:C.__("Top-Center","masterstudy-lms-learning-management-system"),value:v.TOP_CENTER},{label:C.__("Top-Right","masterstudy-lms-learning-management-system"),value:v.TOP_RIGHT},{label:C.__("Bottom-Left","masterstudy-lms-learning-management-system"),value:v.BOTTOM_lEFT},{label:C.__("Bottom-Center","masterstudy-lms-learning-management-system"),value:v.BOTTOM_CENTER},{label:C.__("Bottom-Right","masterstudy-lms-learning-management-system"),value:v.BOTTOM_RIGHT}],{min:n,max:l}=((e,t=!1)=>{const n=B(),[a,l]=(0,M.useState)(e.default||{min:3,max:6});return(0,M.useEffect)((()=>{if(n===p.DESKTOP){const n=e.desktop||{min:t?2:3,max:6};l(n)}if(n===p.TABLET){const n=e.tablet||{min:t?1:2,max:3};l(n)}if(n===p.MOBILE){const t=e.mobile||{min:1,max:1};l(t)}}),[n,t,e]),a})({default:{min:3,max:6}},!0);return(0,a.createElement)(a.Fragment,null,(0,a.createElement)($n,{title:C.__("Layout","masterstudy-lms-learning-management-system"),accordionFields:Jn},(0,a.createElement)(gt,{name:"slidesToShow",label:C.__("Slides to show","masterstudy-lms-learning-management-system"),min:n,max:l,isAdaptive:!0}),(0,a.createElement)(gt,{name:"slidesCount",label:C.__("Slides in carousel","masterstudy-lms-learning-management-system"),min:1,max:24}),(0,a.createElement)(Rt,{name:"orderBy",label:C.__("Sort By","masterstudy-lms-learning-management-system"),options:S}),(0,a.createElement)(m,{condition:Boolean(e.length)},(0,a.createElement)(Rt,{name:"categories",multiple:!0,label:C.__("Course Category","masterstudy-lms-learning-management-system"),options:e})),(0,a.createElement)(vt,{name:"autoplay",label:C.__("Autoplay","masterstudy-lms-learning-management-system")}),(0,a.createElement)(vt,{name:"loop",label:C.__("Loop","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Rt,{name:"navigationPosition",label:C.__("Nav arrows position","masterstudy-lms-learning-management-system"),options:t})))},ma=()=>{const{widthOptions:e}=(()=>{const e=[{label:C.__("Auto","masterstudy-lms-learning-management-system"),value:"alignauto"},{label:C.__("Full width","masterstudy-lms-learning-management-system"),value:"alignfull"}],t=[{label:C.__("Cover","masterstudy-lms-learning-management-system"),value:"cover"},{label:C.__("Contain","masterstudy-lms-learning-management-system"),value:"contain"},{label:C.__("Inherit","masterstudy-lms-learning-management-system"),value:"inherit"},{label:C.__("Initial","masterstudy-lms-learning-management-system"),value:"initial"},{label:C.__("Revert","masterstudy-lms-learning-management-system"),value:"revert"},{label:C.__("Revert-layer","masterstudy-lms-learning-management-system"),value:"revert-layer"},{label:C.__("Unset","masterstudy-lms-learning-management-system"),value:"unset"}],n=[{label:C.__("Center center","masterstudy-lms-learning-management-system"),value:"center center"},{label:C.__("Center left","masterstudy-lms-learning-management-system"),value:"center left"},{label:C.__("Center right","masterstudy-lms-learning-management-system"),value:"center right"},{label:C.__("Top center","masterstudy-lms-learning-management-system"),value:"top center"},{label:C.__("Top left","masterstudy-lms-learning-management-system"),value:"top left"},{label:C.__("Top right","masterstudy-lms-learning-management-system"),value:"top right"},{label:C.__("Bottom center","masterstudy-lms-learning-management-system"),value:"bottom center"},{label:C.__("Bottom left","masterstudy-lms-learning-management-system"),value:"bottom left"},{label:C.__("Bottom right","masterstudy-lms-learning-management-system"),value:"bottom right"}],a=[{label:C.__("Center","masterstudy-lms-learning-management-system"),value:"center"},{label:C.__("Start","masterstudy-lms-learning-management-system"),value:"flex-start"},{label:C.__("End","masterstudy-lms-learning-management-system"),value:"flex-end"},{label:C.__("Space Between","masterstudy-lms-learning-management-system"),value:"space-between"},{label:C.__("Space Around","masterstudy-lms-learning-management-system"),value:"space-around"},{label:C.__("Space Evenly","masterstudy-lms-learning-management-system"),value:"space-evenly"}];return{filterOptions:S,widthOptions:e,backgroundSizeOptions:t,backgroundPositionOptions:n,alignContentOptions:a}})();return(0,a.createElement)(a.Fragment,null,(0,a.createElement)($n,{title:C.__("Layout","masterstudy-lms-learning-management-system"),accordionFields:ea},(0,a.createElement)(Ke,{name:"layoutMargin",label:C.__("Margin","masterstudy-lms-learning-management-system"),unitName:"layoutMarginUnit",isAdaptive:!0,dependencies:[{name:"layoutWidth",value:"alignauto"}]}),(0,a.createElement)(Ke,{name:"layoutPadding",label:C.__("Padding","masterstudy-lms-learning-management-system"),unitName:"layoutPaddingUnit",isAdaptive:!0}),(0,a.createElement)(bt,{name:"cardGap",label:C.__("Space between cards","masterstudy-lms-learning-management-system"),unitName:"cardGapUnit",isAdaptive:!0}),(0,a.createElement)(Ue,{name:"layoutBackground",label:C.__("Background","masterstudy-lms-learning-management-system"),placeholder:C.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(lt,{label:C.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"layoutBorderStyle",borderColorName:"layoutBorderColor",borderWidthName:"layoutBorderWidth",isAdaptive:!0}),(0,a.createElement)(st,{name:"layoutBorderRadius",label:C.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,a.createElement)(Lt,{name:"layoutWidth",label:C.__("Width","masterstudy-lms-learning-management-system"),options:e}),(0,a.createElement)(gt,{name:"layoutZIndex",label:C.__("Z-Index","masterstudy-lms-learning-management-system"),min:0,max:100,isAdaptive:!0})),(0,a.createElement)($n,{title:C.__("Title","masterstudy-lms-learning-management-system"),accordionFields:na},(0,a.createElement)(Qe,{name:"titleTag",label:C.__("Title tag","masterstudy-lms-learning-management-system"),options:[{label:"H1",value:"h1"},{label:"H2",value:"h2"},{label:"H3",value:"h3"},{label:"H4",value:"h4"},{label:"H5",value:"h5"},{label:"H6",value:"h6"},{label:"P",value:"p"}]}),(0,a.createElement)(Wt,{fontSizeName:"titleFontSize",fontSizeUnitName:"titleFontSizeUnit",fontWeightName:"titleFontWeight",textTransformName:"titleTextTransform",fontStyleName:"titleFontStyle",textDecorationName:"titleTextDecoration",lineHeightName:"titleLineHeight",lineHeightUnitName:"titleLineHeightUnit",letterSpacingName:"titleLetterSpacing",letterSpacingUnitName:"titleLetterSpacingUnit",wordSpacingName:"titleWordSpacing",wordSpacingUnitName:"titleWordSpacingUnit",isAdaptive:!0}),(0,a.createElement)(Ke,{name:"titleMargin",label:C.__("Margin","masterstudy-lms-learning-management-system"),unitName:"titleMarginUnit",isAdaptive:!0})),(0,a.createElement)($n,{title:C.__("Arrows","masterstudy-lms-learning-management-system"),accordionFields:la},(0,a.createElement)(Ue,{name:"navigationBackground",label:C.__("Background","masterstudy-lms-learning-management-system"),placeholder:C.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,a.createElement)(Ue,{name:"navigationColor",label:C.__("Color","masterstudy-lms-learning-management-system"),placeholder:C.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,a.createElement)(lt,{label:C.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"navigationBorderStyle",borderColorName:"navigationBorderColor",borderWidthName:"navigationBorderWidth",isAdaptive:!0,hasHover:!0}),(0,a.createElement)(st,{name:"navigationBorderRadius",label:C.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,a.createElement)(Ke,{name:"navigationMargin",label:C.__("Margin","masterstudy-lms-learning-management-system"),unitName:"navigationMarginUnit",isAdaptive:!0}),(0,a.createElement)(Ke,{name:"navigationPadding",label:C.__("Padding","masterstudy-lms-learning-management-system"),unitName:"navigationMarginUnit",isAdaptive:!0})))},ca=window.wp.apiFetch;var da=n.n(ca);const ua=({attributes:e,setAttributes:t})=>{const{categories:n}=((e=!1)=>{const[t,n]=(0,M.useState)([]),[a,l]=(0,M.useState)({}),[i,r]=(0,M.useState)({}),{setIsFetching:s,setError:o,isFetching:m,error:c}=(()=>{const[e,t]=(0,M.useState)(!0),[n,a]=(0,M.useState)("");return{isFetching:e,setIsFetching:t,error:n,setError:a}})();return(0,M.useEffect)((()=>{s(!0),(async(e=!1)=>{try{let t="?children=true";return e&&(t+="&details=true"),await da()({path:`masterstudy-lms/v2/course-categories${t}`})}catch(e){throw new Error(e)}})(e).then((({categories:e})=>{n((e=>e.map((e=>({label:e.name,value:e.id,image:e.image,icon:e.icon,color:e.color,children:e.children?d(e.children):[]}))))(e)),l(e.reduce(((e,t)=>(e[String(t.id)]=t.name,e)),{})),r(e.reduce(((e,t)=>(e[String(t.id)]={label:t.name,value:t.id,image:t.image,icon:t.icon,color:t.color,courses:t.courses,children:t.children},e)),{}))})).catch((e=>{o(e.message)})).finally((()=>{s(!1)}))}),[]),{categories:t,categoriesMap:a,categoriesMapFull:i,isFetching:m,error:c}})(),{onResetByFieldName:l,changedFieldsByName:r}=((e,t,n,a=[])=>{const l=(e=>{const t={};return Object.entries(e).forEach((([e,n])=>{e.includes("UAG")||(t[e]=n)})),t})(t),i=!c(e,l),r=((e,t,n)=>{const a=new Map;return n.forEach((n=>{a.has(n)||a.set(n,(()=>t({[n]:e[n]})))})),a})(e,n,a),s=((e,t,n)=>{const a=new Map;return n.forEach((n=>{a.has(n)?a.set(n,!1):a.set(n,!c(e[n],t[n]))})),a})(e,l,a);return{hasChanges:i,onResetByFieldName:r,changedFieldsByName:s}})(ra,e,t,Object.keys(ra));return(0,a.createElement)(i.InspectorControls,null,(0,a.createElement)(P,{attributes:e,setAttributes:t,defaultValues:ra,onResetByFieldName:l,changedFieldsByName:r},(0,a.createElement)(tn,{generalTab:(0,a.createElement)(oa,{categories:n}),styleTab:(0,a.createElement)(ma,null),advancedTab:(0,a.createElement)(a.Fragment,null)})))},ga=[["core/group",{className:"lms-course-carousel-presets"},[["masterstudy/courses-preset",{cntrMargin:{top:"50",right:"0",bottom:"50",left:"0"}}]]]],pa=JSON.parse('{"UU":"masterstudy/course-carousel"}');(0,l.registerBlockType)(pa.UU,{icon:{src:(0,a.createElement)("svg",{width:"513",height:"513",viewBox:"0 0 513 513",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,a.createElement)("g",{clipPath:"url(#clip0_3618_45436)"},(0,a.createElement)("rect",{opacity:"0.3",x:"95.0771",y:"99.4442",width:"322.861",height:"288.284",rx:"20",fill:"#227AFF"}),(0,a.createElement)("path",{d:"M110.085 448.188C98.1971 448.188 86.7962 443.465 78.3902 435.059C69.9842 426.653 65.2617 415.252 65.2617 403.364V110.519C65.2617 98.631 69.9842 87.23 78.3902 78.824C86.7962 70.418 98.1971 65.6956 110.085 65.6956H402.931C414.818 65.6956 426.219 70.418 434.625 78.824C443.031 87.23 447.754 98.631 447.754 110.519V403.364C447.754 415.252 443.031 426.653 434.625 435.059C426.219 443.465 414.818 448.188 402.931 448.188H110.085ZM80.9499 110.519V403.364C80.9588 411.089 84.0312 418.494 89.4932 423.956C94.9552 429.418 102.361 432.491 110.085 432.5H402.931C410.655 432.491 418.06 429.418 423.522 423.956C428.984 418.494 432.057 411.089 432.066 403.364V110.519C432.057 102.794 428.984 95.389 423.522 89.927C418.06 84.4651 410.655 81.3926 402.931 81.3837H110.085C102.361 81.3926 94.9552 84.4651 89.4932 89.927C84.0312 95.389 80.9588 102.794 80.9499 110.519ZM145.197 351.071C139.057 351.064 133.17 348.622 128.829 344.28C124.487 339.938 122.045 334.052 122.038 327.912V309.236C122.045 303.096 124.487 297.209 128.829 292.867C133.17 288.526 139.057 286.084 145.197 286.077H256.508C262.648 286.084 268.534 288.526 272.876 292.867C277.217 297.209 279.66 303.096 279.667 309.236V327.912C279.66 334.052 277.217 339.938 272.876 344.28C268.534 348.622 262.648 351.064 256.508 351.071H145.197ZM138.473 309.236V327.912C138.475 329.694 139.184 331.403 140.445 332.664C141.705 333.924 143.414 334.633 145.197 334.635H256.508C258.29 334.633 259.999 333.924 261.26 332.664C262.52 331.403 263.229 329.694 263.231 327.912V309.236C263.229 307.453 262.52 305.744 261.26 304.483C259.999 303.223 258.29 302.514 256.508 302.512H145.197C143.414 302.514 141.705 303.223 140.445 304.483C139.184 305.744 138.475 307.453 138.473 309.236ZM130.256 220.336C128.076 220.336 125.986 219.47 124.445 217.929C122.904 216.388 122.038 214.298 122.038 212.118C122.038 209.939 122.904 207.849 124.445 206.308C125.986 204.767 128.076 203.901 130.256 203.901H272.943C275.122 203.901 277.213 204.767 278.754 206.308C280.295 207.849 281.161 209.939 281.161 212.118C281.161 214.298 280.295 216.388 278.754 217.929C277.213 219.47 275.122 220.336 272.943 220.336H130.256ZM130.256 173.271C128.076 173.271 125.986 172.406 124.445 170.865C122.904 169.323 122.038 167.233 122.038 165.054C122.038 162.874 122.904 160.784 124.445 159.243C125.986 157.702 128.076 156.836 130.256 156.836H334.949C337.128 156.836 339.218 157.702 340.759 159.243C342.3 160.784 343.166 162.874 343.166 165.054C343.166 167.233 342.3 169.323 340.759 170.865C339.218 172.406 337.128 173.271 334.949 173.271H130.256Z",fill:"black"}),(0,a.createElement)("path",{d:"M200.634 491.724H162.383C157.018 491.724 152.668 496.074 152.668 501.439C152.668 506.804 157.018 511.153 162.383 511.153H200.634C205.999 511.153 210.349 506.804 210.349 501.439C210.349 496.074 205.999 491.724 200.634 491.724Z",fill:"black"}),(0,a.createElement)("path",{d:"M275.923 491.724H237.672C232.307 491.724 227.957 496.074 227.957 501.439C227.957 506.804 232.307 511.153 237.672 511.153H275.923C281.288 511.153 285.638 506.804 285.638 501.439C285.638 496.074 281.288 491.724 275.923 491.724Z",fill:"black"}),(0,a.createElement)("path",{d:"M351.211 491.724H312.96C307.595 491.724 303.245 496.074 303.245 501.439C303.245 506.804 307.595 511.153 312.96 511.153H351.211C356.577 511.153 360.926 506.804 360.926 501.439C360.926 496.074 356.577 491.724 351.211 491.724Z",fill:"black"}),(0,a.createElement)("path",{d:"M2.37457 259.903C2.42557 259.945 2.49257 259.962 2.54357 260.003L35.0846 289.458C36.0059 290.254 37.1829 290.692 38.4006 290.692C39.6182 290.692 40.7952 290.254 41.7166 289.458C41.7308 289.443 41.7423 289.426 41.7506 289.407C42.1934 289.042 42.551 288.584 42.7987 288.066C43.0464 287.548 43.1781 286.982 43.1846 286.407V227.491C43.1755 226.905 43.0378 226.329 42.7812 225.802C42.5246 225.275 42.1553 224.811 41.6996 224.442L41.7166 224.425C40.7953 223.629 39.6183 223.191 38.4006 223.191C37.1829 223.191 36.0058 223.629 35.0846 224.425L2.37457 253.947C1.94372 254.315 1.59774 254.772 1.3605 255.286C1.12325 255.8 1.00039 256.36 1.00039 256.926C1.00039 257.492 1.12325 258.052 1.3605 258.566C1.59774 259.08 1.94372 259.536 2.37457 259.903Z",fill:"black"}),(0,a.createElement)("path",{d:"M511.626 259.903C511.575 259.945 511.508 259.962 511.457 260.003L478.915 289.458C477.993 290.254 476.816 290.692 475.599 290.692C474.381 290.692 473.204 290.254 472.283 289.458C472.269 289.443 472.257 289.426 472.249 289.407C471.806 289.042 471.448 288.584 471.201 288.066C470.953 287.548 470.821 286.982 470.815 286.407V227.491C470.824 226.905 470.962 226.329 471.218 225.802C471.475 225.275 471.844 224.811 472.3 224.442L472.283 224.425C473.204 223.629 474.381 223.191 475.599 223.191C476.817 223.191 477.994 223.629 478.915 224.425L511.626 253.947C512.057 254.315 512.403 254.772 512.64 255.286C512.877 255.8 513 256.36 513 256.926C513 257.492 512.877 258.052 512.64 258.566C512.403 259.08 512.057 259.536 511.626 259.903Z",fill:"black"})),(0,a.createElement)("defs",null,(0,a.createElement)("clipPath",{id:"clip0_3618_45436"},(0,a.createElement)("rect",{width:"512",height:"512",fill:"white",transform:"translate(0.507812 0.94165)"}))))},edit:({attributes:e,setAttributes:t})=>{const{cardGap:n,cardGapTablet:l,cardGapMobile:r,layoutWidth:o,navigationPosition:m,slidesCount:c,slidesToShow:d,slidesToShowTablet:u,slidesToShowMobile:g,title:p}=e,v=k(d,u,g),h=k(n,l,r),_=(0,i.useBlockProps)({className:s()("lms-course-carousel-container",o,`courses-visible--${v}`),style:E("carousel",e,sa)}),b=(0,i.useInnerBlocksProps)({},{template:ga,templateLock:"all"}),{ref:y,isDisabled:C,nextSlide:f,prevSlide:N}=((e,t,n,a)=>{const[l,i]=(0,M.useState)(0),r=(0,M.useRef)(null),s=e=>{const a=r.current.querySelector(".lms-course-classic__list"),s=Array.from(a.children),o=s[0].offsetWidth+n,m="next"===e?l+1:l-1,c=-m*o;a.style.transform=`translateX(${c}px)`,s.forEach(((e,n)=>{e.classList.remove("slide-visible"),n>=m&&n<m+t&&e.classList.add("slide-visible")})),i(m)},o=(0,M.useMemo)((()=>({isDisabledPrev:0===l,isDisabledNext:l===e-t})),[e,l,t]);return{ref:r,isDisabled:o,nextSlide:()=>{s("next")},prevSlide:()=>{s("prev")}}})(c,v,h);return(0,a.createElement)(a.Fragment,null,(0,a.createElement)(ua,{attributes:e,setAttributes:t}),(0,a.createElement)("div",{..._},(0,a.createElement)(qn,{titleTag:e.titleTag,value:p,onChange:e=>t({title:e}),navigationPosition:m,nextSlide:f,prevSlide:N,disabledNext:C.isDisabledNext,disabledPrev:C.isDisabledPrev}),(0,a.createElement)("div",{...b,ref:y}),(0,a.createElement)(Xn,{position:m,isVisible:w.includes(m),nextSlide:f,prevSlide:N,disabledNext:C.isDisabledNext,disabledPrev:C.isDisabledPrev,containerClassName:"lms-course-carousel-navigations",navItemClassName:"lms-course-carousel-navigations_item",disabledNavItemClassName:"lms-course-carousel-navigations_item_disabled"})))},save:({attributes:e})=>{const{autoplay:t,cardGap:n,cardGapTablet:l,cardGapMobile:r,layoutWidth:o,loop:c,navigationPosition:d,slidesToShow:u,slidesToShowTablet:g,slidesToShowMobile:p,title:h,titleTag:_}=e,b=null!=n?n:0,y=l||b,C=r||y,f=i.useBlockProps.save({className:`lms-course-carousel-container ${o}`,style:E("carousel",e,sa)}),N=i.useInnerBlocksProps.save({});return(0,a.createElement)("div",{...f,"data-gap":b,"data-gap-tablet":y,"data-gap-mobile":C,"data-slides":u,"data-slides-tablet":g,"data-slides-mobile":p,"data-loop":c,"data-auto-play":t},(0,a.createElement)("div",{className:s()("lms-course-carousel-header",{"top-center":d===v.TOP_CENTER,"space-between":[v.TOP_RIGHT,v.TOP_lEFT].includes(d)})},(0,a.createElement)(i.RichText.Content,{className:s()("lms-course-carousel-title","wp-block-heading",{"top-left":d===v.TOP_RIGHT,"top-right":d===v.TOP_lEFT}),tagName:_,value:h}),(0,a.createElement)(m,{condition:T.includes(d)},(0,a.createElement)("div",{className:s()("lms-course-carousel-navigations",{"bottom-left":d===v.BOTTOM_lEFT,"bottom-right":d===v.BOTTOM_RIGHT,"bottom-center":d===v.BOTTOM_CENTER,"top-left":d===v.TOP_lEFT,"top-right":d===v.TOP_RIGHT})},(0,a.createElement)("div",{className:"lms-course-carousel-navigations_item course-swiper-button-prev"},(0,a.createElement)(Zn,{type:Kn.LEFT})),(0,a.createElement)("div",{className:"lms-course-carousel-navigations_item course-swiper-button-next"},(0,a.createElement)(Zn,{type:Kn.RIGHT}))))),(0,a.createElement)("div",{...N}),(0,a.createElement)(m,{condition:w.includes(d)},(0,a.createElement)("div",{className:s()("lms-course-carousel-navigations",{"bottom-left":d===v.BOTTOM_lEFT,"bottom-right":d===v.BOTTOM_RIGHT,"bottom-center":d===v.BOTTOM_CENTER,"top-left":d===v.TOP_lEFT,"top-right":d===v.TOP_RIGHT})},(0,a.createElement)("div",{className:"lms-course-carousel-navigations_item course-swiper-button-prev"},(0,a.createElement)(Zn,{type:Kn.LEFT})),(0,a.createElement)("div",{className:"lms-course-carousel-navigations_item course-swiper-button-next"},(0,a.createElement)(Zn,{type:Kn.RIGHT})))))}})},6942:(e,t)=>{var n;!function(){"use strict";var a={}.hasOwnProperty;function l(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=r(e,i(n)))}return e}function i(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return l.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)a.call(e,n)&&e[n]&&(t=r(t,n));return t}function r(e,t){return t?e?e+" "+t:e+t:e}e.exports?(l.default=l,e.exports=l):void 0===(n=function(){return l}.apply(t,[]))||(e.exports=n)}()}},n={};function a(e){var l=n[e];if(void 0!==l)return l.exports;var i=n[e]={exports:{}};return t[e](i,i.exports,a),i.exports}a.m=t,e=[],a.O=(t,n,l,i)=>{if(!n){var r=1/0;for(c=0;c<e.length;c++){for(var[n,l,i]=e[c],s=!0,o=0;o<n.length;o++)(!1&i||r>=i)&&Object.keys(a.O).every((e=>a.O[e](n[o])))?n.splice(o--,1):(s=!1,i<r&&(r=i));if(s){e.splice(c--,1);var m=l();void 0!==m&&(t=m)}}return t}i=i||0;for(var c=e.length;c>0&&e[c-1][2]>i;c--)e[c]=e[c-1];e[c]=[n,l,i]},a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={8246:0,9146:0};a.O.j=t=>0===e[t];var t=(t,n)=>{var l,i,[r,s,o]=n,m=0;if(r.some((t=>0!==e[t]))){for(l in s)a.o(s,l)&&(a.m[l]=s[l]);if(o)var c=o(a)}for(t&&t(n);m<r.length;m++)i=r[m],a.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return a.O(c)},n=globalThis.webpackChunkmasterstudy_lms_learning_management_system=globalThis.webpackChunkmasterstudy_lms_learning_management_system||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})();var l=a.O(void 0,[9146],(()=>a(2760)));l=a.O(l)})();