# MasterStudy Course Bundler: Technical Implementation Report

## Project Overview

This document details the technical implementation of the MasterStudy Course Bundler plugin, focusing on the engineering challenges, code architecture, and integration points with the MasterStudy LMS system.

## Technical Requirements

The MasterStudy LMS plugin lacked native functionality for bundling courses. I needed to implement a system that could:

1. Create custom post types for bundles with metadata storage
2. Establish many-to-many relationships between bundles and courses
3. Integrate with MasterStudy LMS's cart and checkout systems
4. Hook into the enrollment system to grant access to multiple courses
5. Track bundle purchases and enrollments

## Architecture Decision

After analyzing the MasterStudy LMS codebase, I determined that a standalone plugin would be the optimal approach rather than modifying core files. This decision was based on:

1. Code inspection revealed that MasterStudy LMS uses a modular hook-based architecture
2. The plugin exposes sufficient hooks to extend functionality without core modifications
3. Maintaining a separate codebase would ensure compatibility with future updates

## Technical Implementation

### Phase 1: Core System Analysis

I began by reverse-engineering the MasterStudy LMS plugin to identify critical integration points:

```php
// Key classes and functions identified
STM_LMS_Course::add_user_course() // Primary enrollment function
stm_lms_add_user_course() // Lower-level DB insertion function
STM_LMS_Cart::masterstudy_add_to_cart() // Cart handling
stm_lms_order_accepted // Action hook for completed orders
```

I discovered that MasterStudy LMS uses:
- Custom database tables for user_courses with a specific schema
- WordPress post meta for course pricing information
- Custom order processing system with hooks
- AJAX-based cart functionality

### Phase 2: Database Architecture

I implemented a relational database design with three custom tables:

```php
// Bundle main table
$sql = "CREATE TABLE {$this->table_bundles} (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    title varchar(255) NOT NULL,
    description text,
    price decimal(10,2) NOT NULL DEFAULT '0.00',
    sale_price decimal(10,2) DEFAULT NULL,
    featured tinyint(1) NOT NULL DEFAULT '0',
    created_at datetime NOT NULL,
    PRIMARY KEY (id)
)";

// Bundle-course relationship table (many-to-many)
$sql = "CREATE TABLE {$this->table_bundle_courses} (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    bundle_id bigint(20) NOT NULL,
    course_id bigint(20) NOT NULL,
    order_index int(11) NOT NULL DEFAULT '0',
    PRIMARY KEY (id),
    KEY bundle_id (bundle_id),
    KEY course_id (course_id)
)";

// Enrollment tracking table
$sql = "CREATE TABLE {$this->table_bundle_enrollments} (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    user_id bigint(20) NOT NULL,
    bundle_id bigint(20) NOT NULL,
    course_id bigint(20) NOT NULL,
    order_id bigint(20) NOT NULL,
    enrolled_date datetime NOT NULL,
    PRIMARY KEY (id),
    KEY user_id (user_id),
    KEY bundle_id (bundle_id),
    KEY course_id (course_id)
)";
```

This schema allows for:
- Efficient querying of bundle-course relationships
- Tracking which users purchased which bundles
- Recording when users were enrolled in specific courses through bundles

### Phase 3: Custom Post Type Implementation

I implemented a custom post type for bundles with meta fields for pricing:

```php
register_post_type('mscb_bundle', array(
    'labels' => $labels,
    'public' => true,
    'publicly_queryable' => true,
    'show_ui' => true,
    'show_in_menu' => true,
    'query_var' => true,
    'rewrite' => array('slug' => 'course-bundle'),
    'capability_type' => 'post',
    'has_archive' => true,
    'hierarchical' => false,
    'menu_position' => null,
    'supports' => array('title', 'editor', 'thumbnail'),
    'menu_icon' => 'dashicons-welcome-learn-more'
));
```

I used WordPress's meta box API to create custom fields for bundle management:

```php
add_meta_box(
    'mscb_bundle_courses',
    __('Bundle Courses', 'masterstudy-course-bundler'),
    array($this, 'render_courses_meta_box'),
    'mscb_bundle',
    'normal',
    'high'
);
```

### Phase 4: Frontend Template System

I developed a template system with two main components:

1. `bundles-grid.php` - Grid display of bundles with pricing and course counts
2. `single-bundle.php` - Detailed bundle view with course listings and purchase button

I implemented these with proper template hierarchy support:

```php
public function bundle_single_template($template) {
    global $post;
    
    if ($post->post_type === 'mscb_bundle') {
        $template_path = MSCB_PLUGIN_DIR . 'templates/single-bundle.php';
        if (file_exists($template_path)) {
            return $template_path;
        }
    }
    
    return $template;
}
```

I created shortcodes for flexible embedding:

```php
public function bundles_shortcode($atts) {
    $atts = shortcode_atts(array(
        'limit' => 10,
        'columns' => 3,
        'featured' => false,
    ), $atts);
    
    ob_start();
    $this->render_bundles_grid($atts);
    return ob_get_clean();
}
```

### Phase 5: MasterStudy LMS Integration

The most technically challenging aspect was integrating with MasterStudy LMS's purchase and enrollment systems. I implemented:

1. **Cart Integration**
   I analyzed the MasterStudy LMS cart system and found it requires specific post meta:
   ```php
   // Make bundle purchasable in MasterStudy LMS
   update_post_meta($bundle_id, 'single_sale', '1');
   update_post_meta($bundle_id, 'price', $price);
   update_post_meta($bundle_id, 'sale_price', $sale_price);
   ```

2. **AJAX Handler for Cart**
   I created a custom AJAX handler to add bundles to the cart:
   ```php
   add_action('wp_ajax_mscb_add_bundle_to_cart', array($this, 'add_bundle_to_cart'));
   add_action('wp_ajax_nopriv_mscb_add_bundle_to_cart', array($this, 'add_bundle_to_cart'));
   ```

3. **Order Processing**
   I hooked into MasterStudy LMS's order completion system:
   ```php
   add_action('stm_lms_order_accepted', array($this, 'process_bundle_purchase'), 10, 2);
   ```

4. **Enrollment System**
   After extensive code analysis, I discovered that `STM_LMS_Course::add_user_course()` is the correct method for enrollment, not the simpler `stm_lms_add_user_course()`:
   ```php
   STM_LMS_Course::add_user_course(
       $course_id,
       $user_id,
       0, // current_lesson_id
       0, // progress
       false, // is_translate
       '', // enterprise
       $bundle_id, // bundle_id - critical for tracking
       '', // for_points
       '' // instructor_id
   );
   ```

5. **Order Display Modification**
   I implemented filters to modify how bundles appear in orders:
   ```php
   add_filter('stm_lms_order_items', array($this, 'modify_order_items'), 10, 1);
   ```

### Phase 6: Testing and Debugging

I implemented a systematic testing approach:

1. Traced the full purchase flow using browser developer tools to monitor AJAX requests
2. Added error logging to track enrollment process:
   ```php
   error_log("Enrolled user {$user_id} in course {$course_id} from bundle {$bundle_id}");
   ```
3. Discovered a critical issue: using the wrong enrollment function
4. Fixed the enrollment by implementing the correct class method with all required parameters

## Technical Challenges Solved

1. **Cart Integration Challenge**
   The MasterStudy LMS cart system expects specific meta fields. I solved this by analyzing the cart.php class and implementing the required meta structure.

2. **Enrollment Function Discovery**
   Initial implementation used the basic `stm_lms_add_user_course()` function, which only inserts a database record. Through code analysis, I discovered that `STM_LMS_Course::add_user_course()` is required for proper enrollment as it handles additional metadata and triggers necessary hooks.

3. **Order Display Integration**
   The checkout page showed "N/A" for bundle purchases. I implemented a filter to modify order items and display the proper bundle name and image.

## Code Architecture

I designed the plugin with a modular architecture:

```
course-bundler/
├── assets/
│   ├── css/
│   │   └── mscb-frontend.css
│   └── js/
│       └── mscb-frontend.js
├── includes/
│   ├── class-mscb-admin.php      # Admin interface handling
│   ├── class-mscb-bundle.php     # Core bundle functionality
│   └── class-mscb-frontend.php   # Frontend display and purchase
├── templates/
│   ├── bundles-grid.php          # Grid display template
│   └── single-bundle.php         # Single bundle template
└── course-bundler.php            # Main plugin file
```

Each class has a specific responsibility:
- `MSCB_Bundle`: Database operations and bundle data management
- `MSCB_Admin`: Admin interface and meta box handling
- `MSCB_Frontend`: Frontend display, shortcodes, and purchase integration

## Technical Insights

1. **MasterStudy LMS Architecture**
   The plugin uses a combination of WordPress custom post types, custom database tables, and AJAX handlers. Understanding this architecture was crucial for proper integration.

2. **Enrollment System Complexity**
   The enrollment system is more complex than it appears, with multiple layers:
   - `stm_lms_add_user_course()`: Basic DB insertion
   - `STM_LMS_Course::add_user_course()`: Complete enrollment with metadata

3. **Hook-Based Integration**
   MasterStudy LMS uses WordPress hooks extensively, making it possible to integrate without modifying core files.

## Conclusion

The Course Bundler plugin demonstrates effective integration with a complex third-party system. By thoroughly analyzing the MasterStudy LMS codebase, I was able to identify the correct integration points and implement a solution that works seamlessly with the existing system.

The implementation uses proper WordPress coding standards, follows object-oriented principles, and maintains separation of concerns. The modular architecture ensures maintainability and compatibility with future MasterStudy LMS updates.
