{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "masterstudy/testimonials", "version": "0.1.0", "title": "Master<PERSON><PERSON>dy Testimonial", "category": "masterstudy-lms-blocks", "icon": "testimonial", "description": "Build customer trust by adding testimonials", "example": {}, "supports": {"html": false, "align": ["wide", "full"], "__experimentalBorder": {"width": true, "radius": true, "color": true, "style": true}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontFamily": true, "__experimentalFontWeight": true, "__experimentalFontStyle": true, "__experimentalTextTransform": true, "__experimentalLetterSpacing": true, "__experimentalDefaultControls": {"fontSize": true}}, "spacing": {"padding": true, "margin": true}}, "attributes": {"slides": {"type": "array", "default": [], "items": {"type": "object", "properties": {"id": {"type": "string"}, "content": {"type": "string"}, "rating": {"type": "number"}, "reviewer": {"type": "string", "default": ""}, "imgUrl": {"type": "string"}}}}, "title": {"type": "string", "default": ""}, "titleColor": {"type": "string", "default": ""}, "textColor": {"type": "string", "default": ""}, "ratingColor": {"type": "string", "default": ""}, "reviewerColor": {"type": "string", "default": ""}, "bgColor": {"type": "string", "default": ""}, "iconColor": {"type": "string", "default": ""}, "iconBgColor": {"type": "string", "default": ""}, "avatarBorderColor": {"type": "string", "default": ""}, "activeAvatarBgColor": {"type": "string", "default": ""}, "avatarBgColor": {"type": "string", "default": ""}, "avatarIconColor": {"type": "string", "default": ""}, "clientId": {"type": "string", "default": ""}}, "keywords": ["lms", "testimonials", "masterstudy"], "textdomain": "masterstudy-lms-learning-management-system", "editorScript": "file:./index.js", "editorStyle": "file:./index.css", "style": "file:./style-index.css", "viewScript": "file:./view.js"}