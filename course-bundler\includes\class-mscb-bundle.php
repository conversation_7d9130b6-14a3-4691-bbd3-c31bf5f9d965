<?php

if (!defined('ABSPATH')) {
    exit;
}

/*
 * MSCB_Bundle handles bundle data operations and post type registration.
 */
class MSCB_Bundle {
    public $wpdb;
    public $table_bundles;
    public $table_bundle_courses;
    public $table_bundle_enrollments;

    // Initialize table names and register the custom post type
    public function __construct() {
        global $wpdb;
        $this->wpdb = $wpdb;
        $this->table_bundles = $wpdb->prefix . 'mscb_bundles';
        $this->table_bundle_courses = $wpdb->prefix . 'mscb_bundle_courses';
        $this->table_bundle_enrollments = $wpdb->prefix . 'mscb_bundle_enrollments';
        add_action('init', array($this, 'register_bundle_post_type'));
    }

    /* Register the custom post type for course bundles */
    public function register_bundle_post_type() {
        $labels = array(
            'name'               => _x('Course Bundles', 'post type general name', 'masterstudy-course-bundler'),
            'singular_name'      => _x('Course Bundle', 'post type singular name', 'masterstudy-course-bundler'),
            'menu_name'          => _x('Course Bundles', 'admin menu', 'masterstudy-course-bundler'),
            'name_admin_bar'     => _x('Course Bundle', 'add new on admin bar', 'masterstudy-course-bundler'),
            'add_new'            => _x('Add New', 'bundle', 'masterstudy-course-bundler'),
            'add_new_item'       => __('Add New Bundle', 'masterstudy-course-bundler'),
            'new_item'           => __('New Bundle', 'masterstudy-course-bundler'),
            'edit_item'          => __('Edit Bundle', 'masterstudy-course-bundler'),
            'view_item'          => __('View Bundle', 'masterstudy-course-bundler'),
            'all_items'          => __('All Bundles', 'masterstudy-course-bundler'),
            'search_items'       => __('Search Bundles', 'masterstudy-course-bundler'),
            'not_found'          => __('No bundles found.', 'masterstudy-course-bundler'),
            'not_found_in_trash' => __('No bundles found in Trash.', 'masterstudy-course-bundler')
        );
        $args = array(
            'labels'             => $labels,
            'public'             => true,
            'publicly_queryable' => true,
            'show_ui'            => true,
            'show_in_menu'       => true,
            'query_var'          => true,
            'rewrite'            => array('slug' => 'course-bundle'),
            'capability_type'    => 'post',
            'has_archive'        => true,
            'hierarchical'       => false,
            'menu_position'      => null,
            'supports'           => array('title', 'editor', 'thumbnail'),
            'menu_icon'          => 'dashicons-welcome-learn-more'
        );
        register_post_type('mscb_bundle', $args);
    }

    /* Get a bundle row from the custom table by bundle ID */
    public function get_bundle($bundle_id) {
        return $this->wpdb->get_row(
            $this->wpdb->prepare(
                "SELECT * FROM {$this->table_bundles} WHERE id = %d",
                $bundle_id
            ),
            ARRAY_A
        );
    }

    /* Get all courses for a bundle from the relationship table */
    public function get_bundle_courses($bundle_id) {
        return $this->wpdb->get_results(
            $this->wpdb->prepare(
                "SELECT bc.*, p.post_title as course_title 
                FROM {$this->table_bundle_courses} bc 
                LEFT JOIN {$this->wpdb->posts} p ON bc.course_id = p.ID 
                WHERE bc.bundle_id = %d 
                ORDER BY bc.order_index ASC",
                $bundle_id
            ),
            ARRAY_A
        );
    }

    /* Add a course to a bundle in the relationship table */
    public function add_course_to_bundle($bundle_id, $course_id, $order_index = 0) {
        return $this->wpdb->insert(
            $this->table_bundle_courses,
            array(
                'bundle_id' => $bundle_id, //* Bundle post ID
                'course_id' => $course_id, //* Course post ID
                'order_index' => $order_index
            ),
            array('%d', '%d', '%d')
        );
    }

    /* Remove a course from a bundle in the relationship table */
    public function remove_course_from_bundle($bundle_id, $course_id) {
        return $this->wpdb->delete(
            $this->table_bundle_courses,
            array(
                'bundle_id' => $bundle_id,
                'course_id' => $course_id
            ),
            array('%d', '%d')
        );
    }

    /* Get the price for a bundle (sale price if set, otherwise regular price) */
    public function get_bundle_price($bundle_id) {
        $bundle = $this->get_bundle($bundle_id);
        if (!$bundle) {
            return 0;
        }
        return $bundle['sale_price'] ? $bundle['sale_price'] : $bundle['price'];
    }

    // Public getter for the bundle-courses table name
    public function get_table_bundle_courses() {
        return $this->table_bundle_courses;
    }
    
    /**
     * Get all published bundles
     * 
     * @param int $limit Number of bundles to retrieve
     * @param bool $featured Whether to get only featured bundles
     * @return array Array of bundle post objects
     */
    public static function get_all_bundles($limit = -1, $featured = false) {
        $args = array(
            'post_type' => 'mscb_bundle',
            'posts_per_page' => $limit,
            'post_status' => 'publish',
        );
        
        if ($featured) {
            $args['meta_query'] = array(
                array(
                    'key' => '_mscb_featured',
                    'value' => '1',
                    'compare' => '=',
                )
            );
        }
        
        return get_posts($args);
    }
    
    /**
     * Get the regular price for a bundle
     * 
     * @param int $bundle_id Bundle ID
     * @return float Regular price
     */
    public static function get_bundle_regular_price($bundle_id) {
        return get_post_meta($bundle_id, '_mscb_price', true);
    }
    
    /**
     * Get the sale price for a bundle
     * 
     * @param int $bundle_id Bundle ID
     * @return float|null Sale price or null if not on sale
     */
    public static function get_bundle_sale_price($bundle_id) {
        return get_post_meta($bundle_id, '_mscb_sale_price', true);
    }
    
    /**
     * Check if a user is enrolled in a bundle
     * 
     * @param int $bundle_id Bundle ID
     * @param int $user_id User ID
     * @return bool Whether the user is enrolled
     */
    public function is_user_enrolled($bundle_id, $user_id) {
        $enrollment = $this->wpdb->get_row(
            $this->wpdb->prepare(
                "SELECT * FROM {$this->table_bundle_enrollments} WHERE bundle_id = %d AND user_id = %d AND status = 'active'",
                $bundle_id,
                $user_id
            )
        );
        
        return !empty($enrollment);
    }
    
    /**
     * Enroll a user in a bundle
     * 
     * @param int $bundle_id Bundle ID
     * @param int $user_id User ID
     * @return bool|int False on failure, enrollment ID on success
     */
    public function enroll_user($bundle_id, $user_id) {
        // Check if already enrolled
        if ($this->is_user_enrolled($bundle_id, $user_id)) {
            return false;
        }
        
        // Enroll in bundle
        $result = $this->wpdb->insert(
            $this->table_bundle_enrollments,
            array(
                'bundle_id' => $bundle_id,
                'user_id' => $user_id,
                'status' => 'active',
                'enrolled_at' => current_time('mysql')
            ),
            array('%d', '%d', '%s', '%s')
        );
        
        if (!$result) {
            return false;
        }
        
        return $this->wpdb->insert_id;
    }
}