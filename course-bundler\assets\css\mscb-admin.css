/* Admin Table Layout */
.mscb-admin-table-scroll {
    overflow-x: auto;
    margin: 20px 0;
    background: #fff;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    border-radius: 3px;
}

.wp-list-table.widefat.fixed {
    border: none;
    margin: 0;
}

/* Thumbnail Column */
.column-thumbnail {
    width: 52px;
    text-align: center;
    white-space: nowrap;
    padding: 8px !important;
}

.column-thumbnail img {
    border-radius: 3px;
    border: 1px solid #e5e5e5;
    background: #f8f8f8;
    padding: 2px;
    max-width: 40px;
    height: auto;
}

/* Courses Column */
.column-mscb_courses {
    max-width: 350px;
    white-space: nowrap;
    overflow-x: scroll;
    text-overflow: ellipsis;
    padding: 12px 10px;
}

.mscb-course-list {
    margin: 0;
    padding: 0;
    list-style: none;
}

.mscb-course-list li {
    display: inline-block;
    margin: 0 8px 4px 0;
    padding: 2px 8px;
    background: #f0f0f1;
    border-radius: 3px;
    font-size: 12px;
}

/* Price Column */
.column-mscb_price {
    width: 120px;
    text-align: right;
    background: #fafbfc;
    border-left: 1px solid #e1e1e1;
    font-weight: 600;
    padding: 12px 10px;
}

.mscb-regular-price {
    color: #82878c;
    text-decoration: line-through;
    margin-right: 5px;
}

.mscb-sale-price {
    color: #46b450;
}

/* Bundle Edit Screen */
.mscb-courses-container {
    margin: 15px 0;
    padding: 15px;
    background: #fff;
    border: 1px solid #e5e5e5;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

#mscb-course-select {
    width: calc(100% - 110px);
    margin-right: 10px;
}

#mscb-add-course {
    width: 100px;
}

#mscb-courses-list {
    margin-top: 15px;
    border-top: 1px solid #e5e5e5;
    padding-top: 15px;
}

.mscb-course-item {
    display: flex;
    align-items: center;
    padding: 8px;
    background: #f8f8f8;
    border: 1px solid #e5e5e5;
    margin-bottom: 5px;
    border-radius: 3px;
}

.mscb-course-title {
    flex: 1;
    margin-right: 10px;
}

.mscb-remove-course {
    color: #a00;
    text-decoration: none;
}

.mscb-remove-course:hover {
    color: #dc3232;
}

/* Pricing Fields */
.mscb-pricing-fields {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    padding: 15px;
    background: #fff;
    border: 1px solid #e5e5e5;
}

.mscb-pricing-fields label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.mscb-pricing-fields input[type="number"] {
    width: 100%;
}

/* Bulk Actions */
.bulkactions {
    padding: 8px 0;
}

.bulkactions select {
    margin-right: 8px;
}

/* Notice Styling */
.mscb-notice {
    margin: 15px 0;
    padding: 10px 15px;
    background: #fff;
    border-left: 4px solid #46b450;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

/* Responsive Design */
@media screen and (max-width: 782px) {
    .column-thumbnail {
        display: none !important;
    }
    
    .column-mscb_courses {
        max-width: none;
    }
    
    .mscb-pricing-fields {
        grid-template-columns: 1fr;
    }
    
    #mscb-course-select {
        width: 100%;
        margin-bottom: 10px;
    }
    
    #mscb-add-course {
        width: 100%;
    }
}

/* Remove all custom width/max-width/min-width for .wrap and .mscb-admin-table-scroll */
body.post-type-mscb_bundle .wrap,
body.post-type-mscb_bundle .mscb-admin-table-scroll,
body.post-type-mscb_bundle .wp-list-table.widefat.fixed {
    max-width: none;
    width: auto ;
    min-width: 0 ;
    padding: 0;
    margin: 0;
} 