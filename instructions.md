# 📦 Custom Linking Plugin – Instructions

## 🔧 Plugin Purpose

The **Custom Linking Plugin** is designed to link **WooCommerce products** with **MasterStudy LMS courses**. It enables a seamless integration between course display and WooCommerce checkout by allowing admin to define which WooCommerce product grants access to which LMS course.

---

## ✅ Features

1. **Admin Panel Integration**
   - Admin can manually **link LMS course IDs** with **WooCommerce product IDs**
   - Each link is stored with a **type**: either `course` or `bundle`

2. **Frontend Integration**
   - When a user clicks the **"Get Course"** button on any LMS course:
     - If the course is linked to a WooCommerce product:
       - The plugin will try to **add the product to cart** and redirect to **WooCommerce cart page**
       - If adding to cart fails, the user will be redirected to the **product page** to add it manually

3. **After Purchase**
   - Once the linked WooCommerce product is purchased successfully, the user will automatically receive **access to the associated LMS course(s)**

---

## 🔄 Workflow Overview

1. User visits a course page on the LMS
2. User clicks on the **Get Course** button
3. Plugin checks for any linked WooCommerce product:
   - If found, product is either added to cart or user is redirected to the product page
4. User completes checkout via **WooCommerce**
5. Plugin grants **course access** after successful payment

---

## 🧠 Detailed Functional Flow

### 1. Admin Panel Functionality
- UI for entering:
  - LMS Course ID
  - WooCommerce Product ID
  - Type: `course` or `bundle`
- Plugin stores this link in a custom database table

### 2. Required WooCommerce Setup
- **WooCommerce Bundled Products plugin** must be installed to support `bundle` product types

### 3. Frontend Flow (User Perspective)
- Course listing and detail pages remain unchanged
- On clicking **Get Course**:
  - Instead of default LMS cart logic, the plugin:
    - Adds **linked WooCommerce product** to cart (if possible)
    - Or redirects user to the WooCommerce product page

### 4. Course Access After Purchase
- On successful WooCommerce order:
  - Plugin checks if the product was linked to a course or bundle
  - Grants access to that course(s) using LMS functions

---

## 📌 Notes

- The plugin does **not alter course display pages** in LMS
- It only overrides what happens when user initiates a course purchase
- All linking and access logic is controlled in the backend via admin panel
- A custom table `wp_linking_table` will be used to store all course-product relationships
- Debugging information will be logged to `debug.log` in the plugin's root directory



## Referance plugins

1. Masterstudy LMS Learning Management System
2. WooCommerce
3. Course-bundler

## Note :
    all the plugins are present in the core plugin Directory which is ( wp-content/plugins/custom-linking-plugin )
    and yes all the plugins are installed and are in use
