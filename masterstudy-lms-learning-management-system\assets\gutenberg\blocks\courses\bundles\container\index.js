(()=>{var e,t={6432:(e,t,n)=>{"use strict";const a=window.React,r=window.wp.i18n,l=window.wp.blocks,i=window.wp.blockEditor;var o=n(6942),s=n.n(o);const c=window.wp.element,d=(0,c.createContext)(null),m=({children:e,...t})=>(0,a.createElement)(d.Provider,{value:{...t}},e),u=window.wp.components,g=({condition:e,fallback:t=null,children:n})=>(0,a.createElement)(a.Fragment,null,e?n:t),p=(e,t)=>{if(typeof e!=typeof t)return!1;if(Number.isNaN(e)&&Number.isNaN(t))return!0;if("object"!=typeof e||null===e||null===t)return e===t;if(Object.keys(e).length!==Object.keys(t).length)return!1;if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;const n=e.slice().sort(),a=t.slice().sort();return n.every(((e,t)=>p(e,a[t])))}for(const n of Object.keys(e))if(!p(e[n],t[n]))return!1;return!0};let b=function(e){return e.ALL="all",e.SOME="some",e}({}),h=function(e){return e.NORMAL="Normal",e.HOVER="Hover",e.ACTIVE="Active",e.FOCUS="Focus",e}({}),y=function(e){return e.DESKTOP="Desktop",e.TABLET="Tablet",e.MOBILE="Mobile",e}({}),v=function(e){return e.TOP_lEFT="top-left",e.TOP_CENTER="top-center",e.TOP_RIGHT="top-right",e.BOTTOM_lEFT="bottom-left",e.BOTTOM_CENTER="bottom-center",e.BOTTOM_RIGHT="bottom-right",e}({});const _=["",null,void 0,"null","undefined"],S=[".jpg",".jpeg",".png",".gif"],C=e=>_.includes(e),E=(e,t,n="")=>{const a=e[t];return"object"==typeof a&&null!==a?((e,t)=>{return n=e,Object.values(n).every((e=>_.includes(e)))?null:((e,t="")=>{const n=Object.entries(e).reduce(((e,[n,a])=>(e[n]=(a||"0")+t,e)),{});return`${n.top} ${n.right} ${n.bottom} ${n.left}`})(e,t);var n})(a,n):((e,t)=>C(e)?e:(e=>{if("string"!=typeof e)return!1;const t=e.slice(e.lastIndexOf("."));return S.includes(t)||e.startsWith("blob")})(e)?`url('${e}')`:String(e+t))(a,n)},f=e=>({desktopPropertyName:e,tabletPropertyName:e+"Tablet",mobilePropertyName:e+"Mobile"}),P=(e,t,n)=>{const a={};return n.forEach((({isAdaptive:n,hasHover:r,unit:l},i)=>{if(t.hasOwnProperty(i)){const{unitMeasureDesktop:o,unitMeasureTablet:s,unitMeasureMobile:c}=((e,t)=>{var n;return{unitMeasureDesktop:null!==(n=e[t])&&void 0!==n?n:"",unitMeasureTablet:t?e[t+"Tablet"]:"",unitMeasureMobile:t?e[t+"Mobile"]:""}})(t,l);if(n&&r){const{desktopHoverPropertyName:n,mobileHoverPropertyName:r,tabletHoverPropertyName:l}=(e=>{const t=e+h.HOVER;return{desktopHoverPropertyName:t,tabletHoverPropertyName:t+"Tablet",mobileHoverPropertyName:t+"Mobile"}})(i),d=E(t,n,o);C(d)||(a[`--lms-${e}-${n}`]=d);const m=E(t,l,s);C(m)||(a[`--lms-${e}-${l}`]=m);const u=E(t,r,c);C(u)||(a[`--lms-${e}-${r}`]=u)}if(r){const n=i+h.HOVER,r=E(t,n,o);C(r)||(a[`--lms-${e}-${n}`]=r)}if(n){const{desktopPropertyName:n,mobilePropertyName:r,tabletPropertyName:l}=f(i),d=E(t,n,o);C(d)||(a[`--lms-${e}-${n}`]=d);const m=E(t,l,s);C(m)||(a[`--lms-${e}-${l}`]=m);const u=E(t,r,c);C(u)||(a[`--lms-${e}-${r}`]=u)}const d=E(t,i,o);C(d)||(a[`--lms-${e}-${i}`]=d)}})),a},T=(e,t,n,a,r,l)=>`${!0===e?"inset ":""} ${t}px ${n}px ${""!==a?`${a}px`:""} ${""!==r?`${r}px`:""} ${l}`,N=(e,t,n,a,r,l,i,o,s)=>{const c={};if(t[n]&&null!==t[a]&&null!==t[r]&&(c[`--lms-${e}-boxShadow`]=T(t[o],t[a],t[r],t[l],t[i],t[n])),s){const{tabletPropertyName:s,mobilePropertyName:d}=f(o),{tabletPropertyName:m,mobilePropertyName:u}=f(a),{tabletPropertyName:g,mobilePropertyName:p}=f(r),{tabletPropertyName:b,mobilePropertyName:h}=f(n),{tabletPropertyName:y,mobilePropertyName:v}=f(l),{tabletPropertyName:_,mobilePropertyName:S}=f(i);t[b]&&null!==t[m]&&null!==t[g]&&(c[`--lms-${e}-boxShadowTablet`]=T(t[s],t[m],t[g],t[y],t[_],t[b])),t[h]&&null!==t[u]&&null!==t[p]&&(c[`--lms-${e}-boxShadowMobile`]=T(t[d],t[u],t[p],t[v],t[S],t[h]))}return c},x=(r.__("Small","masterstudy-lms-learning-management-system"),r.__("Normal","masterstudy-lms-learning-management-system"),r.__("Large","masterstudy-lms-learning-management-system"),r.__("Extra Large","masterstudy-lms-learning-management-system"),"wp-block-masterstudy-settings__"),H={top:"",right:"",bottom:"",left:""},L=(v.TOP_lEFT,v.TOP_CENTER,v.TOP_RIGHT,v.BOTTOM_lEFT,v.BOTTOM_CENTER,v.BOTTOM_RIGHT,[{label:r.__("Newest","masterstudy-lms-learning-management-system"),value:"date_high"},{label:r.__("Oldest","masterstudy-lms-learning-management-system"),value:"date_low"},{label:r.__("Overall rating","masterstudy-lms-learning-management-system"),value:"rating"},{label:r.__("Popular","masterstudy-lms-learning-management-system"),value:"popular"},{label:r.__("Price low","masterstudy-lms-learning-management-system"),value:"price_low"},{label:r.__("Price high","masterstudy-lms-learning-management-system"),value:"price_high"}]);function w(e){return Array.isArray(e)?e.map((e=>x+e)):x+e}const U=window.wp.data,M=()=>(0,U.useSelect)((e=>e("core/editor").getDeviceType()||e("core/edit-site")?.__experimentalGetPreviewDeviceType()||e("core/edit-post")?.__experimentalGetPreviewDeviceType()||e("masterstudy/store")?.getDeviceType()),[])||"Desktop",O=(e=!1)=>{const[t,n]=(0,c.useState)(e),a=(0,c.useCallback)((()=>{n(!0)}),[]);return{isOpen:t,onClose:(0,c.useCallback)((()=>{n(!1)}),[]),onOpen:a,onToggle:(0,c.useCallback)((()=>{n((e=>!e))}),[])}},R=()=>{const e=(0,c.useContext)(d);if(!e)throw new Error("No settings context provided");return e},B=(e="")=>{const{attributes:t,setAttributes:n,onResetByFieldName:a,changedFieldsByName:r}=R();return{value:t[e],onChange:t=>n({[e]:t}),onReset:a.get(e),isChanged:r.get(e)}},F=(e,t=!1,n=!1)=>{const{hoverName:a,onChangeHoverName:r}=(()=>{const[e,t]=(0,c.useState)(h.NORMAL);return{hoverName:e,onChangeHoverName:(0,c.useCallback)((e=>{t(e)}),[])}})(),l=M();return{fieldName:(0,c.useMemo)((()=>{const r=a===h.HOVER?a:"",i=l===y.DESKTOP?"":l;return n&&t?e+r+i:n&&!t?e+r:t&&!n?e+i:e}),[e,n,t,a,l]),hoverName:a,onChangeHoverName:r}},A=(e,t=!1,n="Normal")=>{const a=M(),r=(0,c.useMemo)((()=>{const r=n===h.NORMAL?"":n,l=a===y.DESKTOP?"":a;return r&&t?e+r+l:r&&!t?e+r:t&&!r?e+l:e}),[e,t,n,a]),{value:l,isChanged:i,onReset:o}=B(r);return{fieldName:r,value:l,isChanged:i,onReset:o}},W=(e=[],t=b.ALL)=>{const{attributes:n}=R();return!e.length||(t===b.ALL?e.every((({name:e,value:t})=>{const a=n[e];return Array.isArray(t)?Array.isArray(a)?p(t,a):t.includes(a):t===a})):t!==b.SOME||e.some((({name:e,value:t})=>{const a=n[e];return Array.isArray(t)?Array.isArray(a)?p(t,a):t.includes(a):t===a})))},k=e=>{const t=(0,c.useRef)(null);return(0,c.useEffect)((()=>{const n=n=>{t.current&&!t.current.contains(n.target)&&e()};return document.addEventListener("click",n),()=>{document.removeEventListener("click",n)}}),[t,e]),t},D=e=>(0,a.createElement)(u.SVG,{width:"16",height:"16",viewBox:"0 0 12 13",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(u.Path,{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})),I=e=>(0,a.createElement)(u.SVG,{width:"16",height:"16",viewBox:"0 0 14 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(u.G,{"clip-path":"url(#clip0_1068_38993)"},(0,a.createElement)(u.Path,{d:"M11.1973 8.60005L8.74967 8.11005V5.67171C8.74967 5.517 8.68822 5.36863 8.57882 5.25923C8.46942 5.14984 8.32105 5.08838 8.16634 5.08838H6.99967C6.84496 5.08838 6.69659 5.14984 6.5872 5.25923C6.4778 5.36863 6.41634 5.517 6.41634 5.67171V8.58838H5.24967C5.15021 8.58844 5.05241 8.61391 4.96555 8.66238C4.87869 8.71084 4.80565 8.78068 4.75336 8.86529C4.70106 8.9499 4.67125 9.04646 4.66674 9.14582C4.66223 9.24518 4.68317 9.34405 4.72759 9.43305L6.47759 12.933C6.57676 13.1302 6.77859 13.255 6.99967 13.255H11.083C11.2377 13.255 11.3861 13.1936 11.4955 13.0842C11.6049 12.9748 11.6663 12.8264 11.6663 12.6717V9.17171C11.6665 9.03685 11.6198 8.90612 11.5343 8.80186C11.4487 8.69759 11.3296 8.62626 11.1973 8.60005Z"}),(0,a.createElement)(u.Path,{d:"M10.4997 1.58838H3.49967C2.85626 1.58838 2.33301 2.11221 2.33301 2.75505V6.83838C2.33301 7.4818 2.85626 8.00505 3.49967 8.00505H5.24967V6.83838H3.49967V2.75505H10.4997V6.83838H11.6663V2.75505C11.6663 2.11221 11.1431 1.58838 10.4997 1.58838Z"})),(0,a.createElement)("defs",null,(0,a.createElement)("clipPath",{id:"clip0_1068_38993"},(0,a.createElement)(u.Rect,{width:"14",height:"14",transform:"translate(0 0.421875)"})))),z=[{value:h.NORMAL,label:r.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(D,{onClick:e})},{value:h.HOVER,label:r.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(I,{onClick:e})},{value:h.ACTIVE,label:r.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(I,{onClick:e})},{value:h.FOCUS,label:r.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(I,{onClick:e})}],V={[h.NORMAL]:{icon:(0,a.createElement)(D,null),label:r.__("Normal State","masterstudy-lms-learning-management-system")},[h.HOVER]:{icon:(0,a.createElement)(I,null),label:r.__("Hovered State","masterstudy-lms-learning-management-system")},[h.ACTIVE]:{icon:(0,a.createElement)(I,null),label:r.__("Active State","masterstudy-lms-learning-management-system")},[h.FOCUS]:{icon:(0,a.createElement)(I,null),label:r.__("Focus State","masterstudy-lms-learning-management-system")}},$=(e,t)=>{let n=[];return n=e.length?z.filter((t=>e.includes(t.value))):z,n=n.filter((e=>e.value!==t)),{ICONS_MAP:V,options:n}},[j,G,Z,K,X]=w(["hover-state","hover-state__selected","hover-state__selected__opened-menu","hover-state__menu","hover-state__menu__item"]),Y=({stateOptions:e,currentState:t,onSelect:n})=>{const{isOpen:r,onOpen:l,onClose:i}=O(),o=k(i),{ICONS_MAP:c,options:d}=$(e,t);return(0,a.createElement)("div",{className:j,ref:o},(0,a.createElement)("div",{className:s()([G],{[Z]:r}),onClick:l,title:c[t]?.label},c[t]?.icon),(0,a.createElement)(g,{condition:r},(0,a.createElement)("div",{className:K},d.map((({value:e,icon:t,label:r})=>(0,a.createElement)("div",{key:e,className:X,title:r},t((()=>n(e)))))))))},q=w("color-indicator"),J=(0,c.memo)((({color:e,onChange:t})=>(0,a.createElement)("div",{className:q},(0,a.createElement)(i.PanelColorSettings,{enableAlpha:!0,disableCustomColors:!1,__experimentalHasMultipleOrigins:!0,__experimentalIsRenderedInSidebar:!0,colorSettings:[{label:"",value:e,onChange:t}]}))));var Q;function ee(){return ee=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},ee.apply(null,arguments)}var te,ne,ae=function(e){return a.createElement("svg",ee({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 12 13"},e),Q||(Q=a.createElement("path",{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})))};function re(){return re=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},re.apply(null,arguments)}var le=function(e){return a.createElement("svg",re({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 14 15"},e),te||(te=a.createElement("g",{clipPath:"url(#state-hover_svg__a)"},a.createElement("path",{d:"M11.197 8.6 8.75 8.11V5.672a.583.583 0 0 0-.584-.584H7a.583.583 0 0 0-.584.584v2.916H5.25a.584.584 0 0 0-.522.845l1.75 3.5c.099.197.3.322.522.322h4.083a.583.583 0 0 0 .583-.583v-3.5a.58.58 0 0 0-.469-.572"}),a.createElement("path",{d:"M10.5 1.588h-7c-.644 0-1.167.524-1.167 1.167v4.083c0 .644.523 1.167 1.167 1.167h1.75V6.838H3.5V2.755h7v4.083h1.166V2.755c0-.643-.523-1.167-1.166-1.167"}))),ne||(ne=a.createElement("defs",null,a.createElement("clipPath",{id:"state-hover_svg__a"},a.createElement("path",{d:"M0 .422h14v14H0z"})))))};const ie=[{value:h.NORMAL,label:r.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(ae,{onClick:e})},{value:h.HOVER,label:r.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(le,{onClick:e})}],oe={[h.NORMAL]:{icon:(0,a.createElement)(ae,null),label:r.__("Normal State","masterstudy-lms-learning-management-system")},[h.HOVER]:{icon:(0,a.createElement)(le,null),label:r.__("Hovered State","masterstudy-lms-learning-management-system")}},se=w("hover-state"),ce=w("hover-state__selected"),de=w("hover-state__selected__opened-menu"),me=w("has-changes"),ue=w("hover-state__menu"),ge=w("hover-state__menu__item"),pe=(0,c.memo)((e=>{const{hoverName:t,onChangeHoverName:n,fieldName:r}=e,{changedFieldsByName:l}=R(),i=l.get(r),{isOpen:o,onOpen:d,onClose:m}=O(),u=k(m),{ICONS_MAP:p,options:b}=(e=>{const t=(0,c.useMemo)((()=>ie.filter((t=>t.value!==e))),[e]);return{ICONS_MAP:oe,options:t}})(t),h=(0,c.useCallback)((e=>{n(e),m()}),[n,m]);return(0,a.createElement)("div",{className:se,ref:u},(0,a.createElement)("div",{className:s()([ce],{[de]:o,[me]:i}),onClick:d,title:p[t]?.label},p[t]?.icon),(0,a.createElement)(g,{condition:o},(0,a.createElement)("div",{className:ue},b.map((({value:e,icon:t,label:n})=>(0,a.createElement)("div",{key:e,className:ge,title:n},t((()=>h(e)))))))))})),be={Desktop:{icon:"desktop",label:r.__("Desktop","masterstudy-lms-learning-management-system")},Tablet:{icon:"tablet",label:r.__("Tablet","masterstudy-lms-learning-management-system")},Mobile:{icon:"smartphone",label:r.__("Mobile","masterstudy-lms-learning-management-system")}},he=[{value:y.DESKTOP,icon:"desktop",label:r.__("Desktop","masterstudy-lms-learning-management-system")},{value:y.TABLET,icon:"tablet",label:r.__("Tablet","masterstudy-lms-learning-management-system")},{value:y.MOBILE,icon:"smartphone",label:r.__("Mobile","masterstudy-lms-learning-management-system")}],ye=w("device-picker"),ve=w("device-picker__selected"),_e=w("device-picker__selected__opened-menu"),Se=w("device-picker__menu"),Ce=w("device-picker__menu__item"),Ee=()=>{const{isOpen:e,onOpen:t,onClose:n}=O(),{value:r,onChange:l}=(e=>{const t=M(),n=(0,U.useDispatch)();return{value:(0,c.useMemo)((()=>be[t]),[t]),onChange:t=>{n("core/edit-site")&&n("core/edit-site").__experimentalSetPreviewDeviceType?n("core/edit-site").__experimentalSetPreviewDeviceType(t):n("core/edit-post")&&n("core/edit-post").__experimentalSetPreviewDeviceType?n("core/edit-post").__experimentalSetPreviewDeviceType(t):n("masterstudy/store").setDeviceType(t),e()}}})(n),i=(e=>(0,c.useMemo)((()=>he.filter((t=>t.icon!==e))),[e]))(r.icon),o=k(n);return(0,a.createElement)("div",{className:ye,ref:o},(0,a.createElement)(u.Dashicon,{className:s()([ve],{[_e]:e}),icon:r.icon,size:16,onClick:t,title:r.label}),(0,a.createElement)(g,{condition:e},(0,a.createElement)("div",{className:Se},i.map((e=>(0,a.createElement)(u.Dashicon,{key:e.value,icon:e.icon,size:16,onClick:()=>l(e.value),className:Ce,title:e.label}))))))},fe=w("reset-button"),Pe=({onReset:e})=>(0,a.createElement)(u.Dashicon,{icon:"undo",onClick:e,className:fe,size:16}),Te=[{label:"PX",value:"px"},{label:"%",value:"%"},{label:"EM",value:"em"},{label:"REM",value:"rem"},{label:"VW",value:"vw"},{label:"VH",value:"vh"}],Ne=w("unit"),xe=w("unit__single"),He=w("unit__list"),Le=({name:e,isAdaptive:t})=>{const{isOpen:n,onOpen:r,onClose:l}=O(),{fieldName:i}=F(e,t),{value:o,onChange:s}=B(i),c=k(l);return(0,a.createElement)("div",{className:Ne,ref:c},(0,a.createElement)("div",{className:xe,onClick:r},o),(0,a.createElement)(g,{condition:n},(0,a.createElement)("div",{className:He},Te.map((({value:e,label:t})=>(0,a.createElement)("div",{key:e,onClick:()=>(s(e),void l())},t))))))},we=w("popover-modal"),Ue=w("popover-modal__close dashicon dashicons dashicons-no-alt"),Me=e=>{const{isOpen:t,onClose:n,popoverContent:r}=e;return(0,a.createElement)(g,{condition:t},(0,a.createElement)(u.Popover,{position:"middle left",onClose:n,className:we},r,(0,a.createElement)("span",{onClick:n,className:Ue})))},Oe=w("setting-label"),Re=w("setting-label__content"),Be=e=>{const{label:t,isChanged:n=!1,onReset:r,showDevicePicker:l=!0,HoverStateControl:i=null,unitName:o,popoverContent:s=null,dependencies:c}=e,{isOpen:d,onClose:m,onToggle:u}=O();return W(c)?(0,a.createElement)("div",{className:Oe},(0,a.createElement)("div",{className:Re},(0,a.createElement)("div",{onClick:u},t),(0,a.createElement)(g,{condition:Boolean(s)},(0,a.createElement)(Me,{isOpen:d,onClose:m,popoverContent:s})),(0,a.createElement)(g,{condition:l},(0,a.createElement)(Ee,null)),(0,a.createElement)(g,{condition:Boolean(i)},i)),(0,a.createElement)(g,{condition:Boolean(o)},(0,a.createElement)(Le,{name:o,isAdaptive:l})),(0,a.createElement)(g,{condition:n},(0,a.createElement)(Pe,{onReset:r}))):null},Fe=w("suffix"),Ae=()=>(0,a.createElement)("div",{className:Fe},(0,a.createElement)(u.Dashicon,{icon:"color-picker",size:16})),We=w("color-picker"),ke=e=>{const{name:t,label:n,placeholder:r,dependencyMode:l,dependencies:i,isAdaptive:o=!1,hasHover:s=!1}=e,{fieldName:c,hoverName:d,onChangeHoverName:m}=F(t,o,s),{value:p,isChanged:b,onChange:h,onReset:y}=B(c);return W(i,l)?(0,a.createElement)("div",{className:We},(0,a.createElement)(g,{condition:Boolean(n)},(0,a.createElement)(Be,{label:n,isChanged:b,onReset:y,showDevicePicker:o,HoverStateControl:(0,a.createElement)(g,{condition:s},(0,a.createElement)(pe,{hoverName:d,onChangeHoverName:m,fieldName:c}))})),(0,a.createElement)(u.__experimentalInputControl,{prefix:(0,a.createElement)(J,{color:p,onChange:h}),suffix:(0,a.createElement)(Ae,null),onChange:h,value:p,placeholder:r})):null},De=w("number-steppers"),Ie=w("indent-steppers"),ze=w("indent-stepper-plus"),Ve=w("indent-stepper-minus"),$e=({onIncrement:e,onDecrement:t,withArrows:n=!1})=>n?(0,a.createElement)("span",{className:Ie},(0,a.createElement)("button",{onClick:e,className:ze}),(0,a.createElement)("button",{onClick:t,className:Ve})):(0,a.createElement)("span",{className:De},(0,a.createElement)("button",{onClick:e},"+"),(0,a.createElement)("button",{onClick:t},"-")),[je,Ge]=w(["indents","indents-control"]),Ze=({name:e,label:t,unitName:n,popoverContent:l,dependencyMode:i,dependencies:o,isAdaptive:s=!1})=>{const{fieldName:d}=F(e,s),{value:m,onResetSegmentedBox:p,hasChanges:b,handleInputIncrement:h,handleInputDecrement:y,updateDirectionsValues:v,lastFieldValue:_}=((e,t)=>{const{value:n,isChanged:a,onChange:r,onReset:l}=B(e),{onResetByFieldName:i,changedFieldsByName:o}=R(),s=a||o.get(t),d=e=>{r({...n,...e})},[m,u]=(0,c.useState)(!1);return{value:n,onResetSegmentedBox:()=>{l(),i.get(t)()},hasChanges:s,handleInputIncrement:e=>Number(n[e])+1,handleInputDecrement:e=>Number(n[e])-1,updateDirectionsValues:(e,t,n)=>{e?(u(!1),d({top:n,right:n,bottom:n,left:n})):(u(n),d({[t]:n}))},lastFieldValue:m}})(d,n),[S,C]=(0,c.useState)((()=>{const{left:e,right:t,top:n,bottom:a}=m;return""!==e&&e===t&&t===n&&n===a})),E=e=>{const[t,n]=Object.entries(e)[0];v(S,t,n)},f=e=>()=>{const t=h(e);v(S,e,String(t))},P=e=>()=>{const t=y(e);v(S,e,String(t))};return W(o,i)?(0,a.createElement)("div",{className:je},(0,a.createElement)(g,{condition:Boolean(t)},(0,a.createElement)(Be,{label:null!=t?t:"",isChanged:b,onReset:p,unitName:n,popoverContent:l,showDevicePicker:s})),(0,a.createElement)("div",{className:`${Ge} ${S?"active":""}`},(0,a.createElement)("div",null,(0,a.createElement)(u.__experimentalNumberControl,{value:m.top,onChange:e=>{E({top:e})},spinControls:"none",suffix:(0,a.createElement)($e,{onIncrement:f("top"),onDecrement:P("top"),withArrows:!0})}),(0,a.createElement)("div",null,r.__("Top","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(u.__experimentalNumberControl,{value:m.right,onChange:e=>{E({right:e})},spinControls:"none",suffix:(0,a.createElement)($e,{onIncrement:f("right"),onDecrement:P("right"),withArrows:!0})}),(0,a.createElement)("div",null,r.__("Right","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(u.__experimentalNumberControl,{value:m.bottom,onChange:e=>{E({bottom:e})},spinControls:"none",suffix:(0,a.createElement)($e,{onIncrement:f("bottom"),onDecrement:P("bottom"),withArrows:!0})}),(0,a.createElement)("div",null,r.__("Bottom","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(u.__experimentalNumberControl,{value:m.left,onChange:e=>{E({left:e})},spinControls:"none",suffix:(0,a.createElement)($e,{onIncrement:f("left"),onDecrement:P("left"),withArrows:!0})}),(0,a.createElement)("div",null,r.__("Left","masterstudy-lms-learning-management-system"))),(0,a.createElement)(u.Dashicon,{icon:"dashicons "+(S?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{S||!1===_||v(!0,"left",_),C((e=>!e))}}))):null},[Ke,Xe,Ye,qe]=w(["toggle-group-wrapper","toggle-group","toggle-group__toggle","toggle-group__active-toggle"]),Je=e=>{const{name:t,options:n,label:r,isAdaptive:l=!1,dependencyMode:i,dependencies:o}=e,{fieldName:c}=F(t,l),{value:d,isChanged:m,onChange:u,onReset:p}=B(c);return W(o,i)?(0,a.createElement)("div",{className:Ke},(0,a.createElement)(g,{condition:Boolean(r)},(0,a.createElement)(Be,{label:r,isChanged:m,onReset:p,showDevicePicker:l})),(0,a.createElement)("div",{className:Xe},n.map((e=>(0,a.createElement)("div",{key:e.value,className:s()([Ye],{[qe]:e.value===d}),onClick:()=>u(e.value)},e.label))))):null},[Qe,et,tt,nt]=w(["border-control","border-control-solid","border-control-dashed","border-control-dotted"]),at=e=>{const{label:t,borderStyleName:n,borderColorName:l,borderWidthName:i,dependencyMode:o,dependencies:d,isAdaptive:m=!1,hasHover:u=!1}=e,[p,b]=(0,c.useState)("Normal"),{fieldName:h,value:y,isChanged:v,onReset:_}=A(n,m,p),{fieldName:S,isChanged:C,onReset:E}=A(l,m,p),{fieldName:f,isChanged:P,onReset:T}=A(i,m,p);if(!W(d,o))return null;const N=v||C||P;return(0,a.createElement)("div",{className:s()([Qe],{"has-reset-button":N})},(0,a.createElement)(Be,{label:t,isChanged:N,onReset:()=>{_(),E(),T()},showDevicePicker:m,HoverStateControl:(0,a.createElement)(g,{condition:u},(0,a.createElement)(Y,{stateOptions:["Normal","Hover"],currentState:p,onSelect:b}))}),(0,a.createElement)(Je,{options:[{label:(0,a.createElement)("span",null,r.__("None","masterstudy-lms-learning-management-system")),value:"none"},{label:(0,a.createElement)("span",{className:et}),value:"solid"},{label:(0,a.createElement)("span",{className:tt},(0,a.createElement)("span",null)),value:"dashed"},{label:(0,a.createElement)("span",{className:nt},(0,a.createElement)("span",null,(0,a.createElement)("span",null))),value:"dotted"}],name:h}),(0,a.createElement)(g,{condition:"none"!==y},(0,a.createElement)(ke,{name:S,placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Ze,{name:f})))},rt=w("border-radius"),lt=w("border-radius-control"),it=({name:e,label:t,unitName:n,popoverContent:r,dependencyMode:l,dependencies:i,isAdaptive:o=!1,hasHover:d=!1})=>{const{fieldName:m}=F(e,o,d),{value:g,onResetBorderRadius:p,hasChanges:b,handleInputIncrement:h,handleInputDecrement:y,updateDirectionsValues:v,lastFieldValue:_}=((e,t)=>{const[n,a]=(0,c.useState)(!1),{value:r,isChanged:l,onChange:i,onReset:o}=B(e),{onResetByFieldName:s,changedFieldsByName:d}=R(),m=l||d.get(t),u=e=>{i({...r,...e})};return{value:r,onResetBorderRadius:()=>{o(),s.get(t)()},hasChanges:m,handleInputIncrement:e=>Number(r[e])+1,handleInputDecrement:e=>Number(r[e])-1,updateDirectionsValues:(e,t,n)=>{e?(u({top:n,right:n,bottom:n,left:n}),a(!1)):(u({[t]:n}),a(n))},lastFieldValue:n}})(m,n),[S,C]=(0,c.useState)((()=>{const{left:e,right:t,top:n,bottom:a}=g;return""!==e&&e===t&&t===n&&n===a})),E=e=>{const[t,n]=Object.entries(e)[0];v(S,t,n)},f=e=>()=>{const t=h(e);v(S,e,String(t))},P=e=>()=>{const t=y(e);v(S,e,String(t))};return W(i,l)?(0,a.createElement)("div",{className:rt},(0,a.createElement)(Be,{label:t,isChanged:b,onReset:p,unitName:n,popoverContent:r,showDevicePicker:o}),(0,a.createElement)("div",{className:s()([lt],{"has-reset-button":b,active:S})},(0,a.createElement)("div",{className:"number-control-top"},(0,a.createElement)(u.__experimentalNumberControl,{value:g.top,onChange:e=>{E({top:e})},spinControls:"none",suffix:(0,a.createElement)($e,{onIncrement:f("top"),onDecrement:P("top"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-right"},(0,a.createElement)(u.__experimentalNumberControl,{value:g.right,onChange:e=>{E({right:e})},spinControls:"none",suffix:(0,a.createElement)($e,{onIncrement:f("right"),onDecrement:P("right"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-left"},(0,a.createElement)(u.__experimentalNumberControl,{value:g.left,onChange:e=>{E({left:e})},spinControls:"none",suffix:(0,a.createElement)($e,{onIncrement:f("left"),onDecrement:P("left"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-bottom"},(0,a.createElement)(u.__experimentalNumberControl,{value:g.bottom,onChange:e=>{E({bottom:e})},spinControls:"none",suffix:(0,a.createElement)($e,{onIncrement:f("bottom"),onDecrement:P("bottom"),withArrows:!0})})),(0,a.createElement)(u.Dashicon,{icon:"dashicons "+(S?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{S||!1===_||v(!0,"left",_),C((e=>!e))}}))):null},ot=w("box-shadow-preset"),st=({preset:e})=>(0,a.createElement)("div",{className:ot},(0,a.createElement)("div",{style:{boxShadow:`${e.horizontal}px ${e.vertical}px ${e.blur}px ${e.spread}px rgba(0, 0, 0, 0.25) ${e.inset?"inset":""}`}})),ct=w("presets"),dt=w("presets__item-wrapper"),mt=w("presets__item-wrapper__preset"),ut=w("presets__item-wrapper__name"),gt=(0,c.memo)((e=>{const{presets:t,activePreset:n,onSelectPreset:r,PresetItem:l,detectIsActive:i,detectByIndex:o=!1}=e;return(0,a.createElement)("div",{className:ct},t.map((({name:e,...t},c)=>(0,a.createElement)("div",{key:c,className:s()([dt],{active:i(n,o?c:t)}),onClick:()=>r(t)},(0,a.createElement)("div",{className:mt},(0,a.createElement)(l,{preset:t})),(0,a.createElement)("span",{className:ut},e)))))})),pt=w("range-control"),bt=e=>{const{name:t,label:n,min:r,max:l,unitName:i,dependencyMode:o,dependencies:s,isAdaptive:c=!1}=e,{fieldName:d}=F(t,c),{value:m,onChange:p,onResetNumberField:b,hasChanges:h}=((e,t)=>{const{value:n,isChanged:a,onChange:r,onReset:l}=B(e),{onResetByFieldName:i,changedFieldsByName:o}=R();return{value:n,onChange:r,onResetNumberField:()=>{l(),i.get(t)()},hasChanges:a||o.get(t)}})(d,i);return W(s,o)?(0,a.createElement)("div",{className:pt},(0,a.createElement)(g,{condition:Boolean(n)},(0,a.createElement)(Be,{label:n,isChanged:h,onReset:b,unitName:i,showDevicePicker:c})),(0,a.createElement)(u.RangeControl,{value:m,onChange:p,min:r,max:l})):null},ht=w("switch"),yt=e=>{const{name:t,label:n,dependencyMode:r,dependencies:l,isAdaptive:i=!1}=e,{fieldName:o}=F(t,i),{value:s,onChange:c}=B(o);return W(l,r)?(0,a.createElement)("div",{className:ht,"data-has-label":Boolean(n).toString()},(0,a.createElement)(u.ToggleControl,{label:n,checked:s,onChange:c}),(0,a.createElement)(g,{condition:i},(0,a.createElement)(Ee,null))):null},vt=w("box-shadow-settings"),_t=w("box-shadow-presets-title"),St=[{name:"Drop",horizontal:0,vertical:2,blur:2,spread:0,inset:!1},{name:"Glow",horizontal:0,vertical:4,blur:20,spread:0,inset:!1},{name:"Outline",horizontal:0,vertical:2,blur:10,spread:0,inset:!1},{name:"Sparse",horizontal:0,vertical:10,blur:50,spread:0,inset:!1}],Ct=e=>{const{label:t,min:n,max:l,shadowColorName:i,shadowHorizontalName:o,shadowVerticalName:s,shadowBlurName:d,shadowSpreadName:m,shadowInsetName:u,popoverContent:b,dependencyMode:h,dependencies:y,isAdaptive:v=!1,hasHover:_=!1,presets:S=St}=e,[C,E]=(0,c.useState)("Normal"),{fieldName:f}=A(i,v,C),{fieldName:P}=A(o,v,C),{fieldName:T}=A(s,v,C),{fieldName:N}=A(d,v,C),{fieldName:x}=A(m,v,C),{fieldName:H}=A(u,v,C),{isChanged:L,onReset:w,onSelectPreset:U,activePreset:M}=((e,t,n,a,r,l)=>{const{setAttributes:i}=R(),{value:o,isChanged:s,onReset:d}=B(e),{value:m,isChanged:u,onReset:g}=B(t),{value:p,isChanged:b,onReset:h}=B(n),{value:y,isChanged:v,onReset:_}=B(a),{value:S,isChanged:C,onReset:E}=B(r),{value:f,isChanged:P,onReset:T}=B(l),N=s||u||b||v||C||P,x=(0,c.useCallback)((e=>{const{horizontal:o,vertical:s,blur:c,spread:d,inset:m}=e;i({[t]:o,[n]:s,[a]:c,[r]:d,[l]:m})}),[t,n,a,r,i,l]);return{activePreset:(0,c.useMemo)((()=>({horizontal:m,vertical:p,blur:y,spread:S,inset:null!=f&&f})),[m,p,y,S,f]),onReset:()=>{[d,g,h,_,E,T].forEach((e=>e()))},isChanged:N,onSelectPreset:x}})(f,P,T,N,x,H);return W(y,h)?(0,a.createElement)("div",{className:vt},(0,a.createElement)(Be,{label:t,isChanged:L,onReset:w,popoverContent:b,showDevicePicker:v,HoverStateControl:(0,a.createElement)(g,{condition:_},(0,a.createElement)(Y,{stateOptions:["Normal","Hover"],currentState:C,onSelect:E}))}),(0,a.createElement)(gt,{presets:S,onSelectPreset:U,activePreset:M,PresetItem:st,detectIsActive:p,detectByIndex:!1}),(0,a.createElement)(ke,{name:f,label:r.__("Color","masterstudy-lms-learning-management-system"),placeholder:"Select color"}),(0,a.createElement)(bt,{name:P,label:r.__("Horizontal Offset","masterstudy-lms-learning-management-system"),min:n,max:l}),(0,a.createElement)(bt,{name:T,label:r.__("Vertical Offset","masterstudy-lms-learning-management-system"),min:n,max:l}),(0,a.createElement)(bt,{name:N,label:r.__("Blur","masterstudy-lms-learning-management-system"),min:n,max:l}),(0,a.createElement)(bt,{name:x,label:r.__("Spread","masterstudy-lms-learning-management-system"),min:n,max:l}),(0,a.createElement)("div",{className:_t},r.__("Position","masterstudy-lms-learning-management-system")),(0,a.createElement)(yt,{name:H,label:r.__("Inset","masterstudy-lms-learning-management-system")})):null},Et=(w("input-field"),w("input-field-control"),w("number-field")),ft=w("number-field-control"),Pt=e=>{const{name:t,label:n,unitName:r,help:l,popoverContent:i,dependencyMode:o,dependencies:s,isAdaptive:c=!1}=e,{fieldName:d}=F(t,c),{value:m,onResetNumberField:g,hasChanges:p,handleIncrement:b,handleDecrement:h,handleInputChange:y}=((e,t)=>{const{value:n,isChanged:a,onChange:r,onReset:l}=B(e),{onResetByFieldName:i,changedFieldsByName:o}=R(),s=a||o.get(t);return{value:n,onResetNumberField:()=>{l(),i.get(t)()},hasChanges:s,handleIncrement:()=>{r(n+1)},handleDecrement:()=>{r(n-1)},handleInputChange:e=>{const t=Number(""===e?0:e);r(t)}}})(d,r);return W(s,o)?(0,a.createElement)("div",{className:Et},(0,a.createElement)(Be,{label:n,isChanged:p,onReset:g,unitName:r,showDevicePicker:c,popoverContent:i}),(0,a.createElement)("div",{className:ft},(0,a.createElement)(u.__experimentalNumberControl,{value:m,onChange:y,spinControls:"none",suffix:(0,a.createElement)($e,{onIncrement:b,onDecrement:h})})),l&&(0,a.createElement)("small",null,l)):null},Tt=({className:e})=>(0,a.createElement)("div",{className:e},r.__("No options","masterstudy-lms-learning-management-system")),Nt=w("select__single-item"),xt=w("select__container"),Ht=w("select__container__multi-item"),Lt=({multiple:e,value:t,options:n,onChange:r})=>{const{singleValue:l,multipleValue:i}=((e,t,n)=>({singleValue:(0,c.useMemo)((()=>t?null:n.find((t=>t.value===e))?.label),[t,e,n]),multipleValue:(0,c.useMemo)((()=>t?e:null),[t,e])}))(t,e,n);return(0,a.createElement)(g,{condition:e,fallback:(0,a.createElement)("div",{className:Nt},l)},(0,a.createElement)("div",{className:xt},i?.map((e=>{const t=n.find((t=>t.value===e));return t?(0,a.createElement)("div",{key:t.value,className:Ht},(0,a.createElement)("div",null,t.label),(0,a.createElement)(u.Dashicon,{icon:"no-alt",onClick:()=>r(t.value),size:16})):null}))))},wt=w("select"),Ut=w("select__select-box"),Mt=w("select__placeholder"),Ot=w("select__select-box-multiple"),Rt=w("select__menu"),Bt=w("select__menu__options-container"),Ft=w("select__menu__item"),At=e=>{const{options:t,multiple:n=!1,placeholder:r="Select",value:l,onSelect:i}=e,{isOpen:o,onToggle:d,onClose:m}=O(),p=k(m),b=((e,t,n)=>(0,c.useMemo)((()=>n&&Array.isArray(e)?t.filter((t=>!e.includes(t.value))):t.filter((t=>t.value!==e))),[e,t,n]))(l,t,n),h=((e,t,n,a)=>(0,c.useCallback)((r=>{if(t&&Array.isArray(e)){const t=e.includes(r)?e.filter((e=>e!==r)):[...e,r];n(t)}else n(r),a()}),[t,e,n,a]))(l,n,i,m),y=((e,t)=>(0,c.useMemo)((()=>t&&Array.isArray(e)?Boolean(e.length):Boolean(e)),[t,e]))(l,n),v=n&&Array.isArray(l)&&l?.length>0;return(0,a.createElement)("div",{className:wt,ref:p},(0,a.createElement)("div",{className:s()([Ut],{[Ot]:v}),onClick:d},(0,a.createElement)(g,{condition:y,fallback:(0,a.createElement)("div",{className:Mt},r)},(0,a.createElement)(Lt,{onChange:h,options:t,multiple:n,value:l})),(0,a.createElement)(u.Dashicon,{icon:o?"arrow-up-alt2":"arrow-down-alt2",size:16,style:{color:"#4D5E6F"}})),(0,a.createElement)(g,{condition:o},(0,a.createElement)("div",{className:Rt},(0,a.createElement)(g,{condition:Boolean(b.length),fallback:(0,a.createElement)(Tt,{className:Ft})},(0,a.createElement)("div",{className:Bt},b.map((e=>(0,a.createElement)("div",{key:e.value,onClick:()=>h(e.value),className:Ft},e.label))))))))},Wt=w("setting-select"),kt=e=>{const{name:t,options:n,label:r,multiple:l=!1,placeholder:i,isAdaptive:o=!1,dependencyMode:s,dependencies:c}=e,{fieldName:d}=F(t,o),{value:m,isChanged:u,onChange:p,onReset:b}=B(d);return W(c,s)?(0,a.createElement)("div",{className:Wt},(0,a.createElement)(g,{condition:Boolean(r)},(0,a.createElement)(Be,{label:r,isChanged:u,onReset:b,showDevicePicker:o})),(0,a.createElement)(At,{options:n,value:m,onSelect:p,multiple:l,placeholder:i})):null},Dt=w("row-select"),It=w("row-select__label"),zt=w("row-select__control"),Vt=e=>{const{name:t,label:n,options:r,isAdaptive:l=!1}=e,{fieldName:i}=F(t,l),{isChanged:o,onReset:s}=B(i);return(0,a.createElement)("div",{className:Dt},(0,a.createElement)("div",{className:It},(0,a.createElement)("div",null,n),(0,a.createElement)(g,{condition:l},(0,a.createElement)(Ee,null))),(0,a.createElement)("div",{className:zt},(0,a.createElement)(kt,{name:t,options:r,isAdaptive:l}),(0,a.createElement)(g,{condition:o},(0,a.createElement)(Pe,{onReset:s}))))},$t=w("typography-select"),jt=w("typography-select-label"),Gt=e=>{const{name:t,label:n,options:r,isAdaptive:l=!1}=e,{fieldName:i}=F(t,l),{isChanged:o,onReset:s}=B(i);return(0,a.createElement)("div",{className:$t},(0,a.createElement)("div",{className:jt},(0,a.createElement)("div",null,n),(0,a.createElement)(g,{condition:l},(0,a.createElement)(Ee,null))),(0,a.createElement)(kt,{name:t,options:r,isAdaptive:l}),(0,a.createElement)(g,{condition:o},(0,a.createElement)(Pe,{onReset:s})))},Zt=w("typography"),Kt=e=>{const{fontSizeName:t,fontWeightName:n,textTransformName:l,fontStyleName:i,textDecorationName:o,lineHeightName:s,letterSpacingName:c,wordSpacingName:d,fontSizeUnitName:m,lineHeightUnitName:u,letterSpacingUnitName:g,wordSpacingUnitName:p,dependencyMode:b,dependencies:h,isAdaptive:y=!1}=e,{fontWeightOptions:v,textTransformOptions:_,fontStyleOptions:S,textDecorationOptions:C}={fontWeightOptions:[{label:r.__("100 (Thin)","masterstudy-lms-learning-management-system"),value:"100"},{label:r.__("200 (Extra Light)","masterstudy-lms-learning-management-system"),value:"200"},{label:r.__("300 (Light)","masterstudy-lms-learning-management-system"),value:"300"},{label:r.__("400 (Normal)","masterstudy-lms-learning-management-system"),value:"400"},{label:r.__("500 (Medium)","masterstudy-lms-learning-management-system"),value:"500"},{label:r.__("600 (Semi Bold)","masterstudy-lms-learning-management-system"),value:"600"},{label:r.__("700 (Bold)","masterstudy-lms-learning-management-system"),value:"700"},{label:r.__("800 (Extra Bold)","masterstudy-lms-learning-management-system"),value:"800"},{label:r.__("900 (Extra)","masterstudy-lms-learning-management-system"),value:"900"}],textTransformOptions:[{label:r.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:r.__("Uppercase","masterstudy-lms-learning-management-system"),value:"uppercase"},{label:r.__("Lowercase","masterstudy-lms-learning-management-system"),value:"lowercase"},{label:r.__("Capitalize","masterstudy-lms-learning-management-system"),value:"capitalize"},{label:r.__("Normal","masterstudy-lms-learning-management-system"),value:"none"}],fontStyleOptions:[{label:r.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:r.__("Normal","masterstudy-lms-learning-management-system"),value:"none"},{label:r.__("Italic","masterstudy-lms-learning-management-system"),value:"italic"},{label:r.__("Oblique","masterstudy-lms-learning-management-system"),value:"oblique"}],textDecorationOptions:[{label:r.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:r.__("Underline","masterstudy-lms-learning-management-system"),value:"underline"},{label:r.__("Line Through","masterstudy-lms-learning-management-system"),value:"line-through"},{label:r.__("None","masterstudy-lms-learning-management-system"),value:"none"}]};return W(h,b)?(0,a.createElement)("div",{className:Zt},(0,a.createElement)(bt,{name:t,label:r.__("Size","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:m,isAdaptive:y}),(0,a.createElement)(Gt,{name:n,label:r.__("Weight","masterstudy-lms-learning-management-system"),options:v}),(0,a.createElement)(Gt,{name:l,label:r.__("Transform","masterstudy-lms-learning-management-system"),options:_}),(0,a.createElement)(Gt,{name:i,label:r.__("Style","masterstudy-lms-learning-management-system"),options:S}),(0,a.createElement)(Gt,{name:o,label:r.__("Decoration","masterstudy-lms-learning-management-system"),options:C}),(0,a.createElement)(bt,{name:s,label:r.__("Line Height","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:u,isAdaptive:y}),(0,a.createElement)(bt,{name:c,label:r.__("Letter Spacing","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:g,isAdaptive:y}),d&&(0,a.createElement)(bt,{name:d,label:r.__("Word Spacing","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:p,isAdaptive:y})):null},Xt=(w("file-upload"),w("file-upload__wrap"),w("file-upload__image"),w("file-upload__remove"),w("file-upload__replace"),(0,c.createContext)({activeTab:0,setActiveTab:()=>{}})),Yt=()=>{const e=(0,c.useContext)(Xt);if(!e)throw new Error("useTabs should be used inside Tabs");return e},qt=({children:e})=>{const[t,n]=(0,c.useState)(0);return(0,a.createElement)(Xt.Provider,{value:{activeTab:t,setActiveTab:n}},(0,a.createElement)("div",{className:`active-tab-${t}`},e))},Jt=w("tab-list"),Qt=({children:e})=>(0,a.createElement)("div",{className:Jt},c.Children.map(e,((e,t)=>(0,c.cloneElement)(e,{index:t})))),en=w("tab"),tn=w("tab-active"),nn=w("content"),an=({index:e,title:t,icon:n})=>{const{activeTab:r,setActiveTab:l}=Yt();return(0,a.createElement)("div",{className:s()([en],{[tn]:r===e}),onClick:()=>l(e)},(0,a.createElement)("div",{className:nn},(0,a.createElement)("div",null,n),(0,a.createElement)("div",null,t)))},rn=({children:e})=>(0,a.createElement)("div",null,c.Children.map(e,((e,t)=>(0,c.cloneElement)(e,{index:t})))),ln=w("tab-panel"),on=({index:e,children:t})=>{const{activeTab:n}=Yt();return n===e?(0,a.createElement)("div",{className:ln},t):null},sn=({generalTab:e,styleTab:t,advancedTab:n})=>(0,a.createElement)(qt,null,(0,a.createElement)(Qt,null,(0,a.createElement)(an,{title:r.__("General","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(u.Dashicon,{icon:"layout"})}),(0,a.createElement)(an,{title:r.__("Style","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(u.Dashicon,{icon:"admin-appearance"})}),(0,a.createElement)(an,{title:r.__("Advanced","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(u.Dashicon,{icon:"admin-settings"})})),(0,a.createElement)(rn,null,(0,a.createElement)(on,null,e),(0,a.createElement)(on,null,t),(0,a.createElement)(on,null,n)));window.ReactDOM;"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement;function cn(e){const t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function dn(e){return"nodeType"in e}function mn(e){var t,n;return e?cn(e)?e:dn(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function un(e){const{Document:t}=mn(e);return e instanceof t}function gn(e){return!cn(e)&&e instanceof mn(e).HTMLElement}function pn(e){return e instanceof mn(e).SVGElement}function bn(e){return e?cn(e)?e.document:dn(e)?un(e)?e:gn(e)||pn(e)?e.ownerDocument:document:document:document}function hn(e){return function(t){for(var n=arguments.length,a=new Array(n>1?n-1:0),r=1;r<n;r++)a[r-1]=arguments[r];return a.reduce(((t,n)=>{const a=Object.entries(n);for(const[n,r]of a){const a=t[n];null!=a&&(t[n]=a+e*r)}return t}),{...t})}}const yn=hn(-1);function vn(e){if(function(e){if(!e)return!1;const{TouchEvent:t}=mn(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return function(e){return"clientX"in e&&"clientY"in e}(e)?{x:e.clientX,y:e.clientY}:null}var Sn;!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(Sn||(Sn={}));const Cn=Object.freeze({x:0,y:0});var En,fn,Pn,Tn;!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(En||(En={}));class Nn{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach((e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)}))},this.target=e}add(e,t,n){var a;null==(a=this.target)||a.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function xn(e,t){const n=Math.abs(e.x),a=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+a**2)>t:"x"in t&&"y"in t?n>t.x&&a>t.y:"x"in t?n>t.x:"y"in t&&a>t.y}function Hn(e){e.preventDefault()}function Ln(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(fn||(fn={})),(Tn=Pn||(Pn={})).Space="Space",Tn.Down="ArrowDown",Tn.Right="ArrowRight",Tn.Left="ArrowLeft",Tn.Up="ArrowUp",Tn.Esc="Escape",Tn.Enter="Enter";Pn.Space,Pn.Enter,Pn.Esc,Pn.Space,Pn.Enter;function wn(e){return Boolean(e&&"distance"in e)}function Un(e){return Boolean(e&&"delay"in e)}class Mn{constructor(e,t,n){var a;void 0===n&&(n=function(e){const{EventTarget:t}=mn(e);return e instanceof t?e:bn(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;const{event:r}=e,{target:l}=r;this.props=e,this.events=t,this.document=bn(l),this.documentListeners=new Nn(this.document),this.listeners=new Nn(n),this.windowListeners=new Nn(mn(l)),this.initialCoordinates=null!=(a=vn(r))?a:Cn,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),this.windowListeners.add(fn.Resize,this.handleCancel),this.windowListeners.add(fn.DragStart,Hn),this.windowListeners.add(fn.VisibilityChange,this.handleCancel),this.windowListeners.add(fn.ContextMenu,Hn),this.documentListeners.add(fn.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(Un(t))return void(this.timeoutId=setTimeout(this.handleStart,t.delay));if(wn(t))return}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handleStart(){const{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(fn.Click,Ln,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(fn.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;const{activated:n,initialCoordinates:a,props:r}=this,{onMove:l,options:{activationConstraint:i}}=r;if(!a)return;const o=null!=(t=vn(e))?t:Cn,s=yn(a,o);if(!n&&i){if(wn(i)){if(null!=i.tolerance&&xn(s,i.tolerance))return this.handleCancel();if(xn(s,i.distance))return this.handleStart()}return Un(i)&&xn(s,i.tolerance)?this.handleCancel():void 0}e.cancelable&&e.preventDefault(),l(o)}handleEnd(){const{onEnd:e}=this.props;this.detach(),e()}handleCancel(){const{onCancel:e}=this.props;this.detach(),e()}handleKeydown(e){e.code===Pn.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}const On={move:{name:"pointermove"},end:{name:"pointerup"}};(class extends Mn{constructor(e){const{event:t}=e,n=bn(t.target);super(e,On,n)}}).activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;return!(!n.isPrimary||0!==n.button||(null==a||a({event:n}),0))}}];const Rn={move:{name:"mousemove"},end:{name:"mouseup"}};var Bn;!function(e){e[e.RightClick=2]="RightClick"}(Bn||(Bn={})),class extends Mn{constructor(e){super(e,Rn,bn(e.event.target))}}.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;return n.button!==Bn.RightClick&&(null==a||a({event:n}),!0)}}];const Fn={move:{name:"touchmove"},end:{name:"touchend"}};var An,Wn,kn,Dn,In;(class extends Mn{constructor(e){super(e,Fn)}static setup(){return window.addEventListener(Fn.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(Fn.move.name,e)};function e(){}}}).activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;const{touches:r}=n;return!(r.length>1||(null==a||a({event:n}),0))}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(An||(An={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(Wn||(Wn={})),En.Backward,En.Forward,En.Backward,En.Forward,function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(kn||(kn={})),function(e){e.Optimized="optimized"}(Dn||(Dn={})),kn.WhileDragging,Dn.Optimized,Map,function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(In||(In={})),Pn.Down,Pn.Right,Pn.Up,Pn.Left,r.__("Lectures","masterstudy-lms-learning-management-system"),r.__("Duration","masterstudy-lms-learning-management-system"),r.__("Views","masterstudy-lms-learning-management-system"),r.__("Level","masterstudy-lms-learning-management-system"),r.__("Members","masterstudy-lms-learning-management-system"),r.__("Empty","masterstudy-lms-learning-management-system"),w("sortable__item"),w("sortable__item__disabled"),w("sortable__item__content"),w("sortable__item__content__drag-item"),w("sortable__item__content__drag-item__disabled"),w("sortable__item__content__title"),w("sortable__item__control"),w("sortable__item__icon"),w("nested-sortable"),w("nested-sortable__item"),w("sortable");const zn=w("accordion"),Vn=w("accordion__header"),$n=w("accordion__header-flex"),jn=w("accordion__content"),Gn=w("accordion__icon"),Zn=w("accordion__title"),Kn=w("accordion__title-disabled"),Xn=w("accordion__indicator"),Yn=w("accordion__controls"),qn=w("accordion__controls-disabled"),Jn=({title:e,children:t,accordionFields:n,switchName:r,visible:l=!0,isDefaultOpen:i=!1})=>{const{isOpen:o,onToggle:d,disabled:m,onReset:b,hasChanges:h,onClose:y}=((e,t,n)=>{var a;const{isOpen:r,onToggle:l,onClose:i}=O(t),{defaultValues:o,attributes:s,setAttributes:c}=R(),d=((e,t,n)=>{for(const a of n)if(!p(e[a],t[a]))return!0;return!1})(o,s,e);return{isOpen:r,onToggle:l,disabled:!(null===(a=s[n])||void 0===a||a),hasChanges:d,onReset:t=>{t.stopPropagation(),c(e.reduce(((e,t)=>(e[t]=o[t],e)),{}))},onClose:i}})(n,i,r);return((e,t)=>{const{attributes:n}=R(),a=!n[t];(0,c.useEffect)((()=>{a&&e()}),[a,e])})(y,r),l?(0,a.createElement)("div",{className:zn},(0,a.createElement)("div",{className:Vn},(0,a.createElement)("div",{className:$n,onClick:m?null:d},(0,a.createElement)("div",{className:s()(Zn,{[Kn]:m,"with-switch":Boolean(r)})},(0,a.createElement)("div",null,e),(0,a.createElement)(g,{condition:h&&!m},(0,a.createElement)("div",{className:Xn}))),(0,a.createElement)("div",{className:s()(Yn,{[qn]:m})},(0,a.createElement)(u.Dashicon,{icon:o?"arrow-up-alt2":"arrow-down-alt2",className:Gn,size:16}))),(0,a.createElement)(g,{condition:Boolean(r)},(0,a.createElement)(yt,{name:r})),(0,a.createElement)(g,{condition:h&&!m},(0,a.createElement)(Pe,{onReset:b}))),o&&(0,a.createElement)("div",{className:jn},t)):null};w("preset-picker"),w("preset-picker__label"),w("preset-picker__remove"),w("preset-picker__presets-list"),w("preset-picker__presets-list__item"),w("preset-picker__presets-list__item__preset"),w("preset-picker__presets-list__item__preset-active");const Qn={layoutMargin:H,layoutMarginTablet:H,layoutMarginMobile:H,layoutMarginUnit:"px",layoutMarginUnitTablet:"px",layoutMarginUnitMobile:"px",layoutPadding:{top:"100",right:"0",bottom:"100",left:"0"},layoutPaddingTablet:{top:"70",right:"20",bottom:"70",left:"20"},layoutPaddingMobile:{top:"50",right:"20",bottom:"50",left:"20"},layoutPaddingUnit:"px",layoutPaddingUnitTablet:"px",layoutPaddingUnitMobile:"px",bundlesGap:30,bundlesGapTablet:null,bundlesGapMobile:null,bundlesGapUnit:"px",bundlesGapUnitTablet:"px",bundlesGapUnitMobile:"px",layoutBgColor:"",layoutBorderStyle:"none",layoutBorderStyleTablet:"",layoutBorderStyleMobile:"",layoutBorderColor:"",layoutBorderColorTablet:"",layoutBorderColorMobile:"",layoutBorderWidth:H,layoutBorderWidthTablet:H,layoutBorderWidthMobile:H,layoutBorderWidthUnit:"px",layoutBorderWidthUnitTablet:"px",layoutBorderWidthUnitMobile:"px",layoutBorderRadius:H,layoutBorderRadiusTablet:H,layoutBorderRadiusMobile:H,layoutBorderRadiusUnit:"px",layoutBorderRadiusUnitTablet:"px",layoutBorderRadiusUnitMobile:"px",layoutWidth:"alignfull",layoutContentWidth:1170,layoutContentWidthUnit:"px",layoutZIndex:null,layoutZIndexTablet:null,layoutZIndexMobile:null},ea=Object.keys(Qn),ta={cardBorderStyle:"solid",cardBorderStyleTablet:"",cardBorderStyleMobile:"",cardBorderStyleHover:"solid",cardBorderStyleHoverTablet:"",cardBorderStyleHoverMobile:"",cardBorderColor:"#dbe0e9",cardBorderColorTablet:"",cardBorderColorMobile:"",cardBorderColorHover:"#dbe0e9",cardBorderColorHoverTablet:"",cardBorderColorHoverMobile:"",cardBorderWidth:{top:"1",right:"1",bottom:"1",left:"1"},cardBorderWidthTablet:H,cardBorderWidthMobile:H,cardBorderWidthHover:{top:"1",right:"1",bottom:"1",left:"1"},cardBorderWidthHoverTablet:H,cardBorderWidthHoverMobile:H,cardBorderWidthUnit:"px",cardBorderWidthUnitTablet:"px",cardBorderWidthUnitMobile:"px",cardBorderRadius:H,cardBorderRadiusTablet:H,cardBorderRadiusMobile:H,cardBorderRadiusUnit:"px",cardBorderRadiusUnitTablet:"px",cardBorderRadiusUnitMobile:"px",cardShadowColor:"#00000000",cardShadowColorTablet:"",cardShadowColorMobile:"",cardShadowHorizontal:null,cardShadowHorizontalTablet:null,cardShadowHorizontalMobile:null,cardShadowVertical:null,cardShadowVerticalTablet:null,cardShadowVerticalMobile:null,cardShadowBlur:null,cardShadowBlurTablet:null,cardShadowBlurMobile:null,cardShadowSpread:null,cardShadowSpreadTablet:null,cardShadowSpreadMobile:null,cardShadowInset:!1,cardShadowInsetTablet:!1,cardShadowInsetMobile:!1,cardShadowColorHover:"#00000019",cardShadowColorHoverTablet:"",cardShadowColorHoverMobile:"",cardShadowHorizontalHover:0,cardShadowHorizontalHoverTablet:null,cardShadowHorizontalHoverMobile:null,cardShadowVerticalHover:0,cardShadowVerticalHoverTablet:null,cardShadowVerticalHoverMobile:null,cardShadowBlurHover:25,cardShadowBlurHoverTablet:null,cardShadowBlurHoverMobile:null,cardShadowSpreadHover:0,cardShadowSpreadHoverTablet:null,cardShadowSpreadHoverMobile:null,cardShadowInsetHover:!1,cardShadowInsetHoverTablet:!1,cardShadowInsetHoverMobile:!1},na=Object.keys(ta),aa={cardHeaderFontSize:18,cardHeaderFontSizeTablet:null,cardHeaderFontSizeMobile:null,cardHeaderFontSizeUnit:"px",cardHeaderFontSizeUnitTablet:"px",cardHeaderFontSizeUnitMobile:"px",cardHeaderFontWeight:"600",cardHeaderTextTransform:"inherit",cardHeaderFontStyle:"inherit",cardHeaderTextDecoration:"inherit",cardHeaderLineHeight:24,cardHeaderLineHeightTablet:null,cardHeaderLineHeightMobile:null,cardHeaderLineHeightUnit:"px",cardHeaderLineHeightUnitTablet:"px",cardHeaderLineHeightUnitMobile:"px",cardHeaderLetterSpacing:0,cardHeaderLetterSpacingTablet:null,cardHeaderLetterSpacingMobile:null,cardHeaderLetterSpacingUnit:"px",cardHeaderLetterSpacingUnitTablet:"px",cardHeaderLetterSpacingUnitMobile:"px",cardHeaderWordSpacing:0,cardHeaderWordSpacingTablet:null,cardHeaderWordSpacingMobile:null,cardHeaderWordSpacingUnit:"px",cardHeaderWordSpacingUnitTablet:"px",cardHeaderWordSpacingUnitMobile:"px",cardHeaderCountFontSize:14,cardHeaderCountFontSizeTablet:null,cardHeaderCountFontSizeMobile:null,cardHeaderCountFontSizeUnit:"px",cardHeaderCountFontSizeUnitTablet:"px",cardHeaderCountFontSizeUnitMobile:"px",cardHeaderCountFontWeight:"400",cardHeaderCountTextTransform:"inherit",cardHeaderCountFontStyle:"inherit",cardHeaderCountTextDecoration:"inherit",cardHeaderCountLineHeight:20,cardHeaderCountLineHeightTablet:null,cardHeaderCountLineHeightMobile:null,cardHeaderCountLineHeightUnit:"px",cardHeaderCountLineHeightUnitTablet:"px",cardHeaderCountLineHeightUnitMobile:"px",cardHeaderCountLetterSpacing:0,cardHeaderCountLetterSpacingTablet:null,cardHeaderCountLetterSpacingMobile:null,cardHeaderCountLetterSpacingUnit:"px",cardHeaderCountLetterSpacingUnitTablet:"px",cardHeaderCountLetterSpacingUnitMobile:"px",cardHeaderCountWordSpacing:0,cardHeaderCountWordSpacingTablet:null,cardHeaderCountWordSpacingMobile:null,cardHeaderCountWordSpacingUnit:"px",cardHeaderCountWordSpacingUnitTablet:"px",cardHeaderCountWordSpacingUnitMobile:"px",cardHeaderColor:"#ffffff",cardHeaderCountColor:"#ffffff",cardHeaderBgColor:"#166fcb",cardHeaderPadding:{top:"27",right:"20",bottom:"20",left:"20"},cardHeaderPaddingTablet:H,cardHeaderPaddingMobile:H,cardHeaderPaddingUnit:"px",cardHeaderPaddingUnitTablet:"px",cardHeaderPaddingUnitMobile:"px"},ra=Object.keys(aa),la={courseListBackground:"#ffffff",courseListBackgroundHover:"",courseListPadding:{top:"10",right:"20",bottom:"10",left:"20"},courseListPaddingTablet:H,courseListPaddingMobile:H,courseListPaddingUnit:"px",courseListPaddingUnitTablet:"px",courseListPaddingUnitMobile:"px",courseListFadeEffectColor:"#ffffff",courseListFadeIconColor:"#9facb9",courseListSeparatorColor:"#dbe0e9",courseListItemPadding:{top:"10",right:"0",bottom:"10",left:"0"},courseListItemPaddingTablet:H,courseListItemPaddingMobile:H,courseListItemPaddingUnit:"px",courseListItemPaddingUnitTablet:"px",courseListItemPaddingUnitMobile:"px"},ia=Object.keys(la),oa={courseImageHeight:52,courseImageHeightTablet:null,courseImageHeightMobile:null,courseImageHeightUnit:"px",courseImageHeightUnitTablet:"px",courseImageHeightUnitMobile:"px",courseImageWidth:85,courseImageWidthTablet:null,courseImageWidthMobile:null,courseImageWidthUnit:"px",courseImageWidthUnitTablet:"px",courseImageWidthUnitMobile:"px",courseImageBorderStyle:"none",courseImageBorderStyleTablet:"",courseImageBorderStyleMobile:"",courseImageBorderColor:"",courseImageBorderColorTablet:"",courseImageBorderColorMobile:"",courseImageBorderWidth:H,courseImageBorderWidthTablet:H,courseImageBorderWidthMobile:H,courseImageBorderWidthUnit:"px",courseImageBorderWidthUnitTablet:"px",courseImageBorderWidthUnitMobile:"px",courseImageBorderRadius:H,courseImageBorderRadiusTablet:H,courseImageBorderRadiusMobile:H,courseImageBorderRadiusUnit:"px",courseImageBorderRadiusUnitTablet:"px",courseImageBorderRadiusUnitMobile:"px"},sa=Object.keys(oa),ca={courseTitleFontSize:12,courseTitleFontSizeTablet:null,courseTitleFontSizeMobile:null,courseTitleFontSizeUnit:"px",courseTitleFontSizeUnitTablet:"px",courseTitleFontSizeUnitMobile:"px",courseTitleFontWeight:"600",courseTitleTextTransform:"inherit",courseTitleFontStyle:"inherit",courseTitleTextDecoration:"inherit",courseTitleLineHeight:16,courseTitleLineHeightTablet:null,courseTitleLineHeightMobile:null,courseTitleLineHeightUnit:"px",courseTitleLineHeightUnitTablet:"px",courseTitleLineHeightUnitMobile:"px",courseTitleLetterSpacing:0,courseTitleLetterSpacingTablet:null,courseTitleLetterSpacingMobile:null,courseTitleLetterSpacingUnit:"px",courseTitleLetterSpacingUnitTablet:"px",courseTitleLetterSpacingUnitMobile:"px",courseTitleWordSpacing:0,courseTitleWordSpacingTablet:null,courseTitleWordSpacingMobile:null,courseTitleWordSpacingUnit:"px",courseTitleWordSpacingUnitTablet:"px",courseTitleWordSpacingUnitMobile:"px",courseTitleColor:"#001931",courseTitleColorHover:"#166fcb",courseTitleMargin:H,courseTitleMarginTablet:H,courseTitleMarginMobile:H,courseTitleMarginUnit:"px",courseTitleMarginUnitTablet:"px",courseTitleMarginUnitMobile:"px"},da=Object.keys(ca),ma={coursePriceFontSize:14,coursePriceFontSizeTablet:null,coursePriceFontSizeMobile:null,coursePriceFontSizeUnit:"px",coursePriceFontSizeUnitTablet:"px",coursePriceFontSizeUnitMobile:"px",coursePriceFontWeight:"600",coursePriceTextTransform:"inherit",coursePriceFontStyle:"inherit",coursePriceTextDecoration:"inherit",coursePriceLineHeight:20,coursePriceLineHeightTablet:null,coursePriceLineHeightMobile:null,coursePriceLineHeightUnit:"px",coursePriceLineHeightUnitTablet:"px",coursePriceLineHeightUnitMobile:"px",coursePriceLetterSpacing:0,coursePriceLetterSpacingTablet:null,coursePriceLetterSpacingMobile:null,coursePriceLetterSpacingUnit:"px",coursePriceLetterSpacingUnitTablet:"px",coursePriceLetterSpacingUnitMobile:"px",coursePriceWordSpacing:0,coursePriceWordSpacingTablet:null,coursePriceWordSpacingMobile:null,coursePriceWordSpacingUnit:"px",coursePriceWordSpacingUnitTablet:"px",coursePriceWordSpacingUnitMobile:"px",coursePriceColor:"#001931",courseOldPriceFontSize:13,courseOldPriceFontSizeTablet:null,courseOldPriceFontSizeMobile:null,courseOldPriceFontSizeUnit:"px",courseOldPriceFontSizeUnitTablet:"px",courseOldPriceFontSizeUnitMobile:"px",courseOldPriceFontWeight:"400",courseOldPriceTextTransform:"inherit",courseOldPriceFontStyle:"inherit",courseOldPriceTextDecoration:"line-through",courseOldPriceLineHeight:20,courseOldPriceLineHeightTablet:null,courseOldPriceLineHeightMobile:null,courseOldPriceLineHeightUnit:"px",courseOldPriceLineHeightUnitTablet:"px",courseOldPriceLineHeightUnitMobile:"px",courseOldPriceLetterSpacing:0,courseOldPriceLetterSpacingTablet:null,courseOldPriceLetterSpacingMobile:null,courseOldPriceLetterSpacingUnit:"px",courseOldPriceLetterSpacingUnitTablet:"px",courseOldPriceLetterSpacingUnitMobile:"px",courseOldPriceWordSpacing:0,courseOldPriceWordSpacingTablet:null,courseOldPriceWordSpacingMobile:null,courseOldPriceWordSpacingUnit:"px",courseOldPriceWordSpacingUnitTablet:"px",courseOldPriceWordSpacingUnitMobile:"px",courseOldPriceColor:"#4d5e6f",coursePriceLayout:"row",coursePriceMargin:H,coursePriceMarginTablet:H,coursePriceMarginMobile:H,coursePriceMarginUnit:"px",coursePriceMarginUnitTablet:"px",coursePriceMarginUnitMobile:"px"},ua=Object.keys(ma),ga={bundleRatingFontSize:15,bundleRatingFontSizeTablet:null,bundleRatingFontSizeMobile:null,bundleRatingFontSizeUnit:"px",bundleRatingFontSizeUnitTablet:"px",bundleRatingFontSizeUnitMobile:"px",bundleRatingFontWeight:"700",bundleRatingTextTransform:"inherit",bundleRatingFontStyle:"inherit",bundleRatingTextDecoration:"inherit",bundleRatingLineHeight:20,bundleRatingLineHeightTablet:null,bundleRatingLineHeightMobile:null,bundleRatingLineHeightUnit:"px",bundleRatingLineHeightUnitTablet:"px",bundleRatingLineHeightUnitMobile:"px",bundleRatingLetterSpacing:0,bundleRatingLetterSpacingTablet:null,bundleRatingLetterSpacingMobile:null,bundleRatingLetterSpacingUnit:"px",bundleRatingLetterSpacingUnitTablet:"px",bundleRatingLetterSpacingUnitMobile:"px",bundleRatingWordSpacing:0,bundleRatingWordSpacingTablet:null,bundleRatingWordSpacingMobile:null,bundleRatingWordSpacingUnit:"px",bundleRatingWordSpacingUnitTablet:"px",bundleRatingWordSpacingUnitMobile:"px",bundleRatingColor:"#001931",bundleRatingColorEmpty:"#b3bac2",bundleRatingColorFilled:"#ffa800",bundleRatingMargin:H,bundleRatingMarginTablet:H,bundleRatingMarginMobile:H,bundleRatingMarginUnit:"px",bundleRatingMarginUnitTablet:"px",bundleRatingMarginUnitMobile:"px"},pa=Object.keys(ga),ba={bundlePriceFontSize:15,bundlePriceFontSizeTablet:null,bundlePriceFontSizeMobile:null,bundlePriceFontSizeUnit:"px",bundlePriceFontSizeUnitTablet:"px",bundlePriceFontSizeUnitMobile:"px",bundlePriceFontWeight:"500",bundlePriceTextTransform:"inherit",bundlePriceFontStyle:"inherit",bundlePriceTextDecoration:"inherit",bundlePriceLineHeight:20,bundlePriceLineHeightTablet:null,bundlePriceLineHeightMobile:null,bundlePriceLineHeightUnit:"px",bundlePriceLineHeightUnitTablet:"px",bundlePriceLineHeightUnitMobile:"px",bundlePriceLetterSpacing:0,bundlePriceLetterSpacingTablet:null,bundlePriceLetterSpacingMobile:null,bundlePriceLetterSpacingUnit:"px",bundlePriceLetterSpacingUnitTablet:"px",bundlePriceLetterSpacingUnitMobile:"px",bundlePriceWordSpacing:0,bundlePriceWordSpacingTablet:null,bundlePriceWordSpacingMobile:null,bundlePriceWordSpacingUnit:"px",bundlePriceWordSpacingUnitTablet:"px",bundlePriceWordSpacingUnitMobile:"px",bundlePriceColor:"#001931",bundleOldPriceFontSize:13,bundleOldPriceFontSizeTablet:null,bundleOldPriceFontSizeMobile:null,bundleOldPriceFontSizeUnit:"px",bundleOldPriceFontSizeUnitTablet:"px",bundleOldPriceFontSizeUnitMobile:"px",bundleOldPriceFontWeight:"400",bundleOldPriceTextTransform:"inherit",bundleOldPriceFontStyle:"inherit",bundleOldPriceTextDecoration:"line-through",bundleOldPriceLineHeight:20,bundleOldPriceLineHeightTablet:null,bundleOldPriceLineHeightMobile:null,bundleOldPriceLineHeightUnit:"px",bundleOldPriceLineHeightUnitTablet:"px",bundleOldPriceLineHeightUnitMobile:"px",bundleOldPriceLetterSpacing:0,bundleOldPriceLetterSpacingTablet:null,bundleOldPriceLetterSpacingMobile:null,bundleOldPriceLetterSpacingUnit:"px",bundleOldPriceLetterSpacingUnitTablet:"px",bundleOldPriceLetterSpacingUnitMobile:"px",bundleOldPriceWordSpacing:0,bundleOldPriceWordSpacingTablet:null,bundleOldPriceWordSpacingMobile:null,bundleOldPriceWordSpacingUnit:"px",bundleOldPriceWordSpacingUnitTablet:"px",bundleOldPriceWordSpacingUnitMobile:"px",bundleOldPriceColor:"#001931",bundlePriceLayout:"row",bundlePriceMargin:H,bundlePriceMarginTablet:H,bundlePriceMarginMobile:H,bundlePriceMarginUnit:"px",bundlePriceMarginUnitTablet:"px",bundlePriceMarginUnitMobile:"px"},ha=Object.keys(ba),ya={bundlesPerPage:6,bundlesPerRow:3,bundlesPerRowTablet:2,bundlesPerRowMobile:1,bundlesOrderBy:"date_high",bundlesValues:[],bundlesAlignment:"start"},va=Object.keys(ya),_a=window.wp.apiFetch;var Sa=n.n(_a);const Ca={...{...ya},...{...Qn,...ta,...aa,...la,...oa,...ca,...ma,...ga,...ba}},Ea=new Map([["layoutMargin",{unit:"layoutMarginUnit",isAdaptive:!0}],["layoutPadding",{unit:"layoutPaddingUnit",isAdaptive:!0}],["bundlesGap",{unit:"bundlesGapUnit",isAdaptive:!0}],["layoutBgColor",{}],["layoutBorderStyle",{isAdaptive:!0}],["layoutBorderColor",{isAdaptive:!0}],["layoutBorderWidth",{isAdaptive:!0,unit:"layoutBorderWidthUnit"}],["layoutBorderRadius",{isAdaptive:!0,unit:"layoutBorderRadiusUnit"}],["layoutContentWidth",{unit:"layoutContentWidthUnit"}],["layoutZIndex",{isAdaptive:!0}],["cardBorderStyle",{isAdaptive:!0,hasHover:!0}],["cardBorderColor",{isAdaptive:!0,hasHover:!0}],["cardBorderWidth",{isAdaptive:!0,hasHover:!0,unit:"cardBorderWidthUnit"}],["cardBorderRadius",{isAdaptive:!0,unit:"cardBorderRadiusUnit"}],["cardHeaderFontSize",{unit:"cardHeaderFontSizeUnit",isAdaptive:!0}],["cardHeaderFontWeight",{}],["cardHeaderTextTransform",{}],["cardHeaderFontStyle",{}],["cardHeaderTextDecoration",{}],["cardHeaderLineHeight",{unit:"cardHeaderLineHeightUnit",isAdaptive:!0}],["cardHeaderLetterSpacing",{unit:"cardHeaderLetterSpacingUnit",isAdaptive:!0}],["cardHeaderWordSpacing",{unit:"cardHeaderWordSpacingUnit",isAdaptive:!0}],["cardHeaderCountFontSize",{unit:"cardHeaderCountFontSizeUnit",isAdaptive:!0}],["cardHeaderCountFontWeight",{}],["cardHeaderCountTextTransform",{}],["cardHeaderCountFontStyle",{}],["cardHeaderCountTextDecoration",{}],["cardHeaderCountLineHeight",{unit:"cardHeaderCountLineHeightUnit",isAdaptive:!0}],["cardHeaderCountLetterSpacing",{unit:"cardHeaderCountLetterSpacingUnit",isAdaptive:!0}],["cardHeaderCountWordSpacing",{unit:"cardHeaderCountWordSpacingUnit",isAdaptive:!0}],["cardHeaderColor",{}],["cardHeaderCountColor",{}],["cardHeaderBgColor",{}],["cardHeaderPadding",{unit:"cardHeaderPaddingUnit",isAdaptive:!0}],["courseListBackground",{hasHover:!0}],["courseListPadding",{unit:"courseListPaddingUnit",hasHover:!0}],["courseListFadeIconColor",{}],["courseListSeparatorColor",{}],["courseListItemPadding",{unit:"courseListItemPaddingUnit",hasHover:!0}],["courseImageHeight",{isAdaptive:!0,unit:"courseImageHeightUnit"}],["courseImageWidth",{isAdaptive:!0,unit:"courseImageWidthUnit"}],["courseImageBorderStyle",{isAdaptive:!0}],["courseImageBorderColor",{isAdaptive:!0}],["courseImageBorderWidth",{isAdaptive:!0,unit:"courseImageBorderWidthUnit"}],["courseImageBorderRadius",{isAdaptive:!0,unit:"courseImageBorderRadiusUnit"}],["courseTitleFontSize",{unit:"courseTitleFontSizeUnit",isAdaptive:!0}],["courseTitleFontWeight",{}],["courseTitleTextTransform",{}],["courseTitleFontStyle",{}],["courseTitleTextDecoration",{}],["courseTitleLineHeight",{unit:"courseTitleLineHeightUnit",isAdaptive:!0}],["courseTitleLetterSpacing",{unit:"courseTitleLetterSpacingUnit",isAdaptive:!0}],["courseTitleWordSpacing",{unit:"courseTitleWordSpacingUnit",isAdaptive:!0}],["courseTitleColor",{hasHover:!0}],["courseTitleMargin",{unit:"courseTitleMarginUnit",isAdaptive:!0}],["coursePriceFontSize",{unit:"coursePriceFontSizeUnit",isAdaptive:!0}],["coursePriceFontWeight",{}],["coursePriceTextTransform",{}],["coursePriceFontStyle",{}],["coursePriceTextDecoration",{}],["coursePriceLineHeight",{unit:"coursePriceLineHeightUnit",isAdaptive:!0}],["coursePriceLetterSpacing",{unit:"coursePriceLetterSpacingUnit",isAdaptive:!0}],["coursePriceWordSpacing",{unit:"coursePriceWordSpacingUnit",isAdaptive:!0}],["coursePriceColor",{}],["courseOldPriceFontSize",{unit:"courseOldPriceFontSizeUnit",isAdaptive:!0}],["courseOldPriceFontWeight",{}],["courseOldPriceTextTransform",{}],["courseOldPriceFontStyle",{}],["courseOldPriceTextDecoration",{}],["courseOldPriceLineHeight",{unit:"courseOldPriceLineHeightUnit",isAdaptive:!0}],["courseOldPriceLetterSpacing",{unit:"courseOldPriceLetterSpacingUnit",isAdaptive:!0}],["courseOldPriceWordSpacing",{unit:"courseOldPriceWordSpacingUnit",isAdaptive:!0}],["courseOldPriceColor",{}],["coursePriceMargin",{unit:"coursePriceMarginUnit",isAdaptive:!0}],["coursePriceLayout",{isAdaptive:!0}],["bundleRatingFontSize",{unit:"bundleRatingFontSizeUnit",isAdaptive:!0}],["bundleRatingFontWeight",{}],["bundleRatingTextTransform",{}],["bundleRatingFontStyle",{}],["bundleRatingTextDecoration",{}],["bundleRatingLineHeight",{unit:"bundleRatingLineHeightUnit",isAdaptive:!0}],["bundleRatingLetterSpacing",{unit:"bundleRatingLetterSpacingUnit",isAdaptive:!0}],["bundleRatingWordSpacing",{unit:"bundleRatingWordSpacingUnit",isAdaptive:!0}],["bundleRatingColor",{}],["bundleRatingColorEmpty",{}],["bundleRatingColorFilled",{}],["bundleRatingMargin",{unit:"bundleRatingMarginUnit",isAdaptive:!0}],["bundlePriceFontSize",{unit:"bundlePriceFontSizeUnit",isAdaptive:!0}],["bundlePriceFontWeight",{}],["bundlePriceTextTransform",{}],["bundlePriceFontStyle",{}],["bundlePriceTextDecoration",{}],["bundlePriceLineHeight",{unit:"bundlePriceLineHeightUnit",isAdaptive:!0}],["bundlePriceLetterSpacing",{unit:"bundlePriceLetterSpacingUnit",isAdaptive:!0}],["bundlePriceWordSpacing",{unit:"bundlePriceWordSpacingUnit",isAdaptive:!0}],["bundlePriceColor",{}],["bundleOldPriceFontSize",{unit:"bundleOldPriceFontSizeUnit",isAdaptive:!0}],["bundleOldPriceFontWeight",{}],["bundleOldPriceTextTransform",{}],["bundleOldPriceFontStyle",{}],["bundleOldPriceTextDecoration",{}],["bundleOldPriceLineHeight",{unit:"bundleOldPriceLineHeightUnit",isAdaptive:!0}],["bundleOldPriceLetterSpacing",{unit:"bundleOldPriceLetterSpacingUnit",isAdaptive:!0}],["bundleOldPriceWordSpacing",{unit:"bundleOldPriceWordSpacingUnit",isAdaptive:!0}],["bundleOldPriceColor",{}],["bundlePriceMargin",{unit:"bundlePriceMarginUnit",isAdaptive:!0}],["bundlePriceLayout",{isAdaptive:!0}]]),fa=()=>{const{min:e,max:t}=((e,t=!1)=>{const n=M(),[a,r]=(0,c.useState)(e.default||{min:3,max:6});return(0,c.useEffect)((()=>{if(n===y.DESKTOP){const n=e.desktop||{min:t?2:3,max:6};r(n)}if(n===y.TABLET){const n=e.tablet||{min:t?1:2,max:3};r(n)}if(n===y.MOBILE){const t=e.mobile||{min:1,max:1};r(t)}}),[n,t,e]),a})({default:{min:3,max:6}}),{bundlesList:n}=(()=>{const{bundles:e,isFetching:t,error:n}=((e,t=!1)=>{const[n,a]=(0,c.useState)([]),{setIsFetching:r,setError:l,isFetching:i,error:o}=(()=>{const[e,t]=(0,c.useState)(!0),[n,a]=(0,c.useState)("");return{isFetching:e,setIsFetching:t,error:n,setError:a}})();return(0,c.useEffect)((()=>{(""!==e||t)&&(r(!0),(async e=>{try{return await Sa()({path:`masterstudy-lms/v2/course-bundles?${e}`})}catch(e){throw new Error(e)}})(e).then((e=>{a((e=>e.bundles.map((({bundle_info:e,bundle_courses:t})=>({bundleCourses:Object.values(t).map((e=>({id:e.id,cover:e.image,featured:"on"===e.is_featured,permalink:e.link,price:e.price,salePrice:e.sale_price,postTitle:e.title,views:String(e.views)}))),bundleInfo:{id:e.id,title:e.title,priceBundle:e.price,priceCourses:e.courses_price,url:e.url,ratingVisibility:e.rating_visibility,rating:e.rating.count>0?Math.round(e.rating.average/e.rating.count*10)/10:0,reviews:e.rating.count}}))))(e))})).catch((e=>{l(e.message)})).finally((()=>{r(!1)})))}),[e,t]),{bundles:n,isFetching:i,error:o}})("",!0);return{bundlesList:e.map((e=>({label:e.bundleInfo.title,value:e.bundleInfo.id}))),isFetching:t,error:n}})();return(0,a.createElement)(a.Fragment,null,(0,a.createElement)(Jn,{title:r.__("Layout","masterstudy-lms-learning-management-system"),accordionFields:va},(0,a.createElement)(bt,{name:"bundlesPerPage",label:r.__("Number of bundles","masterstudy-lms-learning-management-system"),min:2,max:12}),(0,a.createElement)(bt,{name:"bundlesPerRow",label:r.__("Bundles per row","masterstudy-lms-learning-management-system"),min:e,max:t,isAdaptive:!0}),(0,a.createElement)(kt,{name:"bundlesValues",label:r.__("Select bundles","masterstudy-lms-learning-management-system"),options:n,multiple:!0}),(0,a.createElement)(kt,{name:"bundlesOrderBy",label:r.__("Order by","masterstudy-lms-learning-management-system"),options:L}),(0,a.createElement)(Je,{name:"bundlesAlignment",label:r.__("Alignment","masterstudy-lms-learning-management-system"),options:[{label:(0,a.createElement)(u.Dashicon,{icon:"align-pull-left"}),value:"start"},{label:(0,a.createElement)(u.Dashicon,{icon:"align-center"}),value:"center"},{label:(0,a.createElement)(u.Dashicon,{icon:"align-pull-right"}),value:"end"}]})))},Pa=[{value:"row",label:r.__("Row","masterstudy-lms-learning-management-system")},{value:"row-reverse",label:r.__("Row reverse","masterstudy-lms-learning-management-system")},{value:"column",label:r.__("Column","masterstudy-lms-learning-management-system")},{value:"column-reverse",label:r.__("Column reverse","masterstudy-lms-learning-management-system")}],Ta=()=>(0,a.createElement)(a.Fragment,null,(0,a.createElement)(Jn,{title:r.__("Layout","masterstudy-lms-learning-management-system"),accordionFields:ea},(0,a.createElement)(Ze,{name:"layoutMargin",label:r.__("Margin","masterstudy-lms-learning-management-system"),unitName:"layoutMarginUnit",isAdaptive:!0,dependencies:[{name:"layoutWidth",value:"auto"}]}),(0,a.createElement)(Ze,{name:"layoutPadding",label:r.__("Padding","masterstudy-lms-learning-management-system"),unitName:"layoutPaddingUnit",isAdaptive:!0}),(0,a.createElement)(Pt,{name:"bundlesGap",label:r.__("Space Between Cards","masterstudy-lms-learning-management-system"),unitName:"bundlesGapUnit",isAdaptive:!0}),(0,a.createElement)(ke,{name:"layoutBgColor",label:r.__("Background Color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(at,{label:r.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"layoutBorderStyle",borderColorName:"layoutBorderColor",borderWidthName:"layoutBorderWidth",isAdaptive:!0}),(0,a.createElement)(it,{name:"layoutBorderRadius",label:r.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,a.createElement)(Vt,{name:"layoutWidth",label:r.__("Width","masterstudy-lms-learning-management-system"),options:[{label:r.__("Auto","masterstudy-lms-learning-management-system"),value:r.__("auto","masterstudy-lms-learning-management-system")},{label:r.__("Align full","masterstudy-lms-learning-management-system"),value:r.__("alignfull","masterstudy-lms-learning-management-system")}]}),(0,a.createElement)(Pt,{name:"layoutContentWidth",label:r.__("Content Max Width","masterstudy-lms-learning-management-system"),unitName:"layoutContentWidthUnit"}),(0,a.createElement)(bt,{name:"layoutZIndex",label:r.__("Z-index","masterstudy-lms-learning-management-system"),min:0,max:100})),(0,a.createElement)(Jn,{title:r.__("Card","masterstudy-lms-learning-management-system"),accordionFields:na},(0,a.createElement)(at,{label:r.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"cardBorderStyle",borderColorName:"cardBorderColor",borderWidthName:"cardBorderWidth",isAdaptive:!0,hasHover:!0}),(0,a.createElement)(it,{name:"cardBorderRadius",label:r.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,a.createElement)(Ct,{label:r.__("Select Preset","masterstudy-lms-learning-management-system"),min:0,max:100,shadowColorName:"cardShadowColor",shadowHorizontalName:"cardShadowHorizontal",shadowVerticalName:"cardShadowVertical",shadowBlurName:"cardShadowBlur",shadowSpreadName:"cardShadowSpread",shadowInsetName:"cardShadowInset",popoverContent:null,hasHover:!0,isAdaptive:!0})),(0,a.createElement)(Jn,{title:r.__("Card: Header","masterstudy-lms-learning-management-system"),accordionFields:ra},(0,a.createElement)(u.__experimentalHeading,{level:"3",upperCase:!1,style:{marginBlock:"0px",color:"#4D5E6F"}},r.__("Title Typography","masterstudy-lms-learning-management-system")),(0,a.createElement)(Kt,{fontSizeName:"cardHeaderFontSize",fontSizeUnitName:"cardHeaderFontSizeUnit",fontWeightName:"cardHeaderFontWeight",textTransformName:"cardHeaderTextTransform",fontStyleName:"cardHeaderFontStyle",textDecorationName:"cardHeaderTextDecoration",lineHeightName:"cardHeaderLineHeight",lineHeightUnitName:"cardHeaderLineHeightUnit",letterSpacingName:"cardHeaderLetterSpacing",letterSpacingUnitName:"cardHeaderLetterSpacingUnit",wordSpacingName:"cardHeaderWordSpacing",wordSpacingUnitName:"cardHeaderWordSpacingUnit",isAdaptive:!0}),(0,a.createElement)(ke,{name:"cardHeaderColor",label:r.__("Title Color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(u.__experimentalHeading,{level:"3",upperCase:!1,style:{marginBlock:"0px",color:"#4D5E6F"}},r.__("Courses Count Typography","masterstudy-lms-learning-management-system")),(0,a.createElement)(Kt,{fontSizeName:"cardHeaderCountFontSize",fontSizeUnitName:"cardHeaderCountFontSizeUnit",fontWeightName:"cardHeaderCountFontWeight",textTransformName:"cardHeaderCountTextTransform",fontStyleName:"cardHeaderCountFontStyle",textDecorationName:"cardHeaderCountTextDecoration",lineHeightName:"cardHeaderCountLineHeight",lineHeightUnitName:"cardHeaderCountLineHeightUnit",letterSpacingName:"cardHeaderCountLetterSpacing",letterSpacingUnitName:"cardHeaderCountLetterSpacingUnit",wordSpacingName:"cardHeaderCountWordSpacing",wordSpacingUnitName:"cardHeaderCountWordSpacingUnit",isAdaptive:!0}),(0,a.createElement)(ke,{name:"cardHeaderCountColor",label:r.__("Courses Count Color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(ke,{name:"cardHeaderBgColor",label:r.__("Background Color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Ze,{name:"cardHeaderPadding",label:r.__("Padding","masterstudy-lms-learning-management-system"),unitName:"cardHeaderPaddingUnit",isAdaptive:!0})),(0,a.createElement)(Jn,{title:r.__("Card: Courses List Block","masterstudy-lms-learning-management-system"),accordionFields:ia},(0,a.createElement)(ke,{name:"courseListBackground",label:r.__("Background Color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,a.createElement)(Ze,{name:"courseListPadding",label:r.__("Padding","masterstudy-lms-learning-management-system"),unitName:"courseListPaddingUnit",isAdaptive:!0}),(0,a.createElement)(ke,{name:"courseListFadeEffectColor",label:r.__("Fade Effect Color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(ke,{name:"courseListFadeIconColor",label:r.__("Show more Icon Color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(ke,{name:"courseListSeparatorColor",label:r.__("Separator Color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Ze,{name:"courseListItemPadding",label:r.__("Course Item Padding","masterstudy-lms-learning-management-system"),unitName:"courseListItemPaddingUnit",isAdaptive:!0})),(0,a.createElement)(Jn,{title:r.__("Card: Course Image","masterstudy-lms-learning-management-system"),accordionFields:sa},(0,a.createElement)(Pt,{name:"courseImageHeight",label:r.__("Height","masterstudy-lms-learning-management-system"),unitName:"courseImageHeightUnit",isAdaptive:!0}),(0,a.createElement)(Pt,{name:"courseImageWidth",label:r.__("Width","masterstudy-lms-learning-management-system"),unitName:"courseImageWidthUnit",isAdaptive:!0}),(0,a.createElement)(at,{label:r.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"courseImageBorderStyle",borderColorName:"courseImageBorderColor",borderWidthName:"courseImageBorderWidth",isAdaptive:!0}),(0,a.createElement)(it,{name:"courseImageBorderRadius",label:r.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0})),(0,a.createElement)(Jn,{title:r.__("Card: Course Title","masterstudy-lms-learning-management-system"),accordionFields:da},(0,a.createElement)(Kt,{fontSizeName:"courseTitleFontSize",fontSizeUnitName:"courseTitleFontSizeUnit",fontWeightName:"courseTitleFontWeight",textTransformName:"courseTitleTextTransform",fontStyleName:"courseTitleFontStyle",textDecorationName:"courseTitleTextDecoration",lineHeightName:"courseTitleLineHeight",lineHeightUnitName:"courseTitleLineHeightUnit",letterSpacingName:"courseTitleLetterSpacing",letterSpacingUnitName:"courseTitleLetterSpacingUnit",wordSpacingName:"courseTitleWordSpacing",wordSpacingUnitName:"courseTitleWordSpacingUnit",isAdaptive:!0}),(0,a.createElement)(ke,{name:"courseTitleColor",label:r.__("Color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,a.createElement)(Ze,{name:"courseTitleMargin",label:r.__("Margin","masterstudy-lms-learning-management-system"),unitName:"courseTitleMarginUnit",isAdaptive:!0})),(0,a.createElement)(Jn,{title:r.__("Card: Course Price","masterstudy-lms-learning-management-system"),accordionFields:ua},(0,a.createElement)(Vt,{name:"coursePriceLayout",label:r.__("Layout","masterstudy-lms-learning-management-system"),options:Pa,isAdaptive:!0}),(0,a.createElement)(u.__experimentalHeading,{level:"3",upperCase:!1,style:{marginBlock:"0px",color:"#4D5E6F"}},r.__("Price Typography","masterstudy-lms-learning-management-system")),(0,a.createElement)(Kt,{fontSizeName:"coursePriceFontSize",fontSizeUnitName:"coursePriceFontSizeUnit",fontWeightName:"coursePriceFontWeight",textTransformName:"coursePriceTextTransform",fontStyleName:"coursePriceFontStyle",textDecorationName:"coursePriceTextDecoration",lineHeightName:"coursePriceLineHeight",lineHeightUnitName:"coursePriceLineHeightUnit",letterSpacingName:"coursePriceLetterSpacing",letterSpacingUnitName:"coursePriceLetterSpacingUnit",wordSpacingName:"coursePriceWordSpacing",wordSpacingUnitName:"coursePriceWordSpacingUnit",isAdaptive:!0}),(0,a.createElement)(ke,{name:"coursePriceColor",label:r.__("Price Color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(u.__experimentalHeading,{level:"3",upperCase:!1,style:{marginBlock:"0px",color:"#4D5E6F"}},r.__("Old Price Typography","masterstudy-lms-learning-management-system")),(0,a.createElement)(Kt,{fontSizeName:"courseOldPriceFontSize",fontSizeUnitName:"courseOldPriceFontSizeUnit",fontWeightName:"courseOldPriceFontWeight",textTransformName:"courseOldPriceTextTransform",fontStyleName:"courseOldPriceFontStyle",textDecorationName:"courseOldPriceTextDecoration",lineHeightName:"courseOldPriceLineHeight",lineHeightUnitName:"courseOldPriceLineHeightUnit",letterSpacingName:"courseOldPriceLetterSpacing",letterSpacingUnitName:"courseOldPriceLetterSpacingUnit",wordSpacingName:"courseOldPriceWordSpacing",wordSpacingUnitName:"courseOldPriceWordSpacingUnit",isAdaptive:!0}),(0,a.createElement)(ke,{name:"courseOldPriceColor",label:r.__("Old Price Color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Ze,{name:"coursePriceMargin",label:r.__("Margin","masterstudy-lms-learning-management-system"),unitName:"coursePriceMarginUnit",isAdaptive:!0})),(0,a.createElement)(Jn,{title:r.__("Card: Bundle's Rating","masterstudy-lms-learning-management-system"),accordionFields:pa},(0,a.createElement)(Kt,{fontSizeName:"bundleRatingFontSize",fontSizeUnitName:"bundleRatingFontSizeUnit",fontWeightName:"bundleRatingFontWeight",textTransformName:"bundleRatingTextTransform",fontStyleName:"bundleRatingFontStyle",textDecorationName:"bundleRatingTextDecoration",lineHeightName:"bundleRatingLineHeight",lineHeightUnitName:"bundleRatingLineHeightUnit",letterSpacingName:"bundleRatingLetterSpacing",letterSpacingUnitName:"bundleRatingLetterSpacingUnit",wordSpacingName:"bundleRatingWordSpacing",wordSpacingUnitName:"bundleRatingWordSpacingUnit",isAdaptive:!0}),(0,a.createElement)(ke,{name:"bundleRatingColor",label:r.__("Color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(ke,{name:"bundleRatingColorEmpty",label:r.__("Empty Starts Color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(ke,{name:"bundleRatingColorFilled",label:r.__("Filled Starts Color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Ze,{name:"bundleRatingMargin",label:r.__("Margin","masterstudy-lms-learning-management-system"),unitName:"bundleRatingMarginUnit",isAdaptive:!0})),(0,a.createElement)(Jn,{title:r.__("Card: Bundle Price","masterstudy-lms-learning-management-system"),accordionFields:ha},(0,a.createElement)(Vt,{name:"bundlePriceLayout",label:r.__("Layout","masterstudy-lms-learning-management-system"),options:Pa,isAdaptive:!0}),(0,a.createElement)(u.__experimentalHeading,{level:"3",upperCase:!1,style:{marginBlock:"0px",color:"#4D5E6F"}},r.__("Price Typography","masterstudy-lms-learning-management-system")),(0,a.createElement)(Kt,{fontSizeName:"bundlePriceFontSize",fontSizeUnitName:"bundlePriceFontSizeUnit",fontWeightName:"bundlePriceFontWeight",textTransformName:"bundlePriceTextTransform",fontStyleName:"bundlePriceFontStyle",textDecorationName:"bundlePriceTextDecoration",lineHeightName:"bundlePriceLineHeight",lineHeightUnitName:"bundlePriceLineHeightUnit",letterSpacingName:"bundlePriceLetterSpacing",letterSpacingUnitName:"bundlePriceLetterSpacingUnit",wordSpacingName:"bundlePriceWordSpacing",wordSpacingUnitName:"bundlePriceWordSpacingUnit",isAdaptive:!0}),(0,a.createElement)(ke,{name:"bundlePriceColor",label:r.__("Price Color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(u.__experimentalHeading,{level:"3",upperCase:!1,style:{marginBlock:"0px",color:"#4D5E6F"}},r.__("Old Price Typography","masterstudy-lms-learning-management-system")),(0,a.createElement)(Kt,{fontSizeName:"bundleOldPriceFontSize",fontSizeUnitName:"bundleOldPriceFontSizeUnit",fontWeightName:"bundleOldPriceFontWeight",textTransformName:"bundleOldPriceTextTransform",fontStyleName:"bundleOldPriceFontStyle",textDecorationName:"bundleOldPriceTextDecoration",lineHeightName:"bundleOldPriceLineHeight",lineHeightUnitName:"bundleOldPriceLineHeightUnit",letterSpacingName:"bundleOldPriceLetterSpacing",letterSpacingUnitName:"bundleOldPriceLetterSpacingUnit",wordSpacingName:"bundleOldPriceWordSpacing",wordSpacingUnitName:"bundleOldPriceWordSpacingUnit",isAdaptive:!0}),(0,a.createElement)(ke,{name:"bundleOldPriceColor",label:r.__("Old Price Color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Ze,{name:"bundlePriceMargin",label:r.__("Margin","masterstudy-lms-learning-management-system"),unitName:"bundlePriceMarginUnit",isAdaptive:!0}))),Na=({attributes:e,setAttributes:t})=>{const{onResetByFieldName:n,changedFieldsByName:r}=((e,t,n,a=[])=>{const r=(e=>{const t={};return Object.entries(e).forEach((([e,n])=>{e.includes("UAG")||(t[e]=n)})),t})(t),l=!p(e,r),i=((e,t,n)=>{const a=new Map;return n.forEach((n=>{a.has(n)||a.set(n,(()=>t({[n]:e[n]})))})),a})(e,n,a),o=((e,t,n)=>{const a=new Map;return n.forEach((n=>{a.has(n)?a.set(n,!1):a.set(n,!p(e[n],t[n]))})),a})(e,r,a);return{hasChanges:l,onResetByFieldName:i,changedFieldsByName:o}})(Ca,e,t,Object.keys(Ca));return(0,a.createElement)(i.InspectorControls,null,(0,a.createElement)(m,{attributes:e,setAttributes:t,defaultValues:Ca,onResetByFieldName:n,changedFieldsByName:r},(0,a.createElement)(sn,{generalTab:(0,a.createElement)(fa,null),styleTab:(0,a.createElement)(Ta,null),advancedTab:(0,a.createElement)(a.Fragment,null)})))},xa=e=>({...P("",e,Ea),"--lms--courseListFadeEffectColor":e.courseListFadeEffectColor,...N("bundle",e,"cardShadowColor","cardShadowHorizontal","cardShadowVertical","cardShadowBlur","cardShadowSpread","cardShadowInset",!0),...N("bundle-hover",e,"cardShadowColorHover","cardShadowHorizontalHover","cardShadowVerticalHover","cardShadowBlurHover","cardShadowSpreadHover","cardShadowInsetHover",!0)}),Ha=[["core/group",{},[["core/heading",{textAlign:"center",style:{typography:{fontSize:"48px"},color:{text:"#001931"},spacing:{margin:{top:"0px",bottom:"40px"}}},content:r.__("Course Bundles","masterstudy-lms-learning-management-system"),placeholder:r.__("Course Bundles Title","masterstudy-lms-learning-management-system")}]]],["masterstudy/courses-bundles-cards"],["core/group",{className:"lms-courses-group-load-more"},[["masterstudy/courses-load-more"]]]],La=JSON.parse('{"UU":"masterstudy/courses-bundles-container"}');(0,l.registerBlockType)(La.UU,{title:r._x("MasterStudy Courses Bundles","block title","masterstudy-lms-learning-management-system"),description:r._x("Set up how your course bundles will look on the page with this block","block description","masterstudy-lms-learning-management-system"),category:"masterstudy-lms-blocks",icon:{src:(0,a.createElement)("svg",{width:"512",height:"512",viewBox:"0 0 512 512",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,a.createElement)("g",{clipPath:"url(#clip0_3050_88379)"},(0,a.createElement)("path",{opacity:"0.3",fillRule:"evenodd",clipRule:"evenodd",d:"M457.931 368.504H491.746C493.421 368.503 495.071 368.106 496.55 367.349C498.03 366.591 499.294 365.496 500.234 364.16C501.173 362.823 501.759 361.285 501.94 359.68C502.121 358.075 501.891 356.452 501.272 354.952L464.192 269.026C463.514 267.371 462.382 265.925 460.918 264.842C459.453 263.758 457.712 263.078 455.878 262.873L284.486 243.088C282.589 242.839 280.657 243.121 278.921 243.9C277.186 244.678 275.718 245.921 274.694 247.48C273.64 249.015 273.054 250.804 273.004 252.644C272.976 253.628 273.103 254.605 273.374 255.544C273.188 256.292 273.092 257.065 273.092 257.847V490.107C273.108 492.726 274.195 495.233 276.116 497.085C278.038 498.937 280.639 499.984 283.357 500C283.762 500.002 284.167 499.976 284.568 499.921L448.878 480.136C451.374 479.858 453.678 478.704 455.348 476.894C457.018 475.084 457.938 472.744 457.931 470.322V368.504Z",fill:"#227AFF"}),(0,a.createElement)("path",{d:"M255.722 512C255.301 512.003 254.88 511.975 254.463 511.915L73.2791 490.581C70.6845 490.282 68.2909 489.038 66.5555 487.086C64.8201 485.134 63.8645 482.612 63.8711 480V352C63.8711 349.171 64.9949 346.458 66.9953 344.458C68.9957 342.457 71.7088 341.333 74.5378 341.333C77.3668 341.333 80.0799 342.457 82.0803 344.458C84.0807 346.458 85.2045 349.171 85.2045 352V470.528L245.055 489.344V225.92C244.734 224.174 244.865 222.375 245.436 220.695C246.008 219.014 247 217.508 248.319 216.32C249.859 214.91 251.773 213.974 253.832 213.626C255.891 213.278 258.007 213.532 259.924 214.357C263.786 216.064 266.388 219.776 266.388 224V501.333C266.388 504.162 265.265 506.875 263.264 508.876C261.264 510.876 258.551 512 255.722 512Z",fill:"black"}),(0,a.createElement)("path",{d:"M256.151 512C253.327 511.983 250.624 510.854 248.627 508.857C246.63 506.86 245.501 504.157 245.484 501.333V229.333C245.484 226.504 246.608 223.791 248.609 221.791C250.609 219.79 253.322 218.667 256.151 218.667C258.98 218.667 261.693 219.79 263.694 221.791C265.694 223.791 266.818 226.504 266.818 229.333V489.344L426.647 470.528V352C426.647 349.171 427.771 346.458 429.771 344.457C431.772 342.457 434.485 341.333 437.314 341.333C440.143 341.333 442.856 342.457 444.856 344.457C446.857 346.458 447.98 349.171 447.98 352V480C447.987 482.612 447.031 485.134 445.296 487.086C443.561 489.038 441.167 490.282 438.572 490.581L257.41 511.915C256.993 511.974 256.572 512.003 256.151 512Z",fill:"black"}),(0,a.createElement)("path",{d:"M480 362.667H330.603C328.738 362.667 326.906 362.179 325.289 361.251C323.672 360.323 322.326 358.987 321.387 357.376L246.848 229.376C245.843 227.665 245.338 225.706 245.39 223.723C245.443 221.739 246.052 219.81 247.147 218.155C248.211 216.474 249.736 215.134 251.54 214.294C253.344 213.455 255.351 213.151 257.323 213.419L438.571 234.752C440.476 234.973 442.286 235.707 443.808 236.875C445.329 238.043 446.505 239.603 447.211 241.387L489.899 348.053C490.543 349.671 490.781 351.421 490.593 353.152C490.405 354.883 489.797 356.541 488.82 357.982C487.844 359.424 486.53 360.604 484.992 361.421C483.455 362.237 481.741 362.665 480 362.667ZM336.725 341.333H464.256L429.781 255.189L276.032 237.077L336.725 341.333Z",fill:"black"}),(0,a.createElement)("path",{d:"M181.396 362.667H31.9987C30.2533 362.672 28.5335 362.247 26.9908 361.431C25.4481 360.615 24.1297 359.431 23.1519 357.986C22.174 356.54 21.5666 354.876 21.3832 353.14C21.1997 351.405 21.4459 349.65 22.1 348.032L64.788 241.365C65.4952 239.583 66.6718 238.025 68.1929 236.856C69.7141 235.688 71.5231 234.954 73.428 234.731L254.676 213.397C256.642 213.172 258.632 213.498 260.424 214.337C262.217 215.176 263.741 216.497 264.827 218.151C265.913 219.805 266.518 221.729 266.575 223.707C266.632 225.685 266.139 227.64 265.151 229.355L190.612 357.355C189.675 358.969 188.331 360.309 186.713 361.242C185.096 362.174 183.263 362.665 181.396 362.667ZM47.7427 341.333H175.273L235.988 237.077L82.2387 255.189L47.7427 341.333Z",fill:"black"}),(0,a.createElement)("path",{d:"M303.211 112.931C272.872 131.005 263.35 178.205 262.956 180.208C262.248 183.848 264.622 187.373 268.262 188.085C268.695 188.17 269.128 188.209 269.554 188.209C272.702 188.209 275.515 185.986 276.138 182.779C276.224 182.349 284.848 139.505 310.084 124.467C313.271 122.568 314.314 118.446 312.418 115.262C310.516 112.078 306.398 111.032 303.211 112.931ZM343.353 45.1322C340.848 45.6405 338.848 47.5291 338.199 50.0015L333.097 69.3484L314.274 76.1129C311.867 76.9754 310.162 79.1296 309.874 81.6713C309.579 84.2093 310.759 86.6947 312.91 88.0786L329.725 98.9062L330.335 118.905C330.414 121.46 331.942 123.748 334.264 124.808C335.155 125.214 336.106 125.414 337.051 125.414C338.573 125.414 340.074 124.899 341.294 123.903L356.798 111.249L376.006 116.843C378.453 117.564 381.102 116.82 382.833 114.934C384.559 113.049 385.07 110.347 384.145 107.966L376.899 89.3145L388.159 72.7749C389.595 70.6635 389.707 67.9155 388.448 65.6921C387.195 63.4687 384.939 62.1833 382.224 62.2916L362.254 63.4197L350.003 47.6015C348.429 45.578 345.852 44.6307 343.353 45.1322ZM353.794 74.4343C355.152 76.1817 357.191 77.1754 359.487 77.028L369.396 76.4671L363.808 84.678C362.569 86.5046 362.3 88.826 363.1 90.8853L366.694 100.142L357.165 97.3681C355.053 96.7583 352.764 97.214 351.046 98.6109L343.347 104.894L343.045 94.9645C342.973 92.7546 341.825 90.7213 339.963 89.5244L331.62 84.1499L340.96 80.7921C343.039 80.0447 344.619 78.3234 345.183 76.1853L347.715 66.5838L353.794 74.4343ZM189.8 71.8994L172.361 69.9614L164.078 54.4942C162.872 52.2447 160.569 50.864 157.933 50.9525C155.376 51.0379 153.093 52.5661 152.037 54.8973L144.804 70.8864L127.529 73.9885C125.011 74.4405 122.972 76.2838 122.263 78.7395C121.555 81.1958 122.302 83.8421 124.191 85.5635L137.163 97.3812L134.782 114.767C134.435 117.299 135.55 119.807 137.668 121.24C138.803 122.007 140.115 122.394 141.432 122.394C142.574 122.394 143.722 122.103 144.758 121.516L160.005 112.833L175.804 120.476C178.106 121.591 180.841 121.299 182.854 119.725C184.874 118.155 185.825 115.574 185.307 113.072L181.759 95.8859L193.905 83.2223C195.676 81.376 196.246 78.6874 195.374 76.2838C194.502 73.8801 192.344 72.1817 189.8 71.8994ZM169.62 89.1343C168.092 90.7312 167.442 92.9775 167.889 95.1416L169.397 102.457L162.675 99.2046C160.688 98.2468 158.353 98.326 156.425 99.4145L149.939 103.11L150.949 95.7088C151.25 93.5218 150.457 91.3218 148.824 89.8327L143.302 84.8025L150.653 83.4812C152.831 83.0911 154.674 81.6515 155.585 79.6379L158.668 72.8338L162.189 79.415C163.239 81.3661 165.174 82.6744 167.37 82.9171L174.794 83.7437L169.62 89.1343ZM199.682 105.025C227.673 108.619 239.76 143.226 239.878 143.574C240.829 146.371 243.439 148.135 246.239 148.135C246.954 148.135 247.676 148.021 248.39 147.778C251.899 146.591 253.788 142.794 252.607 139.282C252.017 137.534 237.773 96.3713 201.388 91.7051C197.715 91.2197 194.351 93.8364 193.872 97.5093C193.4 101.188 196.003 104.553 199.682 105.025Z",fill:"black"})),(0,a.createElement)("defs",null,(0,a.createElement)("clipPath",{id:"clip0_3050_88379"},(0,a.createElement)("rect",{width:"512",height:"512",fill:"white"}))))},edit:({attributes:e,setAttributes:t})=>{const n=(0,i.useBlockProps)({className:s()("lms-course-bundle is-loaded",{alignfull:"alignfull"===e.layoutWidth},`lms-course-bundle__col-${e.bundlesPerRow}`,`lms-course-bundle__col-tablet-${e.bundlesPerRowTablet}`,`lms-course-bundle__col-phone-${e.bundlesPerRowMobile}`,{"lms-course-bundle--jc-center":"center"===e.bundlesAlignment},{"lms-course-bundle--jc-end":"end"===e.bundlesAlignment}),style:xa(e)}),r=(0,i.useInnerBlocksProps)({...n},{template:Ha,allowedBlocks:[],templateLock:"all"});return(0,a.createElement)(a.Fragment,null,(0,a.createElement)(Na,{attributes:e,setAttributes:t}),(0,a.createElement)("div",{...r}))},save:({attributes:e})=>{const t=i.useBlockProps.save({className:s()("lms-course-bundle",{alignfull:"alignfull"===e.layoutWidth},`lms-course-bundle__col-${e.bundlesPerRow}`,`lms-course-bundle__col-tablet-${e.bundlesPerRowTablet}`,`lms-course-bundle__col-phone-${e.bundlesPerRowMobile}`,{"lms-course-bundle--jc-center":"center"===e.bundlesAlignment},{"lms-course-bundle--jc-end":"end"===e.bundlesAlignment}),style:xa(e),"data-limit":e.bundlesPerPage,"data-orderby":e.bundlesOrderBy,"data-bundles":e.bundlesValues.join(",")}),n=i.useInnerBlocksProps.save(t);return(0,a.createElement)("div",{...n})}})},6942:(e,t)=>{var n;!function(){"use strict";var a={}.hasOwnProperty;function r(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=i(e,l(n)))}return e}function l(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return r.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)a.call(e,n)&&e[n]&&(t=i(t,n));return t}function i(e,t){return t?e?e+" "+t:e+t:e}e.exports?(r.default=r,e.exports=r):void 0===(n=function(){return r}.apply(t,[]))||(e.exports=n)}()}},n={};function a(e){var r=n[e];if(void 0!==r)return r.exports;var l=n[e]={exports:{}};return t[e](l,l.exports,a),l.exports}a.m=t,e=[],a.O=(t,n,r,l)=>{if(!n){var i=1/0;for(d=0;d<e.length;d++){for(var[n,r,l]=e[d],o=!0,s=0;s<n.length;s++)(!1&l||i>=l)&&Object.keys(a.O).every((e=>a.O[e](n[s])))?n.splice(s--,1):(o=!1,l<i&&(i=l));if(o){e.splice(d--,1);var c=r();void 0!==c&&(t=c)}}return t}l=l||0;for(var d=e.length;d>0&&e[d-1][2]>l;d--)e[d]=e[d-1];e[d]=[n,r,l]},a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={8072:0,5392:0};a.O.j=t=>0===e[t];var t=(t,n)=>{var r,l,[i,o,s]=n,c=0;if(i.some((t=>0!==e[t]))){for(r in o)a.o(o,r)&&(a.m[r]=o[r]);if(s)var d=s(a)}for(t&&t(n);c<i.length;c++)l=i[c],a.o(e,l)&&e[l]&&e[l][0](),e[l]=0;return a.O(d)},n=globalThis.webpackChunkmasterstudy_lms_learning_management_system=globalThis.webpackChunkmasterstudy_lms_learning_management_system||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})();var r=a.O(void 0,[5392],(()=>a(6432)));r=a.O(r)})();