(()=>{var e,t={4103:(e,t,n)=>{"use strict";const s=window.wp.i18n,r=window.wp.blocks,a=window.React,i=window.wp.blockEditor,l=()=>(0,a.createElement)("div",{className:"lms-instructor-preset-empty-view"},(0,a.createElement)("div",{className:"lms-instructor-preset-empty-view__icon"},(0,a.createElement)("svg",{width:"40",height:"41",viewBox:"0 0 40 41",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,a.createElement)("path",{d:"M12.4899 5.89574C7.38783 10.9978 6.57151 18.7692 10.0082 24.7447L2.00824 32.7447C1.2409 33.5121 0.816406 34.5325 0.816406 35.6182C0.816406 36.7039 1.2409 37.7243 2.00824 38.4917C2.80008 39.2835 3.83681 39.6835 4.88171 39.6835C5.92661 39.6835 6.96334 39.2835 7.75518 38.4917L15.7552 30.4917C18.1633 31.8712 20.8491 32.5814 23.5511 32.5814C27.5593 32.5814 31.5593 31.0549 34.6123 28.01C40.7103 21.9121 40.7103 11.9937 34.6123 5.89574C28.5062 -0.202224 18.5878 -0.202224 12.4899 5.89574ZM3.16743 37.3406C2.72057 36.8995 2.463 36.3017 2.44929 35.6739C2.43558 35.0461 2.66679 34.4376 3.09396 33.9774L6.51437 37.3978C6.05703 37.8222 5.45325 38.0533 4.8294 38.0426C4.20555 38.032 3.61 37.7804 3.16743 37.3406ZM7.68171 36.2549L4.24498 32.8182L10.9225 26.1406C11.396 26.7937 11.9103 27.4223 12.4899 28.01C13.0776 28.5978 13.7062 29.1039 14.3593 29.5774L7.68171 36.2549ZM31.2817 28.6304C30.4409 29.1855 29.5593 29.6345 28.6368 29.9937V25.0304H27.0042V30.508C24.7348 31.0794 22.3593 31.0794 20.0899 30.508V25.0304H18.4572V29.9937C17.5429 29.6345 16.6531 29.1855 15.8123 28.6304V24.0427C15.8123 21.7733 17.6817 19.9284 19.9838 19.9284H27.1103C29.4123 19.9284 31.2817 21.7733 31.2817 24.0427V28.6304ZM33.4531 26.859C33.2817 27.0304 33.094 27.1855 32.9144 27.3406V24.0427C32.9144 20.8753 30.3103 18.2957 27.1103 18.2957H19.9838C16.7838 18.2957 14.1797 20.8753 14.1797 24.0427V27.3406C14.0001 27.1774 13.8123 27.0304 13.6409 26.859C8.17967 21.3978 8.17967 12.508 13.6409 7.05492C16.3756 4.32022 19.9593 2.9488 23.5511 2.9488C27.1429 2.9488 30.7266 4.31206 33.4531 7.04676C38.9144 12.508 38.9144 21.3978 33.4531 26.859Z",fill:"#227AFF"}),(0,a.createElement)("path",{d:"M23.4685 7.07959C20.6113 7.07959 18.293 9.43877 18.293 12.3449C18.293 15.251 20.6113 17.6102 23.4685 17.6102C26.3256 17.6102 28.644 15.251 28.644 12.3449C28.644 9.43877 26.3175 7.07959 23.4685 7.07959ZM23.4685 15.9775C21.5175 15.9775 19.9256 14.3531 19.9256 12.3449C19.9256 10.3367 21.5175 8.71224 23.4685 8.71224C25.4195 8.71224 27.0113 10.3367 27.0113 12.3449C27.0113 14.3531 25.4195 15.9775 23.4685 15.9775Z",fill:"#227AFF"}))),(0,a.createElement)("p",null,s.__("No Instructors Found","masterstudy-lms-learning-management-system"))),o=window.wp.components,c=()=>(0,a.createElement)("div",{className:"lms-instructors-loader"},(0,a.createElement)(o.Spinner,{style:{width:"80px",height:"80px"}}));var m=n(6942),d=n.n(m);const u=({condition:e,fallback:t=null,children:n})=>(0,a.createElement)(a.Fragment,null,e?n:t);let _=function(e){return e.NORMAL="Normal",e.HOVER="Hover",e.ACTIVE="Active",e.FOCUS="Focus",e}({}),h=function(e){return e.DESKTOP="Desktop",e.TABLET="Tablet",e.MOBILE="Mobile",e}({}),g=function(e){return e.TOP_lEFT="top-left",e.TOP_CENTER="top-center",e.TOP_RIGHT="top-right",e.BOTTOM_lEFT="bottom-left",e.BOTTOM_CENTER="bottom-center",e.BOTTOM_RIGHT="bottom-right",e}({});s.__("Small","masterstudy-lms-learning-management-system"),s.__("Normal","masterstudy-lms-learning-management-system"),s.__("Large","masterstudy-lms-learning-management-system"),s.__("Extra Large","masterstudy-lms-learning-management-system");const p="wp-block-masterstudy-settings__";function v(e){return Array.isArray(e)?e.map((e=>p+e)):p+e}g.TOP_lEFT,g.TOP_CENTER,g.TOP_RIGHT,g.BOTTOM_lEFT,g.BOTTOM_CENTER,g.BOTTOM_RIGHT,s.__("Newest","masterstudy-lms-learning-management-system"),s.__("Oldest","masterstudy-lms-learning-management-system"),s.__("Overall rating","masterstudy-lms-learning-management-system"),s.__("Popular","masterstudy-lms-learning-management-system"),s.__("Price low","masterstudy-lms-learning-management-system"),s.__("Price high","masterstudy-lms-learning-management-system");const w=window.wp.element,C=window.wp.data,E=(0,w.createContext)(null),f=e=>(0,a.createElement)(o.SVG,{width:"16",height:"16",viewBox:"0 0 14 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(o.G,{"clip-path":"url(#clip0_1068_38993)"},(0,a.createElement)(o.Path,{d:"M11.1973 8.60005L8.74967 8.11005V5.67171C8.74967 5.517 8.68822 5.36863 8.57882 5.25923C8.46942 5.14984 8.32105 5.08838 8.16634 5.08838H6.99967C6.84496 5.08838 6.69659 5.14984 6.5872 5.25923C6.4778 5.36863 6.41634 5.517 6.41634 5.67171V8.58838H5.24967C5.15021 8.58844 5.05241 8.61391 4.96555 8.66238C4.87869 8.71084 4.80565 8.78068 4.75336 8.86529C4.70106 8.9499 4.67125 9.04646 4.66674 9.14582C4.66223 9.24518 4.68317 9.34405 4.72759 9.43305L6.47759 12.933C6.57676 13.1302 6.77859 13.255 6.99967 13.255H11.083C11.2377 13.255 11.3861 13.1936 11.4955 13.0842C11.6049 12.9748 11.6663 12.8264 11.6663 12.6717V9.17171C11.6665 9.03685 11.6198 8.90612 11.5343 8.80186C11.4487 8.69759 11.3296 8.62626 11.1973 8.60005Z"}),(0,a.createElement)(o.Path,{d:"M10.4997 1.58838H3.49967C2.85626 1.58838 2.33301 2.11221 2.33301 2.75505V6.83838C2.33301 7.4818 2.85626 8.00505 3.49967 8.00505H5.24967V6.83838H3.49967V2.75505H10.4997V6.83838H11.6663V2.75505C11.6663 2.11221 11.1431 1.58838 10.4997 1.58838Z"})),(0,a.createElement)("defs",null,(0,a.createElement)("clipPath",{id:"clip0_1068_38993"},(0,a.createElement)(o.Rect,{width:"14",height:"14",transform:"translate(0 0.421875)"})))),[y,b,S,T,L]=(_.NORMAL,s.__("Normal State","masterstudy-lms-learning-management-system"),_.HOVER,s.__("Hovered State","masterstudy-lms-learning-management-system"),_.ACTIVE,s.__("Hovered State","masterstudy-lms-learning-management-system"),_.FOCUS,s.__("Hovered State","masterstudy-lms-learning-management-system"),_.NORMAL,(0,a.createElement)((e=>(0,a.createElement)(o.SVG,{width:"16",height:"16",viewBox:"0 0 12 13",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(o.Path,{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"}))),null),s.__("Normal State","masterstudy-lms-learning-management-system"),_.HOVER,(0,a.createElement)(f,null),s.__("Hovered State","masterstudy-lms-learning-management-system"),_.ACTIVE,(0,a.createElement)(f,null),s.__("Active State","masterstudy-lms-learning-management-system"),_.FOCUS,(0,a.createElement)(f,null),s.__("Focus State","masterstudy-lms-learning-management-system"),v(["hover-state","hover-state__selected","hover-state__selected__opened-menu","hover-state__menu","hover-state__menu__item"])),O=v("color-indicator");var k;function x(){return x=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var s in n)({}).hasOwnProperty.call(n,s)&&(e[s]=n[s])}return e},x.apply(null,arguments)}(0,w.memo)((({color:e,onChange:t})=>(0,a.createElement)("div",{className:O},(0,a.createElement)(i.PanelColorSettings,{enableAlpha:!0,disableCustomColors:!1,__experimentalHasMultipleOrigins:!0,__experimentalIsRenderedInSidebar:!0,colorSettings:[{label:"",value:e,onChange:t}]}))));var M,N,A=function(e){return a.createElement("svg",x({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 12 13"},e),k||(k=a.createElement("path",{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})))};function P(){return P=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var s in n)({}).hasOwnProperty.call(n,s)&&(e[s]=n[s])}return e},P.apply(null,arguments)}var H=function(e){return a.createElement("svg",P({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 14 15"},e),M||(M=a.createElement("g",{clipPath:"url(#state-hover_svg__a)"},a.createElement("path",{d:"M11.197 8.6 8.75 8.11V5.672a.583.583 0 0 0-.584-.584H7a.583.583 0 0 0-.584.584v2.916H5.25a.584.584 0 0 0-.522.845l1.75 3.5c.099.197.3.322.522.322h4.083a.583.583 0 0 0 .583-.583v-3.5a.58.58 0 0 0-.469-.572"}),a.createElement("path",{d:"M10.5 1.588h-7c-.644 0-1.167.524-1.167 1.167v4.083c0 .644.523 1.167 1.167 1.167h1.75V6.838H3.5V2.755h7v4.083h1.166V2.755c0-.643-.523-1.167-1.166-1.167"}))),N||(N=a.createElement("defs",null,a.createElement("clipPath",{id:"state-hover_svg__a"},a.createElement("path",{d:"M0 .422h14v14H0z"})))))};const R=[{value:_.NORMAL,label:s.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(A,{onClick:e})},{value:_.HOVER,label:s.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(H,{onClick:e})}],V={[_.NORMAL]:{icon:(0,a.createElement)(A,null),label:s.__("Normal State","masterstudy-lms-learning-management-system")},[_.HOVER]:{icon:(0,a.createElement)(H,null),label:s.__("Hovered State","masterstudy-lms-learning-management-system")}},D=v("hover-state"),B=v("hover-state__selected"),I=v("hover-state__selected__opened-menu"),F=v("has-changes"),Z=v("hover-state__menu"),U=v("hover-state__menu__item"),[j,z]=((0,w.memo)((e=>{const{hoverName:t,onChangeHoverName:n,fieldName:s}=e,{changedFieldsByName:r}=(()=>{const e=(0,w.useContext)(E);if(!e)throw new Error("No settings context provided");return e})(),i=r.get(s),{isOpen:l,onOpen:o,onClose:c}=((e=!1)=>{const[t,n]=(0,w.useState)(e),s=(0,w.useCallback)((()=>{n(!0)}),[]);return{isOpen:t,onClose:(0,w.useCallback)((()=>{n(!1)}),[]),onOpen:s,onToggle:(0,w.useCallback)((()=>{n((e=>!e))}),[])}})(),m=(e=>{const t=(0,w.useRef)(null);return(0,w.useEffect)((()=>{const n=n=>{t.current&&!t.current.contains(n.target)&&e()};return document.addEventListener("click",n),()=>{document.removeEventListener("click",n)}}),[t,e]),t})(c),{ICONS_MAP:_,options:h}=(e=>{const t=(0,w.useMemo)((()=>R.filter((t=>t.value!==e))),[e]);return{ICONS_MAP:V,options:t}})(t),g=(0,w.useCallback)((e=>{n(e),c()}),[n,c]);return(0,a.createElement)("div",{className:D,ref:m},(0,a.createElement)("div",{className:d()([B],{[I]:l,[F]:i}),onClick:o,title:_[t]?.label},_[t]?.icon),(0,a.createElement)(u,{condition:l},(0,a.createElement)("div",{className:Z},h.map((({value:e,icon:t,label:n})=>(0,a.createElement)("div",{key:e,className:U,title:n},t((()=>g(e)))))))))})),s.__("Desktop","masterstudy-lms-learning-management-system"),s.__("Tablet","masterstudy-lms-learning-management-system"),s.__("Mobile","masterstudy-lms-learning-management-system"),h.DESKTOP,s.__("Desktop","masterstudy-lms-learning-management-system"),h.TABLET,s.__("Tablet","masterstudy-lms-learning-management-system"),h.MOBILE,s.__("Mobile","masterstudy-lms-learning-management-system"),v("device-picker"),v("device-picker__selected"),v("device-picker__selected__opened-menu"),v("device-picker__menu"),v("device-picker__menu__item"),v("reset-button"),v("unit"),v("unit__single"),v("unit__list"),v("popover-modal"),v("popover-modal__close dashicon dashicons dashicons-no-alt"),v("setting-label"),v("setting-label__content"),v("suffix"),v("color-picker"),v("number-steppers"),v("indent-steppers"),v("indent-stepper-plus"),v("indent-stepper-minus"),v(["indents","indents-control"])),[G,K,W,X]=v(["toggle-group-wrapper","toggle-group","toggle-group__toggle","toggle-group__active-toggle"]),[Y,$,q,Q]=v(["border-control","border-control-solid","border-control-dashed","border-control-dotted"]),J=(v("border-radius"),v("border-radius-control"),v("box-shadow-preset"),v("presets")),ee=v("presets__item-wrapper"),te=v("presets__item-wrapper__preset"),ne=v("presets__item-wrapper__name");(0,w.memo)((e=>{const{presets:t,activePreset:n,onSelectPreset:s,PresetItem:r,detectIsActive:i,detectByIndex:l=!1}=e;return(0,a.createElement)("div",{className:J},t.map((({name:e,...t},o)=>(0,a.createElement)("div",{key:o,className:d()([ee],{active:i(n,l?o:t)}),onClick:()=>s(t)},(0,a.createElement)("div",{className:te},(0,a.createElement)(r,{preset:t})),(0,a.createElement)("span",{className:ne},e)))))})),v("range-control"),v("switch"),v("box-shadow-settings"),v("box-shadow-presets-title"),v("input-field"),v("input-field-control"),v("number-field"),v("number-field-control"),v("select__single-item"),v("select__container"),v("select__container__multi-item"),v("select"),v("select__select-box"),v("select__placeholder"),v("select__select-box-multiple"),v("select__menu"),v("select__menu__options-container"),v("select__menu__item"),v("setting-select"),v("row-select"),v("row-select__label"),v("row-select__control"),v("typography-select"),v("typography-select-label"),v("typography"),v("file-upload"),v("file-upload__wrap"),v("file-upload__image"),v("file-upload__remove"),v("file-upload__replace"),(0,w.createContext)({activeTab:0,setActiveTab:()=>{}}),v("tab-list"),v("tab"),v("tab-active"),v("content"),v("tab-panel"),window.ReactDOM;"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement;function se(e){const t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function re(e){return"nodeType"in e}function ae(e){var t,n;return e?se(e)?e:re(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function ie(e){const{Document:t}=ae(e);return e instanceof t}function le(e){return!se(e)&&e instanceof ae(e).HTMLElement}function oe(e){return e instanceof ae(e).SVGElement}function ce(e){return e?se(e)?e.document:re(e)?ie(e)?e:le(e)||oe(e)?e.ownerDocument:document:document:document}function me(e){return function(t){for(var n=arguments.length,s=new Array(n>1?n-1:0),r=1;r<n;r++)s[r-1]=arguments[r];return s.reduce(((t,n)=>{const s=Object.entries(n);for(const[n,r]of s){const s=t[n];null!=s&&(t[n]=s+e*r)}return t}),{...t})}}const de=me(-1);function ue(e){if(function(e){if(!e)return!1;const{TouchEvent:t}=ae(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return function(e){return"clientX"in e&&"clientY"in e}(e)?{x:e.clientX,y:e.clientY}:null}var _e;!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(_e||(_e={}));const he=Object.freeze({x:0,y:0});var ge,pe,ve,we;!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(ge||(ge={}));class Ce{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach((e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)}))},this.target=e}add(e,t,n){var s;null==(s=this.target)||s.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function Ee(e,t){const n=Math.abs(e.x),s=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+s**2)>t:"x"in t&&"y"in t?n>t.x&&s>t.y:"x"in t?n>t.x:"y"in t&&s>t.y}function fe(e){e.preventDefault()}function ye(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(pe||(pe={})),(we=ve||(ve={})).Space="Space",we.Down="ArrowDown",we.Right="ArrowRight",we.Left="ArrowLeft",we.Up="ArrowUp",we.Esc="Escape",we.Enter="Enter";ve.Space,ve.Enter,ve.Esc,ve.Space,ve.Enter;function be(e){return Boolean(e&&"distance"in e)}function Se(e){return Boolean(e&&"delay"in e)}class Te{constructor(e,t,n){var s;void 0===n&&(n=function(e){const{EventTarget:t}=ae(e);return e instanceof t?e:ce(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;const{event:r}=e,{target:a}=r;this.props=e,this.events=t,this.document=ce(a),this.documentListeners=new Ce(this.document),this.listeners=new Ce(n),this.windowListeners=new Ce(ae(a)),this.initialCoordinates=null!=(s=ue(r))?s:he,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),this.windowListeners.add(pe.Resize,this.handleCancel),this.windowListeners.add(pe.DragStart,fe),this.windowListeners.add(pe.VisibilityChange,this.handleCancel),this.windowListeners.add(pe.ContextMenu,fe),this.documentListeners.add(pe.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(Se(t))return void(this.timeoutId=setTimeout(this.handleStart,t.delay));if(be(t))return}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handleStart(){const{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(pe.Click,ye,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(pe.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;const{activated:n,initialCoordinates:s,props:r}=this,{onMove:a,options:{activationConstraint:i}}=r;if(!s)return;const l=null!=(t=ue(e))?t:he,o=de(s,l);if(!n&&i){if(be(i)){if(null!=i.tolerance&&Ee(o,i.tolerance))return this.handleCancel();if(Ee(o,i.distance))return this.handleStart()}return Se(i)&&Ee(o,i.tolerance)?this.handleCancel():void 0}e.cancelable&&e.preventDefault(),a(l)}handleEnd(){const{onEnd:e}=this.props;this.detach(),e()}handleCancel(){const{onCancel:e}=this.props;this.detach(),e()}handleKeydown(e){e.code===ve.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}const Le={move:{name:"pointermove"},end:{name:"pointerup"}};(class extends Te{constructor(e){const{event:t}=e,n=ce(t.target);super(e,Le,n)}}).activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:s}=t;return!(!n.isPrimary||0!==n.button||(null==s||s({event:n}),0))}}];const Oe={move:{name:"mousemove"},end:{name:"mouseup"}};var ke;!function(e){e[e.RightClick=2]="RightClick"}(ke||(ke={})),class extends Te{constructor(e){super(e,Oe,ce(e.event.target))}}.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:s}=t;return n.button!==ke.RightClick&&(null==s||s({event:n}),!0)}}];const xe={move:{name:"touchmove"},end:{name:"touchend"}};var Me,Ne,Ae,Pe,He;(class extends Te{constructor(e){super(e,xe)}static setup(){return window.addEventListener(xe.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(xe.move.name,e)};function e(){}}}).activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:s}=t;const{touches:r}=n;return!(r.length>1||(null==s||s({event:n}),0))}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(Me||(Me={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(Ne||(Ne={})),ge.Backward,ge.Forward,ge.Backward,ge.Forward,function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(Ae||(Ae={})),function(e){e.Optimized="optimized"}(Pe||(Pe={})),Ae.WhileDragging,Pe.Optimized,Map,function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(He||(He={})),ve.Down,ve.Right,ve.Up,ve.Left,s.__("Lectures","masterstudy-lms-learning-management-system"),s.__("Duration","masterstudy-lms-learning-management-system"),s.__("Views","masterstudy-lms-learning-management-system"),s.__("Level","masterstudy-lms-learning-management-system"),s.__("Members","masterstudy-lms-learning-management-system"),s.__("Empty","masterstudy-lms-learning-management-system"),v("sortable__item"),v("sortable__item__disabled"),v("sortable__item__content"),v("sortable__item__content__drag-item"),v("sortable__item__content__drag-item__disabled"),v("sortable__item__content__title"),v("sortable__item__control"),v("sortable__item__icon"),v("nested-sortable"),v("nested-sortable__item"),v("sortable"),v("accordion"),v("accordion__header"),v("accordion__header-flex"),v("accordion__content"),v("accordion__icon"),v("accordion__title"),v("accordion__title-disabled"),v("accordion__indicator"),v("accordion__controls"),v("accordion__controls-disabled"),v("preset-picker"),v("preset-picker__label"),v("preset-picker__remove"),v("preset-picker__presets-list"),v("preset-picker__presets-list__item"),v("preset-picker__presets-list__item__preset"),v("preset-picker__presets-list__item__preset-active");let Re=function(e){return e.ALL="all",e.STARS="stars",e.STARS_AND_RATE="starsAndRate",e.STARS_AND_REVIEWS="starsAndReviews",e}({}),Ve=function(e){return e.BOXED_ROUNDED="boxedRounded",e.BOXED_SQUARED="boxedSquared",e.DEFAULT="default",e}({}),De=function(e){return e.ALL="all",e.QUANTITY="quantity",e}({});const Be={facebook:(0,a.createElement)((()=>(0,a.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 18 18",fill:"none"},(0,a.createElement)("g",{clipPath:"url(#clip0_4454_25439)"},(0,a.createElement)("path",{d:"M17.7188 9C17.7188 4.18359 13.8164 0.28125 9 0.28125C4.18359 0.28125 0.28125 4.18359 0.28125 9C0.28125 13.3516 3.46957 16.9587 7.6377 17.6133V11.5204H5.42285V9H7.6377V7.07906C7.6377 4.8941 8.93848 3.68719 10.9308 3.68719C11.8849 3.68719 12.8827 3.85734 12.8827 3.85734V6.00187H11.783C10.7002 6.00187 10.3623 6.67406 10.3623 7.36348V9H12.7804L12.3936 11.5204H10.3623V17.6133C14.5304 16.9587 17.7188 13.3516 17.7188 9Z",fill:"white"})),(0,a.createElement)("defs",null,(0,a.createElement)("clipPath",{id:"clip0_4454_25439"},(0,a.createElement)("rect",{width:"18",height:"18",fill:"white"}))))),null),linkedin:(0,a.createElement)((()=>(0,a.createElement)("svg",{width:"17",height:"18",viewBox:"0 0 17 18",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,a.createElement)("g",{clipPath:"url(#clip0_4921_36563)"},(0,a.createElement)("path",{d:"M15.125 1.125H1.62148C1.00273 1.125 0.5 1.63477 0.5 2.26055V15.7395C0.5 16.3652 1.00273 16.875 1.62148 16.875H15.125C15.7437 16.875 16.25 16.3652 16.25 15.7395V2.26055C16.25 1.63477 15.7437 1.125 15.125 1.125ZM5.26016 14.625H2.92578V7.10859H5.26367V14.625H5.26016ZM4.09297 6.08203C3.34414 6.08203 2.73945 5.47383 2.73945 4.72852C2.73945 3.9832 3.34414 3.375 4.09297 3.375C4.83828 3.375 5.44648 3.9832 5.44648 4.72852C5.44648 5.47734 4.8418 6.08203 4.09297 6.08203ZM14.0105 14.625H11.6762V10.9688C11.6762 10.0969 11.6586 8.97539 10.4633 8.97539C9.24687 8.97539 9.06055 9.92461 9.06055 10.9055V14.625H6.72617V7.10859H8.96562V8.13516H8.99727C9.31016 7.54453 10.073 6.92227 11.2086 6.92227C13.5711 6.92227 14.0105 8.47969 14.0105 10.5047V14.625Z",fill:"white"})),(0,a.createElement)("defs",null,(0,a.createElement)("clipPath",{id:"clip0_4921_36563"},(0,a.createElement)("rect",{width:"15.75",height:"18",fill:"white",transform:"translate(0.5)"}))))),null),twitter:(0,a.createElement)((()=>(0,a.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"28",height:"28",viewBox:"0 0 28 28",fill:"none"},(0,a.createElement)("rect",{width:"28",height:"28",rx:"14"}),(0,a.createElement)("path",{d:"M16.1876 12.7383L15.1695 11.3931L11.4161 6.43164H6.43359L12.5925 14.4804L13.6054 15.8019L17.579 20.9968H22.4336L16.1863 12.7383H16.1876ZM14.5483 14.6822L13.5276 13.3594L9.3904 7.99445H10.9519L14.2978 12.4389L15.308 13.7828L19.5638 19.4366H18.2133L14.5483 14.6835V14.6822Z",fill:"white"}),(0,a.createElement)("path",{d:"M13.5276 13.3594L14.5484 14.6822L13.6054 15.8019L9.01064 21.25H6.94141L12.5939 14.4791L13.5289 13.3581L13.5276 13.3594ZM19.3107 6.43164L15.1696 11.3931L14.2978 12.4376L15.308 13.7815L16.1877 12.7383L21.5065 6.43164H19.312H19.3107Z",fill:"white"}))),null),instagram:(0,a.createElement)((()=>(0,a.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none"},(0,a.createElement)("g",{clipPath:"url(#clip0_4532_34439)"},(0,a.createElement)("path",{d:"M5.33377 8C5.33377 6.5273 6.5273 5.33312 8 5.33312C9.4727 5.33312 10.6669 6.5273 10.6669 8C10.6669 9.4727 9.4727 10.6669 8 10.6669C6.5273 10.6669 5.33377 9.4727 5.33377 8ZM3.89208 8C3.89208 10.2688 5.73118 12.1079 8 12.1079C10.2688 12.1079 12.1079 10.2688 12.1079 8C12.1079 5.73118 10.2688 3.89208 8 3.89208C5.73118 3.89208 3.89208 5.73118 3.89208 8ZM11.3105 3.72924C11.3105 4.25913 11.7402 4.6895 12.2708 4.6895C12.8006 4.6895 13.231 4.25913 13.231 3.72924C13.231 3.19935 12.8013 2.76963 12.2708 2.76963C11.7402 2.76963 11.3105 3.19935 11.3105 3.72924ZM4.76769 14.5118C3.98772 14.4763 3.56381 14.3464 3.28207 14.2365C2.90856 14.0911 2.64233 13.9179 2.36187 13.6381C2.08207 13.3583 1.90824 13.0921 1.76349 12.7186C1.65364 12.4368 1.52375 12.0129 1.48821 11.233C1.44943 10.3897 1.44168 10.1363 1.44168 8C1.44168 5.86365 1.45008 5.61099 1.48821 4.76704C1.52375 3.98708 1.65428 3.56381 1.76349 3.28142C1.90889 2.90792 2.08207 2.64168 2.36187 2.36123C2.64168 2.08142 2.90792 1.90759 3.28207 1.76284C3.56381 1.65299 3.98772 1.5231 4.76769 1.48756C5.61099 1.44879 5.8643 1.44103 8 1.44103C10.1363 1.44103 10.389 1.44943 11.233 1.48756C12.0129 1.5231 12.4362 1.65363 12.7186 1.76284C13.0921 1.90759 13.3583 2.08142 13.6388 2.36123C13.9186 2.64103 14.0918 2.90792 14.2372 3.28142C14.347 3.56317 14.4769 3.98708 14.5124 4.76704C14.5512 5.61099 14.559 5.86365 14.559 8C14.559 10.1357 14.5512 10.389 14.5124 11.233C14.4769 12.0129 14.3464 12.4368 14.2372 12.7186C14.0918 13.0921 13.9186 13.3583 13.6388 13.6381C13.359 13.9179 13.0921 14.0911 12.7186 14.2365C12.4368 14.3464 12.0129 14.4763 11.233 14.5118C10.3897 14.5506 10.1363 14.5583 8 14.5583C5.8643 14.5583 5.61099 14.5506 4.76769 14.5118ZM4.70178 0.0484653C3.85008 0.0872375 3.2685 0.222294 2.75994 0.420032C2.23393 0.624233 1.78805 0.898223 1.34281 1.34281C0.898223 1.7874 0.624233 2.23328 0.420032 2.75994C0.222294 3.2685 0.0872375 3.85008 0.0484653 4.70178C0.00904685 5.55477 0 5.82746 0 8C0 10.1725 0.00904685 10.4452 0.0484653 11.2982C0.0872375 12.1499 0.222294 12.7315 0.420032 13.2401C0.624233 13.7661 0.897577 14.2126 1.34281 14.6572C1.7874 15.1018 2.23328 15.3751 2.75994 15.58C3.26914 15.7777 3.85008 15.9128 4.70178 15.9515C5.55541 15.9903 5.82746 16 8 16C10.1732 16 10.4452 15.991 11.2982 15.9515C12.1499 15.9128 12.7315 15.7777 13.2401 15.58C13.7661 15.3751 14.212 15.1018 14.6572 14.6572C15.1018 14.2126 15.3751 13.7661 15.58 13.2401C15.7777 12.7315 15.9134 12.1499 15.9515 11.2982C15.9903 10.4446 15.9994 10.1725 15.9994 8C15.9994 5.82746 15.9903 5.55477 15.9515 4.70178C15.9128 3.85008 15.7777 3.2685 15.58 2.75994C15.3751 2.23393 15.1018 1.78805 14.6572 1.34281C14.2126 0.898223 13.7661 0.624233 13.2407 0.420032C12.7315 0.222294 12.1499 0.0865913 11.2989 0.0484653C10.4459 0.00969305 10.1732 0 8.00065 0C5.82746 0 5.55541 0.00904685 4.70178 0.0484653Z",fill:"white"})),(0,a.createElement)("defs",null,(0,a.createElement)("clipPath",{id:"clip0_4532_34439"},(0,a.createElement)("rect",{width:"16",height:"16",fill:"white"}))))),null)},Ie=({socials:e,cardPreset:t})=>(0,a.createElement)("div",{className:d()("lms-instructor-classic__list-item-socials",{"lms-instructor-classic__list-item-boxed-socials":t!==Ve.DEFAULT})},Object.entries(e).map((([e,t],n)=>t?(0,a.createElement)("a",{key:n,onClick:e=>{e.stopPropagation()},className:d()("lms-instructor-socials",{"lms-instructor-socials-facebook":"facebook"===e,"lms-instructor-socials-linkedin":"linkedin"===e,"lms-instructor-socials-twitter":"twitter"===e,"lms-instructor-socials-instagram":"instagram"===e}),href:t,target:"_blank",rel:"noreferrer"},Be[e]):null))),Fe=e=>{const{instructor:t,showPosition:n,showCourseCount:s,showBiography:r,showRating:i,showSocials:l,ratingStyle:o,cardPreset:c}=e,m=t.avatar||t.defaultAvatar,d=((e,t,n,s)=>(0,w.useMemo)((()=>e&&t?n+(n?", Courses: ":"Courses: ")+s:e&&!t?n:!e&&t?"Courses: "+s:""),[e,t,n,s]))(n,s,t.position,t.courses),_=o===Re.ALL||o===Re.STARS_AND_RATE,h=o===Re.ALL||o===Re.STARS_AND_REVIEWS,g=(t.rating?20*+t.rating:0)+"%";return(0,a.createElement)("div",{className:"lms-instructor-classic__list-item"},(0,a.createElement)("div",{className:"lms-instructor-classic__list-item__image"},(0,a.createElement)("img",{src:m,alt:t.name}),(0,a.createElement)(u,{condition:c===Ve.DEFAULT},(0,a.createElement)("div",{className:"lms-instructor-classic__list-item__popup"},(0,a.createElement)(u,{condition:l},(0,a.createElement)(Ie,{socials:t.socials,cardPreset:c}))))),(0,a.createElement)("div",{className:"lms-instructor-classic__list-item__info"},(0,a.createElement)("span",{className:"lms-instructor-classic__list-item__info-name"},t.name),(0,a.createElement)(u,{condition:(n||s)&&(Boolean(t.position)||Boolean(t.courses))},(0,a.createElement)("span",{className:"lms-instructor-classic__list-item__info-position"},d)),(0,a.createElement)(u,{condition:r&&!!t.description},(0,a.createElement)("span",{className:"lms-instructor-classic__list-item__info-description"},t.description))),(0,a.createElement)(u,{condition:i&&t.ratingVisibility},(0,a.createElement)("div",{className:"lms-instructor-classic__list-item__rating"},(0,a.createElement)("span",{className:"lms-instructor-classic__list-item__rating-progress"},(0,a.createElement)("span",{className:"lms-instructor-classic__list-item__rating-progress--active",style:{width:g}})),(0,a.createElement)(u,{condition:_},(0,a.createElement)("div",{className:"lms-instructor-classic__list-item__rating-value"},t.rating||"0"))),(0,a.createElement)(u,{condition:h&&!!t.reviews},(0,a.createElement)("div",{className:"lms-instructor-classic__list-item__reviews"},t.reviews," Reviews"))),(0,a.createElement)(u,{condition:c!==Ve.DEFAULT},(0,a.createElement)(u,{condition:l},(0,a.createElement)(Ie,{socials:t.socials,cardPreset:c}))))},Ze=({instructors:e,context:t})=>(0,a.createElement)(u,{condition:Boolean(e.length),fallback:(0,a.createElement)(l,null)},(0,a.createElement)("div",{className:"lms-instructor-classic__list"},e.map((e=>(0,a.createElement)(Fe,{key:e.id,instructor:e,showPosition:t["masterstudy/showPosition"],showCourseCount:t["masterstudy/showCourseCount"],showBiography:t["masterstudy/showBiography"],showRating:t["masterstudy/showRating"],showSocials:t["masterstudy/showSocials"],ratingStyle:t["masterstudy/ratingStyle"],cardPreset:t["masterstudy/cardPreset"]}))))),Ue=e=>({avatar:e.avatar,defaultAvatar:e.avatar_urls[96],name:e.name,position:e.position,description:e.description,id:e.id,courses:e.courses,ratingVisibility:e.rating_visibility,rating:e.sum_rating,reviews:e.total_reviews,link:e.page_url,socials:{facebook:e.facebook,instagram:e.instagram,linkedin:e.linkedin,twitter:e.twitter}}),je=window.wp.apiFetch;var ze=n.n(je);const Ge=JSON.parse('{"UU":"masterstudy/instructors-preset-classic"}');(0,r.registerBlockType)(Ge.UU,{title:s._x("MasterStudy Instructors Preset Classic","block title","masterstudy-lms-learning-management-system"),description:s._x("Displays Instructors Preset Classic","block description","masterstudy-lms-learning-management-system"),category:"masterstudy-lms-blocks",icon:"carrot",edit:({isSelected:e,context:t})=>{(e=>{const t=(0,C.useSelect)((e=>{const{getBlockParents:t,getSelectedBlockClientId:n}=e(i.store);return t(n(),!0)}),[]),{selectBlock:n}=(0,C.useDispatch)(i.store);(0,w.useEffect)((()=>{e&&t.length&&n(t[0])}),[e,t,n])})(e);const n=(0,i.useBlockProps)({className:"lms-instructor-classic"}),s=((e,t,n,s)=>{const[r,a]=(0,w.useState)("");return(0,w.useEffect)((()=>{const r=["users?roles=stm_lms_instructor"];s?.length&&r.push(`include=${s.toString()}`),e===De.QUANTITY&&r.push(`per_page=${t}`),n&&(n.includes("registered_date")?(r.push("orderby=registered_date"),n.includes("desc")&&r.push("order=desc")):(r.push(`orderby=${n}`),r.push("order=desc"))),a(r.join("&"))}),[e,t,n,s]),r})(t["masterstudy/instructorPerPage"],t["masterstudy/quantity"],t["masterstudy/orderBy"],t["masterstudy/instructors"]),{instructors:r,isFetching:l}=(e=>{const[t,n]=(0,w.useState)([]),{setIsFetching:s,setError:r,isFetching:a,error:i}=(()=>{const[e,t]=(0,w.useState)(!0),[n,s]=(0,w.useState)("");return{isFetching:e,setIsFetching:t,error:n,setError:s}})();return(0,w.useEffect)((()=>{s(!0),(async e=>{try{const t=await ze()({path:`masterstudy-lms/v2/${e}`,parse:!1});return{instructors:await t.json(),total:+t.headers.get("X-WP-Total"),totalPages:+t.headers.get("X-WP-TotalPages")}}catch(e){throw new Error(`Failed to fetch instructors: ${e.message}`)}})(e).then((e=>{n((e=>e.map(Ue))(e.instructors))})).catch((e=>{r(e.message)})).finally((()=>{s(!1)}))}),[e,r,s]),{instructors:t,isFetching:a,error:i}})(s);return(0,a.createElement)("div",{...n},(0,a.createElement)(u,{condition:!l,fallback:(0,a.createElement)(c,null)},(0,a.createElement)(Ze,{instructors:r,context:t})))},save:()=>{const e=i.useBlockProps.save({className:"lms-instructor-classic"});return(0,a.createElement)("div",{...e},(0,a.createElement)("div",{className:"lms-instructors-preloader"},(0,a.createElement)("div",{className:"lms-instructors-preloader-item"})),(0,a.createElement)("div",{className:"lms-instructor-classic__list"}))}})},6942:(e,t)=>{var n;!function(){"use strict";var s={}.hasOwnProperty;function r(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=i(e,a(n)))}return e}function a(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return r.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)s.call(e,n)&&e[n]&&(t=i(t,n));return t}function i(e,t){return t?e?e+" "+t:e+t:e}e.exports?(r.default=r,e.exports=r):void 0===(n=function(){return r}.apply(t,[]))||(e.exports=n)}()}},n={};function s(e){var r=n[e];if(void 0!==r)return r.exports;var a=n[e]={exports:{}};return t[e](a,a.exports,s),a.exports}s.m=t,e=[],s.O=(t,n,r,a)=>{if(!n){var i=1/0;for(m=0;m<e.length;m++){for(var[n,r,a]=e[m],l=!0,o=0;o<n.length;o++)(!1&a||i>=a)&&Object.keys(s.O).every((e=>s.O[e](n[o])))?n.splice(o--,1):(l=!1,a<i&&(i=a));if(l){e.splice(m--,1);var c=r();void 0!==c&&(t=c)}}return t}a=a||0;for(var m=e.length;m>0&&e[m-1][2]>a;m--)e[m]=e[m-1];e[m]=[n,r,a]},s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},s.d=(e,t)=>{for(var n in t)s.o(t,n)&&!s.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={1528:0,672:0};s.O.j=t=>0===e[t];var t=(t,n)=>{var r,a,[i,l,o]=n,c=0;if(i.some((t=>0!==e[t]))){for(r in l)s.o(l,r)&&(s.m[r]=l[r]);if(o)var m=o(s)}for(t&&t(n);c<i.length;c++)a=i[c],s.o(e,a)&&e[a]&&e[a][0](),e[a]=0;return s.O(m)},n=globalThis.webpackChunkmasterstudy_lms_learning_management_system=globalThis.webpackChunkmasterstudy_lms_learning_management_system||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})();var r=s.O(void 0,[672],(()=>s(4103)));r=s.O(r)})();