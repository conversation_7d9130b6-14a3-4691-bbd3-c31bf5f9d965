(()=>{var e,t={1585:(e,t,n)=>{"use strict";const a=window.React,i=window.wp.i18n,r=window.wp.blocks,l=window.wp.blockEditor;let o=function(e){return e.ALL="all",e.SOME="some",e}({}),s=function(e){return e.NORMAL="Normal",e.HOVER="Hover",e.ACTIVE="Active",e.FOCUS="Focus",e}({}),m=function(e){return e.DESKTOP="Desktop",e.TABLET="Tablet",e.MOBILE="Mobile",e}({}),c=function(e){return e.START="start",e.CENTER="center",e.END="end",e}({}),d=function(e){return e.TOP_lEFT="top-left",e.TOP_CENTER="top-center",e.TOP_RIGHT="top-right",e.BOTTOM_lEFT="bottom-left",e.BOTTOM_CENTER="bottom-center",e.BOTTOM_RIGHT="bottom-right",e}({});const g=window.wp.data,u=()=>(0,g.useSelect)((e=>e("core/editor").getDeviceType()||e("core/edit-site")?.__experimentalGetPreviewDeviceType()||e("core/edit-post")?.__experimentalGetPreviewDeviceType()||e("masterstudy/store")?.getDeviceType()),[])||"Desktop",p=(i.__("Small","masterstudy-lms-learning-management-system"),i.__("Normal","masterstudy-lms-learning-management-system"),i.__("Large","masterstudy-lms-learning-management-system"),i.__("Extra Large","masterstudy-lms-learning-management-system"),"wp-block-masterstudy-settings__"),h={top:"",right:"",bottom:"",left:""},b=(d.TOP_lEFT,d.TOP_CENTER,d.TOP_RIGHT,d.BOTTOM_lEFT,d.BOTTOM_CENTER,d.BOTTOM_RIGHT,i.__("Newest","masterstudy-lms-learning-management-system"),i.__("Oldest","masterstudy-lms-learning-management-system"),i.__("Overall rating","masterstudy-lms-learning-management-system"),i.__("Popular","masterstudy-lms-learning-management-system"),i.__("Price low","masterstudy-lms-learning-management-system"),i.__("Price high","masterstudy-lms-learning-management-system"),(e,t)=>{if(typeof e!=typeof t)return!1;if(Number.isNaN(e)&&Number.isNaN(t))return!0;if("object"!=typeof e||null===e||null===t)return e===t;if(Object.keys(e).length!==Object.keys(t).length)return!1;if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;const n=e.slice().sort(),a=t.slice().sort();return n.every(((e,t)=>b(e,a[t])))}for(const n of Object.keys(e))if(!b(e[n],t[n]))return!1;return!0}),v=["",null,void 0,"null","undefined"],y=[".jpg",".jpeg",".png",".gif"],_=e=>v.includes(e),S=(e,t,n="")=>{const a=e[t];return"object"==typeof a&&null!==a?((e,t)=>{return n=e,Object.values(n).every((e=>v.includes(e)))?null:((e,t="")=>{const n=Object.entries(e).reduce(((e,[n,a])=>(e[n]=(a||"0")+t,e)),{});return`${n.top} ${n.right} ${n.bottom} ${n.left}`})(e,t);var n})(a,n):((e,t)=>_(e)?e:(e=>{if("string"!=typeof e)return!1;const t=e.slice(e.lastIndexOf("."));return y.includes(t)||e.startsWith("blob")})(e)?`url('${e}')`:String(e+t))(a,n)},C=e=>({desktopPropertyName:e,tabletPropertyName:e+"Tablet",mobilePropertyName:e+"Mobile"}),E=(e,t,n)=>{const a={};return n.forEach((({isAdaptive:n,hasHover:i,unit:r},l)=>{if(t.hasOwnProperty(l)){const{unitMeasureDesktop:o,unitMeasureTablet:m,unitMeasureMobile:c}=((e,t)=>{var n;return{unitMeasureDesktop:null!==(n=e[t])&&void 0!==n?n:"",unitMeasureTablet:t?e[t+"Tablet"]:"",unitMeasureMobile:t?e[t+"Mobile"]:""}})(t,r);if(n&&i){const{desktopHoverPropertyName:n,mobileHoverPropertyName:i,tabletHoverPropertyName:r}=(e=>{const t=e+s.HOVER;return{desktopHoverPropertyName:t,tabletHoverPropertyName:t+"Tablet",mobileHoverPropertyName:t+"Mobile"}})(l),d=S(t,n,o);_(d)||(a[`--lms-${e}-${n}`]=d);const g=S(t,r,m);_(g)||(a[`--lms-${e}-${r}`]=g);const u=S(t,i,c);_(u)||(a[`--lms-${e}-${i}`]=u)}if(i){const n=l+s.HOVER,i=S(t,n,o);_(i)||(a[`--lms-${e}-${n}`]=i)}if(n){const{desktopPropertyName:n,mobilePropertyName:i,tabletPropertyName:r}=C(l),s=S(t,n,o);_(s)||(a[`--lms-${e}-${n}`]=s);const d=S(t,r,m);_(d)||(a[`--lms-${e}-${r}`]=d);const g=S(t,i,c);_(g)||(a[`--lms-${e}-${i}`]=g)}const d=S(t,l,o);_(d)||(a[`--lms-${e}-${l}`]=d)}})),a},f=(e,t,n,a,i,r)=>`${!0===e?"inset ":""} ${t}px ${n}px ${""!==a?`${a}px`:""} ${""!==i?`${i}px`:""} ${r}`,w=(e,t,n,a,i,r,l,o,s)=>{const m={};if(t[n]&&null!==t[a]&&null!==t[i]&&(m[`--lms-${e}-boxShadow`]=f(t[o],t[a],t[i],t[r],t[l],t[n])),s){const{tabletPropertyName:s,mobilePropertyName:c}=C(o),{tabletPropertyName:d,mobilePropertyName:g}=C(a),{tabletPropertyName:u,mobilePropertyName:p}=C(i),{tabletPropertyName:h,mobilePropertyName:b}=C(n),{tabletPropertyName:v,mobilePropertyName:y}=C(r),{tabletPropertyName:_,mobilePropertyName:S}=C(l);t[h]&&null!==t[d]&&null!==t[u]&&(m[`--lms-${e}-boxShadowTablet`]=f(t[s],t[d],t[u],t[v],t[_],t[h])),t[b]&&null!==t[g]&&null!==t[p]&&(m[`--lms-${e}-boxShadowMobile`]=f(t[c],t[g],t[p],t[y],t[S],t[b]))}return m};function N(e){return Array.isArray(e)?e.map((e=>p+e)):p+e}const T=window.wp.element,x=(e=!1)=>{const[t,n]=(0,T.useState)(e),a=(0,T.useCallback)((()=>{n(!0)}),[]);return{isOpen:t,onClose:(0,T.useCallback)((()=>{n(!1)}),[]),onOpen:a,onToggle:(0,T.useCallback)((()=>{n((e=>!e))}),[])}},M=(0,T.createContext)(null),U=({children:e,...t})=>(0,a.createElement)(M.Provider,{value:{...t}},e),A=()=>{const e=(0,T.useContext)(M);if(!e)throw new Error("No settings context provided");return e},B=(e="")=>{const{attributes:t,setAttributes:n,onResetByFieldName:a,changedFieldsByName:i}=A();return{value:t[e],onChange:t=>n({[e]:t}),onReset:a.get(e),isChanged:i.get(e)}},R=(e,t=!1,n=!1)=>{const{hoverName:a,onChangeHoverName:i}=(()=>{const[e,t]=(0,T.useState)(s.NORMAL);return{hoverName:e,onChangeHoverName:(0,T.useCallback)((e=>{t(e)}),[])}})(),r=u();return{fieldName:(0,T.useMemo)((()=>{const i=a===s.HOVER?a:"",l=r===m.DESKTOP?"":r;return n&&t?e+i+l:n&&!t?e+i:t&&!n?e+l:e}),[e,n,t,a,r]),hoverName:a,onChangeHoverName:i}},L=(e,t=!1,n="Normal")=>{const a=u(),i=(0,T.useMemo)((()=>{const i=n===s.NORMAL?"":n,r=a===m.DESKTOP?"":a;return i&&t?e+i+r:i&&!t?e+i:t&&!i?e+r:e}),[e,t,n,a]),{value:r,isChanged:l,onReset:o}=B(i);return{fieldName:i,value:r,isChanged:l,onReset:o}},H=(e=[],t=o.ALL)=>{const{attributes:n}=A();return!e.length||(t===o.ALL?e.every((({name:e,value:t})=>{const a=n[e];return Array.isArray(t)?Array.isArray(a)?b(t,a):t.includes(a):t===a})):t!==o.SOME||e.some((({name:e,value:t})=>{const a=n[e];return Array.isArray(t)?Array.isArray(a)?b(t,a):t.includes(a):t===a})))},P=e=>{const t=(0,T.useRef)(null);return(0,T.useEffect)((()=>{const n=n=>{t.current&&!t.current.contains(n.target)&&e()};return document.addEventListener("click",n),()=>{document.removeEventListener("click",n)}}),[t,e]),t},F=window.wp.components;var D=n(6942),k=n.n(D);const O=({condition:e,fallback:t=null,children:n})=>(0,a.createElement)(a.Fragment,null,e?n:t),W=e=>(0,a.createElement)(F.SVG,{width:"16",height:"16",viewBox:"0 0 12 13",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(F.Path,{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})),z=e=>(0,a.createElement)(F.SVG,{width:"16",height:"16",viewBox:"0 0 14 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(F.G,{"clip-path":"url(#clip0_1068_38993)"},(0,a.createElement)(F.Path,{d:"M11.1973 8.60005L8.74967 8.11005V5.67171C8.74967 5.517 8.68822 5.36863 8.57882 5.25923C8.46942 5.14984 8.32105 5.08838 8.16634 5.08838H6.99967C6.84496 5.08838 6.69659 5.14984 6.5872 5.25923C6.4778 5.36863 6.41634 5.517 6.41634 5.67171V8.58838H5.24967C5.15021 8.58844 5.05241 8.61391 4.96555 8.66238C4.87869 8.71084 4.80565 8.78068 4.75336 8.86529C4.70106 8.9499 4.67125 9.04646 4.66674 9.14582C4.66223 9.24518 4.68317 9.34405 4.72759 9.43305L6.47759 12.933C6.57676 13.1302 6.77859 13.255 6.99967 13.255H11.083C11.2377 13.255 11.3861 13.1936 11.4955 13.0842C11.6049 12.9748 11.6663 12.8264 11.6663 12.6717V9.17171C11.6665 9.03685 11.6198 8.90612 11.5343 8.80186C11.4487 8.69759 11.3296 8.62626 11.1973 8.60005Z"}),(0,a.createElement)(F.Path,{d:"M10.4997 1.58838H3.49967C2.85626 1.58838 2.33301 2.11221 2.33301 2.75505V6.83838C2.33301 7.4818 2.85626 8.00505 3.49967 8.00505H5.24967V6.83838H3.49967V2.75505H10.4997V6.83838H11.6663V2.75505C11.6663 2.11221 11.1431 1.58838 10.4997 1.58838Z"})),(0,a.createElement)("defs",null,(0,a.createElement)("clipPath",{id:"clip0_1068_38993"},(0,a.createElement)(F.Rect,{width:"14",height:"14",transform:"translate(0 0.421875)"})))),I=[{value:s.NORMAL,label:i.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(W,{onClick:e})},{value:s.HOVER,label:i.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(z,{onClick:e})},{value:s.ACTIVE,label:i.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(z,{onClick:e})},{value:s.FOCUS,label:i.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(z,{onClick:e})}],V={[s.NORMAL]:{icon:(0,a.createElement)(W,null),label:i.__("Normal State","masterstudy-lms-learning-management-system")},[s.HOVER]:{icon:(0,a.createElement)(z,null),label:i.__("Hovered State","masterstudy-lms-learning-management-system")},[s.ACTIVE]:{icon:(0,a.createElement)(z,null),label:i.__("Active State","masterstudy-lms-learning-management-system")},[s.FOCUS]:{icon:(0,a.createElement)(z,null),label:i.__("Focus State","masterstudy-lms-learning-management-system")}},$=(e,t)=>{let n=[];return n=e.length?I.filter((t=>e.includes(t.value))):I,n=n.filter((e=>e.value!==t)),{ICONS_MAP:V,options:n}},[j,G,Z,X,q]=N(["hover-state","hover-state__selected","hover-state__selected__opened-menu","hover-state__menu","hover-state__menu__item"]),K=({stateOptions:e,currentState:t,onSelect:n})=>{const{isOpen:i,onOpen:r,onClose:l}=x(),o=P(l),{ICONS_MAP:s,options:m}=$(e,t);return(0,a.createElement)("div",{className:j,ref:o},(0,a.createElement)("div",{className:k()([G],{[Z]:i}),onClick:r,title:s[t]?.label},s[t]?.icon),(0,a.createElement)(O,{condition:i},(0,a.createElement)("div",{className:X},m.map((({value:e,icon:t,label:i})=>(0,a.createElement)("div",{key:e,className:q,title:i},t((()=>n(e)))))))))},Y=N("color-indicator"),Q=(0,T.memo)((({color:e,onChange:t})=>(0,a.createElement)("div",{className:Y},(0,a.createElement)(l.PanelColorSettings,{enableAlpha:!0,disableCustomColors:!1,__experimentalHasMultipleOrigins:!0,__experimentalIsRenderedInSidebar:!0,colorSettings:[{label:"",value:e,onChange:t}]}))));var J;function ee(){return ee=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},ee.apply(null,arguments)}var te,ne,ae=function(e){return a.createElement("svg",ee({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 12 13"},e),J||(J=a.createElement("path",{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})))};function ie(){return ie=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},ie.apply(null,arguments)}var re=function(e){return a.createElement("svg",ie({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 14 15"},e),te||(te=a.createElement("g",{clipPath:"url(#state-hover_svg__a)"},a.createElement("path",{d:"M11.197 8.6 8.75 8.11V5.672a.583.583 0 0 0-.584-.584H7a.583.583 0 0 0-.584.584v2.916H5.25a.584.584 0 0 0-.522.845l1.75 3.5c.099.197.3.322.522.322h4.083a.583.583 0 0 0 .583-.583v-3.5a.58.58 0 0 0-.469-.572"}),a.createElement("path",{d:"M10.5 1.588h-7c-.644 0-1.167.524-1.167 1.167v4.083c0 .644.523 1.167 1.167 1.167h1.75V6.838H3.5V2.755h7v4.083h1.166V2.755c0-.643-.523-1.167-1.166-1.167"}))),ne||(ne=a.createElement("defs",null,a.createElement("clipPath",{id:"state-hover_svg__a"},a.createElement("path",{d:"M0 .422h14v14H0z"})))))};const le=[{value:s.NORMAL,label:i.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(ae,{onClick:e})},{value:s.HOVER,label:i.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(re,{onClick:e})}],oe={[s.NORMAL]:{icon:(0,a.createElement)(ae,null),label:i.__("Normal State","masterstudy-lms-learning-management-system")},[s.HOVER]:{icon:(0,a.createElement)(re,null),label:i.__("Hovered State","masterstudy-lms-learning-management-system")}},se=N("hover-state"),me=N("hover-state__selected"),ce=N("hover-state__selected__opened-menu"),de=N("has-changes"),ge=N("hover-state__menu"),ue=N("hover-state__menu__item"),pe=(0,T.memo)((e=>{const{hoverName:t,onChangeHoverName:n,fieldName:i}=e,{changedFieldsByName:r}=A(),l=r.get(i),{isOpen:o,onOpen:s,onClose:m}=x(),c=P(m),{ICONS_MAP:d,options:g}=(e=>{const t=(0,T.useMemo)((()=>le.filter((t=>t.value!==e))),[e]);return{ICONS_MAP:oe,options:t}})(t),u=(0,T.useCallback)((e=>{n(e),m()}),[n,m]);return(0,a.createElement)("div",{className:se,ref:c},(0,a.createElement)("div",{className:k()([me],{[ce]:o,[de]:l}),onClick:s,title:d[t]?.label},d[t]?.icon),(0,a.createElement)(O,{condition:o},(0,a.createElement)("div",{className:ge},g.map((({value:e,icon:t,label:n})=>(0,a.createElement)("div",{key:e,className:ue,title:n},t((()=>u(e)))))))))})),he={Desktop:{icon:"desktop",label:i.__("Desktop","masterstudy-lms-learning-management-system")},Tablet:{icon:"tablet",label:i.__("Tablet","masterstudy-lms-learning-management-system")},Mobile:{icon:"smartphone",label:i.__("Mobile","masterstudy-lms-learning-management-system")}},be=[{value:m.DESKTOP,icon:"desktop",label:i.__("Desktop","masterstudy-lms-learning-management-system")},{value:m.TABLET,icon:"tablet",label:i.__("Tablet","masterstudy-lms-learning-management-system")},{value:m.MOBILE,icon:"smartphone",label:i.__("Mobile","masterstudy-lms-learning-management-system")}],ve=N("device-picker"),ye=N("device-picker__selected"),_e=N("device-picker__selected__opened-menu"),Se=N("device-picker__menu"),Ce=N("device-picker__menu__item"),Ee=()=>{const{isOpen:e,onOpen:t,onClose:n}=x(),{value:i,onChange:r}=(e=>{const t=u(),n=(0,g.useDispatch)();return{value:(0,T.useMemo)((()=>he[t]),[t]),onChange:t=>{n("core/edit-site")&&n("core/edit-site").__experimentalSetPreviewDeviceType?n("core/edit-site").__experimentalSetPreviewDeviceType(t):n("core/edit-post")&&n("core/edit-post").__experimentalSetPreviewDeviceType?n("core/edit-post").__experimentalSetPreviewDeviceType(t):n("masterstudy/store").setDeviceType(t),e()}}})(n),l=(e=>(0,T.useMemo)((()=>be.filter((t=>t.icon!==e))),[e]))(i.icon),o=P(n);return(0,a.createElement)("div",{className:ve,ref:o},(0,a.createElement)(F.Dashicon,{className:k()([ye],{[_e]:e}),icon:i.icon,size:16,onClick:t,title:i.label}),(0,a.createElement)(O,{condition:e},(0,a.createElement)("div",{className:Se},l.map((e=>(0,a.createElement)(F.Dashicon,{key:e.value,icon:e.icon,size:16,onClick:()=>r(e.value),className:Ce,title:e.label}))))))},fe=N("reset-button"),we=({onReset:e})=>(0,a.createElement)(F.Dashicon,{icon:"undo",onClick:e,className:fe,size:16}),Ne=[{label:"PX",value:"px"},{label:"%",value:"%"},{label:"EM",value:"em"},{label:"REM",value:"rem"},{label:"VW",value:"vw"},{label:"VH",value:"vh"}],Te=N("unit"),xe=N("unit__single"),Me=N("unit__list"),Ue=({name:e,isAdaptive:t})=>{const{isOpen:n,onOpen:i,onClose:r}=x(),{fieldName:l}=R(e,t),{value:o,onChange:s}=B(l),m=P(r);return(0,a.createElement)("div",{className:Te,ref:m},(0,a.createElement)("div",{className:xe,onClick:i},o),(0,a.createElement)(O,{condition:n},(0,a.createElement)("div",{className:Me},Ne.map((({value:e,label:t})=>(0,a.createElement)("div",{key:e,onClick:()=>(s(e),void r())},t))))))},Ae=N("popover-modal"),Be=N("popover-modal__close dashicon dashicons dashicons-no-alt"),Re=e=>{const{isOpen:t,onClose:n,popoverContent:i}=e;return(0,a.createElement)(O,{condition:t},(0,a.createElement)(F.Popover,{position:"middle left",onClose:n,className:Ae},i,(0,a.createElement)("span",{onClick:n,className:Be})))},Le=N("setting-label"),He=N("setting-label__content"),Pe=e=>{const{label:t,isChanged:n=!1,onReset:i,showDevicePicker:r=!0,HoverStateControl:l=null,unitName:o,popoverContent:s=null,dependencies:m}=e,{isOpen:c,onClose:d,onToggle:g}=x();return H(m)?(0,a.createElement)("div",{className:Le},(0,a.createElement)("div",{className:He},(0,a.createElement)("div",{onClick:g},t),(0,a.createElement)(O,{condition:Boolean(s)},(0,a.createElement)(Re,{isOpen:c,onClose:d,popoverContent:s})),(0,a.createElement)(O,{condition:r},(0,a.createElement)(Ee,null)),(0,a.createElement)(O,{condition:Boolean(l)},l)),(0,a.createElement)(O,{condition:Boolean(o)},(0,a.createElement)(Ue,{name:o,isAdaptive:r})),(0,a.createElement)(O,{condition:n},(0,a.createElement)(we,{onReset:i}))):null},Fe=N("suffix"),De=()=>(0,a.createElement)("div",{className:Fe},(0,a.createElement)(F.Dashicon,{icon:"color-picker",size:16})),ke=N("color-picker"),Oe=e=>{const{name:t,label:n,placeholder:i,dependencyMode:r,dependencies:l,isAdaptive:o=!1,hasHover:s=!1}=e,{fieldName:m,hoverName:c,onChangeHoverName:d}=R(t,o,s),{value:g,isChanged:u,onChange:p,onReset:h}=B(m);return H(l,r)?(0,a.createElement)("div",{className:ke},(0,a.createElement)(O,{condition:Boolean(n)},(0,a.createElement)(Pe,{label:n,isChanged:u,onReset:h,showDevicePicker:o,HoverStateControl:(0,a.createElement)(O,{condition:s},(0,a.createElement)(pe,{hoverName:c,onChangeHoverName:d,fieldName:m}))})),(0,a.createElement)(F.__experimentalInputControl,{prefix:(0,a.createElement)(Q,{color:g,onChange:p}),suffix:(0,a.createElement)(De,null),onChange:p,value:g,placeholder:i})):null},We=N("number-steppers"),ze=N("indent-steppers"),Ie=N("indent-stepper-plus"),Ve=N("indent-stepper-minus"),$e=({onIncrement:e,onDecrement:t,withArrows:n=!1})=>n?(0,a.createElement)("span",{className:ze},(0,a.createElement)("button",{onClick:e,className:Ie}),(0,a.createElement)("button",{onClick:t,className:Ve})):(0,a.createElement)("span",{className:We},(0,a.createElement)("button",{onClick:e},"+"),(0,a.createElement)("button",{onClick:t},"-")),[je,Ge]=N(["indents","indents-control"]),Ze=({name:e,label:t,unitName:n,popoverContent:r,dependencyMode:l,dependencies:o,isAdaptive:s=!1})=>{const{fieldName:m}=R(e,s),{value:c,onResetSegmentedBox:d,hasChanges:g,handleInputIncrement:u,handleInputDecrement:p,updateDirectionsValues:h,lastFieldValue:b}=((e,t)=>{const{value:n,isChanged:a,onChange:i,onReset:r}=B(e),{onResetByFieldName:l,changedFieldsByName:o}=A(),s=a||o.get(t),m=e=>{i({...n,...e})},[c,d]=(0,T.useState)(!1);return{value:n,onResetSegmentedBox:()=>{r(),l.get(t)()},hasChanges:s,handleInputIncrement:e=>Number(n[e])+1,handleInputDecrement:e=>Number(n[e])-1,updateDirectionsValues:(e,t,n)=>{e?(d(!1),m({top:n,right:n,bottom:n,left:n})):(d(n),m({[t]:n}))},lastFieldValue:c}})(m,n),[v,y]=(0,T.useState)((()=>{const{left:e,right:t,top:n,bottom:a}=c;return""!==e&&e===t&&t===n&&n===a})),_=e=>{const[t,n]=Object.entries(e)[0];h(v,t,n)},S=e=>()=>{const t=u(e);h(v,e,String(t))},C=e=>()=>{const t=p(e);h(v,e,String(t))};return H(o,l)?(0,a.createElement)("div",{className:je},(0,a.createElement)(O,{condition:Boolean(t)},(0,a.createElement)(Pe,{label:null!=t?t:"",isChanged:g,onReset:d,unitName:n,popoverContent:r,showDevicePicker:s})),(0,a.createElement)("div",{className:`${Ge} ${v?"active":""}`},(0,a.createElement)("div",null,(0,a.createElement)(F.__experimentalNumberControl,{value:c.top,onChange:e=>{_({top:e})},spinControls:"none",suffix:(0,a.createElement)($e,{onIncrement:S("top"),onDecrement:C("top"),withArrows:!0})}),(0,a.createElement)("div",null,i.__("Top","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(F.__experimentalNumberControl,{value:c.right,onChange:e=>{_({right:e})},spinControls:"none",suffix:(0,a.createElement)($e,{onIncrement:S("right"),onDecrement:C("right"),withArrows:!0})}),(0,a.createElement)("div",null,i.__("Right","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(F.__experimentalNumberControl,{value:c.bottom,onChange:e=>{_({bottom:e})},spinControls:"none",suffix:(0,a.createElement)($e,{onIncrement:S("bottom"),onDecrement:C("bottom"),withArrows:!0})}),(0,a.createElement)("div",null,i.__("Bottom","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(F.__experimentalNumberControl,{value:c.left,onChange:e=>{_({left:e})},spinControls:"none",suffix:(0,a.createElement)($e,{onIncrement:S("left"),onDecrement:C("left"),withArrows:!0})}),(0,a.createElement)("div",null,i.__("Left","masterstudy-lms-learning-management-system"))),(0,a.createElement)(F.Dashicon,{icon:"dashicons "+(v?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{v||!1===b||h(!0,"left",b),y((e=>!e))}}))):null},[Xe,qe,Ke,Ye]=N(["toggle-group-wrapper","toggle-group","toggle-group__toggle","toggle-group__active-toggle"]),Qe=e=>{const{name:t,options:n,label:i,isAdaptive:r=!1,dependencyMode:l,dependencies:o}=e,{fieldName:s}=R(t,r),{value:m,isChanged:c,onChange:d,onReset:g}=B(s);return H(o,l)?(0,a.createElement)("div",{className:Xe},(0,a.createElement)(O,{condition:Boolean(i)},(0,a.createElement)(Pe,{label:i,isChanged:c,onReset:g,showDevicePicker:r})),(0,a.createElement)("div",{className:qe},n.map((e=>(0,a.createElement)("div",{key:e.value,className:k()([Ke],{[Ye]:e.value===m}),onClick:()=>d(e.value)},e.label))))):null},[Je,et,tt,nt]=N(["border-control","border-control-solid","border-control-dashed","border-control-dotted"]),at=e=>{const{label:t,borderStyleName:n,borderColorName:r,borderWidthName:l,dependencyMode:o,dependencies:s,isAdaptive:m=!1,hasHover:c=!1}=e,[d,g]=(0,T.useState)("Normal"),{fieldName:u,value:p,isChanged:h,onReset:b}=L(n,m,d),{fieldName:v,isChanged:y,onReset:_}=L(r,m,d),{fieldName:S,isChanged:C,onReset:E}=L(l,m,d);if(!H(s,o))return null;const f=h||y||C;return(0,a.createElement)("div",{className:k()([Je],{"has-reset-button":f})},(0,a.createElement)(Pe,{label:t,isChanged:f,onReset:()=>{b(),_(),E()},showDevicePicker:m,HoverStateControl:(0,a.createElement)(O,{condition:c},(0,a.createElement)(K,{stateOptions:["Normal","Hover"],currentState:d,onSelect:g}))}),(0,a.createElement)(Qe,{options:[{label:(0,a.createElement)("span",null,i.__("None","masterstudy-lms-learning-management-system")),value:"none"},{label:(0,a.createElement)("span",{className:et}),value:"solid"},{label:(0,a.createElement)("span",{className:tt},(0,a.createElement)("span",null)),value:"dashed"},{label:(0,a.createElement)("span",{className:nt},(0,a.createElement)("span",null,(0,a.createElement)("span",null))),value:"dotted"}],name:u}),(0,a.createElement)(O,{condition:"none"!==p},(0,a.createElement)(Oe,{name:v,placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Ze,{name:S})))},it=N("border-radius"),rt=N("border-radius-control"),lt=({name:e,label:t,unitName:n,popoverContent:i,dependencyMode:r,dependencies:l,isAdaptive:o=!1,hasHover:s=!1})=>{const{fieldName:m}=R(e,o,s),{value:c,onResetBorderRadius:d,hasChanges:g,handleInputIncrement:u,handleInputDecrement:p,updateDirectionsValues:h,lastFieldValue:b}=((e,t)=>{const[n,a]=(0,T.useState)(!1),{value:i,isChanged:r,onChange:l,onReset:o}=B(e),{onResetByFieldName:s,changedFieldsByName:m}=A(),c=r||m.get(t),d=e=>{l({...i,...e})};return{value:i,onResetBorderRadius:()=>{o(),s.get(t)()},hasChanges:c,handleInputIncrement:e=>Number(i[e])+1,handleInputDecrement:e=>Number(i[e])-1,updateDirectionsValues:(e,t,n)=>{e?(d({top:n,right:n,bottom:n,left:n}),a(!1)):(d({[t]:n}),a(n))},lastFieldValue:n}})(m,n),[v,y]=(0,T.useState)((()=>{const{left:e,right:t,top:n,bottom:a}=c;return""!==e&&e===t&&t===n&&n===a})),_=e=>{const[t,n]=Object.entries(e)[0];h(v,t,n)},S=e=>()=>{const t=u(e);h(v,e,String(t))},C=e=>()=>{const t=p(e);h(v,e,String(t))};return H(l,r)?(0,a.createElement)("div",{className:it},(0,a.createElement)(Pe,{label:t,isChanged:g,onReset:d,unitName:n,popoverContent:i,showDevicePicker:o}),(0,a.createElement)("div",{className:k()([rt],{"has-reset-button":g,active:v})},(0,a.createElement)("div",{className:"number-control-top"},(0,a.createElement)(F.__experimentalNumberControl,{value:c.top,onChange:e=>{_({top:e})},spinControls:"none",suffix:(0,a.createElement)($e,{onIncrement:S("top"),onDecrement:C("top"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-right"},(0,a.createElement)(F.__experimentalNumberControl,{value:c.right,onChange:e=>{_({right:e})},spinControls:"none",suffix:(0,a.createElement)($e,{onIncrement:S("right"),onDecrement:C("right"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-left"},(0,a.createElement)(F.__experimentalNumberControl,{value:c.left,onChange:e=>{_({left:e})},spinControls:"none",suffix:(0,a.createElement)($e,{onIncrement:S("left"),onDecrement:C("left"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-bottom"},(0,a.createElement)(F.__experimentalNumberControl,{value:c.bottom,onChange:e=>{_({bottom:e})},spinControls:"none",suffix:(0,a.createElement)($e,{onIncrement:S("bottom"),onDecrement:C("bottom"),withArrows:!0})})),(0,a.createElement)(F.Dashicon,{icon:"dashicons "+(v?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{v||!1===b||h(!0,"left",b),y((e=>!e))}}))):null},ot=N("box-shadow-preset"),st=({preset:e})=>(0,a.createElement)("div",{className:ot},(0,a.createElement)("div",{style:{boxShadow:`${e.horizontal}px ${e.vertical}px ${e.blur}px ${e.spread}px rgba(0, 0, 0, 0.25) ${e.inset?"inset":""}`}})),mt=N("presets"),ct=N("presets__item-wrapper"),dt=N("presets__item-wrapper__preset"),gt=N("presets__item-wrapper__name"),ut=(0,T.memo)((e=>{const{presets:t,activePreset:n,onSelectPreset:i,PresetItem:r,detectIsActive:l,detectByIndex:o=!1}=e;return(0,a.createElement)("div",{className:mt},t.map((({name:e,...t},s)=>(0,a.createElement)("div",{key:s,className:k()([ct],{active:l(n,o?s:t)}),onClick:()=>i(t)},(0,a.createElement)("div",{className:dt},(0,a.createElement)(r,{preset:t})),(0,a.createElement)("span",{className:gt},e)))))})),pt=N("range-control"),ht=e=>{const{name:t,label:n,min:i,max:r,unitName:l,dependencyMode:o,dependencies:s,isAdaptive:m=!1}=e,{fieldName:c}=R(t,m),{value:d,onChange:g,onResetNumberField:u,hasChanges:p}=((e,t)=>{const{value:n,isChanged:a,onChange:i,onReset:r}=B(e),{onResetByFieldName:l,changedFieldsByName:o}=A();return{value:n,onChange:i,onResetNumberField:()=>{r(),l.get(t)()},hasChanges:a||o.get(t)}})(c,l);return H(s,o)?(0,a.createElement)("div",{className:pt},(0,a.createElement)(O,{condition:Boolean(n)},(0,a.createElement)(Pe,{label:n,isChanged:p,onReset:u,unitName:l,showDevicePicker:m})),(0,a.createElement)(F.RangeControl,{value:d,onChange:g,min:i,max:r})):null},bt=N("switch"),vt=e=>{const{name:t,label:n,dependencyMode:i,dependencies:r,isAdaptive:l=!1}=e,{fieldName:o}=R(t,l),{value:s,onChange:m}=B(o);return H(r,i)?(0,a.createElement)("div",{className:bt,"data-has-label":Boolean(n).toString()},(0,a.createElement)(F.ToggleControl,{label:n,checked:s,onChange:m}),(0,a.createElement)(O,{condition:l},(0,a.createElement)(Ee,null))):null},yt=N("box-shadow-settings"),_t=N("box-shadow-presets-title"),St=[{name:"Drop",horizontal:0,vertical:2,blur:2,spread:0,inset:!1},{name:"Glow",horizontal:0,vertical:4,blur:20,spread:0,inset:!1},{name:"Outline",horizontal:0,vertical:2,blur:10,spread:0,inset:!1},{name:"Sparse",horizontal:0,vertical:10,blur:50,spread:0,inset:!1}],Ct=e=>{const{label:t,min:n,max:r,shadowColorName:l,shadowHorizontalName:o,shadowVerticalName:s,shadowBlurName:m,shadowSpreadName:c,shadowInsetName:d,popoverContent:g,dependencyMode:u,dependencies:p,isAdaptive:h=!1,hasHover:v=!1,presets:y=St}=e,[_,S]=(0,T.useState)("Normal"),{fieldName:C}=L(l,h,_),{fieldName:E}=L(o,h,_),{fieldName:f}=L(s,h,_),{fieldName:w}=L(m,h,_),{fieldName:N}=L(c,h,_),{fieldName:x}=L(d,h,_),{isChanged:M,onReset:U,onSelectPreset:R,activePreset:P}=((e,t,n,a,i,r)=>{const{setAttributes:l}=A(),{value:o,isChanged:s,onReset:m}=B(e),{value:c,isChanged:d,onReset:g}=B(t),{value:u,isChanged:p,onReset:h}=B(n),{value:b,isChanged:v,onReset:y}=B(a),{value:_,isChanged:S,onReset:C}=B(i),{value:E,isChanged:f,onReset:w}=B(r),N=s||d||p||v||S||f,x=(0,T.useCallback)((e=>{const{horizontal:o,vertical:s,blur:m,spread:c,inset:d}=e;l({[t]:o,[n]:s,[a]:m,[i]:c,[r]:d})}),[t,n,a,i,l,r]);return{activePreset:(0,T.useMemo)((()=>({horizontal:c,vertical:u,blur:b,spread:_,inset:null!=E&&E})),[c,u,b,_,E]),onReset:()=>{[m,g,h,y,C,w].forEach((e=>e()))},isChanged:N,onSelectPreset:x}})(C,E,f,w,N,x);return H(p,u)?(0,a.createElement)("div",{className:yt},(0,a.createElement)(Pe,{label:t,isChanged:M,onReset:U,popoverContent:g,showDevicePicker:h,HoverStateControl:(0,a.createElement)(O,{condition:v},(0,a.createElement)(K,{stateOptions:["Normal","Hover"],currentState:_,onSelect:S}))}),(0,a.createElement)(ut,{presets:y,onSelectPreset:R,activePreset:P,PresetItem:st,detectIsActive:b,detectByIndex:!1}),(0,a.createElement)(Oe,{name:C,label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:"Select color"}),(0,a.createElement)(ht,{name:E,label:i.__("Horizontal Offset","masterstudy-lms-learning-management-system"),min:n,max:r}),(0,a.createElement)(ht,{name:f,label:i.__("Vertical Offset","masterstudy-lms-learning-management-system"),min:n,max:r}),(0,a.createElement)(ht,{name:w,label:i.__("Blur","masterstudy-lms-learning-management-system"),min:n,max:r}),(0,a.createElement)(ht,{name:N,label:i.__("Spread","masterstudy-lms-learning-management-system"),min:n,max:r}),(0,a.createElement)("div",{className:_t},i.__("Position","masterstudy-lms-learning-management-system")),(0,a.createElement)(vt,{name:x,label:i.__("Inset","masterstudy-lms-learning-management-system")})):null},Et=(N("input-field"),N("input-field-control"),N("number-field")),ft=N("number-field-control"),wt=e=>{const{name:t,label:n,unitName:i,help:r,popoverContent:l,dependencyMode:o,dependencies:s,isAdaptive:m=!1}=e,{fieldName:c}=R(t,m),{value:d,onResetNumberField:g,hasChanges:u,handleIncrement:p,handleDecrement:h,handleInputChange:b}=((e,t)=>{const{value:n,isChanged:a,onChange:i,onReset:r}=B(e),{onResetByFieldName:l,changedFieldsByName:o}=A(),s=a||o.get(t);return{value:n,onResetNumberField:()=>{r(),l.get(t)()},hasChanges:s,handleIncrement:()=>{i(n+1)},handleDecrement:()=>{i(n-1)},handleInputChange:e=>{const t=Number(""===e?0:e);i(t)}}})(c,i);return H(s,o)?(0,a.createElement)("div",{className:Et},(0,a.createElement)(Pe,{label:n,isChanged:u,onReset:g,unitName:i,showDevicePicker:m,popoverContent:l}),(0,a.createElement)("div",{className:ft},(0,a.createElement)(F.__experimentalNumberControl,{value:d,onChange:b,spinControls:"none",suffix:(0,a.createElement)($e,{onIncrement:p,onDecrement:h})})),r&&(0,a.createElement)("small",null,r)):null},Nt=({className:e})=>(0,a.createElement)("div",{className:e},i.__("No options","masterstudy-lms-learning-management-system")),Tt=N("select__single-item"),xt=N("select__container"),Mt=N("select__container__multi-item"),Ut=({multiple:e,value:t,options:n,onChange:i})=>{const{singleValue:r,multipleValue:l}=((e,t,n)=>({singleValue:(0,T.useMemo)((()=>t?null:n.find((t=>t.value===e))?.label),[t,e,n]),multipleValue:(0,T.useMemo)((()=>t?e:null),[t,e])}))(t,e,n);return(0,a.createElement)(O,{condition:e,fallback:(0,a.createElement)("div",{className:Tt},r)},(0,a.createElement)("div",{className:xt},l?.map((e=>{const t=n.find((t=>t.value===e));return t?(0,a.createElement)("div",{key:t.value,className:Mt},(0,a.createElement)("div",null,t.label),(0,a.createElement)(F.Dashicon,{icon:"no-alt",onClick:()=>i(t.value),size:16})):null}))))},At=N("select"),Bt=N("select__select-box"),Rt=N("select__placeholder"),Lt=N("select__select-box-multiple"),Ht=N("select__menu"),Pt=N("select__menu__options-container"),Ft=N("select__menu__item"),Dt=e=>{const{options:t,multiple:n=!1,placeholder:i="Select",value:r,onSelect:l}=e,{isOpen:o,onToggle:s,onClose:m}=x(),c=P(m),d=((e,t,n)=>(0,T.useMemo)((()=>n&&Array.isArray(e)?t.filter((t=>!e.includes(t.value))):t.filter((t=>t.value!==e))),[e,t,n]))(r,t,n),g=((e,t,n,a)=>(0,T.useCallback)((i=>{if(t&&Array.isArray(e)){const t=e.includes(i)?e.filter((e=>e!==i)):[...e,i];n(t)}else n(i),a()}),[t,e,n,a]))(r,n,l,m),u=((e,t)=>(0,T.useMemo)((()=>t&&Array.isArray(e)?Boolean(e.length):Boolean(e)),[t,e]))(r,n),p=n&&Array.isArray(r)&&r?.length>0;return(0,a.createElement)("div",{className:At,ref:c},(0,a.createElement)("div",{className:k()([Bt],{[Lt]:p}),onClick:s},(0,a.createElement)(O,{condition:u,fallback:(0,a.createElement)("div",{className:Rt},i)},(0,a.createElement)(Ut,{onChange:g,options:t,multiple:n,value:r})),(0,a.createElement)(F.Dashicon,{icon:o?"arrow-up-alt2":"arrow-down-alt2",size:16,style:{color:"#4D5E6F"}})),(0,a.createElement)(O,{condition:o},(0,a.createElement)("div",{className:Ht},(0,a.createElement)(O,{condition:Boolean(d.length),fallback:(0,a.createElement)(Nt,{className:Ft})},(0,a.createElement)("div",{className:Pt},d.map((e=>(0,a.createElement)("div",{key:e.value,onClick:()=>g(e.value),className:Ft},e.label))))))))},kt=N("setting-select"),Ot=e=>{const{name:t,options:n,label:i,multiple:r=!1,placeholder:l,isAdaptive:o=!1,dependencyMode:s,dependencies:m}=e,{fieldName:c}=R(t,o),{value:d,isChanged:g,onChange:u,onReset:p}=B(c);return H(m,s)?(0,a.createElement)("div",{className:kt},(0,a.createElement)(O,{condition:Boolean(i)},(0,a.createElement)(Pe,{label:i,isChanged:g,onReset:p,showDevicePicker:o})),(0,a.createElement)(Dt,{options:n,value:d,onSelect:u,multiple:r,placeholder:l})):null},Wt=(N("row-select"),N("row-select__label"),N("row-select__control"),N("typography-select")),zt=N("typography-select-label"),It=e=>{const{name:t,label:n,options:i,isAdaptive:r=!1}=e,{fieldName:l}=R(t,r),{isChanged:o,onReset:s}=B(l);return(0,a.createElement)("div",{className:Wt},(0,a.createElement)("div",{className:zt},(0,a.createElement)("div",null,n),(0,a.createElement)(O,{condition:r},(0,a.createElement)(Ee,null))),(0,a.createElement)(Ot,{name:t,options:i,isAdaptive:r}),(0,a.createElement)(O,{condition:o},(0,a.createElement)(we,{onReset:s})))},Vt=N("typography"),$t=e=>{const{fontSizeName:t,fontWeightName:n,textTransformName:r,fontStyleName:l,textDecorationName:o,lineHeightName:s,letterSpacingName:m,wordSpacingName:c,fontSizeUnitName:d,lineHeightUnitName:g,letterSpacingUnitName:u,wordSpacingUnitName:p,dependencyMode:h,dependencies:b,isAdaptive:v=!1}=e,{fontWeightOptions:y,textTransformOptions:_,fontStyleOptions:S,textDecorationOptions:C}={fontWeightOptions:[{label:i.__("100 (Thin)","masterstudy-lms-learning-management-system"),value:"100"},{label:i.__("200 (Extra Light)","masterstudy-lms-learning-management-system"),value:"200"},{label:i.__("300 (Light)","masterstudy-lms-learning-management-system"),value:"300"},{label:i.__("400 (Normal)","masterstudy-lms-learning-management-system"),value:"400"},{label:i.__("500 (Medium)","masterstudy-lms-learning-management-system"),value:"500"},{label:i.__("600 (Semi Bold)","masterstudy-lms-learning-management-system"),value:"600"},{label:i.__("700 (Bold)","masterstudy-lms-learning-management-system"),value:"700"},{label:i.__("800 (Extra Bold)","masterstudy-lms-learning-management-system"),value:"800"},{label:i.__("900 (Extra)","masterstudy-lms-learning-management-system"),value:"900"}],textTransformOptions:[{label:i.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:i.__("Uppercase","masterstudy-lms-learning-management-system"),value:"uppercase"},{label:i.__("Lowercase","masterstudy-lms-learning-management-system"),value:"lowercase"},{label:i.__("Capitalize","masterstudy-lms-learning-management-system"),value:"capitalize"},{label:i.__("Normal","masterstudy-lms-learning-management-system"),value:"none"}],fontStyleOptions:[{label:i.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:i.__("Normal","masterstudy-lms-learning-management-system"),value:"none"},{label:i.__("Italic","masterstudy-lms-learning-management-system"),value:"italic"},{label:i.__("Oblique","masterstudy-lms-learning-management-system"),value:"oblique"}],textDecorationOptions:[{label:i.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:i.__("Underline","masterstudy-lms-learning-management-system"),value:"underline"},{label:i.__("Line Through","masterstudy-lms-learning-management-system"),value:"line-through"},{label:i.__("None","masterstudy-lms-learning-management-system"),value:"none"}]};return H(b,h)?(0,a.createElement)("div",{className:Vt},(0,a.createElement)(ht,{name:t,label:i.__("Size","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:d,isAdaptive:v}),(0,a.createElement)(It,{name:n,label:i.__("Weight","masterstudy-lms-learning-management-system"),options:y}),(0,a.createElement)(It,{name:r,label:i.__("Transform","masterstudy-lms-learning-management-system"),options:_}),(0,a.createElement)(It,{name:l,label:i.__("Style","masterstudy-lms-learning-management-system"),options:S}),(0,a.createElement)(It,{name:o,label:i.__("Decoration","masterstudy-lms-learning-management-system"),options:C}),(0,a.createElement)(ht,{name:s,label:i.__("Line Height","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:g,isAdaptive:v}),(0,a.createElement)(ht,{name:m,label:i.__("Letter Spacing","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:u,isAdaptive:v}),c&&(0,a.createElement)(ht,{name:c,label:i.__("Word Spacing","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:p,isAdaptive:v})):null},jt=(N("file-upload"),N("file-upload__wrap"),N("file-upload__image"),N("file-upload__remove"),N("file-upload__replace"),(0,T.createContext)({activeTab:0,setActiveTab:()=>{}})),Gt=()=>{const e=(0,T.useContext)(jt);if(!e)throw new Error("useTabs should be used inside Tabs");return e},Zt=({children:e})=>{const[t,n]=(0,T.useState)(0);return(0,a.createElement)(jt.Provider,{value:{activeTab:t,setActiveTab:n}},(0,a.createElement)("div",{className:`active-tab-${t}`},e))},Xt=N("tab-list"),qt=({children:e})=>(0,a.createElement)("div",{className:Xt},T.Children.map(e,((e,t)=>(0,T.cloneElement)(e,{index:t})))),Kt=N("tab"),Yt=N("tab-active"),Qt=N("content"),Jt=({index:e,title:t,icon:n})=>{const{activeTab:i,setActiveTab:r}=Gt();return(0,a.createElement)("div",{className:k()([Kt],{[Yt]:i===e}),onClick:()=>r(e)},(0,a.createElement)("div",{className:Qt},(0,a.createElement)("div",null,n),(0,a.createElement)("div",null,t)))},en=({children:e})=>(0,a.createElement)("div",null,T.Children.map(e,((e,t)=>(0,T.cloneElement)(e,{index:t})))),tn=N("tab-panel"),nn=({index:e,children:t})=>{const{activeTab:n}=Gt();return n===e?(0,a.createElement)("div",{className:tn},t):null},an=({generalTab:e,styleTab:t,advancedTab:n})=>(0,a.createElement)(Zt,null,(0,a.createElement)(qt,null,(0,a.createElement)(Jt,{title:i.__("General","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(F.Dashicon,{icon:"layout"})}),(0,a.createElement)(Jt,{title:i.__("Style","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(F.Dashicon,{icon:"admin-appearance"})}),(0,a.createElement)(Jt,{title:i.__("Advanced","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(F.Dashicon,{icon:"admin-settings"})})),(0,a.createElement)(en,null,(0,a.createElement)(nn,null,e),(0,a.createElement)(nn,null,t),(0,a.createElement)(nn,null,n)));window.ReactDOM;"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement;function rn(e){const t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function ln(e){return"nodeType"in e}function on(e){var t,n;return e?rn(e)?e:ln(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function sn(e){const{Document:t}=on(e);return e instanceof t}function mn(e){return!rn(e)&&e instanceof on(e).HTMLElement}function cn(e){return e instanceof on(e).SVGElement}function dn(e){return e?rn(e)?e.document:ln(e)?sn(e)?e:mn(e)||cn(e)?e.ownerDocument:document:document:document}function gn(e){return function(t){for(var n=arguments.length,a=new Array(n>1?n-1:0),i=1;i<n;i++)a[i-1]=arguments[i];return a.reduce(((t,n)=>{const a=Object.entries(n);for(const[n,i]of a){const a=t[n];null!=a&&(t[n]=a+e*i)}return t}),{...t})}}const un=gn(-1);function pn(e){if(function(e){if(!e)return!1;const{TouchEvent:t}=on(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return function(e){return"clientX"in e&&"clientY"in e}(e)?{x:e.clientX,y:e.clientY}:null}var hn;!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(hn||(hn={}));const bn=Object.freeze({x:0,y:0});var vn,yn,Sn,Cn;!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(vn||(vn={}));class En{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach((e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)}))},this.target=e}add(e,t,n){var a;null==(a=this.target)||a.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function fn(e,t){const n=Math.abs(e.x),a=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+a**2)>t:"x"in t&&"y"in t?n>t.x&&a>t.y:"x"in t?n>t.x:"y"in t&&a>t.y}function wn(e){e.preventDefault()}function Nn(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(yn||(yn={})),(Cn=Sn||(Sn={})).Space="Space",Cn.Down="ArrowDown",Cn.Right="ArrowRight",Cn.Left="ArrowLeft",Cn.Up="ArrowUp",Cn.Esc="Escape",Cn.Enter="Enter";Sn.Space,Sn.Enter,Sn.Esc,Sn.Space,Sn.Enter;function Tn(e){return Boolean(e&&"distance"in e)}function xn(e){return Boolean(e&&"delay"in e)}class Mn{constructor(e,t,n){var a;void 0===n&&(n=function(e){const{EventTarget:t}=on(e);return e instanceof t?e:dn(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;const{event:i}=e,{target:r}=i;this.props=e,this.events=t,this.document=dn(r),this.documentListeners=new En(this.document),this.listeners=new En(n),this.windowListeners=new En(on(r)),this.initialCoordinates=null!=(a=pn(i))?a:bn,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),this.windowListeners.add(yn.Resize,this.handleCancel),this.windowListeners.add(yn.DragStart,wn),this.windowListeners.add(yn.VisibilityChange,this.handleCancel),this.windowListeners.add(yn.ContextMenu,wn),this.documentListeners.add(yn.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(xn(t))return void(this.timeoutId=setTimeout(this.handleStart,t.delay));if(Tn(t))return}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handleStart(){const{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(yn.Click,Nn,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(yn.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;const{activated:n,initialCoordinates:a,props:i}=this,{onMove:r,options:{activationConstraint:l}}=i;if(!a)return;const o=null!=(t=pn(e))?t:bn,s=un(a,o);if(!n&&l){if(Tn(l)){if(null!=l.tolerance&&fn(s,l.tolerance))return this.handleCancel();if(fn(s,l.distance))return this.handleStart()}return xn(l)&&fn(s,l.tolerance)?this.handleCancel():void 0}e.cancelable&&e.preventDefault(),r(o)}handleEnd(){const{onEnd:e}=this.props;this.detach(),e()}handleCancel(){const{onCancel:e}=this.props;this.detach(),e()}handleKeydown(e){e.code===Sn.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}const Un={move:{name:"pointermove"},end:{name:"pointerup"}};(class extends Mn{constructor(e){const{event:t}=e,n=dn(t.target);super(e,Un,n)}}).activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;return!(!n.isPrimary||0!==n.button||(null==a||a({event:n}),0))}}];const An={move:{name:"mousemove"},end:{name:"mouseup"}};var Bn;!function(e){e[e.RightClick=2]="RightClick"}(Bn||(Bn={})),class extends Mn{constructor(e){super(e,An,dn(e.event.target))}}.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;return n.button!==Bn.RightClick&&(null==a||a({event:n}),!0)}}];const Rn={move:{name:"touchmove"},end:{name:"touchend"}};var Ln,Hn,Pn,Fn,Dn;(class extends Mn{constructor(e){super(e,Rn)}static setup(){return window.addEventListener(Rn.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(Rn.move.name,e)};function e(){}}}).activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;const{touches:i}=n;return!(i.length>1||(null==a||a({event:n}),0))}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(Ln||(Ln={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(Hn||(Hn={})),vn.Backward,vn.Forward,vn.Backward,vn.Forward,function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(Pn||(Pn={})),function(e){e.Optimized="optimized"}(Fn||(Fn={})),Pn.WhileDragging,Fn.Optimized,Map,function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(Dn||(Dn={})),Sn.Down,Sn.Right,Sn.Up,Sn.Left,i.__("Lectures","masterstudy-lms-learning-management-system"),i.__("Duration","masterstudy-lms-learning-management-system"),i.__("Views","masterstudy-lms-learning-management-system"),i.__("Level","masterstudy-lms-learning-management-system"),i.__("Members","masterstudy-lms-learning-management-system"),i.__("Empty","masterstudy-lms-learning-management-system"),N("sortable__item"),N("sortable__item__disabled"),N("sortable__item__content"),N("sortable__item__content__drag-item"),N("sortable__item__content__drag-item__disabled"),N("sortable__item__content__title"),N("sortable__item__control"),N("sortable__item__icon"),N("nested-sortable"),N("nested-sortable__item"),N("sortable");const kn=N("accordion"),On=N("accordion__header"),Wn=N("accordion__header-flex"),zn=N("accordion__content"),In=N("accordion__icon"),Vn=N("accordion__title"),$n=N("accordion__title-disabled"),jn=N("accordion__indicator"),Gn=N("accordion__controls"),Zn=N("accordion__controls-disabled"),Xn=({title:e,children:t,accordionFields:n,switchName:i,visible:r=!0,isDefaultOpen:l=!1})=>{const{isOpen:o,onToggle:s,disabled:m,onReset:c,hasChanges:d,onClose:g}=((e,t,n)=>{var a;const{isOpen:i,onToggle:r,onClose:l}=x(t),{defaultValues:o,attributes:s,setAttributes:m}=A(),c=((e,t,n)=>{for(const a of n)if(!b(e[a],t[a]))return!0;return!1})(o,s,e);return{isOpen:i,onToggle:r,disabled:!(null===(a=s[n])||void 0===a||a),hasChanges:c,onReset:t=>{t.stopPropagation(),m(e.reduce(((e,t)=>(e[t]=o[t],e)),{}))},onClose:l}})(n,l,i);return((e,t)=>{const{attributes:n}=A(),a=!n[t];(0,T.useEffect)((()=>{a&&e()}),[a,e])})(g,i),r?(0,a.createElement)("div",{className:kn},(0,a.createElement)("div",{className:On},(0,a.createElement)("div",{className:Wn,onClick:m?null:s},(0,a.createElement)("div",{className:k()(Vn,{[$n]:m,"with-switch":Boolean(i)})},(0,a.createElement)("div",null,e),(0,a.createElement)(O,{condition:d&&!m},(0,a.createElement)("div",{className:jn}))),(0,a.createElement)("div",{className:k()(Gn,{[Zn]:m})},(0,a.createElement)(F.Dashicon,{icon:o?"arrow-up-alt2":"arrow-down-alt2",className:In,size:16}))),(0,a.createElement)(O,{condition:Boolean(i)},(0,a.createElement)(vt,{name:i})),(0,a.createElement)(O,{condition:d&&!m},(0,a.createElement)(we,{onReset:c}))),o&&(0,a.createElement)("div",{className:zn},t)):null};N("preset-picker"),N("preset-picker__label"),N("preset-picker__remove"),N("preset-picker__presets-list"),N("preset-picker__presets-list__item"),N("preset-picker__presets-list__item__preset"),N("preset-picker__presets-list__item__preset-active");let qn=function(e){return e.ALL="all",e.STARS="stars",e.STARS_AND_RATE="starsAndRate",e.STARS_AND_REVIEWS="starsAndReviews",e}({}),Kn=function(e){return e.BOXED_ROUNDED="boxedRounded",e.BOXED_SQUARED="boxedSquared",e.DEFAULT="default",e}({});const Yn={cardPreset:Kn.DEFAULT,showPosition:!0,showCourseCount:!1,showBiography:!0,showRating:!0,ratingStyle:qn.ALL,cardAlignment:c.START,showSocials:!0},Qn=Object.keys(Yn),Jn={presetMargin:h,presetMarginTablet:h,presetMarginMobile:h,presetMarginUnit:"px",presetMarginUnitTablet:"px",presetMarginUnitMobile:"px",presetPadding:h,presetPaddingTablet:h,presetPaddingMobile:h,presetPaddingUnit:"px",presetPaddingUnitTablet:"px",presetPaddingUnitMobile:"px"},ea=Object.keys(Jn),ta={cardPadding:{top:"0",right:"0",bottom:"20",left:"0"},cardPaddingTablet:h,cardPaddingMobile:h,cardPaddingUnit:"px",cardPaddingUnitTablet:"px",cardPaddingUnitMobile:"px",cardBackground:"",cardBackgroundHover:"",cardBorderStyle:"none",cardBorderStyleHover:"",cardBorderStyleTablet:"",cardBorderStyleHoverTablet:"",cardBorderStyleMobile:"",cardBorderStyleHoverMobile:"",cardBorderColor:"",cardBorderColorHover:"",cardBorderColorTablet:"",cardBorderColorHoverTablet:"",cardBorderColorMobile:"",cardBorderColorHoverMobile:"",cardBorderWidth:h,cardBorderWidthHover:h,cardBorderWidthTablet:h,cardBorderWidthHoverTablet:h,cardBorderWidthMobile:h,cardBorderWidthHoverMobile:h,cardBorderWidthUnit:"px",cardBorderWidthUnitTablet:"px",cardBorderWidthUnitMobile:"px",cardBorderRadius:h,cardBorderRadiusTablet:h,cardBorderRadiusMobile:h,cardBorderRadiusUnit:"px",cardBorderRadiusUnitTablet:"px",cardBorderRadiusUnitMobile:"px",cardShadowColor:"",cardShadowColorTablet:"",cardShadowColorMobile:"",cardShadowHorizontal:null,cardShadowHorizontalTablet:null,cardShadowHorizontalMobile:null,cardShadowVertical:null,cardShadowVerticalTablet:null,cardShadowVerticalMobile:null,cardShadowBlur:null,cardShadowBlurTablet:null,cardShadowBlurMobile:null,cardShadowSpread:null,cardShadowSpreadTablet:null,cardShadowSpreadMobile:null,cardShadowInset:!1,cardShadowInsetTablet:!1,cardShadowInsetMobile:!1},na=Object.keys(ta),aa={infoPadding:h,infoPaddingTablet:h,infoPaddingMobile:h,infoPaddingUnit:"px",infoPaddingUnitTablet:"px",infoPaddingUnitMobile:"px",infoBackground:"",infoBorderStyle:"none",infoBorderStyleTablet:"",infoBorderStyleMobile:"",infoBorderColor:"",infoBorderColorTablet:"",infoBorderColorMobile:"",infoBorderWidth:h,infoBorderWidthTablet:h,infoBorderWidthMobile:h,infoBorderWidthUnit:"px",infoBorderWidthUnitTablet:"px",infoBorderWidthUnitMobile:"px",infoBorderRadius:h,infoBorderRadiusTablet:h,infoBorderRadiusMobile:h,infoBorderRadiusUnit:"px",infoBorderRadiusUnitTablet:"px",infoBorderRadiusUnitMobile:"px"},ia=Object.keys(aa),ra={imageHeight:230,imageHeightTablet:null,imageHeightMobile:null,imageHeightUnit:"px",imageHeightUnitTablet:"px",imageHeightUnitMobile:"px",imageBorderStyle:"none",imageBorderStyleTablet:"",imageBorderStyleMobile:"",imageBorderColor:"",imageBorderColorTablet:"",imageBorderColorMobile:"",imageBorderWidth:h,imageBorderWidthTablet:h,imageBorderWidthMobile:h,imageBorderWidthUnit:"px",imageBorderWidthUnitTablet:"px",imageBorderWidthUnitMobile:"px",imageBorderRadius:h,imageBorderRadiusTablet:h,imageBorderRadiusMobile:h,imageBorderRadiusUnit:"px",imageBorderRadiusUnitTablet:"px",imageBorderRadiusUnitMobile:"px"},la=Object.keys(ra),oa={nameFontSize:20,nameFontSizeTablet:null,nameFontSizeMobile:null,nameFontSizeUnit:"px",nameFontSizeUnitTablet:"px",nameFontSizeUnitMobile:"px",nameFontWeight:"700",nameTextTransform:"inherit",nameFontStyle:"inherit",nameTextDecoration:"inherit",nameLineHeight:24,nameLineHeightTablet:null,nameLineHeightMobile:null,nameLineHeightUnit:"px",nameLineHeightUnitTablet:"px",nameLineHeightUnitMobile:"px",nameLetterSpacing:0,nameLetterSpacingTablet:null,nameLetterSpacingMobile:null,nameLetterSpacingUnit:"px",nameLetterSpacingUnitTablet:"px",nameLetterSpacingUnitMobile:"px",nameWordSpacing:0,nameWordSpacingTablet:null,nameWordSpacingMobile:null,nameWordSpacingUnit:"px",nameWordSpacingUnitTablet:"px",nameWordSpacingUnitMobile:"px",nameColor:"#001931",nameMargin:{top:"30",right:"0",bottom:"0",left:"0"},nameMarginTablet:h,nameMarginMobile:h,nameMarginUnit:"px",nameMarginUnitTablet:"px",nameMarginUnitMobile:"px"},sa=Object.keys(oa),ma={positionFontSize:14,positionFontSizeTablet:null,positionFontSizeMobile:null,positionFontSizeUnit:"px",positionFontSizeUnitTablet:"px",positionFontSizeUnitMobile:"px",positionFontWeight:"400",positionTextTransform:"inherit",positionFontStyle:"inherit",positionTextDecoration:"inherit",positionLineHeight:16,positionLineHeightTablet:null,positionLineHeightMobile:null,positionLineHeightUnit:"px",positionLineHeightUnitTablet:"px",positionLineHeightUnitMobile:"px",positionLetterSpacing:0,positionLetterSpacingTablet:null,positionLetterSpacingMobile:null,positionLetterSpacingUnit:"px",positionLetterSpacingUnitTablet:"px",positionLetterSpacingUnitMobile:"px",positionWordSpacing:0,positionWordSpacingTablet:null,positionWordSpacingMobile:null,positionWordSpacingUnit:"px",positionWordSpacingUnitTablet:"px",positionWordSpacingUnitMobile:"px",positionColor:"#808c98",positionMargin:{top:"20",right:"0",bottom:"0",left:"0"},positionMarginTablet:h,positionMarginMobile:h,positionMarginUnit:"px",positionMarginUnitTablet:"px",positionMarginUnitMobile:"px"},ca=Object.keys(ma),da={biographyFontSize:14,biographyFontSizeTablet:null,biographyFontSizeMobile:null,biographyFontSizeUnit:"px",biographyFontSizeUnitTablet:"px",biographyFontSizeUnitMobile:"px",biographyFontWeight:"400",biographyTextTransform:"inherit",biographyFontStyle:"inherit",biographyTextDecoration:"inherit",biographyLineHeight:16,biographyLineHeightTablet:null,biographyLineHeightMobile:null,biographyLineHeightUnit:"px",biographyLineHeightUnitTablet:"px",biographyLineHeightUnitMobile:"px",biographyLetterSpacing:0,biographyLetterSpacingTablet:null,biographyLetterSpacingMobile:null,biographyLetterSpacingUnit:"px",biographyLetterSpacingUnitTablet:"px",biographyLetterSpacingUnitMobile:"px",biographyWordSpacing:0,biographyWordSpacingTablet:null,biographyWordSpacingMobile:null,biographyWordSpacingUnit:"px",biographyWordSpacingUnitTablet:"px",biographyWordSpacingUnitMobile:"px",biographyColor:"#4d5e6f",biographyMargin:{top:"16",right:"0",bottom:"0",left:"0"},biographyMarginTablet:h,biographyMarginMobile:h,biographyMarginUnit:"px",biographyMarginUnitTablet:"px",biographyMarginUnitMobile:"px"},ga=Object.keys(da),ua={ratingFontSize:18,ratingFontSizeTablet:null,ratingFontSizeMobile:null,ratingFontSizeUnit:"px",ratingFontSizeUnitTablet:"px",ratingFontSizeUnitMobile:"px",ratingFontWeight:"600",ratingTextTransform:"inherit",ratingFontStyle:"inherit",ratingTextDecoration:"inherit",ratingLineHeight:20,ratingLineHeightTablet:null,ratingLineHeightMobile:null,ratingLineHeightUnit:"px",ratingLineHeightUnitTablet:"px",ratingLineHeightUnitMobile:"px",ratingLetterSpacing:0,ratingLetterSpacingTablet:null,ratingLetterSpacingMobile:null,ratingLetterSpacingUnit:"px",ratingLetterSpacingUnitTablet:"px",ratingLetterSpacingUnitMobile:"px",ratingWordSpacing:0,ratingWordSpacingTablet:null,ratingWordSpacingMobile:null,ratingWordSpacingUnit:"px",ratingWordSpacingUnitTablet:"px",ratingWordSpacingUnitMobile:"px",ratingColor:"#001931",ratingEmptyStarsColor:"#b3bac2",ratingFilledStarsColor:"#ffa800",ratingMargin:{top:"16",right:"0",bottom:"0",left:"0"},ratingMarginTablet:h,ratingMarginMobile:h,ratingMarginUnit:"px",ratingMarginUnitTablet:"px",ratingMarginUnitMobile:"px"},pa=Object.keys(ua),ha={reviewCountFontSize:14,reviewCountFontSizeTablet:null,reviewCountFontSizeMobile:null,reviewCountFontSizeUnit:"px",reviewCountFontSizeUnitTablet:"px",reviewCountFontSizeUnitMobile:"px",reviewCountFontWeight:"400",reviewCountTextTransform:"inherit",reviewCountFontStyle:"inherit",reviewCountTextDecoration:"inherit",reviewCountLineHeight:16,reviewCountLineHeightTablet:null,reviewCountLineHeightMobile:null,reviewCountLineHeightUnit:"px",reviewCountLineHeightUnitTablet:"px",reviewCountLineHeightUnitMobile:"px",reviewCountLetterSpacing:0,reviewCountLetterSpacingTablet:null,reviewCountLetterSpacingMobile:null,reviewCountLetterSpacingUnit:"px",reviewCountLetterSpacingUnitTablet:"px",reviewCountLetterSpacingUnitMobile:"px",reviewCountWordSpacing:0,reviewCountWordSpacingTablet:null,reviewCountWordSpacingMobile:null,reviewCountWordSpacingUnit:"px",reviewCountWordSpacingUnitTablet:"px",reviewCountWordSpacingUnitMobile:"px",reviewCountColor:"#4D5E6F",reviewCountMargin:{top:"8",right:"0",bottom:"0",left:"0"},reviewCountMarginTablet:h,reviewCountMarginMobile:h,reviewCountMarginUnit:"px",reviewCountMarginUnitTablet:"px",reviewCountMarginUnitMobile:"px"},ba=Object.keys(ha),va={...Jn,...ta,...aa,...ra,...oa,...ma,...da,...ua,...ha},ya={...Yn,...va},_a=new Map([["presetMargin",{isAdaptive:!0,unit:"presetMarginUnit"}],["presetPadding",{isAdaptive:!0,unit:"presetPaddingUnit"}],["cardPadding",{unit:"cardPaddingUnit",isAdaptive:!0}],["cardBackground",{hasHover:!0}],["cardBorderStyle",{isAdaptive:!0,hasHover:!0}],["cardBorderColor",{isAdaptive:!0,hasHover:!0}],["cardBorderWidth",{unit:"cardBorderWidthUnit",isAdaptive:!0,hasHover:!0}],["cardBorderRadius",{unit:"cardBorderRadiusUnit",isAdaptive:!0}],["infoPadding",{unit:"infoPaddingUnit",isAdaptive:!0}],["infoBackground",{}],["infoBorderStyle",{isAdaptive:!0}],["infoBorderColor",{isAdaptive:!0}],["infoBorderWidth",{unit:"infoBorderWidthUnit",isAdaptive:!0}],["infoBorderRadius",{unit:"infoBorderRadiusUnit",isAdaptive:!0}],["imageHeight",{unit:"imageHeightUnit",isAdaptive:!0}],["imageBorderStyle",{isAdaptive:!0}],["imageBorderColor",{isAdaptive:!0}],["imageBorderWidth",{unit:"imageBorderWidthUnit",isAdaptive:!0}],["imageBorderRadius",{unit:"imageBorderRadiusUnit",isAdaptive:!0}],["nameFontSize",{unit:"nameFontSizeUnit",isAdaptive:!0}],["nameFontWeight",{}],["nameTextTransform",{}],["nameFontStyle",{}],["nameTextDecoration",{}],["nameLineHeight",{unit:"nameLineHeightUnit",isAdaptive:!0}],["nameLetterSpacing",{unit:"nameLetterSpacingUnit",isAdaptive:!0}],["nameWordSpacing",{unit:"nameWordSpacingUnit",isAdaptive:!0}],["nameColor",{}],["nameMargin",{unit:"nameMarginUnit",isAdaptive:!0}],["positionFontSize",{unit:"positionFontSizeUnit",isAdaptive:!0}],["positionFontWeight",{}],["positionTextTransform",{}],["positionFontStyle",{}],["positionTextDecoration",{}],["positionLineHeight",{unit:"positionLineHeightUnit",isAdaptive:!0}],["positionLetterSpacing",{unit:"positionLetterSpacingUnit",isAdaptive:!0}],["positionWordSpacing",{unit:"positionWordSpacingUnit",isAdaptive:!0}],["positionColor",{}],["positionMargin",{unit:"positionMarginUnit",isAdaptive:!0}],["biographyFontSize",{unit:"biographyFontSizeUnit",isAdaptive:!0}],["biographyFontWeight",{}],["biographyTextTransform",{}],["biographyFontStyle",{}],["biographyTextDecoration",{}],["biographyLineHeight",{unit:"biographyLineHeightUnit",isAdaptive:!0}],["biographyLetterSpacing",{unit:"biographyLetterSpacingUnit",isAdaptive:!0}],["biographyWordSpacing",{unit:"biographyWordSpacingUnit",isAdaptive:!0}],["biographyColor",{}],["biographyMargin",{unit:"biographyMarginUnit",isAdaptive:!0}],["ratingFontSize",{unit:"ratingFontSizeUnit",isAdaptive:!0}],["ratingFontWeight",{}],["ratingTextTransform",{}],["ratingFontStyle",{}],["ratingTextDecoration",{}],["ratingLineHeight",{unit:"ratingLineHeightUnit",isAdaptive:!0}],["ratingLetterSpacing",{unit:"ratingLetterSpacingUnit",isAdaptive:!0}],["ratingWordSpacing",{unit:"ratingWordSpacingUnit",isAdaptive:!0}],["ratingColor",{}],["ratingEmptyStarsColor",{}],["ratingFilledStarsColor",{}],["ratingMargin",{unit:"ratingMarginUnit",isAdaptive:!0}],["reviewCountFontSize",{unit:"reviewCountFontSizeUnit",isAdaptive:!0}],["reviewCountFontWeight",{}],["reviewCountTextTransform",{}],["reviewCountFontStyle",{}],["reviewCountTextDecoration",{}],["reviewCountLineHeight",{unit:"reviewCountLineHeightUnit",isAdaptive:!0}],["reviewCountLetterSpacing",{unit:"reviewCountLetterSpacingUnit",isAdaptive:!0}],["reviewCountWordSpacing",{unit:"reviewCountWordSpacingUnit",isAdaptive:!0}],["reviewCountColor",{}],["reviewCountMargin",{unit:"reviewCountMarginUnit",isAdaptive:!0}]]),Sa={cardAlignment:c.CENTER,imageBorderRadius:{top:"200",right:"200",bottom:"200",left:"200"},imageHeight:205,cardPadding:{top:"30",right:"30",bottom:"30",left:"30"},cardBorderRadius:{top:"4",right:"4",bottom:"4",left:"4"},cardBackgroundHover:"#FFFFFF",infoPadding:ya.infoPadding,ratingMargin:ya.ratingMargin,reviewCountMargin:ya.reviewCountMargin},Ca={cardAlignment:c.CENTER,imageBorderRadius:ya.imageBorderRadius,imageHeight:ya.imageHeight,cardPadding:ya.cardPadding,cardBorderRadius:{top:"4",right:"4",bottom:"4",left:"4"},cardBackgroundHover:"#FFFFFF",infoPadding:{top:"0",right:"10",bottom:"0",left:"10"},ratingMargin:{top:"16",right:"0",bottom:"0",left:"10"},reviewCountMargin:{top:"0",right:"0",bottom:"0",left:"10"}},Ea={cardAlignment:ya.cardAlignment,imageBorderRadius:ya.imageBorderRadius,imageHeight:ya.imageHeight,cardPadding:ya.cardPadding,cardBorderRadius:ya.cardBorderRadius,cardBackgroundHover:ya.cardBackgroundHover,infoPadding:ya.infoPadding,ratingMargin:ya.ratingMargin,reviewCountMargin:ya.reviewCountMargin},fa={[Kn.DEFAULT]:Ea,[Kn.BOXED_ROUNDED]:Sa,[Kn.BOXED_SQUARED]:Ca},wa=()=>{const{instructorCardTypes:e,ratingStyles:t,cardAlignments:n}={instructorCardTypes:[{label:i.__("Boxed Card Picture Rounded","masterstudy-lms-learning-management-system"),value:Kn.BOXED_ROUNDED},{label:i.__("Boxed Card Picture Squared","masterstudy-lms-learning-management-system"),value:Kn.BOXED_SQUARED},{label:i.__("Squared Picture Default","masterstudy-lms-learning-management-system"),value:Kn.DEFAULT}],ratingStyles:[{label:i.__("Stars, Rate, Reviews Count","masterstudy-lms-learning-management-system"),value:qn.ALL},{label:i.__("Stars","masterstudy-lms-learning-management-system"),value:qn.STARS},{label:i.__("Stars, Rate","masterstudy-lms-learning-management-system"),value:qn.STARS_AND_RATE},{label:i.__("Stars, Reviews Count","masterstudy-lms-learning-management-system"),value:qn.STARS_AND_REVIEWS}],cardAlignments:[{label:i.__("Left","masterstudy-lms-learning-management-system"),value:c.START},{label:i.__("Center","masterstudy-lms-learning-management-system"),value:c.CENTER},{label:i.__("Right","masterstudy-lms-learning-management-system"),value:c.END}]};return(0,a.createElement)(a.Fragment,null,(0,a.createElement)(Xn,{title:i.__("Instructor Card","masterstudy-lms-learning-management-system"),accordionFields:Qn},(0,a.createElement)(Ot,{name:"cardPreset",label:i.__("Presets","masterstudy-lms-learning-management-system"),options:e}),(0,a.createElement)(vt,{name:"showPosition",label:i.__("Position","masterstudy-lms-learning-management-system")}),(0,a.createElement)(vt,{name:"showCourseCount",label:i.__("Course Count","masterstudy-lms-learning-management-system")}),(0,a.createElement)(vt,{name:"showBiography",label:i.__("Biography","masterstudy-lms-learning-management-system")}),(0,a.createElement)(vt,{name:"showRating",label:i.__("Rating","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Ot,{name:"ratingStyle",label:i.__("Rating style","masterstudy-lms-learning-management-system"),options:t,dependencies:[{name:"showRating",value:!0}]}),(0,a.createElement)(Qe,{name:"cardAlignment",label:i.__("Card content alignment","masterstudy-lms-learning-management-system"),options:n}),(0,a.createElement)(vt,{name:"showSocials",label:i.__("Show socials","masterstudy-lms-learning-management-system")})))},Na=[{name:"No Shadow",horizontal:0,vertical:0,blur:0,spread:0,inset:!1},{name:"Outline",horizontal:0,vertical:2,blur:10,spread:0,inset:!1}],Ta=()=>{const{attributes:e}=A();return(0,a.createElement)(a.Fragment,null,(0,a.createElement)(Xn,{title:i.__("Container","masterstudy-lms-learning-management-system"),accordionFields:ea},(0,a.createElement)(Ze,{name:"presetMargin",label:i.__("Margin","masterstudy-lms-learning-management-system"),unitName:"presetMarginUnit",isAdaptive:!0}),(0,a.createElement)(Ze,{name:"presetPadding",label:i.__("Padding","masterstudy-lms-learning-management-system"),unitName:"presetPaddingUnit",isAdaptive:!0})),(0,a.createElement)(Xn,{title:i.__("Card","masterstudy-lms-learning-management-system"),accordionFields:na},(0,a.createElement)(Ze,{name:"cardPadding",label:i.__("Padding","masterstudy-lms-learning-management-system"),unitName:"cardPaddingUnit",isAdaptive:!0}),(0,a.createElement)(Oe,{name:"cardBackground",label:i.__("Background","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,a.createElement)(at,{label:i.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"cardBorderStyle",borderColorName:"cardBorderColor",borderWidthName:"cardBorderWidth",isAdaptive:!0,hasHover:!0}),(0,a.createElement)(lt,{name:"cardBorderRadius",label:i.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,a.createElement)(Ct,{label:i.__("Shadow","masterstudy-lms-learning-management-system"),min:0,max:100,shadowColorName:"cardShadowColor",shadowHorizontalName:"cardShadowHorizontal",shadowVerticalName:"cardShadowVertical",shadowBlurName:"cardShadowBlur",shadowSpreadName:"cardShadowSpread",shadowInsetName:"cardShadowInset",popoverContent:null,presets:Na,isAdaptive:!0})),(0,a.createElement)(Xn,{title:i.__("Card: Infoblock","masterstudy-lms-learning-management-system"),accordionFields:ia},(0,a.createElement)(Ze,{name:"infoPadding",label:i.__("Padding","masterstudy-lms-learning-management-system"),unitName:"infoPaddingUnit",isAdaptive:!0}),(0,a.createElement)(Oe,{name:"infoBackground",label:i.__("Background","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(at,{label:i.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"infoBorderStyle",borderColorName:"infoBorderColor",borderWidthName:"infoBorderWidth",isAdaptive:!0}),(0,a.createElement)(lt,{name:"infoBorderRadius",label:i.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0})),(0,a.createElement)(Xn,{title:i.__("Card: Instructor’s profile picture","masterstudy-lms-learning-management-system"),accordionFields:la},(0,a.createElement)(wt,{name:"imageHeight",label:i.__("Height","masterstudy-lms-learning-management-system"),unitName:"imageHeightUnit",isAdaptive:!0}),(0,a.createElement)(at,{label:i.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"imageBorderStyle",borderColorName:"imageBorderColor",borderWidthName:"imageBorderWidth",isAdaptive:!0}),(0,a.createElement)(lt,{name:"imageBorderRadius",label:i.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0})),(0,a.createElement)(Xn,{title:i.__("Card: Instructor’s name","masterstudy-lms-learning-management-system"),accordionFields:sa},(0,a.createElement)($t,{fontSizeName:"nameFontSize",fontSizeUnitName:"nameFontSizeUnit",fontWeightName:"nameFontWeight",textTransformName:"nameTextTransform",fontStyleName:"nameFontStyle",textDecorationName:"nameTextDecoration",lineHeightName:"nameLineHeight",lineHeightUnitName:"nameLineHeightUnit",letterSpacingName:"nameLetterSpacing",letterSpacingUnitName:"nameLetterSpacingUnit",wordSpacingName:"nameWordSpacing",wordSpacingUnitName:"nameWordSpacingUnit",isAdaptive:!0}),(0,a.createElement)(Oe,{name:"nameColor",label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Ze,{name:"nameMargin",label:i.__("Margin","masterstudy-lms-learning-management-system"),unitName:"nameMarginUnit",isAdaptive:!0})),(0,a.createElement)(Xn,{title:i.__("Card: Instructor’s position","masterstudy-lms-learning-management-system"),accordionFields:ca,visible:Boolean(e.showPosition)},(0,a.createElement)($t,{fontSizeName:"positionFontSize",fontSizeUnitName:"positionFontSizeUnit",fontWeightName:"positionFontWeight",textTransformName:"positionTextTransform",fontStyleName:"positionFontStyle",textDecorationName:"positionTextDecoration",lineHeightName:"positionLineHeight",lineHeightUnitName:"positionLineHeightUnit",letterSpacingName:"positionLetterSpacing",letterSpacingUnitName:"positionLetterSpacingUnit",wordSpacingName:"positionWordSpacing",wordSpacingUnitName:"positionWordSpacingUnit",isAdaptive:!0}),(0,a.createElement)(Oe,{name:"positionColor",label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Ze,{name:"positionMargin",label:i.__("Margin","masterstudy-lms-learning-management-system"),unitName:"positionMarginUnit",isAdaptive:!0})),(0,a.createElement)(Xn,{title:i.__("Card: Instructor’s biography","masterstudy-lms-learning-management-system"),accordionFields:ga,visible:Boolean(e.showBiography)},(0,a.createElement)($t,{fontSizeName:"biographyFontSize",fontSizeUnitName:"biographyFontSizeUnit",fontWeightName:"biographyFontWeight",textTransformName:"biographyTextTransform",fontStyleName:"biographyFontStyle",textDecorationName:"biographyTextDecoration",lineHeightName:"biographyLineHeight",lineHeightUnitName:"biographyLineHeightUnit",letterSpacingName:"biographyLetterSpacing",letterSpacingUnitName:"biographyLetterSpacingUnit",wordSpacingName:"biographyWordSpacing",wordSpacingUnitName:"biographyWordSpacingUnit",isAdaptive:!0}),(0,a.createElement)(Oe,{name:"biographyColor",label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Ze,{name:"biographyMargin",label:i.__("Margin","masterstudy-lms-learning-management-system"),unitName:"biographyMarginUnit",isAdaptive:!0})),(0,a.createElement)(Xn,{title:i.__("Card: Instructor’s Rating","masterstudy-lms-learning-management-system"),accordionFields:pa,visible:Boolean(e.showRating)},(0,a.createElement)($t,{fontSizeName:"ratingFontSize",fontSizeUnitName:"ratingFontSizeUnit",fontWeightName:"ratingFontWeight",textTransformName:"ratingTextTransform",fontStyleName:"ratingFontStyle",textDecorationName:"ratingTextDecoration",lineHeightName:"ratingLineHeight",lineHeightUnitName:"ratingLineHeightUnit",letterSpacingName:"ratingLetterSpacing",letterSpacingUnitName:"ratingLetterSpacingUnit",wordSpacingName:"ratingWordSpacing",wordSpacingUnitName:"ratingWordSpacingUnit",isAdaptive:!0,dependencies:[{name:"ratingStyle",value:[qn.ALL,qn.STARS_AND_RATE]}]}),(0,a.createElement)(Oe,{name:"ratingColor",label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system"),dependencies:[{name:"ratingStyle",value:[qn.ALL,qn.STARS_AND_RATE]}]}),(0,a.createElement)(Oe,{name:"ratingEmptyStarsColor",label:i.__("Empty Stars Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Oe,{name:"ratingFilledStarsColor",label:i.__("Filled Stars Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Ze,{name:"ratingMargin",label:i.__("Margin","masterstudy-lms-learning-management-system"),unitName:"ratingMarginUnit",isAdaptive:!0})),(0,a.createElement)(Xn,{title:i.__("Card: Reviews Count","masterstudy-lms-learning-management-system"),accordionFields:ba,visible:Boolean(e.showRating)&&[qn.ALL,qn.STARS_AND_REVIEWS].includes(e.ratingStyle)},(0,a.createElement)($t,{fontSizeName:"reviewCountFontSize",fontSizeUnitName:"reviewCountFontSizeUnit",fontWeightName:"reviewCountFontWeight",textTransformName:"reviewCountTextTransform",fontStyleName:"reviewCountFontStyle",textDecorationName:"reviewCountTextDecoration",lineHeightName:"reviewCountLineHeight",lineHeightUnitName:"reviewCountLineHeightUnit",letterSpacingName:"reviewCountLetterSpacing",letterSpacingUnitName:"reviewCountLetterSpacingUnit",wordSpacingName:"reviewCountWordSpacing",wordSpacingUnitName:"reviewCountWordSpacingUnit",isAdaptive:!0}),(0,a.createElement)(Oe,{name:"reviewCountColor",label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Ze,{name:"reviewCountMargin",label:i.__("Margin","masterstudy-lms-learning-management-system"),unitName:"reviewCountMarginUnit",isAdaptive:!0})))},xa=({attributes:e,setAttributes:t})=>{const{onResetByFieldName:n,changedFieldsByName:i}=((e,t,n,a=[])=>{const i=(e=>{const t={};return Object.entries(e).forEach((([e,n])=>{e.includes("UAG")||(t[e]=n)})),t})(t),r=!b(e,i),l=((e,t,n)=>{const a=new Map;return n.forEach((n=>{a.has(n)||a.set(n,(()=>t({[n]:e[n]})))})),a})(e,n,a),o=((e,t,n)=>{const a=new Map;return n.forEach((n=>{a.has(n)?a.set(n,!1):a.set(n,!b(e[n],t[n]))})),a})(e,i,a);return{hasChanges:r,onResetByFieldName:l,changedFieldsByName:o}})(ya,e,t,Object.keys(ya));return(0,a.createElement)(l.InspectorControls,null,(0,a.createElement)(U,{attributes:e,setAttributes:t,defaultValues:ya,onResetByFieldName:n,changedFieldsByName:i},(0,a.createElement)(an,{generalTab:(0,a.createElement)(wa,null),styleTab:(0,a.createElement)(Ta,null),advancedTab:(0,a.createElement)(a.Fragment,null)})))},Ma=JSON.parse('{"UU":"masterstudy/instructors-preset"}');(0,r.registerBlockType)(Ma.UU,{title:i._x("MasterStudy Instructors Preset","block title","masterstudy-lms-learning-management-system"),description:i._x("Choose instructors preset","block description","masterstudy-lms-learning-management-system"),category:"masterstudy-lms-blocks",icon:{src:(0,a.createElement)("svg",{width:"512",height:"512",viewBox:"0 0 512 512",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,a.createElement)("path",{opacity:"0.3",d:"M248.058 189.337C248.058 145.322 212.23 109.515 168.192 109.515C124.155 109.515 88.3267 145.322 88.3267 189.337C88.3267 233.328 124.155 269.117 168.192 269.117C212.23 269.117 248.058 233.328 248.058 189.337Z",fill:"#227AFF"}),(0,a.createElement)("path",{opacity:"0.3",fillRule:"evenodd",clipRule:"evenodd",d:"M168.023 420.192L262.316 420.192C282.345 420.192 296.945 401.318 292.047 381.733C277.856 324.987 225.521 284.28 167.063 284.279C167.062 284.279 167.061 284.28 167.061 284.281C167.061 284.282 167.06 284.283 167.059 284.283C108.298 284.791 57.4639 324.719 43.2053 381.733C38.3071 401.318 52.9073 420.192 72.9367 420.192H168.023Z",fill:"#227AFF"}),(0,a.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M479.225 420.486C495.909 420.486 508.014 404.774 503.962 388.558C495.077 353.026 465.49 327.158 429.934 322.436C455.764 317.005 475.214 294.064 475.214 266.661C475.214 235.216 449.627 209.643 418.17 209.643C386.714 209.643 361.126 235.216 361.126 266.661C361.126 294.078 380.595 317.028 406.445 322.444C392.107 324.374 378.78 329.79 367.36 337.839C347.822 316.86 320.063 304.076 289.765 304.076C272.216 304.076 255.568 308.442 240.839 316.126C215.632 289.41 179.998 273.194 141.175 273.194C78.2187 273.194 23.5855 315.788 8.33275 376.777C2.77455 399.001 19.3471 420.486 42.1511 420.486H479.225ZM289.765 324.968C328.92 324.968 362.873 351.445 372.354 389.357C373.677 394.644 369.811 399.592 364.593 399.592H272.106C275.236 392.503 275.957 384.532 274.018 376.775C269.986 360.65 263.126 345.88 254.163 332.793C265.067 327.77 277.133 324.968 289.765 324.968ZM418.17 230.537C438.104 230.537 454.321 246.746 454.321 266.661C454.321 286.56 438.106 302.761 418.17 302.761C398.234 302.761 382.019 286.56 382.019 266.661C382.019 246.746 398.236 230.537 418.17 230.537ZM418.17 342.552C449.236 342.552 476.172 363.556 483.694 393.629C484.471 396.738 482.222 399.592 479.225 399.592H392.352C393.785 394.683 393.908 389.423 392.622 384.285C389.929 373.513 385.643 363.483 380.058 354.412C390.995 346.909 404.136 342.552 418.17 342.552ZM28.6015 381.848C41.5237 330.177 87.8112 294.086 141.175 294.086C194.542 294.086 240.829 330.177 253.749 381.848C256.026 390.956 249.328 399.592 240.2 399.592H42.1511C33.0163 399.592 26.3259 390.95 28.6015 381.848Z",fill:"black"}),(0,a.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M289.765 301.809C327.016 301.809 357.322 271.534 357.322 234.315C357.322 197.076 327.017 166.792 289.765 166.792C252.513 166.792 222.205 197.076 222.205 234.315C222.205 271.535 252.514 301.809 289.765 301.809ZM289.765 187.684C315.495 187.684 336.429 208.606 336.429 234.314C336.429 260.002 315.497 280.915 289.765 280.915C264.031 280.915 243.099 260.001 243.099 234.314C243.099 208.606 264.033 187.684 289.765 187.684Z",fill:"black"}),(0,a.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M227.202 177.495C227.202 130.08 188.611 91.5149 141.176 91.5149C93.7409 91.5149 55.1501 130.08 55.1501 177.495C55.1501 224.887 93.7423 263.432 141.176 263.432C188.61 263.432 227.202 224.887 227.202 177.495ZM76.0431 177.495C76.0431 141.61 105.263 112.408 141.176 112.408C177.089 112.408 206.309 141.61 206.309 177.495C206.309 213.354 177.091 242.538 141.176 242.538C105.261 242.538 76.0431 213.353 76.0431 177.495Z",fill:"black"}))},edit:({attributes:e,setAttributes:t,context:n})=>{((e,t)=>{(0,T.useEffect)((()=>{t({instructorsPerRow:e["masterstudy/instructorsPerRow"],instructorsPerRowTablet:e["masterstudy/instructorsPerRowTablet"],instructorsPerRowMobile:e["masterstudy/instructorsPerRowMobile"],instructorPerPage:e["masterstudy/instructorPerPage"],quantity:e["masterstudy/quantity"],orderBy:e["masterstudy/orderBy"],instructors:e["masterstudy/instructors"]})}),[e,t])})(n,t);const i=(0,l.useBlockProps)({className:k()("lms-instructors-preset",`alignment-${e.cardAlignment}`,`lms-instructors-preset__item-${n["masterstudy/instructorsPerRow"]}`,`lms-instructors-preset__item-tablet-${n["masterstudy/instructorsPerRowTablet"]}`,`lms-instructors-preset__item-mobile-${n["masterstudy/instructorsPerRowMobile"]}`),style:{...E("instructor-preset",e,_a),...w("instructor-preset",e,"cardShadowColor","cardShadowHorizontal","cardShadowVertical","cardShadowBlur","cardShadowSpread","cardShadowInset",!0)}}),r=(0,l.useInnerBlocksProps)({...i},{allowedBlocks:["masterstudy/instructors-preset-classic"],templateLock:"all",renderAppender:void 0});var o,s;return o=e.cardPreset,s=t,(0,T.useEffect)((()=>{s({...fa[o]})}),[o,s]),(0,a.createElement)(a.Fragment,null,(0,a.createElement)(xa,{attributes:e,setAttributes:t}),(0,a.createElement)("div",{...r}))},save:({attributes:e})=>{const t=l.useBlockProps.save({className:k()("lms-instructors-preset",`alignment-${e.cardAlignment}`,`lms-instructors-preset__item-${e.instructorsPerRow}`,`lms-instructors-preset__item-tablet-${e.instructorsPerRowTablet}`,`lms-instructors-preset__item-mobile-${e.instructorsPerRowMobile}`),style:{...E("instructor-preset",e,_a),...w("instructor-preset",e,"cardShadowColor","cardShadowHorizontal","cardShadowVertical","cardShadowBlur","cardShadowSpread","cardShadowInset",!0)},"data-card-preset":e.cardPreset,"data-show-position":e.showPosition,"data-show-course-count":e.showCourseCount,"data-show-biography":e.showBiography,"data-show-rating":e.showRating,"data-rating-style":e.ratingStyle,"data-show-socials":e.showSocials,"data-instructors-per-page":e.instructorPerPage,"data-quantity":e.quantity,"data-order-by":e.orderBy,"data-instructors":JSON.stringify(e.instructors)}),n=l.useInnerBlocksProps.save(t);return(0,a.createElement)("div",{...n})}})},6942:(e,t)=>{var n;!function(){"use strict";var a={}.hasOwnProperty;function i(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=l(e,r(n)))}return e}function r(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return i.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)a.call(e,n)&&e[n]&&(t=l(t,n));return t}function l(e,t){return t?e?e+" "+t:e+t:e}e.exports?(i.default=i,e.exports=i):void 0===(n=function(){return i}.apply(t,[]))||(e.exports=n)}()}},n={};function a(e){var i=n[e];if(void 0!==i)return i.exports;var r=n[e]={exports:{}};return t[e](r,r.exports,a),r.exports}a.m=t,e=[],a.O=(t,n,i,r)=>{if(!n){var l=1/0;for(c=0;c<e.length;c++){for(var[n,i,r]=e[c],o=!0,s=0;s<n.length;s++)(!1&r||l>=r)&&Object.keys(a.O).every((e=>a.O[e](n[s])))?n.splice(s--,1):(o=!1,r<l&&(l=r));if(o){e.splice(c--,1);var m=i();void 0!==m&&(t=m)}}return t}r=r||0;for(var c=e.length;c>0&&e[c-1][2]>r;c--)e[c]=e[c-1];e[c]=[n,i,r]},a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={7824:0,2744:0};a.O.j=t=>0===e[t];var t=(t,n)=>{var i,r,[l,o,s]=n,m=0;if(l.some((t=>0!==e[t]))){for(i in o)a.o(o,i)&&(a.m[i]=o[i]);if(s)var c=s(a)}for(t&&t(n);m<l.length;m++)r=l[m],a.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return a.O(c)},n=globalThis.webpackChunkmasterstudy_lms_learning_management_system=globalThis.webpackChunkmasterstudy_lms_learning_management_system||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})();var i=a.O(void 0,[2744],(()=>a(1585)));i=a.O(i)})();