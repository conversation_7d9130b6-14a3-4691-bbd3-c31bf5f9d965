jQuery(document).ready(function($) {
    var $select = $('#mscb-course-select');
    var $addBtn = $('#mscb-add-course');
    var $list = $('#mscb-courses-list');
    var $input = $('#mscb-courses-input');

    function updateInput() {
        var ids = [];
        $list.find('.mscb-course-item').each(function() {
            ids.push($(this).data('course-id'));
        });
        $input.val(ids.join(','));
    }

    $addBtn.on('click', function(e) {
        e.preventDefault();
        var courseId = $select.val();
        var courseTitle = $select.find('option:selected').text();
        if (!courseId || $list.find('[data-course-id="' + courseId + '"]').length) {
            return;
        }
        var $item = $('<div class="mscb-course-item" data-course-id="' + courseId + '">' +
            '<span class="mscb-course-title">' + courseTitle + '</span> ' +
            '<button type="button" class="button mscb-remove-course">Remove</button>' +
            '</div>');
        $list.append($item);
        updateInput();
    });

    $list.on('click', '.mscb-remove-course', function() {
        $(this).closest('.mscb-course-item').remove();
        updateInput();
    });
}); 