/**
 * Course Bundler Frontend Styles
 */

/* Bundles Grid */
.mscb-bundles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin: 30px 0;
    overflow: visible !important;
    position: relative;
    z-index: 1;
}

.mscb-bundles-grid__header {
    text-align: center;
    margin-bottom: 30px;
}

.mscb-bundles-grid__title {
    font-size: 36px;
    margin-bottom: 10px;
    color: #273044;
}

.mscb-bundles-grid__subtitle {
    font-size: 18px;
    color: #777;
}

.mscb-bundle-item,
.mscb-bundle-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.2s;
}

.mscb-bundle-item:hover,
.mscb-bundle-card:hover {
    transform: translateY(-5px);
}

.mscb-bundle-thumb img {
    width: 100%;
    height: auto;
    display: block;
}

.mscb-bundle-content {
    padding: 20px;
}

.mscb-bundle-title {
    font-size: 18px;
    margin: 0 0 10px;
    color: #333;
}

.mscb-bundle-meta {
    color: #666;
    font-size: 14px;
    margin-bottom: 15px;
}

.mscb-bundle-meta i {
    color: #2441e7;
    margin-right: 5px;
}

.mscb-bundle-courses {
    margin: 15px 0;
}

.mscb-course-item {
    font-size: 14px;
    color: #555;
    margin-bottom: 5px;
    padding: 5px 0;
    border-bottom: 1px solid #eee;
}

.mscb-course-item:last-child {
    border-bottom: none;
}

.mscb-course-more {
    font-size: 13px;
    color: #777;
    font-style: italic;
    margin-top: 5px;
}

.mscb-bundle-pricing,
.mscb-bundle-card__price {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 15px 0;
    flex-wrap: wrap;
}

.mscb-bundle-discount,
.mscb-bundle-card__discount span {
    background: #ff0000;
    color: white;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
    margin-left: auto;
}

.mscb-regular-price,
.mscb-bundle-card__price-regular del {
    text-decoration: line-through;
    color: #aaa;
    font-size: 14px;
    margin-right: 5px;
}

.mscb-bundle-price,
.mscb-bundle-card__price-sale {
    color: #ff0000;
    font-size: 20px;
    font-weight: bold;
}

.mscb-bundle-actions {
    margin-top: 20px;
}

.mscb-view-bundle,
.mscb-bundle-card__button {
    display: inline-block;
    width: 100%;
    background: #ff9800;
    color: #fff;
    padding: 12px 20px;
    border-radius: 5px;
    text-decoration: none;
    transition: background 0.2s;
    text-align: center;
    font-weight: 600;
    font-size: 14px;
}

.mscb-view-bundle:hover,
.mscb-bundle-card__button:hover {
    background: #f57c00;
    color: #fff;
    text-decoration: none;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .mscb-bundles-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }
    
    .mscb-bundle-content {
        padding: 15px;
    }
    
    .mscb-bundle-title {
        font-size: 16px;
    }
}

/* Bundle Card */
.mscb-bundle-card {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    background-color: #fff;
    position: relative;
    max-height: none;
    display: flex;
    flex-direction: column;
}

.mscb-bundle-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.mscb-bundle-card--featured {
    border: 2px solid #ff9800;
}

.mscb-bundle-card__image {
    position: relative;
    overflow: hidden;
    height: 200px;
}

.mscb-bundle-card__thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.mscb-bundle-card:hover .mscb-bundle-card__thumbnail {
    transform: scale(1.05);
}

.mscb-bundle-card__featured {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #ff9800;
    color: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    z-index: 1;
}

.mscb-bundle-card__content {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.mscb-bundle-card__title {
    font-size: 18px;
    margin-bottom: 10px;
    line-height: 1.3;
}

.mscb-bundle-card__title a {
    color: #273044;
    text-decoration: none;
}

.mscb-bundle-card__title a:hover {
    color: #ff9800;
}

.mscb-bundle-card__excerpt {
    font-size: 14px;
    color: #555;
    margin-bottom: 15px;
    line-height: 1.5;
}

.mscb-bundle-card__meta {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.mscb-bundle-card__courses-count {
    display: flex;
    align-items: center;
    color: #555;
    font-size: 14px;
}

.mscb-bundle-card__courses-icon {
    margin-right: 5px;
    color: #ff9800;
}

.mscb-bundle-card__footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: auto;
    padding-top: 15px;
    border-top: 1px solid #eee;
    position: relative;
}

.mscb-bundle-card__discount {
    position: absolute;
    top: -30px;
    right: 0;
    background-color: #ff5252;
    color: #fff;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 600;
    z-index: 1;
}

.mscb-bundle-card__price {
    margin-bottom: 15px;
}

.mscb-bundle-card__price del {
    color: #aaa;
    font-size: 14px;
    margin-right: 8px;
}

.mscb-bundle-card__price span {
    color: #ff0000;
    font-size: 18px;
    font-weight: bold;
}

.mscb-bundle-card__price small {
    background: #ff0000;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    margin-left: 8px;
}

.mscb-bundle-card__button {
    display: inline-block;
    padding: 8px 15px;
    background-color: #ff9800;
    color: #fff;
    border-radius: 4px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    transition: background-color 0.3s ease;
}

.mscb-bundle-card__button:hover {
    background-color: #f57c00;
    color: #fff;
}

/* Single Bundle Page */
.mscb-single-bundle {
    margin: 40px auto;
    max-width: 1200px;
    padding: 0 20px;
}

.mscb-single-bundle__header {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 30px;
    align-items: center;
    justify-content: space-between;
}

.mscb-single-bundle__header-content {
    flex: 1;
    min-width: 300px;
    padding-right: 30px;
    max-width: 60%;
}

.mscb-single-bundle__header-image {
    flex: 0 0 auto;
    width: 300px;
    max-width: 35%;
}

.mscb-single-bundle__thumbnail {
    width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.mscb-single-bundle__title {
    font-size: 36px;
    margin-bottom: 15px;
    color: #273044;
}

.mscb-single-bundle__featured {
    display: inline-block;
    background-color: #ff9800;
    color: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 15px;
}

.mscb-single-bundle__meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
}

.mscb-single-bundle__courses-count,
.mscb-single-bundle__savings {
    display: flex;
    align-items: center;
    color: #555;
    font-size: 16px;
}

.mscb-single-bundle__courses-icon,
.mscb-single-bundle__savings-icon {
    margin-right: 8px;
    color: #ff9800;
}

.mscb-single-bundle__description {
    margin-bottom: 30px;
}

.mscb-single-bundle__description h3 {
    font-size: 24px;
    margin-bottom: 20px;
    color: #273044;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.mscb-single-bundle__content {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
}

.mscb-single-bundle__main {
    flex: 2;
    min-width: 300px;
    width: 65%;
}

.mscb-single-bundle__sidebar {
    flex: 1;
    min-width: 280px;
    max-width: 30%;
}

.mscb-single-bundle__courses h3 {
    font-size: 24px;
    margin-bottom: 20px;
    color: #273044;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.mscb-single-bundle__courses-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.mscb-single-bundle__course-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-radius: 8px;
    background-color: #f9f9f9;
    transition: background-color 0.3s ease;
}

.mscb-single-bundle__course-item:hover {
    background-color: #f0f0f0;
}

.mscb-single-bundle__course-number {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #ff9800;
    color: #fff;
    border-radius: 50%;
    font-weight: 600;
}

.mscb-single-bundle__course-image {
    width: 80px;
    height: 80px;
    overflow: hidden;
    border-radius: 4px;
}

.mscb-single-bundle__course-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.mscb-single-bundle__course-content {
    flex: 1;
}

.mscb-single-bundle__course-title {
    font-size: 18px;
    margin-bottom: 5px;
}

.mscb-single-bundle__course-title a {
    color: #273044;
    text-decoration: none;
}

.mscb-single-bundle__course-title a:hover {
    color: #ff9800;
}

/* Course list alignment in single bundle */
.mscb-single-bundle__course-excerpt {
    font-size: 14px;
    color: #555;
}

.mscb-single-bundle__course-content {
    flex: 1;
    padding-right: 15px;
}

.mscb-single-bundle__course-price {
    font-weight: 600;
    color: #273044;
    min-width: 100px;
    text-align: right;
}

.mscb-single-bundle__purchase-box {
    padding: 25px;
    border-radius: 8px;
    background-color: #f9f9f9;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.mscb-single-bundle__price {
    margin-bottom: 20px;
    text-align: center;
}

.mscb-single-bundle__price-regular {
    font-size: 28px;
    font-weight: 700;
    color: #273044;
}

.mscb-single-bundle__price-regular del {
    color: #777;
    font-size: 18px;
    font-weight: 400;
}

.mscb-single-bundle__price-sale {
    color: #ff5252;
    font-size: 28px;
    font-weight: 700;
    margin-left: 10px;
}

.mscb-single-bundle__savings-box,
.mscb-single-bundle__courses-count-box {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.mscb-single-bundle__savings-label,
.mscb-single-bundle__courses-count-label {
    color: #555;
}

.mscb-single-bundle__savings-value {
    font-weight: 600;
    color: #4caf50;
}

.mscb-single-bundle__courses-count-value {
    font-weight: 600;
    color: #273044;
}

.mscb-single-bundle__purchase-button {
    display: block;
    width: 100%;
    padding: 15px;
    background-color: #ff9800;
    color: #fff;
    text-align: center;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    margin-bottom: 15px;
    transition: background-color 0.3s ease;
}

.mscb-single-bundle__purchase-button:hover {
    background-color: #f57c00;
    color: #fff;
}

/* Course list in bundle card */
.mscb-bundle-card__courses-list {
    margin-bottom: 15px;
}

.mscb-bundle-card__courses-list ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.mscb-bundle-card__courses-list li {
    font-size: 14px;
    color: #555;
    padding: 5px 0;
    border-bottom: 1px dashed #eee;
}

.mscb-bundle-card__courses-list li:last-child {
    border-bottom: none;
}

.mscb-bundle-card__more-courses {
    color: #777;
    font-style: italic;
}

/* Responsive */
@media (max-width: 992px) {
    .mscb-single-bundle__header-content {
        max-width: 100%;
        padding-right: 0;
        margin-bottom: 20px;
    }
    
    .mscb-single-bundle__header-image {
        max-width: 100%;
        width: 100%;
    }
    
    .mscb-single-bundle__main {
        width: 100%;
    }
    
    .mscb-single-bundle__sidebar {
        max-width: 100%;
        width: 100%;
    }
}

/* Price display in grid cards */
.mscb-bundle-card__price-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    width: 100%;
}

.mscb-bundle-card__price-container {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.mscb-bundle-card__original-price {
    text-decoration: line-through;
    color: #aaa;
    font-size: 14px;
}

.mscb-bundle-card__discount-price {
    color: #ff0000;
    font-size: 18px;
    font-weight: bold;
}

.mscb-bundle-card__regular-price {
    color: #ff0000;
    font-size: 18px;
    font-weight: bold;
}

.mscb-bundle-card__discount-label {
    background: #ff0000;
    color: white;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
    display: inline-block;
}

/* Price display in standard grid */
.mscb-bundle-price-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 15px 0;
    width: 100%;
}

.mscb-bundle-price-container {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.mscb-bundle-original-price {
    text-decoration: line-through;
    color: #aaa;
    font-size: 14px;
}

.mscb-bundle-discount-price {
    color: #ff0000;
    font-size: 18px;
    font-weight: bold;
}

.mscb-bundle-regular-price {
    color: #ff0000;
    font-size: 18px;
    font-weight: bold;
}

.mscb-bundle-discount-label {
    background: #ff0000;
    color: white;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
    display: inline-block;
}

/* Standard bundle grid price display */
.mscb-bundle-price {
    margin: 15px 0;
}

.mscb-bundle-price del {
    color: #aaa;
    font-size: 14px;
    margin-right: 8px;
}

.mscb-bundle-price span {
    color: #ff0000;
    font-size: 18px;
    font-weight: bold;
}

.mscb-bundle-price small {
    background: #ff0000;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    margin-left: 8px;
}

/* Scrolling fixes */
html, body {
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch;
    position: relative;
    height: auto !important;
}

/* Fix WordPress admin bar issues */
body.admin-bar {
    position: relative !important;
}

/* Address Firefox scrolling issues */
@-moz-document url-prefix() {
    html, body {
        scrollbar-width: auto;
    }
}

/* Ultra-simple price display */
.mscb-simple-price {
    margin: 15px 0;
    font-size: 18px;
    display: block;
    width: 100%;
    text-align: left;
}

.mscb-simple-price del {
    color: #aaa;
    margin-right: 10px;
    font-size: 16px;
    display: inline-block;
}

.mscb-simple-price strong {
    color: #ff0000;
    font-size: 20px;
    font-weight: bold;
    display: inline-block;
}

.mscb-savings-badge {
    background: #ff0000;
    color: white;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
    margin-left: 10px;
    display: inline-block;
}
