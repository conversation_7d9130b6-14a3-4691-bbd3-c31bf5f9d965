<?php

if (!defined('ABSPATH')) {
    exit;
}

class MSCB_Enrollment {
    public function __construct() {
        // Hook directly into order status change
        add_action('save_post', array($this, 'handle_order_status_change'), 30);
        
        // Hook into both order accepted action and WooCommerce order completion (if WooCommerce is used)
        add_action('stm_lms_order_accepted', array($this, 'handle_order_completion'), 10, 2);
        add_action('woocommerce_order_status_completed', array($this, 'handle_woocommerce_completion'), 10, 1);
    }

    /**
     * Handle order status change - Direct hook into MasterStudy LMS order status saving
     */
    public function handle_order_status_change($post_id) {
        // Check if this is an order post
        if (!is_user_logged_in() || 'stm-orders' !== get_post_type($post_id)) {
            return;
        }

        // Get status and user ID
        $status = sanitize_text_field($_POST['order_status'] ?? '');
        $user_id = get_post_meta($post_id, 'user_id', true);
        
        // Process only completed orders
        if ('completed' === $status && !empty($user_id)) {
            // Get order items directly from meta
            $cart_items = get_post_meta($post_id, 'items', true);
            
            // Process bundle enrollment
            $this->process_bundle_enrollment($user_id, $cart_items);
            
            // Add debug log
            error_log(sprintf('Order %d status changed to completed for user %d - processing bundle enrollment', $post_id, $user_id));
        }
    }

    /**
     * Handle MasterStudy LMS order completion
     * 
     * @param int $user_id The user ID
     * @param array $cart_items The cart items from the order
     */
    public function handle_order_completion($user_id, $cart_items) {
        if (empty($cart_items) || !$user_id) {
            return;
        }
        
        // Process bundle enrollment
        $this->process_bundle_enrollment($user_id, $cart_items);
        
        // Add debug log
        error_log(sprintf('Order accepted for user %d - processing bundle enrollment', $user_id));
    }
    
    /**
     * Handle WooCommerce order completion
     * 
     * @param int $order_id The WooCommerce order ID
     */
    public function handle_woocommerce_completion($order_id) {
        if (!function_exists('wc_get_order')) {
            return;
        }
        
        $order = wc_get_order($order_id);
        if (!$order) {
            return;
        }
        
        $user_id = $order->get_user_id();
        if (!$user_id) {
            return;
        }
        
        // Loop through order items and check for bundles
        $cart_items = array();
        foreach ($order->get_items() as $item) {
            $product_id = $item->get_product_id();
            
            $cart_items[] = array(
                'item_id' => $product_id,
                'price' => $item->get_total()
            );
        }
        
        // Process bundle enrollment
        $this->process_bundle_enrollment($user_id, $cart_items);
        
        // Add debug log
        error_log(sprintf('WooCommerce order %d completed for user %d - processing bundle enrollment', $order_id, $user_id));
    }
    
    /**
     * Process bundle enrollment for cart items
     * 
     * @param int $user_id The user ID
     * @param array $cart_items The cart items from the order
     */
    private function process_bundle_enrollment($user_id, $cart_items) {
        if (empty($cart_items) || !is_array($cart_items)) {
            error_log('No cart items found for bundle enrollment');
            return;
        }
        
        // Loop through order items
        foreach ($cart_items as $item) {
            if (empty($item['item_id'])) {
                continue;
            }
            
            // Check if this item is a bundle post type
            if ('mscb_bundle' === get_post_type($item['item_id'])) {
                // This is a bundle, enroll user directly
                $bundle_id = $item['item_id'];
                $this->enroll_user_in_bundle($user_id, $bundle_id);
                continue;
            }
            
            // Check if this is a course with bundle metadata
            $bundle_id = get_post_meta($item['item_id'], '_mscb_bundle_id', true);
            if (!$bundle_id) {
                continue;
            }
            
            // This is a course linked to a bundle, enroll user
            $this->enroll_user_in_bundle($user_id, $bundle_id);
        }
    }
    
    /**
     * Enroll a user in all courses within a bundle
     * 
     * @param int $user_id The user ID
     * @param int $bundle_id The bundle ID
     */
    private function enroll_user_in_bundle($user_id, $bundle_id) {
        // Get the bundle courses
        $bundle = new MSCB_Bundle();
        $courses = $bundle->get_bundle_courses($bundle_id);
        
        if (empty($courses)) {
            error_log(sprintf('No courses found in bundle %d', $bundle_id));
            return;
        }
        
        error_log(sprintf('Enrolling user %d in bundle %d with %d courses', $user_id, $bundle_id, count($courses)));
        
        // Enroll user in each course
        foreach ($courses as $course) {
            if (empty($course['course_id'])) {
                continue;
            }
            
            if (class_exists('STM_LMS_Course')) {
                // Use the same enrollment method as the LMS plugin
                STM_LMS_Course::add_user_course(
                    $course['course_id'],
                    $user_id,
                    0, // current_lesson_id
                    0, // progress
                    false, // is_translate
                    '', // enterprise
                    $bundle_id, // bundle_id - critical for tracking
                    '', // for_points
                    '' // instructor_id
                );
                
                // Add student count like the LMS plugin does
                STM_LMS_Course::add_student($course['course_id']);
                
                error_log(sprintf('Enrolled user %d in course %d from bundle %d', $user_id, $course['course_id'], $bundle_id));
            }
        }
        
        // Record the bundle enrollment
        $bundle->enroll_user($bundle_id, $user_id);
        error_log(sprintf('Recorded bundle enrollment for user %d in bundle %d', $user_id, $bundle_id));
    }
} 