/**
 * Course Bundler Frontend JavaScript
 */
(function($) {
    'use strict';
    
    // Initialize when document is ready
    $(document).ready(function() {
        initBundleInteractions();
    });
    
    /**
     * Initialize bundle interactions
     */
    function initBundleInteractions() {
        // Add hover effects to bundle cards
        $('.mscb-bundle-card').hover(
            function() {
                $(this).find('.mscb-bundle-card__button').addClass('hover');
            },
            function() {
                $(this).find('.mscb-bundle-card__button').removeClass('hover');
            }
        );
        
        // Display discount percentage more prominently on hover
        $('.mscb-bundle-card__discount').each(function() {
            const $discount = $(this);
            const $card = $discount.closest('.mscb-bundle-card');
            
            $card.hover(
                function() {
                    $discount.addClass('active');
                },
                function() {
                    $discount.removeClass('active');
                }
            );
        });
    }
    
    /**
     * Show message to the user
     * 
     * @param {string} type - Message type (success, error)
     * @param {string} message - Message text
     */
    function showMessage(type, message) {
        // Remove any existing messages
        $('.mscb-message').remove();
        
        // Create message element
        const $message = $('<div class="mscb-message mscb-message--' + type + '">' + 
                          '<span class="mscb-message__text">' + message + '</span>' +
                          '<span class="mscb-message__close">&times;</span>' +
                          '</div>');
        
        // Add message to the page
        $('body').append($message);
        
        // Show message with animation
        setTimeout(function() {
            $message.addClass('mscb-message--visible');
        }, 10);
        
        // Auto-hide message after 5 seconds
        setTimeout(function() {
            $message.removeClass('mscb-message--visible');
            
            // Remove message from DOM after animation
            setTimeout(function() {
                $message.remove();
            }, 300);
        }, 5000);
        
        // Close message when close button is clicked
        $message.find('.mscb-message__close').on('click', function() {
            $message.removeClass('mscb-message--visible');
            
            // Remove message from DOM after animation
            setTimeout(function() {
                $message.remove();
            }, 300);
        });
    }
    
})(jQuery);
