(()=>{var e,t={5227:(e,t,n)=>{"use strict";const a=window.React;var r=n.n(a);const i=window.wp.blocks,s=window.wp.i18n;let o=function(e){return e.TOP_lEFT="top-left",e.TOP_CENTER="top-center",e.TOP_RIGHT="top-right",e.BOTTOM_lEFT="bottom-left",e.BOTTOM_CENTER="bottom-center",e.BOTTOM_RIGHT="bottom-right",e}({});window.wp.data;const l=["padding","margin","borderWidth","inset"],c=["top","right","bottom","left"];s.__("Small","masterstudy-lms-learning-management-system"),s.__("Normal","masterstudy-lms-learning-management-system"),s.__("Large","masterstudy-lms-learning-management-system"),s.__("Extra Large","masterstudy-lms-learning-management-system"),o.TOP_lEFT,o.TOP_CENTER,o.TOP_RIGHT,o.BOTTOM_lEFT,o.BOTTOM_CENTER,o.BOTTOM_RIGHT,s.__("Newest","masterstudy-lms-learning-management-system"),s.__("Oldest","masterstudy-lms-learning-management-system"),s.__("Overall rating","masterstudy-lms-learning-management-system"),s.__("Popular","masterstudy-lms-learning-management-system"),s.__("Price low","masterstudy-lms-learning-management-system"),s.__("Price high","masterstudy-lms-learning-management-system");const d=()=>{const e=Date.now();return`${Math.random().toString(36).substring(2,15)}-${e}`},u=(e,t={},n,a={})=>{const r=n?`-${n}`:"",i=`wp-block-masterstudy-${e}`,s=[],o={};return Object.entries(t).forEach((([e,t])=>{const n=l.includes(e),d=`--${i}${r}--${e}`,u=new RegExp(l.join("|"),"i"),f=n||u.test(e)?(e=>{const t=c.map((t=>e[t]?e[t]:"undefined")),n=[...new Set(t)];return 1===n.length?n[0]:t.join(" ").replace(/undefined/g,"0px")})(t):"string"==typeof t?t.trim():t;"undefined"!==f&&t&&(s.push(`${d}:${f}`),o[d]=f,a[d]=f)})),{blockClassName:i,blockStyleVariables:s.join(";"),blockStyleObject:o,accumulatedStyles:a}},f=window.wp.element,p=((0,f.createContext)(null),window.wp.components);function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){b(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function g(e){return g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},g(e)}function h(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}function b(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function y(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,i=[],_n=!0,s=!1;try{for(n=n.call(e);!(_n=(a=n.next()).done)&&(i.push(a.value),!t||i.length!==t);_n=!0);}catch(e){s=!0,r=e}finally{try{_n||null==n.return||n.return()}finally{if(s)throw r}}return i}}(e,t)||C(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function w(e){return function(e){if(Array.isArray(e))return x(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||C(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function C(e,t){if(e){if("string"==typeof e)return x(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?x(e,t):void 0}}function x(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var S=function(){},E={},k={},T=null,P={mark:S,measure:S};try{"undefined"!=typeof window&&(E=window),"undefined"!=typeof document&&(k=document),"undefined"!=typeof MutationObserver&&(T=MutationObserver),"undefined"!=typeof performance&&(P=performance)}catch(e){}var O,M,_,L,A,I=(E.navigator||{}).userAgent,N=void 0===I?"":I,z=E,j=k,D=T,B=P,R=(z.document,!!j.documentElement&&!!j.head&&"function"==typeof j.addEventListener&&"function"==typeof j.createElement),$=~N.indexOf("MSIE")||~N.indexOf("Trident/"),F="___FONT_AWESOME___",G="fa",H="svg-inline--fa",V="data-fa-i2svg",Y="data-fa-pseudo-element",W="data-fa-pseudo-element-pending",q="data-prefix",X="data-icon",U="fontawesome-i2svg",Z="async",K=["HTML","HEAD","STYLE","SCRIPT"],J=function(){try{return!0}catch(e){return!1}}(),Q="classic",ee="sharp",te=[Q,ee];function ne(e){return new Proxy(e,{get:function(e,t){return t in e?e[t]:e[Q]}})}var ae=ne((b(O={},Q,{fa:"solid",fas:"solid","fa-solid":"solid",far:"regular","fa-regular":"regular",fal:"light","fa-light":"light",fat:"thin","fa-thin":"thin",fad:"duotone","fa-duotone":"duotone",fab:"brands","fa-brands":"brands",fak:"kit",fakd:"kit","fa-kit":"kit","fa-kit-duotone":"kit"}),b(O,ee,{fa:"solid",fass:"solid","fa-solid":"solid",fasr:"regular","fa-regular":"regular",fasl:"light","fa-light":"light",fast:"thin","fa-thin":"thin"}),O)),re=ne((b(M={},Q,{solid:"fas",regular:"far",light:"fal",thin:"fat",duotone:"fad",brands:"fab",kit:"fak"}),b(M,ee,{solid:"fass",regular:"fasr",light:"fasl",thin:"fast"}),M)),ie=ne((b(_={},Q,{fab:"fa-brands",fad:"fa-duotone",fak:"fa-kit",fal:"fa-light",far:"fa-regular",fas:"fa-solid",fat:"fa-thin"}),b(_,ee,{fass:"fa-solid",fasr:"fa-regular",fasl:"fa-light",fast:"fa-thin"}),_)),se=ne((b(L={},Q,{"fa-brands":"fab","fa-duotone":"fad","fa-kit":"fak","fa-light":"fal","fa-regular":"far","fa-solid":"fas","fa-thin":"fat"}),b(L,ee,{"fa-solid":"fass","fa-regular":"fasr","fa-light":"fasl","fa-thin":"fast"}),L)),oe=/fa(s|r|l|t|d|b|k|ss|sr|sl|st)?[\-\ ]/,le="fa-layers-text",ce=/Font ?Awesome ?([56 ]*)(Solid|Regular|Light|Thin|Duotone|Brands|Free|Pro|Sharp|Kit)?.*/i,de=ne((b(A={},Q,{900:"fas",400:"far",normal:"far",300:"fal",100:"fat"}),b(A,ee,{900:"fass",400:"fasr",300:"fasl",100:"fast"}),A)),ue=[1,2,3,4,5,6,7,8,9,10],fe=ue.concat([11,12,13,14,15,16,17,18,19,20]),pe=["class","data-prefix","data-icon","data-fa-transform","data-fa-mask"],me={GROUP:"duotone-group",SWAP_OPACITY:"swap-opacity",PRIMARY:"primary",SECONDARY:"secondary"},ve=new Set;Object.keys(re[Q]).map(ve.add.bind(ve)),Object.keys(re[ee]).map(ve.add.bind(ve));var ge=[].concat(te,w(ve),["2xs","xs","sm","lg","xl","2xl","beat","border","fade","beat-fade","bounce","flip-both","flip-horizontal","flip-vertical","flip","fw","inverse","layers-counter","layers-text","layers","li","pull-left","pull-right","pulse","rotate-180","rotate-270","rotate-90","rotate-by","shake","spin-pulse","spin-reverse","spin","stack-1x","stack-2x","stack","ul",me.GROUP,me.SWAP_OPACITY,me.PRIMARY,me.SECONDARY]).concat(ue.map((function(e){return"".concat(e,"x")}))).concat(fe.map((function(e){return"w-".concat(e)}))),he=z.FontAwesomeConfig||{};j&&"function"==typeof j.querySelector&&[["data-family-prefix","familyPrefix"],["data-css-prefix","cssPrefix"],["data-family-default","familyDefault"],["data-style-default","styleDefault"],["data-replacement-class","replacementClass"],["data-auto-replace-svg","autoReplaceSvg"],["data-auto-add-css","autoAddCss"],["data-auto-a11y","autoA11y"],["data-search-pseudo-elements","searchPseudoElements"],["data-observe-mutations","observeMutations"],["data-mutate-approach","mutateApproach"],["data-keep-original-source","keepOriginalSource"],["data-measure-performance","measurePerformance"],["data-show-missing-icons","showMissingIcons"]].forEach((function(e){var t=y(e,2),n=t[0],a=t[1],r=function(e){return""===e||"false"!==e&&("true"===e||e)}(function(e){var t=j.querySelector("script["+e+"]");if(t)return t.getAttribute(e)}(n));null!=r&&(he[a]=r)}));var be={styleDefault:"solid",familyDefault:"classic",cssPrefix:G,replacementClass:H,autoReplaceSvg:!0,autoAddCss:!0,autoA11y:!0,searchPseudoElements:!1,observeMutations:!0,mutateApproach:"async",keepOriginalSource:!0,measurePerformance:!1,showMissingIcons:!0};he.familyPrefix&&(he.cssPrefix=he.familyPrefix);var ye=v(v({},be),he);ye.autoReplaceSvg||(ye.observeMutations=!1);var we={};Object.keys(be).forEach((function(e){Object.defineProperty(we,e,{enumerable:!0,set:function(t){ye[e]=t,Ce.forEach((function(e){return e(we)}))},get:function(){return ye[e]}})})),Object.defineProperty(we,"familyPrefix",{enumerable:!0,set:function(e){ye.cssPrefix=e,Ce.forEach((function(e){return e(we)}))},get:function(){return ye.cssPrefix}}),z.FontAwesomeConfig=we;var Ce=[],xe=16,Se={size:16,x:0,y:0,rotate:0,flipX:!1,flipY:!1},Ee="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";function ke(){for(var e=12,t="";e-- >0;)t+=Ee[62*Math.random()|0];return t}function Te(e){for(var t=[],n=(e||[]).length>>>0;n--;)t[n]=e[n];return t}function Pe(e){return e.classList?Te(e.classList):(e.getAttribute("class")||"").split(" ").filter((function(e){return e}))}function Oe(e){return"".concat(e).replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function Me(e){return Object.keys(e||{}).reduce((function(t,n){return t+"".concat(n,": ").concat(e[n].trim(),";")}),"")}function _e(e){return e.size!==Se.size||e.x!==Se.x||e.y!==Se.y||e.rotate!==Se.rotate||e.flipX||e.flipY}var Le=':root, :host {\n  --fa-font-solid: normal 900 1em/1 "Font Awesome 6 Solid";\n  --fa-font-regular: normal 400 1em/1 "Font Awesome 6 Regular";\n  --fa-font-light: normal 300 1em/1 "Font Awesome 6 Light";\n  --fa-font-thin: normal 100 1em/1 "Font Awesome 6 Thin";\n  --fa-font-duotone: normal 900 1em/1 "Font Awesome 6 Duotone";\n  --fa-font-sharp-solid: normal 900 1em/1 "Font Awesome 6 Sharp";\n  --fa-font-sharp-regular: normal 400 1em/1 "Font Awesome 6 Sharp";\n  --fa-font-sharp-light: normal 300 1em/1 "Font Awesome 6 Sharp";\n  --fa-font-sharp-thin: normal 100 1em/1 "Font Awesome 6 Sharp";\n  --fa-font-brands: normal 400 1em/1 "Font Awesome 6 Brands";\n}\n\nsvg:not(:root).svg-inline--fa, svg:not(:host).svg-inline--fa {\n  overflow: visible;\n  box-sizing: content-box;\n}\n\n.svg-inline--fa {\n  display: var(--fa-display, inline-block);\n  height: 1em;\n  overflow: visible;\n  vertical-align: -0.125em;\n}\n.svg-inline--fa.fa-2xs {\n  vertical-align: 0.1em;\n}\n.svg-inline--fa.fa-xs {\n  vertical-align: 0em;\n}\n.svg-inline--fa.fa-sm {\n  vertical-align: -0.0714285705em;\n}\n.svg-inline--fa.fa-lg {\n  vertical-align: -0.2em;\n}\n.svg-inline--fa.fa-xl {\n  vertical-align: -0.25em;\n}\n.svg-inline--fa.fa-2xl {\n  vertical-align: -0.3125em;\n}\n.svg-inline--fa.fa-pull-left {\n  margin-right: var(--fa-pull-margin, 0.3em);\n  width: auto;\n}\n.svg-inline--fa.fa-pull-right {\n  margin-left: var(--fa-pull-margin, 0.3em);\n  width: auto;\n}\n.svg-inline--fa.fa-li {\n  width: var(--fa-li-width, 2em);\n  top: 0.25em;\n}\n.svg-inline--fa.fa-fw {\n  width: var(--fa-fw-width, 1.25em);\n}\n\n.fa-layers svg.svg-inline--fa {\n  bottom: 0;\n  left: 0;\n  margin: auto;\n  position: absolute;\n  right: 0;\n  top: 0;\n}\n\n.fa-layers-counter, .fa-layers-text {\n  display: inline-block;\n  position: absolute;\n  text-align: center;\n}\n\n.fa-layers {\n  display: inline-block;\n  height: 1em;\n  position: relative;\n  text-align: center;\n  vertical-align: -0.125em;\n  width: 1em;\n}\n.fa-layers svg.svg-inline--fa {\n  -webkit-transform-origin: center center;\n          transform-origin: center center;\n}\n\n.fa-layers-text {\n  left: 50%;\n  top: 50%;\n  -webkit-transform: translate(-50%, -50%);\n          transform: translate(-50%, -50%);\n  -webkit-transform-origin: center center;\n          transform-origin: center center;\n}\n\n.fa-layers-counter {\n  background-color: var(--fa-counter-background-color, #ff253a);\n  border-radius: var(--fa-counter-border-radius, 1em);\n  box-sizing: border-box;\n  color: var(--fa-inverse, #fff);\n  line-height: var(--fa-counter-line-height, 1);\n  max-width: var(--fa-counter-max-width, 5em);\n  min-width: var(--fa-counter-min-width, 1.5em);\n  overflow: hidden;\n  padding: var(--fa-counter-padding, 0.25em 0.5em);\n  right: var(--fa-right, 0);\n  text-overflow: ellipsis;\n  top: var(--fa-top, 0);\n  -webkit-transform: scale(var(--fa-counter-scale, 0.25));\n          transform: scale(var(--fa-counter-scale, 0.25));\n  -webkit-transform-origin: top right;\n          transform-origin: top right;\n}\n\n.fa-layers-bottom-right {\n  bottom: var(--fa-bottom, 0);\n  right: var(--fa-right, 0);\n  top: auto;\n  -webkit-transform: scale(var(--fa-layers-scale, 0.25));\n          transform: scale(var(--fa-layers-scale, 0.25));\n  -webkit-transform-origin: bottom right;\n          transform-origin: bottom right;\n}\n\n.fa-layers-bottom-left {\n  bottom: var(--fa-bottom, 0);\n  left: var(--fa-left, 0);\n  right: auto;\n  top: auto;\n  -webkit-transform: scale(var(--fa-layers-scale, 0.25));\n          transform: scale(var(--fa-layers-scale, 0.25));\n  -webkit-transform-origin: bottom left;\n          transform-origin: bottom left;\n}\n\n.fa-layers-top-right {\n  top: var(--fa-top, 0);\n  right: var(--fa-right, 0);\n  -webkit-transform: scale(var(--fa-layers-scale, 0.25));\n          transform: scale(var(--fa-layers-scale, 0.25));\n  -webkit-transform-origin: top right;\n          transform-origin: top right;\n}\n\n.fa-layers-top-left {\n  left: var(--fa-left, 0);\n  right: auto;\n  top: var(--fa-top, 0);\n  -webkit-transform: scale(var(--fa-layers-scale, 0.25));\n          transform: scale(var(--fa-layers-scale, 0.25));\n  -webkit-transform-origin: top left;\n          transform-origin: top left;\n}\n\n.fa-1x {\n  font-size: 1em;\n}\n\n.fa-2x {\n  font-size: 2em;\n}\n\n.fa-3x {\n  font-size: 3em;\n}\n\n.fa-4x {\n  font-size: 4em;\n}\n\n.fa-5x {\n  font-size: 5em;\n}\n\n.fa-6x {\n  font-size: 6em;\n}\n\n.fa-7x {\n  font-size: 7em;\n}\n\n.fa-8x {\n  font-size: 8em;\n}\n\n.fa-9x {\n  font-size: 9em;\n}\n\n.fa-10x {\n  font-size: 10em;\n}\n\n.fa-2xs {\n  font-size: 0.625em;\n  line-height: 0.1em;\n  vertical-align: 0.225em;\n}\n\n.fa-xs {\n  font-size: 0.75em;\n  line-height: 0.0833333337em;\n  vertical-align: 0.125em;\n}\n\n.fa-sm {\n  font-size: 0.875em;\n  line-height: 0.0714285718em;\n  vertical-align: 0.0535714295em;\n}\n\n.fa-lg {\n  font-size: 1.25em;\n  line-height: 0.05em;\n  vertical-align: -0.075em;\n}\n\n.fa-xl {\n  font-size: 1.5em;\n  line-height: 0.0416666682em;\n  vertical-align: -0.125em;\n}\n\n.fa-2xl {\n  font-size: 2em;\n  line-height: 0.03125em;\n  vertical-align: -0.1875em;\n}\n\n.fa-fw {\n  text-align: center;\n  width: 1.25em;\n}\n\n.fa-ul {\n  list-style-type: none;\n  margin-left: var(--fa-li-margin, 2.5em);\n  padding-left: 0;\n}\n.fa-ul > li {\n  position: relative;\n}\n\n.fa-li {\n  left: calc(var(--fa-li-width, 2em) * -1);\n  position: absolute;\n  text-align: center;\n  width: var(--fa-li-width, 2em);\n  line-height: inherit;\n}\n\n.fa-border {\n  border-color: var(--fa-border-color, #eee);\n  border-radius: var(--fa-border-radius, 0.1em);\n  border-style: var(--fa-border-style, solid);\n  border-width: var(--fa-border-width, 0.08em);\n  padding: var(--fa-border-padding, 0.2em 0.25em 0.15em);\n}\n\n.fa-pull-left {\n  float: left;\n  margin-right: var(--fa-pull-margin, 0.3em);\n}\n\n.fa-pull-right {\n  float: right;\n  margin-left: var(--fa-pull-margin, 0.3em);\n}\n\n.fa-beat {\n  -webkit-animation-name: fa-beat;\n          animation-name: fa-beat;\n  -webkit-animation-delay: var(--fa-animation-delay, 0s);\n          animation-delay: var(--fa-animation-delay, 0s);\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\n          animation-direction: var(--fa-animation-direction, normal);\n  -webkit-animation-duration: var(--fa-animation-duration, 1s);\n          animation-duration: var(--fa-animation-duration, 1s);\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  -webkit-animation-timing-function: var(--fa-animation-timing, ease-in-out);\n          animation-timing-function: var(--fa-animation-timing, ease-in-out);\n}\n\n.fa-bounce {\n  -webkit-animation-name: fa-bounce;\n          animation-name: fa-bounce;\n  -webkit-animation-delay: var(--fa-animation-delay, 0s);\n          animation-delay: var(--fa-animation-delay, 0s);\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\n          animation-direction: var(--fa-animation-direction, normal);\n  -webkit-animation-duration: var(--fa-animation-duration, 1s);\n          animation-duration: var(--fa-animation-duration, 1s);\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  -webkit-animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));\n          animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));\n}\n\n.fa-fade {\n  -webkit-animation-name: fa-fade;\n          animation-name: fa-fade;\n  -webkit-animation-delay: var(--fa-animation-delay, 0s);\n          animation-delay: var(--fa-animation-delay, 0s);\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\n          animation-direction: var(--fa-animation-direction, normal);\n  -webkit-animation-duration: var(--fa-animation-duration, 1s);\n          animation-duration: var(--fa-animation-duration, 1s);\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  -webkit-animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\n          animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\n}\n\n.fa-beat-fade {\n  -webkit-animation-name: fa-beat-fade;\n          animation-name: fa-beat-fade;\n  -webkit-animation-delay: var(--fa-animation-delay, 0s);\n          animation-delay: var(--fa-animation-delay, 0s);\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\n          animation-direction: var(--fa-animation-direction, normal);\n  -webkit-animation-duration: var(--fa-animation-duration, 1s);\n          animation-duration: var(--fa-animation-duration, 1s);\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  -webkit-animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\n          animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\n}\n\n.fa-flip {\n  -webkit-animation-name: fa-flip;\n          animation-name: fa-flip;\n  -webkit-animation-delay: var(--fa-animation-delay, 0s);\n          animation-delay: var(--fa-animation-delay, 0s);\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\n          animation-direction: var(--fa-animation-direction, normal);\n  -webkit-animation-duration: var(--fa-animation-duration, 1s);\n          animation-duration: var(--fa-animation-duration, 1s);\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  -webkit-animation-timing-function: var(--fa-animation-timing, ease-in-out);\n          animation-timing-function: var(--fa-animation-timing, ease-in-out);\n}\n\n.fa-shake {\n  -webkit-animation-name: fa-shake;\n          animation-name: fa-shake;\n  -webkit-animation-delay: var(--fa-animation-delay, 0s);\n          animation-delay: var(--fa-animation-delay, 0s);\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\n          animation-direction: var(--fa-animation-direction, normal);\n  -webkit-animation-duration: var(--fa-animation-duration, 1s);\n          animation-duration: var(--fa-animation-duration, 1s);\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  -webkit-animation-timing-function: var(--fa-animation-timing, linear);\n          animation-timing-function: var(--fa-animation-timing, linear);\n}\n\n.fa-spin {\n  -webkit-animation-name: fa-spin;\n          animation-name: fa-spin;\n  -webkit-animation-delay: var(--fa-animation-delay, 0s);\n          animation-delay: var(--fa-animation-delay, 0s);\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\n          animation-direction: var(--fa-animation-direction, normal);\n  -webkit-animation-duration: var(--fa-animation-duration, 2s);\n          animation-duration: var(--fa-animation-duration, 2s);\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  -webkit-animation-timing-function: var(--fa-animation-timing, linear);\n          animation-timing-function: var(--fa-animation-timing, linear);\n}\n\n.fa-spin-reverse {\n  --fa-animation-direction: reverse;\n}\n\n.fa-pulse,\n.fa-spin-pulse {\n  -webkit-animation-name: fa-spin;\n          animation-name: fa-spin;\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\n          animation-direction: var(--fa-animation-direction, normal);\n  -webkit-animation-duration: var(--fa-animation-duration, 1s);\n          animation-duration: var(--fa-animation-duration, 1s);\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  -webkit-animation-timing-function: var(--fa-animation-timing, steps(8));\n          animation-timing-function: var(--fa-animation-timing, steps(8));\n}\n\n@media (prefers-reduced-motion: reduce) {\n  .fa-beat,\n.fa-bounce,\n.fa-fade,\n.fa-beat-fade,\n.fa-flip,\n.fa-pulse,\n.fa-shake,\n.fa-spin,\n.fa-spin-pulse {\n    -webkit-animation-delay: -1ms;\n            animation-delay: -1ms;\n    -webkit-animation-duration: 1ms;\n            animation-duration: 1ms;\n    -webkit-animation-iteration-count: 1;\n            animation-iteration-count: 1;\n    -webkit-transition-delay: 0s;\n            transition-delay: 0s;\n    -webkit-transition-duration: 0s;\n            transition-duration: 0s;\n  }\n}\n@-webkit-keyframes fa-beat {\n  0%, 90% {\n    -webkit-transform: scale(1);\n            transform: scale(1);\n  }\n  45% {\n    -webkit-transform: scale(var(--fa-beat-scale, 1.25));\n            transform: scale(var(--fa-beat-scale, 1.25));\n  }\n}\n@keyframes fa-beat {\n  0%, 90% {\n    -webkit-transform: scale(1);\n            transform: scale(1);\n  }\n  45% {\n    -webkit-transform: scale(var(--fa-beat-scale, 1.25));\n            transform: scale(var(--fa-beat-scale, 1.25));\n  }\n}\n@-webkit-keyframes fa-bounce {\n  0% {\n    -webkit-transform: scale(1, 1) translateY(0);\n            transform: scale(1, 1) translateY(0);\n  }\n  10% {\n    -webkit-transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);\n            transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);\n  }\n  30% {\n    -webkit-transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));\n            transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));\n  }\n  50% {\n    -webkit-transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);\n            transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);\n  }\n  57% {\n    -webkit-transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));\n            transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));\n  }\n  64% {\n    -webkit-transform: scale(1, 1) translateY(0);\n            transform: scale(1, 1) translateY(0);\n  }\n  100% {\n    -webkit-transform: scale(1, 1) translateY(0);\n            transform: scale(1, 1) translateY(0);\n  }\n}\n@keyframes fa-bounce {\n  0% {\n    -webkit-transform: scale(1, 1) translateY(0);\n            transform: scale(1, 1) translateY(0);\n  }\n  10% {\n    -webkit-transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);\n            transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);\n  }\n  30% {\n    -webkit-transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));\n            transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));\n  }\n  50% {\n    -webkit-transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);\n            transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);\n  }\n  57% {\n    -webkit-transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));\n            transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));\n  }\n  64% {\n    -webkit-transform: scale(1, 1) translateY(0);\n            transform: scale(1, 1) translateY(0);\n  }\n  100% {\n    -webkit-transform: scale(1, 1) translateY(0);\n            transform: scale(1, 1) translateY(0);\n  }\n}\n@-webkit-keyframes fa-fade {\n  50% {\n    opacity: var(--fa-fade-opacity, 0.4);\n  }\n}\n@keyframes fa-fade {\n  50% {\n    opacity: var(--fa-fade-opacity, 0.4);\n  }\n}\n@-webkit-keyframes fa-beat-fade {\n  0%, 100% {\n    opacity: var(--fa-beat-fade-opacity, 0.4);\n    -webkit-transform: scale(1);\n            transform: scale(1);\n  }\n  50% {\n    opacity: 1;\n    -webkit-transform: scale(var(--fa-beat-fade-scale, 1.125));\n            transform: scale(var(--fa-beat-fade-scale, 1.125));\n  }\n}\n@keyframes fa-beat-fade {\n  0%, 100% {\n    opacity: var(--fa-beat-fade-opacity, 0.4);\n    -webkit-transform: scale(1);\n            transform: scale(1);\n  }\n  50% {\n    opacity: 1;\n    -webkit-transform: scale(var(--fa-beat-fade-scale, 1.125));\n            transform: scale(var(--fa-beat-fade-scale, 1.125));\n  }\n}\n@-webkit-keyframes fa-flip {\n  50% {\n    -webkit-transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));\n            transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));\n  }\n}\n@keyframes fa-flip {\n  50% {\n    -webkit-transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));\n            transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));\n  }\n}\n@-webkit-keyframes fa-shake {\n  0% {\n    -webkit-transform: rotate(-15deg);\n            transform: rotate(-15deg);\n  }\n  4% {\n    -webkit-transform: rotate(15deg);\n            transform: rotate(15deg);\n  }\n  8%, 24% {\n    -webkit-transform: rotate(-18deg);\n            transform: rotate(-18deg);\n  }\n  12%, 28% {\n    -webkit-transform: rotate(18deg);\n            transform: rotate(18deg);\n  }\n  16% {\n    -webkit-transform: rotate(-22deg);\n            transform: rotate(-22deg);\n  }\n  20% {\n    -webkit-transform: rotate(22deg);\n            transform: rotate(22deg);\n  }\n  32% {\n    -webkit-transform: rotate(-12deg);\n            transform: rotate(-12deg);\n  }\n  36% {\n    -webkit-transform: rotate(12deg);\n            transform: rotate(12deg);\n  }\n  40%, 100% {\n    -webkit-transform: rotate(0deg);\n            transform: rotate(0deg);\n  }\n}\n@keyframes fa-shake {\n  0% {\n    -webkit-transform: rotate(-15deg);\n            transform: rotate(-15deg);\n  }\n  4% {\n    -webkit-transform: rotate(15deg);\n            transform: rotate(15deg);\n  }\n  8%, 24% {\n    -webkit-transform: rotate(-18deg);\n            transform: rotate(-18deg);\n  }\n  12%, 28% {\n    -webkit-transform: rotate(18deg);\n            transform: rotate(18deg);\n  }\n  16% {\n    -webkit-transform: rotate(-22deg);\n            transform: rotate(-22deg);\n  }\n  20% {\n    -webkit-transform: rotate(22deg);\n            transform: rotate(22deg);\n  }\n  32% {\n    -webkit-transform: rotate(-12deg);\n            transform: rotate(-12deg);\n  }\n  36% {\n    -webkit-transform: rotate(12deg);\n            transform: rotate(12deg);\n  }\n  40%, 100% {\n    -webkit-transform: rotate(0deg);\n            transform: rotate(0deg);\n  }\n}\n@-webkit-keyframes fa-spin {\n  0% {\n    -webkit-transform: rotate(0deg);\n            transform: rotate(0deg);\n  }\n  100% {\n    -webkit-transform: rotate(360deg);\n            transform: rotate(360deg);\n  }\n}\n@keyframes fa-spin {\n  0% {\n    -webkit-transform: rotate(0deg);\n            transform: rotate(0deg);\n  }\n  100% {\n    -webkit-transform: rotate(360deg);\n            transform: rotate(360deg);\n  }\n}\n.fa-rotate-90 {\n  -webkit-transform: rotate(90deg);\n          transform: rotate(90deg);\n}\n\n.fa-rotate-180 {\n  -webkit-transform: rotate(180deg);\n          transform: rotate(180deg);\n}\n\n.fa-rotate-270 {\n  -webkit-transform: rotate(270deg);\n          transform: rotate(270deg);\n}\n\n.fa-flip-horizontal {\n  -webkit-transform: scale(-1, 1);\n          transform: scale(-1, 1);\n}\n\n.fa-flip-vertical {\n  -webkit-transform: scale(1, -1);\n          transform: scale(1, -1);\n}\n\n.fa-flip-both,\n.fa-flip-horizontal.fa-flip-vertical {\n  -webkit-transform: scale(-1, -1);\n          transform: scale(-1, -1);\n}\n\n.fa-rotate-by {\n  -webkit-transform: rotate(var(--fa-rotate-angle, 0));\n          transform: rotate(var(--fa-rotate-angle, 0));\n}\n\n.fa-stack {\n  display: inline-block;\n  vertical-align: middle;\n  height: 2em;\n  position: relative;\n  width: 2.5em;\n}\n\n.fa-stack-1x,\n.fa-stack-2x {\n  bottom: 0;\n  left: 0;\n  margin: auto;\n  position: absolute;\n  right: 0;\n  top: 0;\n  z-index: var(--fa-stack-z-index, auto);\n}\n\n.svg-inline--fa.fa-stack-1x {\n  height: 1em;\n  width: 1.25em;\n}\n.svg-inline--fa.fa-stack-2x {\n  height: 2em;\n  width: 2.5em;\n}\n\n.fa-inverse {\n  color: var(--fa-inverse, #fff);\n}\n\n.sr-only,\n.fa-sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0;\n}\n\n.sr-only-focusable:not(:focus),\n.fa-sr-only-focusable:not(:focus) {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0;\n}\n\n.svg-inline--fa .fa-primary {\n  fill: var(--fa-primary-color, currentColor);\n  opacity: var(--fa-primary-opacity, 1);\n}\n\n.svg-inline--fa .fa-secondary {\n  fill: var(--fa-secondary-color, currentColor);\n  opacity: var(--fa-secondary-opacity, 0.4);\n}\n\n.svg-inline--fa.fa-swap-opacity .fa-primary {\n  opacity: var(--fa-secondary-opacity, 0.4);\n}\n\n.svg-inline--fa.fa-swap-opacity .fa-secondary {\n  opacity: var(--fa-primary-opacity, 1);\n}\n\n.svg-inline--fa mask .fa-primary,\n.svg-inline--fa mask .fa-secondary {\n  fill: black;\n}\n\n.fad.fa-inverse,\n.fa-duotone.fa-inverse {\n  color: var(--fa-inverse, #fff);\n}';function Ae(){var e=G,t=H,n=we.cssPrefix,a=we.replacementClass,r=Le;if(n!==e||a!==t){var i=new RegExp("\\.".concat(e,"\\-"),"g"),s=new RegExp("\\--".concat(e,"\\-"),"g"),o=new RegExp("\\.".concat(t),"g");r=r.replace(i,".".concat(n,"-")).replace(s,"--".concat(n,"-")).replace(o,".".concat(a))}return r}var Ie=!1;function Ne(){we.autoAddCss&&!Ie&&(function(e){if(e&&R){var t=j.createElement("style");t.setAttribute("type","text/css"),t.innerHTML=e;for(var n=j.head.childNodes,a=null,r=n.length-1;r>-1;r--){var i=n[r],s=(i.tagName||"").toUpperCase();["STYLE","LINK"].indexOf(s)>-1&&(a=i)}j.head.insertBefore(t,a)}}(Ae()),Ie=!0)}var ze={mixout:function(){return{dom:{css:Ae,insertCss:Ne}}},hooks:function(){return{beforeDOMElementCreation:function(){Ne()},beforeI2svg:function(){Ne()}}}},je=z||{};je[F]||(je[F]={}),je[F].styles||(je[F].styles={}),je[F].hooks||(je[F].hooks={}),je[F].shims||(je[F].shims=[]);var De=je[F],Be=[],Re=!1;function $e(e){var t=e.tag,n=e.attributes,a=void 0===n?{}:n,r=e.children,i=void 0===r?[]:r;return"string"==typeof e?Oe(e):"<".concat(t," ").concat(function(e){return Object.keys(e||{}).reduce((function(t,n){return t+"".concat(n,'="').concat(Oe(e[n]),'" ')}),"").trim()}(a),">").concat(i.map($e).join(""),"</").concat(t,">")}function Fe(e,t,n){if(e&&e[t]&&e[t][n])return{prefix:t,iconName:n,icon:e[t][n]}}R&&((Re=(j.documentElement.doScroll?/^loaded|^c/:/^loaded|^i|^c/).test(j.readyState))||j.addEventListener("DOMContentLoaded",(function e(){j.removeEventListener("DOMContentLoaded",e),Re=1,Be.map((function(e){return e()}))})));var Ge=function(e,t,n,a){var r,i,s,o=Object.keys(e),l=o.length,c=void 0!==a?function(e,t){return function(n,a,r,i){return e.call(t,n,a,r,i)}}(t,a):t;for(void 0===n?(r=1,s=e[o[0]]):(r=0,s=n);r<l;r++)s=c(s,e[i=o[r]],i,e);return s};function He(e){var t=function(e){for(var t=[],n=0,a=e.length;n<a;){var r=e.charCodeAt(n++);if(r>=55296&&r<=56319&&n<a){var i=e.charCodeAt(n++);56320==(64512&i)?t.push(((1023&r)<<10)+(1023&i)+65536):(t.push(r),n--)}else t.push(r)}return t}(e);return 1===t.length?t[0].toString(16):null}function Ve(e){return Object.keys(e).reduce((function(t,n){var a=e[n];return a.icon?t[a.iconName]=a.icon:t[n]=a,t}),{})}function Ye(e,t){var n=(arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}).skipHooks,a=void 0!==n&&n,r=Ve(t);"function"!=typeof De.hooks.addPack||a?De.styles[e]=v(v({},De.styles[e]||{}),r):De.hooks.addPack(e,Ve(t)),"fas"===e&&Ye("fa",t)}var We,qe,Xe,Ue=De.styles,Ze=De.shims,Ke=(b(We={},Q,Object.values(ie[Q])),b(We,ee,Object.values(ie[ee])),We),Je=null,Qe={},et={},tt={},nt={},at={},rt=(b(qe={},Q,Object.keys(ae[Q])),b(qe,ee,Object.keys(ae[ee])),qe);var it,st=function(){var e=function(e){return Ge(Ue,(function(t,n,a){return t[a]=Ge(n,e,{}),t}),{})};Qe=e((function(e,t,n){return t[3]&&(e[t[3]]=n),t[2]&&t[2].filter((function(e){return"number"==typeof e})).forEach((function(t){e[t.toString(16)]=n})),e})),et=e((function(e,t,n){return e[n]=n,t[2]&&t[2].filter((function(e){return"string"==typeof e})).forEach((function(t){e[t]=n})),e})),at=e((function(e,t,n){var a=t[2];return e[n]=n,a.forEach((function(t){e[t]=n})),e}));var t="far"in Ue||we.autoFetchSvg,n=Ge(Ze,(function(e,n){var a=n[0],r=n[1],i=n[2];return"far"!==r||t||(r="fas"),"string"==typeof a&&(e.names[a]={prefix:r,iconName:i}),"number"==typeof a&&(e.unicodes[a.toString(16)]={prefix:r,iconName:i}),e}),{names:{},unicodes:{}});tt=n.names,nt=n.unicodes,Je=ft(we.styleDefault,{family:we.familyDefault})};function ot(e,t){return(Qe[e]||{})[t]}function lt(e,t){return(at[e]||{})[t]}function ct(e){return tt[e]||{prefix:null,iconName:null}}function dt(){return Je}it=function(e){Je=ft(e.styleDefault,{family:we.familyDefault})},Ce.push(it),st();var ut=function(){return{prefix:null,iconName:null,rest:[]}};function ft(e){var t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).family,n=void 0===t?Q:t,a=ae[n][e],r=re[n][e]||re[n][a],i=e in De.styles?e:null;return r||i||null}var pt=(b(Xe={},Q,Object.keys(ie[Q])),b(Xe,ee,Object.keys(ie[ee])),Xe);function mt(e){var t,n=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).skipLookups,a=void 0!==n&&n,r=(b(t={},Q,"".concat(we.cssPrefix,"-").concat(Q)),b(t,ee,"".concat(we.cssPrefix,"-").concat(ee)),t),i=null,s=Q;(e.includes(r[Q])||e.some((function(e){return pt[Q].includes(e)})))&&(s=Q),(e.includes(r[ee])||e.some((function(e){return pt[ee].includes(e)})))&&(s=ee);var o=e.reduce((function(e,t){var n=function(e,t){var n,a=t.split("-"),r=a[0],i=a.slice(1).join("-");return r!==e||""===i||(n=i,~ge.indexOf(n))?null:i}(we.cssPrefix,t);if(Ue[t]?(t=Ke[s].includes(t)?se[s][t]:t,i=t,e.prefix=t):rt[s].indexOf(t)>-1?(i=t,e.prefix=ft(t,{family:s})):n?e.iconName=n:t!==we.replacementClass&&t!==r[Q]&&t!==r[ee]&&e.rest.push(t),!a&&e.prefix&&e.iconName){var o="fa"===i?ct(e.iconName):{},l=lt(e.prefix,e.iconName);o.prefix&&(i=null),e.iconName=o.iconName||l||e.iconName,e.prefix=o.prefix||e.prefix,"far"!==e.prefix||Ue.far||!Ue.fas||we.autoFetchSvg||(e.prefix="fas")}return e}),ut());return(e.includes("fa-brands")||e.includes("fab"))&&(o.prefix="fab"),(e.includes("fa-duotone")||e.includes("fad"))&&(o.prefix="fad"),o.prefix||s!==ee||!Ue.fass&&!we.autoFetchSvg||(o.prefix="fass",o.iconName=lt(o.prefix,o.iconName)||o.iconName),"fa"!==o.prefix&&"fa"!==i||(o.prefix=dt()||"fas"),o}var vt=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.definitions={}}var t,n;return t=e,n=[{key:"add",value:function(){for(var e=this,t=arguments.length,n=new Array(t),a=0;a<t;a++)n[a]=arguments[a];var r=n.reduce(this._pullDefinitions,{});Object.keys(r).forEach((function(t){e.definitions[t]=v(v({},e.definitions[t]||{}),r[t]),Ye(t,r[t]);var n=ie[Q][t];n&&Ye(n,r[t]),st()}))}},{key:"reset",value:function(){this.definitions={}}},{key:"_pullDefinitions",value:function(e,t){var n=t.prefix&&t.iconName&&t.icon?{0:t}:t;return Object.keys(n).map((function(t){var a=n[t],r=a.prefix,i=a.iconName,s=a.icon,o=s[2];e[r]||(e[r]={}),o.length>0&&o.forEach((function(t){"string"==typeof t&&(e[r][t]=s)})),e[r][i]=s})),e}}],n&&h(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),e}(),gt=[],ht={},bt={},yt=Object.keys(bt);function wt(e,t){for(var n=arguments.length,a=new Array(n>2?n-2:0),r=2;r<n;r++)a[r-2]=arguments[r];return(ht[e]||[]).forEach((function(e){t=e.apply(null,[t].concat(a))})),t}function Ct(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),a=1;a<t;a++)n[a-1]=arguments[a];(ht[e]||[]).forEach((function(e){e.apply(null,n)}))}function xt(){var e=arguments[0],t=Array.prototype.slice.call(arguments,1);return bt[e]?bt[e].apply(null,t):void 0}function St(e){"fa"===e.prefix&&(e.prefix="fas");var t=e.iconName,n=e.prefix||dt();if(t)return t=lt(n,t)||t,Fe(Et.definitions,n,t)||Fe(De.styles,n,t)}var Et=new vt,kt={i2svg:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return R?(Ct("beforeI2svg",e),xt("pseudoElements2svg",e),xt("i2svg",e)):Promise.reject("Operation requires a DOM of some kind.")},watch:function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.autoReplaceSvgRoot;!1===we.autoReplaceSvg&&(we.autoReplaceSvg=!0),we.observeMutations=!0,e=function(){Pt({autoReplaceSvgRoot:n}),Ct("watch",t)},R&&(Re?setTimeout(e,0):Be.push(e))}},Tt={noAuto:function(){we.autoReplaceSvg=!1,we.observeMutations=!1,Ct("noAuto")},config:we,dom:kt,parse:{icon:function(e){if(null===e)return null;if("object"===g(e)&&e.prefix&&e.iconName)return{prefix:e.prefix,iconName:lt(e.prefix,e.iconName)||e.iconName};if(Array.isArray(e)&&2===e.length){var t=0===e[1].indexOf("fa-")?e[1].slice(3):e[1],n=ft(e[0]);return{prefix:n,iconName:lt(n,t)||t}}if("string"==typeof e&&(e.indexOf("".concat(we.cssPrefix,"-"))>-1||e.match(oe))){var a=mt(e.split(" "),{skipLookups:!0});return{prefix:a.prefix||dt(),iconName:lt(a.prefix,a.iconName)||a.iconName}}if("string"==typeof e){var r=dt();return{prefix:r,iconName:lt(r,e)||e}}}},library:Et,findIconDefinition:St,toHtml:$e},Pt=function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).autoReplaceSvgRoot,t=void 0===e?j:e;(Object.keys(De.styles).length>0||we.autoFetchSvg)&&R&&we.autoReplaceSvg&&Tt.dom.i2svg({node:t})};function Ot(e,t){return Object.defineProperty(e,"abstract",{get:t}),Object.defineProperty(e,"html",{get:function(){return e.abstract.map((function(e){return $e(e)}))}}),Object.defineProperty(e,"node",{get:function(){if(R){var t=j.createElement("div");return t.innerHTML=e.html,t.children}}}),e}function Mt(e){var t=e.icons,n=t.main,a=t.mask,r=e.prefix,i=e.iconName,s=e.transform,o=e.symbol,l=e.title,c=e.maskId,d=e.titleId,u=e.extra,f=e.watchable,p=void 0!==f&&f,m=a.found?a:n,g=m.width,h=m.height,b="fak"===r,y=[we.replacementClass,i?"".concat(we.cssPrefix,"-").concat(i):""].filter((function(e){return-1===u.classes.indexOf(e)})).filter((function(e){return""!==e||!!e})).concat(u.classes).join(" "),w={children:[],attributes:v(v({},u.attributes),{},{"data-prefix":r,"data-icon":i,class:y,role:u.attributes.role||"img",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 ".concat(g," ").concat(h)})},C=b&&!~u.classes.indexOf("fa-fw")?{width:"".concat(g/h*16*.0625,"em")}:{};p&&(w.attributes[V]=""),l&&(w.children.push({tag:"title",attributes:{id:w.attributes["aria-labelledby"]||"title-".concat(d||ke())},children:[l]}),delete w.attributes.title);var x=v(v({},w),{},{prefix:r,iconName:i,main:n,mask:a,maskId:c,transform:s,symbol:o,styles:v(v({},C),u.styles)}),S=a.found&&n.found?xt("generateAbstractMask",x)||{children:[],attributes:{}}:xt("generateAbstractIcon",x)||{children:[],attributes:{}},E=S.children,k=S.attributes;return x.children=E,x.attributes=k,o?function(e){var t=e.prefix,n=e.iconName,a=e.children,r=e.attributes,i=e.symbol,s=!0===i?"".concat(t,"-").concat(we.cssPrefix,"-").concat(n):i;return[{tag:"svg",attributes:{style:"display: none;"},children:[{tag:"symbol",attributes:v(v({},r),{},{id:s}),children:a}]}]}(x):function(e){var t=e.children,n=e.main,a=e.mask,r=e.attributes,i=e.styles,s=e.transform;if(_e(s)&&n.found&&!a.found){var o={x:n.width/n.height/2,y:.5};r.style=Me(v(v({},i),{},{"transform-origin":"".concat(o.x+s.x/16,"em ").concat(o.y+s.y/16,"em")}))}return[{tag:"svg",attributes:r,children:t}]}(x)}function _t(e){var t=e.content,n=e.width,a=e.height,r=e.transform,i=e.title,s=e.extra,o=e.watchable,l=void 0!==o&&o,c=v(v(v({},s.attributes),i?{title:i}:{}),{},{class:s.classes.join(" ")});l&&(c[V]="");var d=v({},s.styles);_e(r)&&(d.transform=function(e){var t=e.transform,n=e.width,a=void 0===n?16:n,r=e.height,i=void 0===r?16:r,s=e.startCentered,o=void 0!==s&&s,l="";return l+=o&&$?"translate(".concat(t.x/xe-a/2,"em, ").concat(t.y/xe-i/2,"em) "):o?"translate(calc(-50% + ".concat(t.x/xe,"em), calc(-50% + ").concat(t.y/xe,"em)) "):"translate(".concat(t.x/xe,"em, ").concat(t.y/xe,"em) "),(l+="scale(".concat(t.size/xe*(t.flipX?-1:1),", ").concat(t.size/xe*(t.flipY?-1:1),") "))+"rotate(".concat(t.rotate,"deg) ")}({transform:r,startCentered:!0,width:n,height:a}),d["-webkit-transform"]=d.transform);var u=Me(d);u.length>0&&(c.style=u);var f=[];return f.push({tag:"span",attributes:c,children:[t]}),i&&f.push({tag:"span",attributes:{class:"sr-only"},children:[i]}),f}var Lt=De.styles;function At(e){var t=e[0],n=e[1],a=y(e.slice(4),1)[0];return{found:!0,width:t,height:n,icon:Array.isArray(a)?{tag:"g",attributes:{class:"".concat(we.cssPrefix,"-").concat(me.GROUP)},children:[{tag:"path",attributes:{class:"".concat(we.cssPrefix,"-").concat(me.SECONDARY),fill:"currentColor",d:a[0]}},{tag:"path",attributes:{class:"".concat(we.cssPrefix,"-").concat(me.PRIMARY),fill:"currentColor",d:a[1]}}]}:{tag:"path",attributes:{fill:"currentColor",d:a}}}}var It={found:!1,width:512,height:512};function Nt(e,t){var n=t;return"fa"===t&&null!==we.styleDefault&&(t=dt()),new Promise((function(a,r){if(xt("missingIconAbstract"),"fa"===n){var i=ct(e)||{};e=i.iconName||e,t=i.prefix||t}if(e&&t&&Lt[t]&&Lt[t][e])return a(At(Lt[t][e]));!function(e,t){J||we.showMissingIcons||!e||console.error('Icon with name "'.concat(e,'" and prefix "').concat(t,'" is missing.'))}(e,t),a(v(v({},It),{},{icon:we.showMissingIcons&&e&&xt("missingIconAbstract")||{}}))}))}var zt=function(){},jt=we.measurePerformance&&B&&B.mark&&B.measure?B:{mark:zt,measure:zt},Dt='FA "6.5.2"',Bt=function(e){jt.mark("".concat(Dt," ").concat(e," ends")),jt.measure("".concat(Dt," ").concat(e),"".concat(Dt," ").concat(e," begins"),"".concat(Dt," ").concat(e," ends"))},Rt={begin:function(e){return jt.mark("".concat(Dt," ").concat(e," begins")),function(){return Bt(e)}},end:Bt},$t=function(){};function Ft(e){return"string"==typeof(e.getAttribute?e.getAttribute(V):null)}function Gt(e){return j.createElementNS("http://www.w3.org/2000/svg",e)}function Ht(e){return j.createElement(e)}function Vt(e){var t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).ceFn,n=void 0===t?"svg"===e.tag?Gt:Ht:t;if("string"==typeof e)return j.createTextNode(e);var a=n(e.tag);return Object.keys(e.attributes||[]).forEach((function(t){a.setAttribute(t,e.attributes[t])})),(e.children||[]).forEach((function(e){a.appendChild(Vt(e,{ceFn:n}))})),a}var Yt={replace:function(e){var t=e[0];if(t.parentNode)if(e[1].forEach((function(e){t.parentNode.insertBefore(Vt(e),t)})),null===t.getAttribute(V)&&we.keepOriginalSource){var n=j.createComment(function(e){var t=" ".concat(e.outerHTML," ");return"".concat(t,"Font Awesome fontawesome.com ")}(t));t.parentNode.replaceChild(n,t)}else t.remove()},nest:function(e){var t=e[0],n=e[1];if(~Pe(t).indexOf(we.replacementClass))return Yt.replace(e);var a=new RegExp("".concat(we.cssPrefix,"-.*"));if(delete n[0].attributes.id,n[0].attributes.class){var r=n[0].attributes.class.split(" ").reduce((function(e,t){return t===we.replacementClass||t.match(a)?e.toSvg.push(t):e.toNode.push(t),e}),{toNode:[],toSvg:[]});n[0].attributes.class=r.toSvg.join(" "),0===r.toNode.length?t.removeAttribute("class"):t.setAttribute("class",r.toNode.join(" "))}var i=n.map((function(e){return $e(e)})).join("\n");t.setAttribute(V,""),t.innerHTML=i}};function Wt(e){e()}function qt(e,t){var n="function"==typeof t?t:$t;if(0===e.length)n();else{var a=Wt;we.mutateApproach===Z&&(a=z.requestAnimationFrame||Wt),a((function(){var t=!0===we.autoReplaceSvg?Yt.replace:Yt[we.autoReplaceSvg]||Yt.replace,a=Rt.begin("mutate");e.map(t),a(),n()}))}}var Xt=!1;function Ut(){Xt=!0}function Zt(){Xt=!1}var Kt=null;function Jt(e){if(D&&we.observeMutations){var t=e.treeCallback,n=void 0===t?$t:t,a=e.nodeCallback,r=void 0===a?$t:a,i=e.pseudoElementsCallback,s=void 0===i?$t:i,o=e.observeMutationsRoot,l=void 0===o?j:o;Kt=new D((function(e){if(!Xt){var t=dt();Te(e).forEach((function(e){if("childList"===e.type&&e.addedNodes.length>0&&!Ft(e.addedNodes[0])&&(we.searchPseudoElements&&s(e.target),n(e.target)),"attributes"===e.type&&e.target.parentNode&&we.searchPseudoElements&&s(e.target.parentNode),"attributes"===e.type&&Ft(e.target)&&~pe.indexOf(e.attributeName))if("class"===e.attributeName&&function(e){var t=e.getAttribute?e.getAttribute(q):null,n=e.getAttribute?e.getAttribute(X):null;return t&&n}(e.target)){var a=mt(Pe(e.target)),i=a.prefix,o=a.iconName;e.target.setAttribute(q,i||t),o&&e.target.setAttribute(X,o)}else(l=e.target)&&l.classList&&l.classList.contains&&l.classList.contains(we.replacementClass)&&r(e.target);var l}))}})),R&&Kt.observe(l,{childList:!0,attributes:!0,characterData:!0,subtree:!0})}}function Qt(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{styleParser:!0},n=function(e){var t,n,a=e.getAttribute("data-prefix"),r=e.getAttribute("data-icon"),i=void 0!==e.innerText?e.innerText.trim():"",s=mt(Pe(e));return s.prefix||(s.prefix=dt()),a&&r&&(s.prefix=a,s.iconName=r),s.iconName&&s.prefix||(s.prefix&&i.length>0&&(s.iconName=(t=s.prefix,n=e.innerText,(et[t]||{})[n]||ot(s.prefix,He(e.innerText)))),!s.iconName&&we.autoFetchSvg&&e.firstChild&&e.firstChild.nodeType===Node.TEXT_NODE&&(s.iconName=e.firstChild.data)),s}(e),a=n.iconName,r=n.prefix,i=n.rest,s=function(e){var t=Te(e.attributes).reduce((function(e,t){return"class"!==e.name&&"style"!==e.name&&(e[t.name]=t.value),e}),{}),n=e.getAttribute("title"),a=e.getAttribute("data-fa-title-id");return we.autoA11y&&(n?t["aria-labelledby"]="".concat(we.replacementClass,"-title-").concat(a||ke()):(t["aria-hidden"]="true",t.focusable="false")),t}(e),o=wt("parseNodeAttributes",{},e),l=t.styleParser?function(e){var t=e.getAttribute("style"),n=[];return t&&(n=t.split(";").reduce((function(e,t){var n=t.split(":"),a=n[0],r=n.slice(1);return a&&r.length>0&&(e[a]=r.join(":").trim()),e}),{})),n}(e):[];return v({iconName:a,title:e.getAttribute("title"),titleId:e.getAttribute("data-fa-title-id"),prefix:r,transform:Se,mask:{iconName:null,prefix:null,rest:[]},maskId:null,symbol:!1,extra:{classes:i,styles:l,attributes:s}},o)}var en=De.styles;function tn(e){var t="nest"===we.autoReplaceSvg?Qt(e,{styleParser:!1}):Qt(e);return~t.extra.classes.indexOf(le)?xt("generateLayersText",e,t):xt("generateSvgReplacementMutation",e,t)}var nn=new Set;function an(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(!R)return Promise.resolve();var n=j.documentElement.classList,a=function(e){return n.add("".concat(U,"-").concat(e))},r=function(e){return n.remove("".concat(U,"-").concat(e))},i=we.autoFetchSvg?nn:te.map((function(e){return"fa-".concat(e)})).concat(Object.keys(en));i.includes("fa")||i.push("fa");var s=[".".concat(le,":not([").concat(V,"])")].concat(i.map((function(e){return".".concat(e,":not([").concat(V,"])")}))).join(", ");if(0===s.length)return Promise.resolve();var o=[];try{o=Te(e.querySelectorAll(s))}catch(e){}if(!(o.length>0))return Promise.resolve();a("pending"),r("complete");var l=Rt.begin("onTree"),c=o.reduce((function(e,t){try{var n=tn(t);n&&e.push(n)}catch(e){J||"MissingIcon"===e.name&&console.error(e)}return e}),[]);return new Promise((function(e,n){Promise.all(c).then((function(n){qt(n,(function(){a("active"),a("complete"),r("pending"),"function"==typeof t&&t(),l(),e()}))})).catch((function(e){l(),n(e)}))}))}function rn(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;tn(e).then((function(e){e&&qt([e],t)}))}te.map((function(e){nn.add("fa-".concat(e))})),Object.keys(ae[Q]).map(nn.add.bind(nn)),Object.keys(ae[ee]).map(nn.add.bind(nn)),nn=w(nn);var sn=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.transform,a=void 0===n?Se:n,r=t.symbol,i=void 0!==r&&r,s=t.mask,o=void 0===s?null:s,l=t.maskId,c=void 0===l?null:l,d=t.title,u=void 0===d?null:d,f=t.titleId,p=void 0===f?null:f,m=t.classes,g=void 0===m?[]:m,h=t.attributes,b=void 0===h?{}:h,y=t.styles,w=void 0===y?{}:y;if(e){var C=e.prefix,x=e.iconName,S=e.icon;return Ot(v({type:"icon"},e),(function(){return Ct("beforeDOMElementCreation",{iconDefinition:e,params:t}),we.autoA11y&&(u?b["aria-labelledby"]="".concat(we.replacementClass,"-title-").concat(p||ke()):(b["aria-hidden"]="true",b.focusable="false")),Mt({icons:{main:At(S),mask:o?At(o.icon):{found:!1,width:null,height:null,icon:{}}},prefix:C,iconName:x,transform:v(v({},Se),a),symbol:i,title:u,maskId:c,titleId:p,extra:{attributes:b,styles:w,classes:g}})}))}},on={mixout:function(){return{icon:(e=sn,function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=(t||{}).icon?t:St(t||{}),r=n.mask;return r&&(r=(r||{}).icon?r:St(r||{})),e(a,v(v({},n),{},{mask:r}))})};var e},hooks:function(){return{mutationObserverCallbacks:function(e){return e.treeCallback=an,e.nodeCallback=rn,e}}},provides:function(e){e.i2svg=function(e){var t=e.node,n=void 0===t?j:t,a=e.callback;return an(n,void 0===a?function(){}:a)},e.generateSvgReplacementMutation=function(e,t){var n=t.iconName,a=t.title,r=t.titleId,i=t.prefix,s=t.transform,o=t.symbol,l=t.mask,c=t.maskId,d=t.extra;return new Promise((function(t,u){Promise.all([Nt(n,i),l.iconName?Nt(l.iconName,l.prefix):Promise.resolve({found:!1,width:512,height:512,icon:{}})]).then((function(l){var u=y(l,2),f=u[0],p=u[1];t([e,Mt({icons:{main:f,mask:p},prefix:i,iconName:n,transform:s,symbol:o,maskId:c,title:a,titleId:r,extra:d,watchable:!0})])})).catch(u)}))},e.generateAbstractIcon=function(e){var t,n=e.children,a=e.attributes,r=e.main,i=e.transform,s=Me(e.styles);return s.length>0&&(a.style=s),_e(i)&&(t=xt("generateAbstractTransformGrouping",{main:r,transform:i,containerWidth:r.width,iconWidth:r.width})),n.push(t||r.icon),{children:n,attributes:a}}}},ln={mixout:function(){return{layer:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.classes,a=void 0===n?[]:n;return Ot({type:"layer"},(function(){Ct("beforeDOMElementCreation",{assembler:e,params:t});var n=[];return e((function(e){Array.isArray(e)?e.map((function(e){n=n.concat(e.abstract)})):n=n.concat(e.abstract)})),[{tag:"span",attributes:{class:["".concat(we.cssPrefix,"-layers")].concat(w(a)).join(" ")},children:n}]}))}}}},cn={mixout:function(){return{counter:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.title,a=void 0===n?null:n,r=t.classes,i=void 0===r?[]:r,s=t.attributes,o=void 0===s?{}:s,l=t.styles,c=void 0===l?{}:l;return Ot({type:"counter",content:e},(function(){return Ct("beforeDOMElementCreation",{content:e,params:t}),function(e){var t=e.content,n=e.title,a=e.extra,r=v(v(v({},a.attributes),n?{title:n}:{}),{},{class:a.classes.join(" ")}),i=Me(a.styles);i.length>0&&(r.style=i);var s=[];return s.push({tag:"span",attributes:r,children:[t]}),n&&s.push({tag:"span",attributes:{class:"sr-only"},children:[n]}),s}({content:e.toString(),title:a,extra:{attributes:o,styles:c,classes:["".concat(we.cssPrefix,"-layers-counter")].concat(w(i))}})}))}}}},dn={mixout:function(){return{text:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.transform,a=void 0===n?Se:n,r=t.title,i=void 0===r?null:r,s=t.classes,o=void 0===s?[]:s,l=t.attributes,c=void 0===l?{}:l,d=t.styles,u=void 0===d?{}:d;return Ot({type:"text",content:e},(function(){return Ct("beforeDOMElementCreation",{content:e,params:t}),_t({content:e,transform:v(v({},Se),a),title:i,extra:{attributes:c,styles:u,classes:["".concat(we.cssPrefix,"-layers-text")].concat(w(o))}})}))}}},provides:function(e){e.generateLayersText=function(e,t){var n=t.title,a=t.transform,r=t.extra,i=null,s=null;if($){var o=parseInt(getComputedStyle(e).fontSize,10),l=e.getBoundingClientRect();i=l.width/o,s=l.height/o}return we.autoA11y&&!n&&(r.attributes["aria-hidden"]="true"),Promise.resolve([e,_t({content:e.innerHTML,width:i,height:s,transform:a,title:n,extra:r,watchable:!0})])}}},un=new RegExp('"',"ug"),fn=[1105920,1112319];function pn(e,t){var n="".concat(W).concat(t.replace(":","-"));return new Promise((function(a,r){if(null!==e.getAttribute(n))return a();var i,s,o,l=Te(e.children).filter((function(e){return e.getAttribute(Y)===t}))[0],c=z.getComputedStyle(e,t),d=c.getPropertyValue("font-family").match(ce),u=c.getPropertyValue("font-weight"),f=c.getPropertyValue("content");if(l&&!d)return e.removeChild(l),a();if(d&&"none"!==f&&""!==f){var p=c.getPropertyValue("content"),m=~["Sharp"].indexOf(d[2])?ee:Q,g=~["Solid","Regular","Light","Thin","Duotone","Brands","Kit"].indexOf(d[2])?re[m][d[2].toLowerCase()]:de[m][u],h=function(e){var t,n,a,r,i=e.replace(un,""),s=(a=(t=i).length,(r=t.charCodeAt(0))>=55296&&r<=56319&&a>1&&(n=t.charCodeAt(1))>=56320&&n<=57343?1024*(r-55296)+n-56320+65536:r),o=s>=fn[0]&&s<=fn[1],l=2===i.length&&i[0]===i[1];return{value:He(l?i[0]:i),isSecondary:o||l}}(p),b=h.value,y=h.isSecondary,w=d[0].startsWith("FontAwesome"),C=ot(g,b),x=C;if(w){var S=(s=nt[i=b],o=ot("fas",i),s||(o?{prefix:"fas",iconName:o}:null)||{prefix:null,iconName:null});S.iconName&&S.prefix&&(C=S.iconName,g=S.prefix)}if(!C||y||l&&l.getAttribute(q)===g&&l.getAttribute(X)===x)a();else{e.setAttribute(n,x),l&&e.removeChild(l);var E={iconName:null,title:null,titleId:null,prefix:null,transform:Se,symbol:!1,mask:{iconName:null,prefix:null,rest:[]},maskId:null,extra:{classes:[],styles:{},attributes:{}}},k=E.extra;k.attributes[Y]=t,Nt(C,g).then((function(r){var i=Mt(v(v({},E),{},{icons:{main:r,mask:ut()},prefix:g,iconName:x,extra:k,watchable:!0})),s=j.createElementNS("http://www.w3.org/2000/svg","svg");"::before"===t?e.insertBefore(s,e.firstChild):e.appendChild(s),s.outerHTML=i.map((function(e){return $e(e)})).join("\n"),e.removeAttribute(n),a()})).catch(r)}}else a()}))}function mn(e){return Promise.all([pn(e,"::before"),pn(e,"::after")])}function vn(e){return!(e.parentNode===document.head||~K.indexOf(e.tagName.toUpperCase())||e.getAttribute(Y)||e.parentNode&&"svg"===e.parentNode.tagName)}function gn(e){if(R)return new Promise((function(t,n){var a=Te(e.querySelectorAll("*")).filter(vn).map(mn),r=Rt.begin("searchPseudoElements");Ut(),Promise.all(a).then((function(){r(),Zt(),t()})).catch((function(){r(),Zt(),n()}))}))}var hn=!1,bn=function(e){return e.toLowerCase().split(" ").reduce((function(e,t){var n=t.toLowerCase().split("-"),a=n[0],r=n.slice(1).join("-");if(a&&"h"===r)return e.flipX=!0,e;if(a&&"v"===r)return e.flipY=!0,e;if(r=parseFloat(r),isNaN(r))return e;switch(a){case"grow":e.size=e.size+r;break;case"shrink":e.size=e.size-r;break;case"left":e.x=e.x-r;break;case"right":e.x=e.x+r;break;case"up":e.y=e.y-r;break;case"down":e.y=e.y+r;break;case"rotate":e.rotate=e.rotate+r}return e}),{size:16,x:0,y:0,flipX:!1,flipY:!1,rotate:0})},yn={mixout:function(){return{parse:{transform:function(e){return bn(e)}}}},hooks:function(){return{parseNodeAttributes:function(e,t){var n=t.getAttribute("data-fa-transform");return n&&(e.transform=bn(n)),e}}},provides:function(e){e.generateAbstractTransformGrouping=function(e){var t=e.main,n=e.transform,a=e.containerWidth,r=e.iconWidth,i={transform:"translate(".concat(a/2," 256)")},s="translate(".concat(32*n.x,", ").concat(32*n.y,") "),o="scale(".concat(n.size/16*(n.flipX?-1:1),", ").concat(n.size/16*(n.flipY?-1:1),") "),l="rotate(".concat(n.rotate," 0 0)"),c={outer:i,inner:{transform:"".concat(s," ").concat(o," ").concat(l)},path:{transform:"translate(".concat(r/2*-1," -256)")}};return{tag:"g",attributes:v({},c.outer),children:[{tag:"g",attributes:v({},c.inner),children:[{tag:t.icon.tag,children:t.icon.children,attributes:v(v({},t.icon.attributes),c.path)}]}]}}}},wn={x:0,y:0,width:"100%",height:"100%"};function Cn(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return e.attributes&&(e.attributes.fill||t)&&(e.attributes.fill="black"),e}var xn,Sn={hooks:function(){return{parseNodeAttributes:function(e,t){var n=t.getAttribute("data-fa-mask"),a=n?mt(n.split(" ").map((function(e){return e.trim()}))):ut();return a.prefix||(a.prefix=dt()),e.mask=a,e.maskId=t.getAttribute("data-fa-mask-id"),e}}},provides:function(e){e.generateAbstractMask=function(e){var t,n=e.children,a=e.attributes,r=e.main,i=e.mask,s=e.maskId,o=e.transform,l=r.width,c=r.icon,d=i.width,u=i.icon,f=function(e){var t=e.transform,n=e.iconWidth,a={transform:"translate(".concat(e.containerWidth/2," 256)")},r="translate(".concat(32*t.x,", ").concat(32*t.y,") "),i="scale(".concat(t.size/16*(t.flipX?-1:1),", ").concat(t.size/16*(t.flipY?-1:1),") "),s="rotate(".concat(t.rotate," 0 0)");return{outer:a,inner:{transform:"".concat(r," ").concat(i," ").concat(s)},path:{transform:"translate(".concat(n/2*-1," -256)")}}}({transform:o,containerWidth:d,iconWidth:l}),p={tag:"rect",attributes:v(v({},wn),{},{fill:"white"})},m=c.children?{children:c.children.map(Cn)}:{},g={tag:"g",attributes:v({},f.inner),children:[Cn(v({tag:c.tag,attributes:v(v({},c.attributes),f.path)},m))]},h={tag:"g",attributes:v({},f.outer),children:[g]},b="mask-".concat(s||ke()),y="clip-".concat(s||ke()),w={tag:"mask",attributes:v(v({},wn),{},{id:b,maskUnits:"userSpaceOnUse",maskContentUnits:"userSpaceOnUse"}),children:[p,h]},C={tag:"defs",children:[{tag:"clipPath",attributes:{id:y},children:(t=u,"g"===t.tag?t.children:[t])},w]};return n.push(C,{tag:"rect",attributes:v({fill:"currentColor","clip-path":"url(#".concat(y,")"),mask:"url(#".concat(b,")")},wn)}),{children:n,attributes:a}}}},En={provides:function(e){var t=!1;z.matchMedia&&(t=z.matchMedia("(prefers-reduced-motion: reduce)").matches),e.missingIconAbstract=function(){var e=[],n={fill:"currentColor"},a={attributeType:"XML",repeatCount:"indefinite",dur:"2s"};e.push({tag:"path",attributes:v(v({},n),{},{d:"M156.5,447.7l-12.6,29.5c-18.7-9.5-35.9-21.2-51.5-34.9l22.7-22.7C127.6,430.5,141.5,440,156.5,447.7z M40.6,272H8.5 c1.4,21.2,5.4,41.7,11.7,61.1L50,321.2C45.1,305.5,41.8,289,40.6,272z M40.6,240c1.4-18.8,5.2-37,11.1-54.1l-29.5-12.6 C14.7,194.3,10,216.7,8.5,240H40.6z M64.3,156.5c7.8-14.9,17.2-28.8,28.1-41.5L69.7,92.3c-13.7,15.6-25.5,32.8-34.9,51.5 L64.3,156.5z M397,419.6c-13.9,12-29.4,22.3-46.1,30.4l11.9,29.8c20.7-9.9,39.8-22.6,56.9-37.6L397,419.6z M115,92.4 c13.9-12,29.4-22.3,46.1-30.4l-11.9-29.8c-20.7,9.9-39.8,22.6-56.8,37.6L115,92.4z M447.7,355.5c-7.8,14.9-17.2,28.8-28.1,41.5 l22.7,22.7c13.7-15.6,25.5-32.9,34.9-51.5L447.7,355.5z M471.4,272c-1.4,18.8-5.2,37-11.1,54.1l29.5,12.6 c7.5-21.1,12.2-43.5,13.6-66.8H471.4z M321.2,462c-15.7,5-32.2,8.2-49.2,9.4v32.1c21.2-1.4,41.7-5.4,61.1-11.7L321.2,462z M240,471.4c-18.8-1.4-37-5.2-54.1-11.1l-12.6,29.5c21.1,7.5,43.5,12.2,66.8,13.6V471.4z M462,190.8c5,15.7,8.2,32.2,9.4,49.2h32.1 c-1.4-21.2-5.4-41.7-11.7-61.1L462,190.8z M92.4,397c-12-13.9-22.3-29.4-30.4-46.1l-29.8,11.9c9.9,20.7,22.6,39.8,37.6,56.9 L92.4,397z M272,40.6c18.8,1.4,36.9,5.2,54.1,11.1l12.6-29.5C317.7,14.7,295.3,10,272,8.5V40.6z M190.8,50 c15.7-5,32.2-8.2,49.2-9.4V8.5c-21.2,1.4-41.7,5.4-61.1,11.7L190.8,50z M442.3,92.3L419.6,115c12,13.9,22.3,29.4,30.5,46.1 l29.8-11.9C470,128.5,457.3,109.4,442.3,92.3z M397,92.4l22.7-22.7c-15.6-13.7-32.8-25.5-51.5-34.9l-12.6,29.5 C370.4,72.1,384.4,81.5,397,92.4z"})});var r=v(v({},a),{},{attributeName:"opacity"}),i={tag:"circle",attributes:v(v({},n),{},{cx:"256",cy:"364",r:"28"}),children:[]};return t||i.children.push({tag:"animate",attributes:v(v({},a),{},{attributeName:"r",values:"28;14;28;28;14;28;"})},{tag:"animate",attributes:v(v({},r),{},{values:"1;0;1;1;0;1;"})}),e.push(i),e.push({tag:"path",attributes:v(v({},n),{},{opacity:"1",d:"M263.7,312h-16c-6.6,0-12-5.4-12-12c0-71,77.4-63.9,77.4-107.8c0-20-17.8-40.2-57.4-40.2c-29.1,0-44.3,9.6-59.2,28.7 c-3.9,5-11.1,6-16.2,2.4l-13.1-9.2c-5.6-3.9-6.9-11.8-2.6-17.2c21.2-27.2,46.4-44.7,91.2-44.7c52.3,0,97.4,29.8,97.4,80.2 c0,67.6-77.4,63.5-77.4,107.8C275.7,306.6,270.3,312,263.7,312z"}),children:t?[]:[{tag:"animate",attributes:v(v({},r),{},{values:"1;0;0;0;0;1;"})}]}),t||e.push({tag:"path",attributes:v(v({},n),{},{opacity:"0",d:"M232.5,134.5l7,168c0.3,6.4,5.6,11.5,12,11.5h9c6.4,0,11.7-5.1,12-11.5l7-168c0.3-6.8-5.2-12.5-12-12.5h-23 C237.7,122,232.2,127.7,232.5,134.5z"}),children:[{tag:"animate",attributes:v(v({},r),{},{values:"0;0;1;1;0;0;"})}]}),{tag:"g",attributes:{class:"missing"},children:e}}}};xn={mixoutsTo:Tt}.mixoutsTo,gt=[ze,on,ln,cn,dn,{hooks:function(){return{mutationObserverCallbacks:function(e){return e.pseudoElementsCallback=gn,e}}},provides:function(e){e.pseudoElements2svg=function(e){var t=e.node,n=void 0===t?j:t;we.searchPseudoElements&&gn(n)}}},{mixout:function(){return{dom:{unwatch:function(){Ut(),hn=!0}}}},hooks:function(){return{bootstrap:function(){Jt(wt("mutationObserverCallbacks",{}))},noAuto:function(){Kt&&Kt.disconnect()},watch:function(e){var t=e.observeMutationsRoot;hn?Zt():Jt(wt("mutationObserverCallbacks",{observeMutationsRoot:t}))}}}},yn,Sn,En,{hooks:function(){return{parseNodeAttributes:function(e,t){var n=t.getAttribute("data-fa-symbol"),a=null!==n&&(""===n||n);return e.symbol=a,e}}}}],ht={},Object.keys(bt).forEach((function(e){-1===yt.indexOf(e)&&delete bt[e]})),gt.forEach((function(e){var t=e.mixout?e.mixout():{};if(Object.keys(t).forEach((function(e){"function"==typeof t[e]&&(xn[e]=t[e]),"object"===g(t[e])&&Object.keys(t[e]).forEach((function(n){xn[e]||(xn[e]={}),xn[e][n]=t[e][n]}))})),e.hooks){var n=e.hooks();Object.keys(n).forEach((function(e){ht[e]||(ht[e]=[]),ht[e].push(n[e])}))}e.provides&&e.provides(bt)}));var kn=Tt.parse,Tn=Tt.icon,Pn=n(5556),On=n.n(Pn);function Mn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function Ln(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Mn(Object(n),!0).forEach((function(t){In(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Mn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function An(e){return An="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},An(e)}function In(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Nn(e){return function(e){if(Array.isArray(e))return zn(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return zn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?zn(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function zn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}function jn(e){return t=e,(t-=0)==t?e:(e=e.replace(/[\-_\s]+(.)?/g,(function(e,t){return t?t.toUpperCase():""}))).substr(0,1).toLowerCase()+e.substr(1);var t}var Dn=["style"],Bn=!1;try{Bn=!0}catch(e){}function Rn(e){return e&&"object"===An(e)&&e.prefix&&e.iconName&&e.icon?e:kn.icon?kn.icon(e):null===e?null:e&&"object"===An(e)&&e.prefix&&e.iconName?e:Array.isArray(e)&&2===e.length?{prefix:e[0],iconName:e[1]}:"string"==typeof e?{prefix:"fas",iconName:e}:void 0}function $n(e,t){return Array.isArray(t)&&t.length>0||!Array.isArray(t)&&t?In({},e,t):{}}var Fn={border:!1,className:"",mask:null,maskId:null,fixedWidth:!1,inverse:!1,flip:!1,icon:null,listItem:!1,pull:null,pulse:!1,rotation:null,size:null,spin:!1,spinPulse:!1,spinReverse:!1,beat:!1,fade:!1,beatFade:!1,bounce:!1,shake:!1,symbol:!1,title:"",titleId:null,transform:null,swapOpacity:!1},Gn=r().forwardRef((function(e,t){var n=Ln(Ln({},Fn),e),a=n.icon,r=n.mask,i=n.symbol,s=n.className,o=n.title,l=n.titleId,c=n.maskId,d=Rn(a),u=$n("classes",[].concat(Nn(function(e){var t,n=e.beat,a=e.fade,r=e.beatFade,i=e.bounce,s=e.shake,o=e.flash,l=e.spin,c=e.spinPulse,d=e.spinReverse,u=e.pulse,f=e.fixedWidth,p=e.inverse,m=e.border,v=e.listItem,g=e.flip,h=e.size,b=e.rotation,y=e.pull,w=(In(t={"fa-beat":n,"fa-fade":a,"fa-beat-fade":r,"fa-bounce":i,"fa-shake":s,"fa-flash":o,"fa-spin":l,"fa-spin-reverse":d,"fa-spin-pulse":c,"fa-pulse":u,"fa-fw":f,"fa-inverse":p,"fa-border":m,"fa-li":v,"fa-flip":!0===g,"fa-flip-horizontal":"horizontal"===g||"both"===g,"fa-flip-vertical":"vertical"===g||"both"===g},"fa-".concat(h),null!=h),In(t,"fa-rotate-".concat(b),null!=b&&0!==b),In(t,"fa-pull-".concat(y),null!=y),In(t,"fa-swap-opacity",e.swapOpacity),t);return Object.keys(w).map((function(e){return w[e]?e:null})).filter((function(e){return e}))}(n)),Nn((s||"").split(" ")))),f=$n("transform","string"==typeof n.transform?kn.transform(n.transform):n.transform),p=$n("mask",Rn(r)),m=Tn(d,Ln(Ln(Ln(Ln({},u),f),p),{},{symbol:i,title:o,titleId:l,maskId:c}));if(!m)return function(){var e;!Bn&&console&&"function"==typeof console.error&&(e=console).error.apply(e,arguments)}("Could not find icon",d),null;var v=m.abstract,g={ref:t};return Object.keys(n).forEach((function(e){Fn.hasOwnProperty(e)||(g[e]=n[e])})),Hn(v[0],g)}));Gn.displayName="FontAwesomeIcon",Gn.propTypes={beat:On().bool,border:On().bool,beatFade:On().bool,bounce:On().bool,className:On().string,fade:On().bool,flash:On().bool,mask:On().oneOfType([On().object,On().array,On().string]),maskId:On().string,fixedWidth:On().bool,inverse:On().bool,flip:On().oneOf([!0,!1,"horizontal","vertical","both"]),icon:On().oneOfType([On().object,On().array,On().string]),listItem:On().bool,pull:On().oneOf(["right","left"]),pulse:On().bool,rotation:On().oneOf([0,90,180,270]),shake:On().bool,size:On().oneOf(["2xs","xs","sm","lg","xl","2xl","1x","2x","3x","4x","5x","6x","7x","8x","9x","10x"]),spin:On().bool,spinPulse:On().bool,spinReverse:On().bool,symbol:On().oneOfType([On().bool,On().string]),title:On().string,titleId:On().string,transform:On().oneOfType([On().string,On().object]),swapOpacity:On().bool};var Hn=function e(t,n){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if("string"==typeof n)return n;var r=(n.children||[]).map((function(n){return e(t,n)})),i=Object.keys(n.attributes||{}).reduce((function(e,t){var a=n.attributes[t];switch(t){case"class":e.attrs.className=a,delete n.attributes.class;break;case"style":e.attrs.style=a.split(";").map((function(e){return e.trim()})).filter((function(e){return e})).reduce((function(e,t){var n,a=t.indexOf(":"),r=jn(t.slice(0,a)),i=t.slice(a+1).trim();return r.startsWith("webkit")?e[(n=r,n.charAt(0).toUpperCase()+n.slice(1))]=i:e[r]=i,e}),{});break;default:0===t.indexOf("aria-")||0===t.indexOf("data-")?e.attrs[t.toLowerCase()]=a:e.attrs[jn(t)]=a}return e}),{attrs:{}}),s=a.style,o=void 0===s?{}:s,l=function(e,t){if(null==e)return{};var n,a,r=function(e,t){if(null==e)return{};var n,a,r={},i=Object.keys(e);for(a=0;a<i.length;a++)n=i[a],t.indexOf(n)>=0||(r[n]=e[n]);return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}(a,Dn);return i.attrs.style=Ln(Ln({},i.attrs.style),o),t.apply(void 0,[n.tag,Ln(Ln({},i.attrs),l)].concat(Nn(r)))}.bind(null,r().createElement),Vn={prefix:"fas",iconName:"user",icon:[448,512,[128100,62144],"f007","M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-45.7 48C79.8 304 0 383.8 0 482.3C0 498.7 13.3 512 29.7 512H418.3c16.4 0 29.7-13.3 29.7-29.7C448 383.8 368.2 304 269.7 304H178.3z"]},Yn={prefix:"fas",iconName:"star",icon:[576,512,[11088,61446],"f005","M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"]},Wn={prefix:"fas",iconName:"quote-right",icon:[448,512,[8221,"quote-right-alt"],"f10e","M448 296c0 66.3-53.7 120-120 120h-8c-17.7 0-32-14.3-32-32s14.3-32 32-32h8c30.9 0 56-25.1 56-56v-8H320c-35.3 0-64-28.7-64-64V160c0-35.3 28.7-64 64-64h64c35.3 0 64 28.7 64 64v32 32 72zm-256 0c0 66.3-53.7 120-120 120H64c-17.7 0-32-14.3-32-32s14.3-32 32-32h8c30.9 0 56-25.1 56-56v-8H64c-35.3 0-64-28.7-64-64V160c0-35.3 28.7-64 64-64h64c35.3 0 64 28.7 64 64v32 32 72z"]};function qn(e){return null!==e&&"object"==typeof e&&"constructor"in e&&e.constructor===Object}function Xn(e,t){void 0===e&&(e={}),void 0===t&&(t={}),Object.keys(t).forEach((n=>{void 0===e[n]?e[n]=t[n]:qn(t[n])&&qn(e[n])&&Object.keys(t[n]).length>0&&Xn(e[n],t[n])}))}const Un={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector:()=>null,querySelectorAll:()=>[],getElementById:()=>null,createEvent:()=>({initEvent(){}}),createElement:()=>({children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName:()=>[]}),createElementNS:()=>({}),importNode:()=>null,location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function Zn(){const e="undefined"!=typeof document?document:{};return Xn(e,Un),e}const Kn={document:Un,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle:()=>({getPropertyValue:()=>""}),Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia:()=>({}),requestAnimationFrame:e=>"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0),cancelAnimationFrame(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};function Jn(){const e="undefined"!=typeof window?window:{};return Xn(e,Kn),e}function Qn(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function ea(){return Date.now()}function ta(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function na(){const e=Object(arguments.length<=0?void 0:arguments[0]),t=["__proto__","constructor","prototype"];for(let a=1;a<arguments.length;a+=1){const r=a<0||arguments.length<=a?void 0:arguments[a];if(null!=r&&(n=r,!("undefined"!=typeof window&&void 0!==window.HTMLElement?n instanceof HTMLElement:n&&(1===n.nodeType||11===n.nodeType)))){const n=Object.keys(Object(r)).filter((e=>t.indexOf(e)<0));for(let t=0,a=n.length;t<a;t+=1){const a=n[t],i=Object.getOwnPropertyDescriptor(r,a);void 0!==i&&i.enumerable&&(ta(e[a])&&ta(r[a])?r[a].__swiper__?e[a]=r[a]:na(e[a],r[a]):!ta(e[a])&&ta(r[a])?(e[a]={},r[a].__swiper__?e[a]=r[a]:na(e[a],r[a])):e[a]=r[a])}}}var n;return e}function aa(e,t,n){e.style.setProperty(t,n)}function ra(e){let{swiper:t,targetPosition:n,side:a}=e;const r=Jn(),i=-t.translate;let s,o=null;const l=t.params.speed;t.wrapperEl.style.scrollSnapType="none",r.cancelAnimationFrame(t.cssModeFrameID);const c=n>i?"next":"prev",d=(e,t)=>"next"===c&&e>=t||"prev"===c&&e<=t,u=()=>{s=(new Date).getTime(),null===o&&(o=s);const e=Math.max(Math.min((s-o)/l,1),0),c=.5-Math.cos(e*Math.PI)/2;let f=i+c*(n-i);if(d(f,n)&&(f=n),t.wrapperEl.scrollTo({[a]:f}),d(f,n))return t.wrapperEl.style.overflow="hidden",t.wrapperEl.style.scrollSnapType="",setTimeout((()=>{t.wrapperEl.style.overflow="",t.wrapperEl.scrollTo({[a]:f})})),void r.cancelAnimationFrame(t.cssModeFrameID);t.cssModeFrameID=r.requestAnimationFrame(u)};u()}function ia(e,t){return void 0===t&&(t=""),[...e.children].filter((e=>e.matches(t)))}function sa(e){try{return void console.warn(e)}catch(e){}}function oa(e,t){void 0===t&&(t=[]);const n=document.createElement(e);return n.classList.add(...Array.isArray(t)?t:function(e){return void 0===e&&(e=""),e.trim().split(" ").filter((e=>!!e.trim()))}(t)),n}function la(e,t){return Jn().getComputedStyle(e,null).getPropertyValue(t)}function ca(e){let t,n=e;if(n){for(t=0;null!==(n=n.previousSibling);)1===n.nodeType&&(t+=1);return t}}function da(e,t){const n=[];let a=e.parentElement;for(;a;)t?a.matches(t)&&n.push(a):n.push(a),a=a.parentElement;return n}function ua(e,t,n){const a=Jn();return n?e["width"===t?"offsetWidth":"offsetHeight"]+parseFloat(a.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-right":"margin-top"))+parseFloat(a.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-left":"margin-bottom")):e.offsetWidth}function fa(e){return(Array.isArray(e)?e:[e]).filter((e=>!!e))}let pa,ma,va;function ga(){return pa||(pa=function(){const e=Jn(),t=Zn();return{smoothScroll:t.documentElement&&t.documentElement.style&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch)}}()),pa}function ha(e){return void 0===e&&(e={}),ma||(ma=function(e){let{userAgent:t}=void 0===e?{}:e;const n=ga(),a=Jn(),r=a.navigator.platform,i=t||a.navigator.userAgent,s={ios:!1,android:!1},o=a.screen.width,l=a.screen.height,c=i.match(/(Android);?[\s\/]+([\d.]+)?/);let d=i.match(/(iPad).*OS\s([\d_]+)/);const u=i.match(/(iPod)(.*OS\s([\d_]+))?/),f=!d&&i.match(/(iPhone\sOS|iOS)\s([\d_]+)/),p="Win32"===r;let m="MacIntel"===r;return!d&&m&&n.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(`${o}x${l}`)>=0&&(d=i.match(/(Version)\/([\d.]+)/),d||(d=[0,1,"13_0_0"]),m=!1),c&&!p&&(s.os="android",s.android=!0),(d||f||u)&&(s.os="ios",s.ios=!0),s}(e)),ma}var ba={on(e,t,n){const a=this;if(!a.eventsListeners||a.destroyed)return a;if("function"!=typeof t)return a;const r=n?"unshift":"push";return e.split(" ").forEach((e=>{a.eventsListeners[e]||(a.eventsListeners[e]=[]),a.eventsListeners[e][r](t)})),a},once(e,t,n){const a=this;if(!a.eventsListeners||a.destroyed)return a;if("function"!=typeof t)return a;function r(){a.off(e,r),r.__emitterProxy&&delete r.__emitterProxy;for(var n=arguments.length,i=new Array(n),s=0;s<n;s++)i[s]=arguments[s];t.apply(a,i)}return r.__emitterProxy=t,a.on(e,r,n)},onAny(e,t){const n=this;if(!n.eventsListeners||n.destroyed)return n;if("function"!=typeof e)return n;const a=t?"unshift":"push";return n.eventsAnyListeners.indexOf(e)<0&&n.eventsAnyListeners[a](e),n},offAny(e){const t=this;if(!t.eventsListeners||t.destroyed)return t;if(!t.eventsAnyListeners)return t;const n=t.eventsAnyListeners.indexOf(e);return n>=0&&t.eventsAnyListeners.splice(n,1),t},off(e,t){const n=this;return!n.eventsListeners||n.destroyed?n:n.eventsListeners?(e.split(" ").forEach((e=>{void 0===t?n.eventsListeners[e]=[]:n.eventsListeners[e]&&n.eventsListeners[e].forEach(((a,r)=>{(a===t||a.__emitterProxy&&a.__emitterProxy===t)&&n.eventsListeners[e].splice(r,1)}))})),n):n},emit(){const e=this;if(!e.eventsListeners||e.destroyed)return e;if(!e.eventsListeners)return e;let t,n,a;for(var r=arguments.length,i=new Array(r),s=0;s<r;s++)i[s]=arguments[s];return"string"==typeof i[0]||Array.isArray(i[0])?(t=i[0],n=i.slice(1,i.length),a=e):(t=i[0].events,n=i[0].data,a=i[0].context||e),n.unshift(a),(Array.isArray(t)?t:t.split(" ")).forEach((t=>{e.eventsAnyListeners&&e.eventsAnyListeners.length&&e.eventsAnyListeners.forEach((e=>{e.apply(a,[t,...n])})),e.eventsListeners&&e.eventsListeners[t]&&e.eventsListeners[t].forEach((e=>{e.apply(a,n)}))})),e}};const ya=(e,t,n)=>{t&&!e.classList.contains(n)?e.classList.add(n):!t&&e.classList.contains(n)&&e.classList.remove(n)},wa=(e,t,n)=>{t&&!e.classList.contains(n)?e.classList.add(n):!t&&e.classList.contains(n)&&e.classList.remove(n)},Ca=(e,t)=>{if(!e||e.destroyed||!e.params)return;const n=t.closest(e.isElement?"swiper-slide":`.${e.params.slideClass}`);if(n){let t=n.querySelector(`.${e.params.lazyPreloaderClass}`);!t&&e.isElement&&(n.shadowRoot?t=n.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`):requestAnimationFrame((()=>{n.shadowRoot&&(t=n.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`),t&&t.remove())}))),t&&t.remove()}},xa=(e,t)=>{if(!e.slides[t])return;const n=e.slides[t].querySelector('[loading="lazy"]');n&&n.removeAttribute("loading")},Sa=e=>{if(!e||e.destroyed||!e.params)return;let t=e.params.lazyPreloadPrevNext;const n=e.slides.length;if(!n||!t||t<0)return;t=Math.min(t,n);const a="auto"===e.params.slidesPerView?e.slidesPerViewDynamic():Math.ceil(e.params.slidesPerView),r=e.activeIndex;if(e.params.grid&&e.params.grid.rows>1){const n=r,i=[n-t];return i.push(...Array.from({length:t}).map(((e,t)=>n+a+t))),void e.slides.forEach(((t,n)=>{i.includes(t.column)&&xa(e,n)}))}const i=r+a-1;if(e.params.rewind||e.params.loop)for(let a=r-t;a<=i+t;a+=1){const t=(a%n+n)%n;(t<r||t>i)&&xa(e,t)}else for(let a=Math.max(r-t,0);a<=Math.min(i+t,n-1);a+=1)a!==r&&(a>i||a<r)&&xa(e,a)};var Ea={updateSize:function(){const e=this;let t,n;const a=e.el;t=void 0!==e.params.width&&null!==e.params.width?e.params.width:a.clientWidth,n=void 0!==e.params.height&&null!==e.params.height?e.params.height:a.clientHeight,0===t&&e.isHorizontal()||0===n&&e.isVertical()||(t=t-parseInt(la(a,"padding-left")||0,10)-parseInt(la(a,"padding-right")||0,10),n=n-parseInt(la(a,"padding-top")||0,10)-parseInt(la(a,"padding-bottom")||0,10),Number.isNaN(t)&&(t=0),Number.isNaN(n)&&(n=0),Object.assign(e,{width:t,height:n,size:e.isHorizontal()?t:n}))},updateSlides:function(){const e=this;function t(t,n){return parseFloat(t.getPropertyValue(e.getDirectionLabel(n))||0)}const n=e.params,{wrapperEl:a,slidesEl:r,size:i,rtlTranslate:s,wrongRTL:o}=e,l=e.virtual&&n.virtual.enabled,c=l?e.virtual.slides.length:e.slides.length,d=ia(r,`.${e.params.slideClass}, swiper-slide`),u=l?e.virtual.slides.length:d.length;let f=[];const p=[],m=[];let v=n.slidesOffsetBefore;"function"==typeof v&&(v=n.slidesOffsetBefore.call(e));let g=n.slidesOffsetAfter;"function"==typeof g&&(g=n.slidesOffsetAfter.call(e));const h=e.snapGrid.length,b=e.slidesGrid.length;let y=n.spaceBetween,w=-v,C=0,x=0;if(void 0===i)return;"string"==typeof y&&y.indexOf("%")>=0?y=parseFloat(y.replace("%",""))/100*i:"string"==typeof y&&(y=parseFloat(y)),e.virtualSize=-y,d.forEach((e=>{s?e.style.marginLeft="":e.style.marginRight="",e.style.marginBottom="",e.style.marginTop=""})),n.centeredSlides&&n.cssMode&&(aa(a,"--swiper-centered-offset-before",""),aa(a,"--swiper-centered-offset-after",""));const S=n.grid&&n.grid.rows>1&&e.grid;let E;S?e.grid.initSlides(d):e.grid&&e.grid.unsetSlides();const k="auto"===n.slidesPerView&&n.breakpoints&&Object.keys(n.breakpoints).filter((e=>void 0!==n.breakpoints[e].slidesPerView)).length>0;for(let a=0;a<u;a+=1){let r;if(E=0,d[a]&&(r=d[a]),S&&e.grid.updateSlide(a,r,d),!d[a]||"none"!==la(r,"display")){if("auto"===n.slidesPerView){k&&(d[a].style[e.getDirectionLabel("width")]="");const i=getComputedStyle(r),s=r.style.transform,o=r.style.webkitTransform;if(s&&(r.style.transform="none"),o&&(r.style.webkitTransform="none"),n.roundLengths)E=e.isHorizontal()?ua(r,"width",!0):ua(r,"height",!0);else{const e=t(i,"width"),n=t(i,"padding-left"),a=t(i,"padding-right"),s=t(i,"margin-left"),o=t(i,"margin-right"),l=i.getPropertyValue("box-sizing");if(l&&"border-box"===l)E=e+s+o;else{const{clientWidth:t,offsetWidth:i}=r;E=e+n+a+s+o+(i-t)}}s&&(r.style.transform=s),o&&(r.style.webkitTransform=o),n.roundLengths&&(E=Math.floor(E))}else E=(i-(n.slidesPerView-1)*y)/n.slidesPerView,n.roundLengths&&(E=Math.floor(E)),d[a]&&(d[a].style[e.getDirectionLabel("width")]=`${E}px`);d[a]&&(d[a].swiperSlideSize=E),m.push(E),n.centeredSlides?(w=w+E/2+C/2+y,0===C&&0!==a&&(w=w-i/2-y),0===a&&(w=w-i/2-y),Math.abs(w)<.001&&(w=0),n.roundLengths&&(w=Math.floor(w)),x%n.slidesPerGroup==0&&f.push(w),p.push(w)):(n.roundLengths&&(w=Math.floor(w)),(x-Math.min(e.params.slidesPerGroupSkip,x))%e.params.slidesPerGroup==0&&f.push(w),p.push(w),w=w+E+y),e.virtualSize+=E+y,C=E,x+=1}}if(e.virtualSize=Math.max(e.virtualSize,i)+g,s&&o&&("slide"===n.effect||"coverflow"===n.effect)&&(a.style.width=`${e.virtualSize+y}px`),n.setWrapperSize&&(a.style[e.getDirectionLabel("width")]=`${e.virtualSize+y}px`),S&&e.grid.updateWrapperSize(E,f),!n.centeredSlides){const t=[];for(let a=0;a<f.length;a+=1){let r=f[a];n.roundLengths&&(r=Math.floor(r)),f[a]<=e.virtualSize-i&&t.push(r)}f=t,Math.floor(e.virtualSize-i)-Math.floor(f[f.length-1])>1&&f.push(e.virtualSize-i)}if(l&&n.loop){const t=m[0]+y;if(n.slidesPerGroup>1){const a=Math.ceil((e.virtual.slidesBefore+e.virtual.slidesAfter)/n.slidesPerGroup),r=t*n.slidesPerGroup;for(let e=0;e<a;e+=1)f.push(f[f.length-1]+r)}for(let a=0;a<e.virtual.slidesBefore+e.virtual.slidesAfter;a+=1)1===n.slidesPerGroup&&f.push(f[f.length-1]+t),p.push(p[p.length-1]+t),e.virtualSize+=t}if(0===f.length&&(f=[0]),0!==y){const t=e.isHorizontal()&&s?"marginLeft":e.getDirectionLabel("marginRight");d.filter(((e,t)=>!(n.cssMode&&!n.loop)||t!==d.length-1)).forEach((e=>{e.style[t]=`${y}px`}))}if(n.centeredSlides&&n.centeredSlidesBounds){let e=0;m.forEach((t=>{e+=t+(y||0)})),e-=y;const t=e-i;f=f.map((e=>e<=0?-v:e>t?t+g:e))}if(n.centerInsufficientSlides){let e=0;m.forEach((t=>{e+=t+(y||0)})),e-=y;const t=(n.slidesOffsetBefore||0)+(n.slidesOffsetAfter||0);if(e+t<i){const n=(i-e-t)/2;f.forEach(((e,t)=>{f[t]=e-n})),p.forEach(((e,t)=>{p[t]=e+n}))}}if(Object.assign(e,{slides:d,snapGrid:f,slidesGrid:p,slidesSizesGrid:m}),n.centeredSlides&&n.cssMode&&!n.centeredSlidesBounds){aa(a,"--swiper-centered-offset-before",-f[0]+"px"),aa(a,"--swiper-centered-offset-after",e.size/2-m[m.length-1]/2+"px");const t=-e.snapGrid[0],n=-e.slidesGrid[0];e.snapGrid=e.snapGrid.map((e=>e+t)),e.slidesGrid=e.slidesGrid.map((e=>e+n))}if(u!==c&&e.emit("slidesLengthChange"),f.length!==h&&(e.params.watchOverflow&&e.checkOverflow(),e.emit("snapGridLengthChange")),p.length!==b&&e.emit("slidesGridLengthChange"),n.watchSlidesProgress&&e.updateSlidesOffset(),e.emit("slidesUpdated"),!(l||n.cssMode||"slide"!==n.effect&&"fade"!==n.effect)){const t=`${n.containerModifierClass}backface-hidden`,a=e.el.classList.contains(t);u<=n.maxBackfaceHiddenSlides?a||e.el.classList.add(t):a&&e.el.classList.remove(t)}},updateAutoHeight:function(e){const t=this,n=[],a=t.virtual&&t.params.virtual.enabled;let r,i=0;"number"==typeof e?t.setTransition(e):!0===e&&t.setTransition(t.params.speed);const s=e=>a?t.slides[t.getSlideIndexByData(e)]:t.slides[e];if("auto"!==t.params.slidesPerView&&t.params.slidesPerView>1)if(t.params.centeredSlides)(t.visibleSlides||[]).forEach((e=>{n.push(e)}));else for(r=0;r<Math.ceil(t.params.slidesPerView);r+=1){const e=t.activeIndex+r;if(e>t.slides.length&&!a)break;n.push(s(e))}else n.push(s(t.activeIndex));for(r=0;r<n.length;r+=1)if(void 0!==n[r]){const e=n[r].offsetHeight;i=e>i?e:i}(i||0===i)&&(t.wrapperEl.style.height=`${i}px`)},updateSlidesOffset:function(){const e=this,t=e.slides,n=e.isElement?e.isHorizontal()?e.wrapperEl.offsetLeft:e.wrapperEl.offsetTop:0;for(let a=0;a<t.length;a+=1)t[a].swiperSlideOffset=(e.isHorizontal()?t[a].offsetLeft:t[a].offsetTop)-n-e.cssOverflowAdjustment()},updateSlidesProgress:function(e){void 0===e&&(e=this&&this.translate||0);const t=this,n=t.params,{slides:a,rtlTranslate:r,snapGrid:i}=t;if(0===a.length)return;void 0===a[0].swiperSlideOffset&&t.updateSlidesOffset();let s=-e;r&&(s=e),t.visibleSlidesIndexes=[],t.visibleSlides=[];let o=n.spaceBetween;"string"==typeof o&&o.indexOf("%")>=0?o=parseFloat(o.replace("%",""))/100*t.size:"string"==typeof o&&(o=parseFloat(o));for(let e=0;e<a.length;e+=1){const l=a[e];let c=l.swiperSlideOffset;n.cssMode&&n.centeredSlides&&(c-=a[0].swiperSlideOffset);const d=(s+(n.centeredSlides?t.minTranslate():0)-c)/(l.swiperSlideSize+o),u=(s-i[0]+(n.centeredSlides?t.minTranslate():0)-c)/(l.swiperSlideSize+o),f=-(s-c),p=f+t.slidesSizesGrid[e],m=f>=0&&f<=t.size-t.slidesSizesGrid[e],v=f>=0&&f<t.size-1||p>1&&p<=t.size||f<=0&&p>=t.size;v&&(t.visibleSlides.push(l),t.visibleSlidesIndexes.push(e)),ya(l,v,n.slideVisibleClass),ya(l,m,n.slideFullyVisibleClass),l.progress=r?-d:d,l.originalProgress=r?-u:u}},updateProgress:function(e){const t=this;if(void 0===e){const n=t.rtlTranslate?-1:1;e=t&&t.translate&&t.translate*n||0}const n=t.params,a=t.maxTranslate()-t.minTranslate();let{progress:r,isBeginning:i,isEnd:s,progressLoop:o}=t;const l=i,c=s;if(0===a)r=0,i=!0,s=!0;else{r=(e-t.minTranslate())/a;const n=Math.abs(e-t.minTranslate())<1,o=Math.abs(e-t.maxTranslate())<1;i=n||r<=0,s=o||r>=1,n&&(r=0),o&&(r=1)}if(n.loop){const n=t.getSlideIndexByData(0),a=t.getSlideIndexByData(t.slides.length-1),r=t.slidesGrid[n],i=t.slidesGrid[a],s=t.slidesGrid[t.slidesGrid.length-1],l=Math.abs(e);o=l>=r?(l-r)/s:(l+s-i)/s,o>1&&(o-=1)}Object.assign(t,{progress:r,progressLoop:o,isBeginning:i,isEnd:s}),(n.watchSlidesProgress||n.centeredSlides&&n.autoHeight)&&t.updateSlidesProgress(e),i&&!l&&t.emit("reachBeginning toEdge"),s&&!c&&t.emit("reachEnd toEdge"),(l&&!i||c&&!s)&&t.emit("fromEdge"),t.emit("progress",r)},updateSlidesClasses:function(){const e=this,{slides:t,params:n,slidesEl:a,activeIndex:r}=e,i=e.virtual&&n.virtual.enabled,s=e.grid&&n.grid&&n.grid.rows>1,o=e=>ia(a,`.${n.slideClass}${e}, swiper-slide${e}`)[0];let l,c,d;if(i)if(n.loop){let t=r-e.virtual.slidesBefore;t<0&&(t=e.virtual.slides.length+t),t>=e.virtual.slides.length&&(t-=e.virtual.slides.length),l=o(`[data-swiper-slide-index="${t}"]`)}else l=o(`[data-swiper-slide-index="${r}"]`);else s?(l=t.filter((e=>e.column===r))[0],d=t.filter((e=>e.column===r+1))[0],c=t.filter((e=>e.column===r-1))[0]):l=t[r];l&&(s||(d=function(e,t){const n=[];for(;e.nextElementSibling;){const a=e.nextElementSibling;t?a.matches(t)&&n.push(a):n.push(a),e=a}return n}(l,`.${n.slideClass}, swiper-slide`)[0],n.loop&&!d&&(d=t[0]),c=function(e,t){const n=[];for(;e.previousElementSibling;){const a=e.previousElementSibling;t?a.matches(t)&&n.push(a):n.push(a),e=a}return n}(l,`.${n.slideClass}, swiper-slide`)[0],n.loop&&0===!c&&(c=t[t.length-1]))),t.forEach((e=>{wa(e,e===l,n.slideActiveClass),wa(e,e===d,n.slideNextClass),wa(e,e===c,n.slidePrevClass)})),e.emitSlidesClasses()},updateActiveIndex:function(e){const t=this,n=t.rtlTranslate?t.translate:-t.translate,{snapGrid:a,params:r,activeIndex:i,realIndex:s,snapIndex:o}=t;let l,c=e;const d=e=>{let n=e-t.virtual.slidesBefore;return n<0&&(n=t.virtual.slides.length+n),n>=t.virtual.slides.length&&(n-=t.virtual.slides.length),n};if(void 0===c&&(c=function(e){const{slidesGrid:t,params:n}=e,a=e.rtlTranslate?e.translate:-e.translate;let r;for(let e=0;e<t.length;e+=1)void 0!==t[e+1]?a>=t[e]&&a<t[e+1]-(t[e+1]-t[e])/2?r=e:a>=t[e]&&a<t[e+1]&&(r=e+1):a>=t[e]&&(r=e);return n.normalizeSlideIndex&&(r<0||void 0===r)&&(r=0),r}(t)),a.indexOf(n)>=0)l=a.indexOf(n);else{const e=Math.min(r.slidesPerGroupSkip,c);l=e+Math.floor((c-e)/r.slidesPerGroup)}if(l>=a.length&&(l=a.length-1),c===i&&!t.params.loop)return void(l!==o&&(t.snapIndex=l,t.emit("snapIndexChange")));if(c===i&&t.params.loop&&t.virtual&&t.params.virtual.enabled)return void(t.realIndex=d(c));const u=t.grid&&r.grid&&r.grid.rows>1;let f;if(t.virtual&&r.virtual.enabled&&r.loop)f=d(c);else if(u){const e=t.slides.filter((e=>e.column===c))[0];let n=parseInt(e.getAttribute("data-swiper-slide-index"),10);Number.isNaN(n)&&(n=Math.max(t.slides.indexOf(e),0)),f=Math.floor(n/r.grid.rows)}else if(t.slides[c]){const e=t.slides[c].getAttribute("data-swiper-slide-index");f=e?parseInt(e,10):c}else f=c;Object.assign(t,{previousSnapIndex:o,snapIndex:l,previousRealIndex:s,realIndex:f,previousIndex:i,activeIndex:c}),t.initialized&&Sa(t),t.emit("activeIndexChange"),t.emit("snapIndexChange"),(t.initialized||t.params.runCallbacksOnInit)&&(s!==f&&t.emit("realIndexChange"),t.emit("slideChange"))},updateClickedSlide:function(e,t){const n=this,a=n.params;let r=e.closest(`.${a.slideClass}, swiper-slide`);!r&&n.isElement&&t&&t.length>1&&t.includes(e)&&[...t.slice(t.indexOf(e)+1,t.length)].forEach((e=>{!r&&e.matches&&e.matches(`.${a.slideClass}, swiper-slide`)&&(r=e)}));let i,s=!1;if(r)for(let e=0;e<n.slides.length;e+=1)if(n.slides[e]===r){s=!0,i=e;break}if(!r||!s)return n.clickedSlide=void 0,void(n.clickedIndex=void 0);n.clickedSlide=r,n.virtual&&n.params.virtual.enabled?n.clickedIndex=parseInt(r.getAttribute("data-swiper-slide-index"),10):n.clickedIndex=i,a.slideToClickedSlide&&void 0!==n.clickedIndex&&n.clickedIndex!==n.activeIndex&&n.slideToClickedSlide()}};function ka(e){let{swiper:t,runCallbacks:n,direction:a,step:r}=e;const{activeIndex:i,previousIndex:s}=t;let o=a;if(o||(o=i>s?"next":i<s?"prev":"reset"),t.emit(`transition${r}`),n&&i!==s){if("reset"===o)return void t.emit(`slideResetTransition${r}`);t.emit(`slideChangeTransition${r}`),"next"===o?t.emit(`slideNextTransition${r}`):t.emit(`slidePrevTransition${r}`)}}var Ta={slideTo:function(e,t,n,a,r){void 0===e&&(e=0),void 0===n&&(n=!0),"string"==typeof e&&(e=parseInt(e,10));const i=this;let s=e;s<0&&(s=0);const{params:o,snapGrid:l,slidesGrid:c,previousIndex:d,activeIndex:u,rtlTranslate:f,wrapperEl:p,enabled:m}=i;if(!m&&!a&&!r||i.destroyed||i.animating&&o.preventInteractionOnTransition)return!1;void 0===t&&(t=i.params.speed);const v=Math.min(i.params.slidesPerGroupSkip,s);let g=v+Math.floor((s-v)/i.params.slidesPerGroup);g>=l.length&&(g=l.length-1);const h=-l[g];if(o.normalizeSlideIndex)for(let e=0;e<c.length;e+=1){const t=-Math.floor(100*h),n=Math.floor(100*c[e]),a=Math.floor(100*c[e+1]);void 0!==c[e+1]?t>=n&&t<a-(a-n)/2?s=e:t>=n&&t<a&&(s=e+1):t>=n&&(s=e)}if(i.initialized&&s!==u){if(!i.allowSlideNext&&(f?h>i.translate&&h>i.minTranslate():h<i.translate&&h<i.minTranslate()))return!1;if(!i.allowSlidePrev&&h>i.translate&&h>i.maxTranslate()&&(u||0)!==s)return!1}let b;if(s!==(d||0)&&n&&i.emit("beforeSlideChangeStart"),i.updateProgress(h),b=s>u?"next":s<u?"prev":"reset",f&&-h===i.translate||!f&&h===i.translate)return i.updateActiveIndex(s),o.autoHeight&&i.updateAutoHeight(),i.updateSlidesClasses(),"slide"!==o.effect&&i.setTranslate(h),"reset"!==b&&(i.transitionStart(n,b),i.transitionEnd(n,b)),!1;if(o.cssMode){const e=i.isHorizontal(),n=f?h:-h;if(0===t){const t=i.virtual&&i.params.virtual.enabled;t&&(i.wrapperEl.style.scrollSnapType="none",i._immediateVirtual=!0),t&&!i._cssModeVirtualInitialSet&&i.params.initialSlide>0?(i._cssModeVirtualInitialSet=!0,requestAnimationFrame((()=>{p[e?"scrollLeft":"scrollTop"]=n}))):p[e?"scrollLeft":"scrollTop"]=n,t&&requestAnimationFrame((()=>{i.wrapperEl.style.scrollSnapType="",i._immediateVirtual=!1}))}else{if(!i.support.smoothScroll)return ra({swiper:i,targetPosition:n,side:e?"left":"top"}),!0;p.scrollTo({[e?"left":"top"]:n,behavior:"smooth"})}return!0}return i.setTransition(t),i.setTranslate(h),i.updateActiveIndex(s),i.updateSlidesClasses(),i.emit("beforeTransitionStart",t,a),i.transitionStart(n,b),0===t?i.transitionEnd(n,b):i.animating||(i.animating=!0,i.onSlideToWrapperTransitionEnd||(i.onSlideToWrapperTransitionEnd=function(e){i&&!i.destroyed&&e.target===this&&(i.wrapperEl.removeEventListener("transitionend",i.onSlideToWrapperTransitionEnd),i.onSlideToWrapperTransitionEnd=null,delete i.onSlideToWrapperTransitionEnd,i.transitionEnd(n,b))}),i.wrapperEl.addEventListener("transitionend",i.onSlideToWrapperTransitionEnd)),!0},slideToLoop:function(e,t,n,a){void 0===e&&(e=0),void 0===n&&(n=!0),"string"==typeof e&&(e=parseInt(e,10));const r=this;if(r.destroyed)return;void 0===t&&(t=r.params.speed);const i=r.grid&&r.params.grid&&r.params.grid.rows>1;let s=e;if(r.params.loop)if(r.virtual&&r.params.virtual.enabled)s+=r.virtual.slidesBefore;else{let e;if(i){const t=s*r.params.grid.rows;e=r.slides.filter((e=>1*e.getAttribute("data-swiper-slide-index")===t))[0].column}else e=r.getSlideIndexByData(s);const t=i?Math.ceil(r.slides.length/r.params.grid.rows):r.slides.length,{centeredSlides:n}=r.params;let o=r.params.slidesPerView;"auto"===o?o=r.slidesPerViewDynamic():(o=Math.ceil(parseFloat(r.params.slidesPerView,10)),n&&o%2==0&&(o+=1));let l=t-e<o;if(n&&(l=l||e<Math.ceil(o/2)),a&&n&&"auto"!==r.params.slidesPerView&&!i&&(l=!1),l){const a=n?e<r.activeIndex?"prev":"next":e-r.activeIndex-1<r.params.slidesPerView?"next":"prev";r.loopFix({direction:a,slideTo:!0,activeSlideIndex:"next"===a?e+1:e-t+1,slideRealIndex:"next"===a?r.realIndex:void 0})}if(i){const e=s*r.params.grid.rows;s=r.slides.filter((t=>1*t.getAttribute("data-swiper-slide-index")===e))[0].column}else s=r.getSlideIndexByData(s)}return requestAnimationFrame((()=>{r.slideTo(s,t,n,a)})),r},slideNext:function(e,t,n){void 0===t&&(t=!0);const a=this,{enabled:r,params:i,animating:s}=a;if(!r||a.destroyed)return a;void 0===e&&(e=a.params.speed);let o=i.slidesPerGroup;"auto"===i.slidesPerView&&1===i.slidesPerGroup&&i.slidesPerGroupAuto&&(o=Math.max(a.slidesPerViewDynamic("current",!0),1));const l=a.activeIndex<i.slidesPerGroupSkip?1:o,c=a.virtual&&i.virtual.enabled;if(i.loop){if(s&&!c&&i.loopPreventsSliding)return!1;if(a.loopFix({direction:"next"}),a._clientLeft=a.wrapperEl.clientLeft,a.activeIndex===a.slides.length-1&&i.cssMode)return requestAnimationFrame((()=>{a.slideTo(a.activeIndex+l,e,t,n)})),!0}return i.rewind&&a.isEnd?a.slideTo(0,e,t,n):a.slideTo(a.activeIndex+l,e,t,n)},slidePrev:function(e,t,n){void 0===t&&(t=!0);const a=this,{params:r,snapGrid:i,slidesGrid:s,rtlTranslate:o,enabled:l,animating:c}=a;if(!l||a.destroyed)return a;void 0===e&&(e=a.params.speed);const d=a.virtual&&r.virtual.enabled;if(r.loop){if(c&&!d&&r.loopPreventsSliding)return!1;a.loopFix({direction:"prev"}),a._clientLeft=a.wrapperEl.clientLeft}function u(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}const f=u(o?a.translate:-a.translate),p=i.map((e=>u(e)));let m=i[p.indexOf(f)-1];if(void 0===m&&r.cssMode){let e;i.forEach(((t,n)=>{f>=t&&(e=n)})),void 0!==e&&(m=i[e>0?e-1:e])}let v=0;if(void 0!==m&&(v=s.indexOf(m),v<0&&(v=a.activeIndex-1),"auto"===r.slidesPerView&&1===r.slidesPerGroup&&r.slidesPerGroupAuto&&(v=v-a.slidesPerViewDynamic("previous",!0)+1,v=Math.max(v,0))),r.rewind&&a.isBeginning){const r=a.params.virtual&&a.params.virtual.enabled&&a.virtual?a.virtual.slides.length-1:a.slides.length-1;return a.slideTo(r,e,t,n)}return r.loop&&0===a.activeIndex&&r.cssMode?(requestAnimationFrame((()=>{a.slideTo(v,e,t,n)})),!0):a.slideTo(v,e,t,n)},slideReset:function(e,t,n){void 0===t&&(t=!0);const a=this;if(!a.destroyed)return void 0===e&&(e=a.params.speed),a.slideTo(a.activeIndex,e,t,n)},slideToClosest:function(e,t,n,a){void 0===t&&(t=!0),void 0===a&&(a=.5);const r=this;if(r.destroyed)return;void 0===e&&(e=r.params.speed);let i=r.activeIndex;const s=Math.min(r.params.slidesPerGroupSkip,i),o=s+Math.floor((i-s)/r.params.slidesPerGroup),l=r.rtlTranslate?r.translate:-r.translate;if(l>=r.snapGrid[o]){const e=r.snapGrid[o];l-e>(r.snapGrid[o+1]-e)*a&&(i+=r.params.slidesPerGroup)}else{const e=r.snapGrid[o-1];l-e<=(r.snapGrid[o]-e)*a&&(i-=r.params.slidesPerGroup)}return i=Math.max(i,0),i=Math.min(i,r.slidesGrid.length-1),r.slideTo(i,e,t,n)},slideToClickedSlide:function(){const e=this;if(e.destroyed)return;const{params:t,slidesEl:n}=e,a="auto"===t.slidesPerView?e.slidesPerViewDynamic():t.slidesPerView;let r,i=e.clickedIndex;const s=e.isElement?"swiper-slide":`.${t.slideClass}`;if(t.loop){if(e.animating)return;r=parseInt(e.clickedSlide.getAttribute("data-swiper-slide-index"),10),t.centeredSlides?i<e.loopedSlides-a/2||i>e.slides.length-e.loopedSlides+a/2?(e.loopFix(),i=e.getSlideIndex(ia(n,`${s}[data-swiper-slide-index="${r}"]`)[0]),Qn((()=>{e.slideTo(i)}))):e.slideTo(i):i>e.slides.length-a?(e.loopFix(),i=e.getSlideIndex(ia(n,`${s}[data-swiper-slide-index="${r}"]`)[0]),Qn((()=>{e.slideTo(i)}))):e.slideTo(i)}else e.slideTo(i)}},Pa={loopCreate:function(e){const t=this,{params:n,slidesEl:a}=t;if(!n.loop||t.virtual&&t.params.virtual.enabled)return;const r=()=>{ia(a,`.${n.slideClass}, swiper-slide`).forEach(((e,t)=>{e.setAttribute("data-swiper-slide-index",t)}))},i=t.grid&&n.grid&&n.grid.rows>1,s=n.slidesPerGroup*(i?n.grid.rows:1),o=t.slides.length%s!=0,l=i&&t.slides.length%n.grid.rows!=0,c=e=>{for(let a=0;a<e;a+=1){const e=t.isElement?oa("swiper-slide",[n.slideBlankClass]):oa("div",[n.slideClass,n.slideBlankClass]);t.slidesEl.append(e)}};o?(n.loopAddBlankSlides?(c(s-t.slides.length%s),t.recalcSlides(),t.updateSlides()):sa("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)"),r()):l?(n.loopAddBlankSlides?(c(n.grid.rows-t.slides.length%n.grid.rows),t.recalcSlides(),t.updateSlides()):sa("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)"),r()):r(),t.loopFix({slideRealIndex:e,direction:n.centeredSlides?void 0:"next"})},loopFix:function(e){let{slideRealIndex:t,slideTo:n=!0,direction:a,setTranslate:r,activeSlideIndex:i,byController:s,byMousewheel:o}=void 0===e?{}:e;const l=this;if(!l.params.loop)return;l.emit("beforeLoopFix");const{slides:c,allowSlidePrev:d,allowSlideNext:u,slidesEl:f,params:p}=l,{centeredSlides:m}=p;if(l.allowSlidePrev=!0,l.allowSlideNext=!0,l.virtual&&p.virtual.enabled)return n&&(p.centeredSlides||0!==l.snapIndex?p.centeredSlides&&l.snapIndex<p.slidesPerView?l.slideTo(l.virtual.slides.length+l.snapIndex,0,!1,!0):l.snapIndex===l.snapGrid.length-1&&l.slideTo(l.virtual.slidesBefore,0,!1,!0):l.slideTo(l.virtual.slides.length,0,!1,!0)),l.allowSlidePrev=d,l.allowSlideNext=u,void l.emit("loopFix");let v=p.slidesPerView;"auto"===v?v=l.slidesPerViewDynamic():(v=Math.ceil(parseFloat(p.slidesPerView,10)),m&&v%2==0&&(v+=1));const g=p.slidesPerGroupAuto?v:p.slidesPerGroup;let h=g;h%g!=0&&(h+=g-h%g),h+=p.loopAdditionalSlides,l.loopedSlides=h;const b=l.grid&&p.grid&&p.grid.rows>1;c.length<v+h?sa("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled and not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):b&&"row"===p.grid.fill&&sa("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");const y=[],w=[];let C=l.activeIndex;void 0===i?i=l.getSlideIndex(c.filter((e=>e.classList.contains(p.slideActiveClass)))[0]):C=i;const x="next"===a||!a,S="prev"===a||!a;let E=0,k=0;const T=b?Math.ceil(c.length/p.grid.rows):c.length,P=(b?c[i].column:i)+(m&&void 0===r?-v/2+.5:0);if(P<h){E=Math.max(h-P,g);for(let e=0;e<h-P;e+=1){const t=e-Math.floor(e/T)*T;if(b){const e=T-t-1;for(let t=c.length-1;t>=0;t-=1)c[t].column===e&&y.push(t)}else y.push(T-t-1)}}else if(P+v>T-h){k=Math.max(P-(T-2*h),g);for(let e=0;e<k;e+=1){const t=e-Math.floor(e/T)*T;b?c.forEach(((e,n)=>{e.column===t&&w.push(n)})):w.push(t)}}if(l.__preventObserver__=!0,requestAnimationFrame((()=>{l.__preventObserver__=!1})),S&&y.forEach((e=>{c[e].swiperLoopMoveDOM=!0,f.prepend(c[e]),c[e].swiperLoopMoveDOM=!1})),x&&w.forEach((e=>{c[e].swiperLoopMoveDOM=!0,f.append(c[e]),c[e].swiperLoopMoveDOM=!1})),l.recalcSlides(),"auto"===p.slidesPerView?l.updateSlides():b&&(y.length>0&&S||w.length>0&&x)&&l.slides.forEach(((e,t)=>{l.grid.updateSlide(t,e,l.slides)})),p.watchSlidesProgress&&l.updateSlidesOffset(),n)if(y.length>0&&S){if(void 0===t){const e=l.slidesGrid[C],t=l.slidesGrid[C+E]-e;o?l.setTranslate(l.translate-t):(l.slideTo(C+Math.ceil(E),0,!1,!0),r&&(l.touchEventsData.startTranslate=l.touchEventsData.startTranslate-t,l.touchEventsData.currentTranslate=l.touchEventsData.currentTranslate-t))}else if(r){const e=b?y.length/p.grid.rows:y.length;l.slideTo(l.activeIndex+e,0,!1,!0),l.touchEventsData.currentTranslate=l.translate}}else if(w.length>0&&x)if(void 0===t){const e=l.slidesGrid[C],t=l.slidesGrid[C-k]-e;o?l.setTranslate(l.translate-t):(l.slideTo(C-k,0,!1,!0),r&&(l.touchEventsData.startTranslate=l.touchEventsData.startTranslate-t,l.touchEventsData.currentTranslate=l.touchEventsData.currentTranslate-t))}else{const e=b?w.length/p.grid.rows:w.length;l.slideTo(l.activeIndex-e,0,!1,!0)}if(l.allowSlidePrev=d,l.allowSlideNext=u,l.controller&&l.controller.control&&!s){const e={slideRealIndex:t,direction:a,setTranslate:r,activeSlideIndex:i,byController:!0};Array.isArray(l.controller.control)?l.controller.control.forEach((t=>{!t.destroyed&&t.params.loop&&t.loopFix({...e,slideTo:t.params.slidesPerView===p.slidesPerView&&n})})):l.controller.control instanceof l.constructor&&l.controller.control.params.loop&&l.controller.control.loopFix({...e,slideTo:l.controller.control.params.slidesPerView===p.slidesPerView&&n})}l.emit("loopFix")},loopDestroy:function(){const e=this,{params:t,slidesEl:n}=e;if(!t.loop||e.virtual&&e.params.virtual.enabled)return;e.recalcSlides();const a=[];e.slides.forEach((e=>{const t=void 0===e.swiperSlideIndex?1*e.getAttribute("data-swiper-slide-index"):e.swiperSlideIndex;a[t]=e})),e.slides.forEach((e=>{e.removeAttribute("data-swiper-slide-index")})),a.forEach((e=>{n.append(e)})),e.recalcSlides(),e.slideTo(e.realIndex,0)}};function Oa(e,t,n){const a=Jn(),{params:r}=e,i=r.edgeSwipeDetection,s=r.edgeSwipeThreshold;return!i||!(n<=s||n>=a.innerWidth-s)||"prevent"===i&&(t.preventDefault(),!0)}function Ma(e){const t=this,n=Zn();let a=e;a.originalEvent&&(a=a.originalEvent);const r=t.touchEventsData;if("pointerdown"===a.type){if(null!==r.pointerId&&r.pointerId!==a.pointerId)return;r.pointerId=a.pointerId}else"touchstart"===a.type&&1===a.targetTouches.length&&(r.touchId=a.targetTouches[0].identifier);if("touchstart"===a.type)return void Oa(t,a,a.targetTouches[0].pageX);const{params:i,touches:s,enabled:o}=t;if(!o)return;if(!i.simulateTouch&&"mouse"===a.pointerType)return;if(t.animating&&i.preventInteractionOnTransition)return;!t.animating&&i.cssMode&&i.loop&&t.loopFix();let l=a.target;if("wrapper"===i.touchEventsTarget&&!t.wrapperEl.contains(l))return;if("which"in a&&3===a.which)return;if("button"in a&&a.button>0)return;if(r.isTouched&&r.isMoved)return;const c=!!i.noSwipingClass&&""!==i.noSwipingClass,d=a.composedPath?a.composedPath():a.path;c&&a.target&&a.target.shadowRoot&&d&&(l=d[0]);const u=i.noSwipingSelector?i.noSwipingSelector:`.${i.noSwipingClass}`,f=!(!a.target||!a.target.shadowRoot);if(i.noSwiping&&(f?function(e,t){return void 0===t&&(t=this),function t(n){if(!n||n===Zn()||n===Jn())return null;n.assignedSlot&&(n=n.assignedSlot);const a=n.closest(e);return a||n.getRootNode?a||t(n.getRootNode().host):null}(t)}(u,l):l.closest(u)))return void(t.allowClick=!0);if(i.swipeHandler&&!l.closest(i.swipeHandler))return;s.currentX=a.pageX,s.currentY=a.pageY;const p=s.currentX,m=s.currentY;if(!Oa(t,a,p))return;Object.assign(r,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),s.startX=p,s.startY=m,r.touchStartTime=ea(),t.allowClick=!0,t.updateSize(),t.swipeDirection=void 0,i.threshold>0&&(r.allowThresholdMove=!1);let v=!0;l.matches(r.focusableElements)&&(v=!1,"SELECT"===l.nodeName&&(r.isTouched=!1)),n.activeElement&&n.activeElement.matches(r.focusableElements)&&n.activeElement!==l&&n.activeElement.blur();const g=v&&t.allowTouchMove&&i.touchStartPreventDefault;!i.touchStartForcePreventDefault&&!g||l.isContentEditable||a.preventDefault(),i.freeMode&&i.freeMode.enabled&&t.freeMode&&t.animating&&!i.cssMode&&t.freeMode.onTouchStart(),t.emit("touchStart",a)}function _a(e){const t=Zn(),n=this,a=n.touchEventsData,{params:r,touches:i,rtlTranslate:s,enabled:o}=n;if(!o)return;if(!r.simulateTouch&&"mouse"===e.pointerType)return;let l,c=e;if(c.originalEvent&&(c=c.originalEvent),"pointermove"===c.type){if(null!==a.touchId)return;if(c.pointerId!==a.pointerId)return}if("touchmove"===c.type){if(l=[...c.changedTouches].filter((e=>e.identifier===a.touchId))[0],!l||l.identifier!==a.touchId)return}else l=c;if(!a.isTouched)return void(a.startMoving&&a.isScrolling&&n.emit("touchMoveOpposite",c));const d=l.pageX,u=l.pageY;if(c.preventedByNestedSwiper)return i.startX=d,void(i.startY=u);if(!n.allowTouchMove)return c.target.matches(a.focusableElements)||(n.allowClick=!1),void(a.isTouched&&(Object.assign(i,{startX:d,startY:u,currentX:d,currentY:u}),a.touchStartTime=ea()));if(r.touchReleaseOnEdges&&!r.loop)if(n.isVertical()){if(u<i.startY&&n.translate<=n.maxTranslate()||u>i.startY&&n.translate>=n.minTranslate())return a.isTouched=!1,void(a.isMoved=!1)}else if(d<i.startX&&n.translate<=n.maxTranslate()||d>i.startX&&n.translate>=n.minTranslate())return;if(t.activeElement&&c.target===t.activeElement&&c.target.matches(a.focusableElements))return a.isMoved=!0,void(n.allowClick=!1);a.allowTouchCallbacks&&n.emit("touchMove",c),i.previousX=i.currentX,i.previousY=i.currentY,i.currentX=d,i.currentY=u;const f=i.currentX-i.startX,p=i.currentY-i.startY;if(n.params.threshold&&Math.sqrt(f**2+p**2)<n.params.threshold)return;if(void 0===a.isScrolling){let e;n.isHorizontal()&&i.currentY===i.startY||n.isVertical()&&i.currentX===i.startX?a.isScrolling=!1:f*f+p*p>=25&&(e=180*Math.atan2(Math.abs(p),Math.abs(f))/Math.PI,a.isScrolling=n.isHorizontal()?e>r.touchAngle:90-e>r.touchAngle)}if(a.isScrolling&&n.emit("touchMoveOpposite",c),void 0===a.startMoving&&(i.currentX===i.startX&&i.currentY===i.startY||(a.startMoving=!0)),a.isScrolling||"touchmove"===c.type&&a.preventTouchMoveFromPointerMove)return void(a.isTouched=!1);if(!a.startMoving)return;n.allowClick=!1,!r.cssMode&&c.cancelable&&c.preventDefault(),r.touchMoveStopPropagation&&!r.nested&&c.stopPropagation();let m=n.isHorizontal()?f:p,v=n.isHorizontal()?i.currentX-i.previousX:i.currentY-i.previousY;r.oneWayMovement&&(m=Math.abs(m)*(s?1:-1),v=Math.abs(v)*(s?1:-1)),i.diff=m,m*=r.touchRatio,s&&(m=-m,v=-v);const g=n.touchesDirection;n.swipeDirection=m>0?"prev":"next",n.touchesDirection=v>0?"prev":"next";const h=n.params.loop&&!r.cssMode,b="next"===n.touchesDirection&&n.allowSlideNext||"prev"===n.touchesDirection&&n.allowSlidePrev;if(!a.isMoved){if(h&&b&&n.loopFix({direction:n.swipeDirection}),a.startTranslate=n.getTranslate(),n.setTransition(0),n.animating){const e=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});n.wrapperEl.dispatchEvent(e)}a.allowMomentumBounce=!1,!r.grabCursor||!0!==n.allowSlideNext&&!0!==n.allowSlidePrev||n.setGrabCursor(!0),n.emit("sliderFirstMove",c)}if((new Date).getTime(),a.isMoved&&a.allowThresholdMove&&g!==n.touchesDirection&&h&&b&&Math.abs(m)>=1)return Object.assign(i,{startX:d,startY:u,currentX:d,currentY:u,startTranslate:a.currentTranslate}),a.loopSwapReset=!0,void(a.startTranslate=a.currentTranslate);n.emit("sliderMove",c),a.isMoved=!0,a.currentTranslate=m+a.startTranslate;let y=!0,w=r.resistanceRatio;if(r.touchReleaseOnEdges&&(w=0),m>0?(h&&b&&a.allowThresholdMove&&a.currentTranslate>(r.centeredSlides?n.minTranslate()-n.slidesSizesGrid[n.activeIndex+1]:n.minTranslate())&&n.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),a.currentTranslate>n.minTranslate()&&(y=!1,r.resistance&&(a.currentTranslate=n.minTranslate()-1+(-n.minTranslate()+a.startTranslate+m)**w))):m<0&&(h&&b&&a.allowThresholdMove&&a.currentTranslate<(r.centeredSlides?n.maxTranslate()+n.slidesSizesGrid[n.slidesSizesGrid.length-1]:n.maxTranslate())&&n.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:n.slides.length-("auto"===r.slidesPerView?n.slidesPerViewDynamic():Math.ceil(parseFloat(r.slidesPerView,10)))}),a.currentTranslate<n.maxTranslate()&&(y=!1,r.resistance&&(a.currentTranslate=n.maxTranslate()+1-(n.maxTranslate()-a.startTranslate-m)**w))),y&&(c.preventedByNestedSwiper=!0),!n.allowSlideNext&&"next"===n.swipeDirection&&a.currentTranslate<a.startTranslate&&(a.currentTranslate=a.startTranslate),!n.allowSlidePrev&&"prev"===n.swipeDirection&&a.currentTranslate>a.startTranslate&&(a.currentTranslate=a.startTranslate),n.allowSlidePrev||n.allowSlideNext||(a.currentTranslate=a.startTranslate),r.threshold>0){if(!(Math.abs(m)>r.threshold||a.allowThresholdMove))return void(a.currentTranslate=a.startTranslate);if(!a.allowThresholdMove)return a.allowThresholdMove=!0,i.startX=i.currentX,i.startY=i.currentY,a.currentTranslate=a.startTranslate,void(i.diff=n.isHorizontal()?i.currentX-i.startX:i.currentY-i.startY)}r.followFinger&&!r.cssMode&&((r.freeMode&&r.freeMode.enabled&&n.freeMode||r.watchSlidesProgress)&&(n.updateActiveIndex(),n.updateSlidesClasses()),r.freeMode&&r.freeMode.enabled&&n.freeMode&&n.freeMode.onTouchMove(),n.updateProgress(a.currentTranslate),n.setTranslate(a.currentTranslate))}function La(e){const t=this,n=t.touchEventsData;let a,r=e;if(r.originalEvent&&(r=r.originalEvent),"touchend"===r.type||"touchcancel"===r.type){if(a=[...r.changedTouches].filter((e=>e.identifier===n.touchId))[0],!a||a.identifier!==n.touchId)return}else{if(null!==n.touchId)return;if(r.pointerId!==n.pointerId)return;a=r}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(r.type)&&(!["pointercancel","contextmenu"].includes(r.type)||!t.browser.isSafari&&!t.browser.isWebView))return;n.pointerId=null,n.touchId=null;const{params:i,touches:s,rtlTranslate:o,slidesGrid:l,enabled:c}=t;if(!c)return;if(!i.simulateTouch&&"mouse"===r.pointerType)return;if(n.allowTouchCallbacks&&t.emit("touchEnd",r),n.allowTouchCallbacks=!1,!n.isTouched)return n.isMoved&&i.grabCursor&&t.setGrabCursor(!1),n.isMoved=!1,void(n.startMoving=!1);i.grabCursor&&n.isMoved&&n.isTouched&&(!0===t.allowSlideNext||!0===t.allowSlidePrev)&&t.setGrabCursor(!1);const d=ea(),u=d-n.touchStartTime;if(t.allowClick){const e=r.path||r.composedPath&&r.composedPath();t.updateClickedSlide(e&&e[0]||r.target,e),t.emit("tap click",r),u<300&&d-n.lastClickTime<300&&t.emit("doubleTap doubleClick",r)}if(n.lastClickTime=ea(),Qn((()=>{t.destroyed||(t.allowClick=!0)})),!n.isTouched||!n.isMoved||!t.swipeDirection||0===s.diff&&!n.loopSwapReset||n.currentTranslate===n.startTranslate&&!n.loopSwapReset)return n.isTouched=!1,n.isMoved=!1,void(n.startMoving=!1);let f;if(n.isTouched=!1,n.isMoved=!1,n.startMoving=!1,f=i.followFinger?o?t.translate:-t.translate:-n.currentTranslate,i.cssMode)return;if(i.freeMode&&i.freeMode.enabled)return void t.freeMode.onTouchEnd({currentPos:f});const p=f>=-t.maxTranslate()&&!t.params.loop;let m=0,v=t.slidesSizesGrid[0];for(let e=0;e<l.length;e+=e<i.slidesPerGroupSkip?1:i.slidesPerGroup){const t=e<i.slidesPerGroupSkip-1?1:i.slidesPerGroup;void 0!==l[e+t]?(p||f>=l[e]&&f<l[e+t])&&(m=e,v=l[e+t]-l[e]):(p||f>=l[e])&&(m=e,v=l[l.length-1]-l[l.length-2])}let g=null,h=null;i.rewind&&(t.isBeginning?h=i.virtual&&i.virtual.enabled&&t.virtual?t.virtual.slides.length-1:t.slides.length-1:t.isEnd&&(g=0));const b=(f-l[m])/v,y=m<i.slidesPerGroupSkip-1?1:i.slidesPerGroup;if(u>i.longSwipesMs){if(!i.longSwipes)return void t.slideTo(t.activeIndex);"next"===t.swipeDirection&&(b>=i.longSwipesRatio?t.slideTo(i.rewind&&t.isEnd?g:m+y):t.slideTo(m)),"prev"===t.swipeDirection&&(b>1-i.longSwipesRatio?t.slideTo(m+y):null!==h&&b<0&&Math.abs(b)>i.longSwipesRatio?t.slideTo(h):t.slideTo(m))}else{if(!i.shortSwipes)return void t.slideTo(t.activeIndex);!t.navigation||r.target!==t.navigation.nextEl&&r.target!==t.navigation.prevEl?("next"===t.swipeDirection&&t.slideTo(null!==g?g:m+y),"prev"===t.swipeDirection&&t.slideTo(null!==h?h:m)):r.target===t.navigation.nextEl?t.slideTo(m+y):t.slideTo(m)}}function Aa(){const e=this,{params:t,el:n}=e;if(n&&0===n.offsetWidth)return;t.breakpoints&&e.setBreakpoint();const{allowSlideNext:a,allowSlidePrev:r,snapGrid:i}=e,s=e.virtual&&e.params.virtual.enabled;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses();const o=s&&t.loop;!("auto"===t.slidesPerView||t.slidesPerView>1)||!e.isEnd||e.isBeginning||e.params.centeredSlides||o?e.params.loop&&!s?e.slideToLoop(e.realIndex,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0):e.slideTo(e.slides.length-1,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&(clearTimeout(e.autoplay.resizeTimeout),e.autoplay.resizeTimeout=setTimeout((()=>{e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.resume()}),500)),e.allowSlidePrev=r,e.allowSlideNext=a,e.params.watchOverflow&&i!==e.snapGrid&&e.checkOverflow()}function Ia(e){const t=this;t.enabled&&(t.allowClick||(t.params.preventClicks&&e.preventDefault(),t.params.preventClicksPropagation&&t.animating&&(e.stopPropagation(),e.stopImmediatePropagation())))}function Na(){const e=this,{wrapperEl:t,rtlTranslate:n,enabled:a}=e;if(!a)return;let r;e.previousTranslate=e.translate,e.isHorizontal()?e.translate=-t.scrollLeft:e.translate=-t.scrollTop,0===e.translate&&(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses();const i=e.maxTranslate()-e.minTranslate();r=0===i?0:(e.translate-e.minTranslate())/i,r!==e.progress&&e.updateProgress(n?-e.translate:e.translate),e.emit("setTranslate",e.translate,!1)}function za(e){const t=this;Ca(t,e.target),t.params.cssMode||"auto"!==t.params.slidesPerView&&!t.params.autoHeight||t.update()}function ja(){const e=this;e.documentTouchHandlerProceeded||(e.documentTouchHandlerProceeded=!0,e.params.touchReleaseOnEdges&&(e.el.style.touchAction="auto"))}const Da=(e,t)=>{const n=Zn(),{params:a,el:r,wrapperEl:i,device:s}=e,o=!!a.nested,l="on"===t?"addEventListener":"removeEventListener",c=t;r&&"string"!=typeof r&&(n[l]("touchstart",e.onDocumentTouchStart,{passive:!1,capture:o}),r[l]("touchstart",e.onTouchStart,{passive:!1}),r[l]("pointerdown",e.onTouchStart,{passive:!1}),n[l]("touchmove",e.onTouchMove,{passive:!1,capture:o}),n[l]("pointermove",e.onTouchMove,{passive:!1,capture:o}),n[l]("touchend",e.onTouchEnd,{passive:!0}),n[l]("pointerup",e.onTouchEnd,{passive:!0}),n[l]("pointercancel",e.onTouchEnd,{passive:!0}),n[l]("touchcancel",e.onTouchEnd,{passive:!0}),n[l]("pointerout",e.onTouchEnd,{passive:!0}),n[l]("pointerleave",e.onTouchEnd,{passive:!0}),n[l]("contextmenu",e.onTouchEnd,{passive:!0}),(a.preventClicks||a.preventClicksPropagation)&&r[l]("click",e.onClick,!0),a.cssMode&&i[l]("scroll",e.onScroll),a.updateOnWindowResize?e[c](s.ios||s.android?"resize orientationchange observerUpdate":"resize observerUpdate",Aa,!0):e[c]("observerUpdate",Aa,!0),r[l]("load",e.onLoad,{capture:!0}))},Ba=(e,t)=>e.grid&&t.grid&&t.grid.rows>1;var Ra={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function $a(e,t){return function(n){void 0===n&&(n={});const a=Object.keys(n)[0],r=n[a];"object"==typeof r&&null!==r?(!0===e[a]&&(e[a]={enabled:!0}),"navigation"===a&&e[a]&&e[a].enabled&&!e[a].prevEl&&!e[a].nextEl&&(e[a].auto=!0),["pagination","scrollbar"].indexOf(a)>=0&&e[a]&&e[a].enabled&&!e[a].el&&(e[a].auto=!0),a in e&&"enabled"in r?("object"!=typeof e[a]||"enabled"in e[a]||(e[a].enabled=!0),e[a]||(e[a]={enabled:!1}),na(t,n)):na(t,n)):na(t,n)}}const Fa={eventsEmitter:ba,update:Ea,translate:{getTranslate:function(e){void 0===e&&(e=this.isHorizontal()?"x":"y");const{params:t,rtlTranslate:n,translate:a,wrapperEl:r}=this;if(t.virtualTranslate)return n?-a:a;if(t.cssMode)return a;let i=function(e,t){void 0===t&&(t="x");const n=Jn();let a,r,i;const s=function(e){const t=Jn();let n;return t.getComputedStyle&&(n=t.getComputedStyle(e,null)),!n&&e.currentStyle&&(n=e.currentStyle),n||(n=e.style),n}(e);return n.WebKitCSSMatrix?(r=s.transform||s.webkitTransform,r.split(",").length>6&&(r=r.split(", ").map((e=>e.replace(",","."))).join(", ")),i=new n.WebKitCSSMatrix("none"===r?"":r)):(i=s.MozTransform||s.OTransform||s.MsTransform||s.msTransform||s.transform||s.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),a=i.toString().split(",")),"x"===t&&(r=n.WebKitCSSMatrix?i.m41:16===a.length?parseFloat(a[12]):parseFloat(a[4])),"y"===t&&(r=n.WebKitCSSMatrix?i.m42:16===a.length?parseFloat(a[13]):parseFloat(a[5])),r||0}(r,e);return i+=this.cssOverflowAdjustment(),n&&(i=-i),i||0},setTranslate:function(e,t){const n=this,{rtlTranslate:a,params:r,wrapperEl:i,progress:s}=n;let o,l=0,c=0;n.isHorizontal()?l=a?-e:e:c=e,r.roundLengths&&(l=Math.floor(l),c=Math.floor(c)),n.previousTranslate=n.translate,n.translate=n.isHorizontal()?l:c,r.cssMode?i[n.isHorizontal()?"scrollLeft":"scrollTop"]=n.isHorizontal()?-l:-c:r.virtualTranslate||(n.isHorizontal()?l-=n.cssOverflowAdjustment():c-=n.cssOverflowAdjustment(),i.style.transform=`translate3d(${l}px, ${c}px, 0px)`);const d=n.maxTranslate()-n.minTranslate();o=0===d?0:(e-n.minTranslate())/d,o!==s&&n.updateProgress(e),n.emit("setTranslate",n.translate,t)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(e,t,n,a,r){void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===n&&(n=!0),void 0===a&&(a=!0);const i=this,{params:s,wrapperEl:o}=i;if(i.animating&&s.preventInteractionOnTransition)return!1;const l=i.minTranslate(),c=i.maxTranslate();let d;if(d=a&&e>l?l:a&&e<c?c:e,i.updateProgress(d),s.cssMode){const e=i.isHorizontal();if(0===t)o[e?"scrollLeft":"scrollTop"]=-d;else{if(!i.support.smoothScroll)return ra({swiper:i,targetPosition:-d,side:e?"left":"top"}),!0;o.scrollTo({[e?"left":"top"]:-d,behavior:"smooth"})}return!0}return 0===t?(i.setTransition(0),i.setTranslate(d),n&&(i.emit("beforeTransitionStart",t,r),i.emit("transitionEnd"))):(i.setTransition(t),i.setTranslate(d),n&&(i.emit("beforeTransitionStart",t,r),i.emit("transitionStart")),i.animating||(i.animating=!0,i.onTranslateToWrapperTransitionEnd||(i.onTranslateToWrapperTransitionEnd=function(e){i&&!i.destroyed&&e.target===this&&(i.wrapperEl.removeEventListener("transitionend",i.onTranslateToWrapperTransitionEnd),i.onTranslateToWrapperTransitionEnd=null,delete i.onTranslateToWrapperTransitionEnd,i.animating=!1,n&&i.emit("transitionEnd"))}),i.wrapperEl.addEventListener("transitionend",i.onTranslateToWrapperTransitionEnd))),!0}},transition:{setTransition:function(e,t){const n=this;n.params.cssMode||(n.wrapperEl.style.transitionDuration=`${e}ms`,n.wrapperEl.style.transitionDelay=0===e?"0ms":""),n.emit("setTransition",e,t)},transitionStart:function(e,t){void 0===e&&(e=!0);const n=this,{params:a}=n;a.cssMode||(a.autoHeight&&n.updateAutoHeight(),ka({swiper:n,runCallbacks:e,direction:t,step:"Start"}))},transitionEnd:function(e,t){void 0===e&&(e=!0);const n=this,{params:a}=n;n.animating=!1,a.cssMode||(n.setTransition(0),ka({swiper:n,runCallbacks:e,direction:t,step:"End"}))}},slide:Ta,loop:Pa,grabCursor:{setGrabCursor:function(e){const t=this;if(!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)return;const n="container"===t.params.touchEventsTarget?t.el:t.wrapperEl;t.isElement&&(t.__preventObserver__=!0),n.style.cursor="move",n.style.cursor=e?"grabbing":"grab",t.isElement&&requestAnimationFrame((()=>{t.__preventObserver__=!1}))},unsetGrabCursor:function(){const e=this;e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.isElement&&(e.__preventObserver__=!0),e["container"===e.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="",e.isElement&&requestAnimationFrame((()=>{e.__preventObserver__=!1})))}},events:{attachEvents:function(){const e=this,{params:t}=e;e.onTouchStart=Ma.bind(e),e.onTouchMove=_a.bind(e),e.onTouchEnd=La.bind(e),e.onDocumentTouchStart=ja.bind(e),t.cssMode&&(e.onScroll=Na.bind(e)),e.onClick=Ia.bind(e),e.onLoad=za.bind(e),Da(e,"on")},detachEvents:function(){Da(this,"off")}},breakpoints:{setBreakpoint:function(){const e=this,{realIndex:t,initialized:n,params:a,el:r}=e,i=a.breakpoints;if(!i||i&&0===Object.keys(i).length)return;const s=e.getBreakpoint(i,e.params.breakpointsBase,e.el);if(!s||e.currentBreakpoint===s)return;const o=(s in i?i[s]:void 0)||e.originalParams,l=Ba(e,a),c=Ba(e,o),d=e.params.grabCursor,u=o.grabCursor,f=a.enabled;l&&!c?(r.classList.remove(`${a.containerModifierClass}grid`,`${a.containerModifierClass}grid-column`),e.emitContainerClasses()):!l&&c&&(r.classList.add(`${a.containerModifierClass}grid`),(o.grid.fill&&"column"===o.grid.fill||!o.grid.fill&&"column"===a.grid.fill)&&r.classList.add(`${a.containerModifierClass}grid-column`),e.emitContainerClasses()),d&&!u?e.unsetGrabCursor():!d&&u&&e.setGrabCursor(),["navigation","pagination","scrollbar"].forEach((t=>{if(void 0===o[t])return;const n=a[t]&&a[t].enabled,r=o[t]&&o[t].enabled;n&&!r&&e[t].disable(),!n&&r&&e[t].enable()}));const p=o.direction&&o.direction!==a.direction,m=a.loop&&(o.slidesPerView!==a.slidesPerView||p),v=a.loop;p&&n&&e.changeDirection(),na(e.params,o);const g=e.params.enabled,h=e.params.loop;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),f&&!g?e.disable():!f&&g&&e.enable(),e.currentBreakpoint=s,e.emit("_beforeBreakpoint",o),n&&(m?(e.loopDestroy(),e.loopCreate(t),e.updateSlides()):!v&&h?(e.loopCreate(t),e.updateSlides()):v&&!h&&e.loopDestroy()),e.emit("breakpoint",o)},getBreakpoint:function(e,t,n){if(void 0===t&&(t="window"),!e||"container"===t&&!n)return;let a=!1;const r=Jn(),i="window"===t?r.innerHeight:n.clientHeight,s=Object.keys(e).map((e=>{if("string"==typeof e&&0===e.indexOf("@")){const t=parseFloat(e.substr(1));return{value:i*t,point:e}}return{value:e,point:e}}));s.sort(((e,t)=>parseInt(e.value,10)-parseInt(t.value,10)));for(let e=0;e<s.length;e+=1){const{point:i,value:o}=s[e];"window"===t?r.matchMedia(`(min-width: ${o}px)`).matches&&(a=i):o<=n.clientWidth&&(a=i)}return a||"max"}},checkOverflow:{checkOverflow:function(){const e=this,{isLocked:t,params:n}=e,{slidesOffsetBefore:a}=n;if(a){const t=e.slides.length-1,n=e.slidesGrid[t]+e.slidesSizesGrid[t]+2*a;e.isLocked=e.size>n}else e.isLocked=1===e.snapGrid.length;!0===n.allowSlideNext&&(e.allowSlideNext=!e.isLocked),!0===n.allowSlidePrev&&(e.allowSlidePrev=!e.isLocked),t&&t!==e.isLocked&&(e.isEnd=!1),t!==e.isLocked&&e.emit(e.isLocked?"lock":"unlock")}},classes:{addClasses:function(){const e=this,{classNames:t,params:n,rtl:a,el:r,device:i}=e,s=function(e,t){const n=[];return e.forEach((e=>{"object"==typeof e?Object.keys(e).forEach((a=>{e[a]&&n.push(t+a)})):"string"==typeof e&&n.push(t+e)})),n}(["initialized",n.direction,{"free-mode":e.params.freeMode&&n.freeMode.enabled},{autoheight:n.autoHeight},{rtl:a},{grid:n.grid&&n.grid.rows>1},{"grid-column":n.grid&&n.grid.rows>1&&"column"===n.grid.fill},{android:i.android},{ios:i.ios},{"css-mode":n.cssMode},{centered:n.cssMode&&n.centeredSlides},{"watch-progress":n.watchSlidesProgress}],n.containerModifierClass);t.push(...s),r.classList.add(...t),e.emitContainerClasses()},removeClasses:function(){const{el:e,classNames:t}=this;e&&"string"!=typeof e&&(e.classList.remove(...t),this.emitContainerClasses())}}},Ga={};class Ha{constructor(){let e,t;for(var n=arguments.length,a=new Array(n),r=0;r<n;r++)a[r]=arguments[r];1===a.length&&a[0].constructor&&"Object"===Object.prototype.toString.call(a[0]).slice(8,-1)?t=a[0]:[e,t]=a,t||(t={}),t=na({},t),e&&!t.el&&(t.el=e);const i=Zn();if(t.el&&"string"==typeof t.el&&i.querySelectorAll(t.el).length>1){const e=[];return i.querySelectorAll(t.el).forEach((n=>{const a=na({},t,{el:n});e.push(new Ha(a))})),e}const s=this;s.__swiper__=!0,s.support=ga(),s.device=ha({userAgent:t.userAgent}),s.browser=(va||(va=function(){const e=Jn(),t=ha();let n=!1;function a(){const t=e.navigator.userAgent.toLowerCase();return t.indexOf("safari")>=0&&t.indexOf("chrome")<0&&t.indexOf("android")<0}if(a()){const t=String(e.navigator.userAgent);if(t.includes("Version/")){const[e,a]=t.split("Version/")[1].split(" ")[0].split(".").map((e=>Number(e)));n=e<16||16===e&&a<2}}const r=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent),i=a();return{isSafari:n||i,needPerspectiveFix:n,need3dFix:i||r&&t.ios,isWebView:r}}()),va),s.eventsListeners={},s.eventsAnyListeners=[],s.modules=[...s.__modules__],t.modules&&Array.isArray(t.modules)&&s.modules.push(...t.modules);const o={};s.modules.forEach((e=>{e({params:t,swiper:s,extendParams:$a(t,o),on:s.on.bind(s),once:s.once.bind(s),off:s.off.bind(s),emit:s.emit.bind(s)})}));const l=na({},Ra,o);return s.params=na({},l,Ga,t),s.originalParams=na({},s.params),s.passedParams=na({},t),s.params&&s.params.on&&Object.keys(s.params.on).forEach((e=>{s.on(e,s.params.on[e])})),s.params&&s.params.onAny&&s.onAny(s.params.onAny),Object.assign(s,{enabled:s.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:()=>"horizontal"===s.params.direction,isVertical:()=>"vertical"===s.params.direction,activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:s.params.allowSlideNext,allowSlidePrev:s.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:s.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:s.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),s.emit("_swiper"),s.params.init&&s.init(),s}getDirectionLabel(e){return this.isHorizontal()?e:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[e]}getSlideIndex(e){const{slidesEl:t,params:n}=this,a=ca(ia(t,`.${n.slideClass}, swiper-slide`)[0]);return ca(e)-a}getSlideIndexByData(e){return this.getSlideIndex(this.slides.filter((t=>1*t.getAttribute("data-swiper-slide-index")===e))[0])}recalcSlides(){const{slidesEl:e,params:t}=this;this.slides=ia(e,`.${t.slideClass}, swiper-slide`)}enable(){const e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}disable(){const e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}setProgress(e,t){const n=this;e=Math.min(Math.max(e,0),1);const a=n.minTranslate(),r=(n.maxTranslate()-a)*e+a;n.translateTo(r,void 0===t?0:t),n.updateActiveIndex(),n.updateSlidesClasses()}emitContainerClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=e.el.className.split(" ").filter((t=>0===t.indexOf("swiper")||0===t.indexOf(e.params.containerModifierClass)));e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){const t=this;return t.destroyed?"":e.className.split(" ").filter((e=>0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass))).join(" ")}emitSlidesClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=[];e.slides.forEach((n=>{const a=e.getSlideClasses(n);t.push({slideEl:n,classNames:a}),e.emit("_slideClass",n,a)})),e.emit("_slideClasses",t)}slidesPerViewDynamic(e,t){void 0===e&&(e="current"),void 0===t&&(t=!1);const{params:n,slides:a,slidesGrid:r,slidesSizesGrid:i,size:s,activeIndex:o}=this;let l=1;if("number"==typeof n.slidesPerView)return n.slidesPerView;if(n.centeredSlides){let e,t=a[o]?Math.ceil(a[o].swiperSlideSize):0;for(let n=o+1;n<a.length;n+=1)a[n]&&!e&&(t+=Math.ceil(a[n].swiperSlideSize),l+=1,t>s&&(e=!0));for(let n=o-1;n>=0;n-=1)a[n]&&!e&&(t+=a[n].swiperSlideSize,l+=1,t>s&&(e=!0))}else if("current"===e)for(let e=o+1;e<a.length;e+=1)(t?r[e]+i[e]-r[o]<s:r[e]-r[o]<s)&&(l+=1);else for(let e=o-1;e>=0;e-=1)r[o]-r[e]<s&&(l+=1);return l}update(){const e=this;if(!e||e.destroyed)return;const{snapGrid:t,params:n}=e;function a(){const t=e.rtlTranslate?-1*e.translate:e.translate,n=Math.min(Math.max(t,e.maxTranslate()),e.minTranslate());e.setTranslate(n),e.updateActiveIndex(),e.updateSlidesClasses()}let r;if(n.breakpoints&&e.setBreakpoint(),[...e.el.querySelectorAll('[loading="lazy"]')].forEach((t=>{t.complete&&Ca(e,t)})),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),n.freeMode&&n.freeMode.enabled&&!n.cssMode)a(),n.autoHeight&&e.updateAutoHeight();else{if(("auto"===n.slidesPerView||n.slidesPerView>1)&&e.isEnd&&!n.centeredSlides){const t=e.virtual&&n.virtual.enabled?e.virtual.slides:e.slides;r=e.slideTo(t.length-1,0,!1,!0)}else r=e.slideTo(e.activeIndex,0,!1,!0);r||a()}n.watchOverflow&&t!==e.snapGrid&&e.checkOverflow(),e.emit("update")}changeDirection(e,t){void 0===t&&(t=!0);const n=this,a=n.params.direction;return e||(e="horizontal"===a?"vertical":"horizontal"),e===a||"horizontal"!==e&&"vertical"!==e||(n.el.classList.remove(`${n.params.containerModifierClass}${a}`),n.el.classList.add(`${n.params.containerModifierClass}${e}`),n.emitContainerClasses(),n.params.direction=e,n.slides.forEach((t=>{"vertical"===e?t.style.width="":t.style.height=""})),n.emit("changeDirection"),t&&n.update()),n}changeLanguageDirection(e){const t=this;t.rtl&&"rtl"===e||!t.rtl&&"ltr"===e||(t.rtl="rtl"===e,t.rtlTranslate="horizontal"===t.params.direction&&t.rtl,t.rtl?(t.el.classList.add(`${t.params.containerModifierClass}rtl`),t.el.dir="rtl"):(t.el.classList.remove(`${t.params.containerModifierClass}rtl`),t.el.dir="ltr"),t.update())}mount(e){const t=this;if(t.mounted)return!0;let n=e||t.params.el;if("string"==typeof n&&(n=document.querySelector(n)),!n)return!1;n.swiper=t,n.parentNode&&n.parentNode.host&&n.parentNode.host.nodeName===t.params.swiperElementNodeName.toUpperCase()&&(t.isElement=!0);const a=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`;let r=n&&n.shadowRoot&&n.shadowRoot.querySelector?n.shadowRoot.querySelector(a()):ia(n,a())[0];return!r&&t.params.createElements&&(r=oa("div",t.params.wrapperClass),n.append(r),ia(n,`.${t.params.slideClass}`).forEach((e=>{r.append(e)}))),Object.assign(t,{el:n,wrapperEl:r,slidesEl:t.isElement&&!n.parentNode.host.slideSlots?n.parentNode.host:r,hostEl:t.isElement?n.parentNode.host:n,mounted:!0,rtl:"rtl"===n.dir.toLowerCase()||"rtl"===la(n,"direction"),rtlTranslate:"horizontal"===t.params.direction&&("rtl"===n.dir.toLowerCase()||"rtl"===la(n,"direction")),wrongRTL:"-webkit-box"===la(r,"display")}),!0}init(e){const t=this;if(t.initialized)return t;if(!1===t.mount(e))return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(),t.attachEvents();const n=[...t.el.querySelectorAll('[loading="lazy"]')];return t.isElement&&n.push(...t.hostEl.querySelectorAll('[loading="lazy"]')),n.forEach((e=>{e.complete?Ca(t,e):e.addEventListener("load",(e=>{Ca(t,e.target)}))})),Sa(t),t.initialized=!0,Sa(t),t.emit("init"),t.emit("afterInit"),t}destroy(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);const n=this,{params:a,el:r,wrapperEl:i,slides:s}=n;return void 0===n.params||n.destroyed||(n.emit("beforeDestroy"),n.initialized=!1,n.detachEvents(),a.loop&&n.loopDestroy(),t&&(n.removeClasses(),r&&"string"!=typeof r&&r.removeAttribute("style"),i&&i.removeAttribute("style"),s&&s.length&&s.forEach((e=>{e.classList.remove(a.slideVisibleClass,a.slideFullyVisibleClass,a.slideActiveClass,a.slideNextClass,a.slidePrevClass),e.removeAttribute("style"),e.removeAttribute("data-swiper-slide-index")}))),n.emit("destroy"),Object.keys(n.eventsListeners).forEach((e=>{n.off(e)})),!1!==e&&(n.el&&"string"!=typeof n.el&&(n.el.swiper=null),function(e){const t=e;Object.keys(t).forEach((e=>{try{t[e]=null}catch(e){}try{delete t[e]}catch(e){}}))}(n)),n.destroyed=!0),null}static extendDefaults(e){na(Ga,e)}static get extendedDefaults(){return Ga}static get defaults(){return Ra}static installModule(e){Ha.prototype.__modules__||(Ha.prototype.__modules__=[]);const t=Ha.prototype.__modules__;"function"==typeof e&&t.indexOf(e)<0&&t.push(e)}static use(e){return Array.isArray(e)?(e.forEach((e=>Ha.installModule(e))),Ha):(Ha.installModule(e),Ha)}}Object.keys(Fa).forEach((e=>{Object.keys(Fa[e]).forEach((t=>{Ha.prototype[t]=Fa[e][t]}))})),Ha.use([function(e){let{swiper:t,on:n,emit:a}=e;const r=Jn();let i=null,s=null;const o=()=>{t&&!t.destroyed&&t.initialized&&(a("beforeResize"),a("resize"))},l=()=>{t&&!t.destroyed&&t.initialized&&a("orientationchange")};n("init",(()=>{t.params.resizeObserver&&void 0!==r.ResizeObserver?t&&!t.destroyed&&t.initialized&&(i=new ResizeObserver((e=>{s=r.requestAnimationFrame((()=>{const{width:n,height:a}=t;let r=n,i=a;e.forEach((e=>{let{contentBoxSize:n,contentRect:a,target:s}=e;s&&s!==t.el||(r=a?a.width:(n[0]||n).inlineSize,i=a?a.height:(n[0]||n).blockSize)})),r===n&&i===a||o()}))})),i.observe(t.el)):(r.addEventListener("resize",o),r.addEventListener("orientationchange",l))})),n("destroy",(()=>{s&&r.cancelAnimationFrame(s),i&&i.unobserve&&t.el&&(i.unobserve(t.el),i=null),r.removeEventListener("resize",o),r.removeEventListener("orientationchange",l)}))},function(e){let{swiper:t,extendParams:n,on:a,emit:r}=e;const i=[],s=Jn(),o=function(e,n){void 0===n&&(n={});const a=new(s.MutationObserver||s.WebkitMutationObserver)((e=>{if(t.__preventObserver__)return;if(1===e.length)return void r("observerUpdate",e[0]);const n=function(){r("observerUpdate",e[0])};s.requestAnimationFrame?s.requestAnimationFrame(n):s.setTimeout(n,0)}));a.observe(e,{attributes:void 0===n.attributes||n.attributes,childList:void 0===n.childList||n.childList,characterData:void 0===n.characterData||n.characterData}),i.push(a)};n({observer:!1,observeParents:!1,observeSlideChildren:!1}),a("init",(()=>{if(t.params.observer){if(t.params.observeParents){const e=da(t.hostEl);for(let t=0;t<e.length;t+=1)o(e[t])}o(t.hostEl,{childList:t.params.observeSlideChildren}),o(t.wrapperEl,{attributes:!1})}})),a("destroy",(()=>{i.forEach((e=>{e.disconnect()})),i.splice(0,i.length)}))}]);const Va=["eventsPrefix","injectStyles","injectStylesUrls","modules","init","_direction","oneWayMovement","swiperElementNodeName","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","breakpointsBase","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_loop","loopAdditionalSlides","loopAddBlankSlides","loopPreventsSliding","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideActiveClass","slideVisibleClass","slideFullyVisibleClass","slideNextClass","slidePrevClass","slideBlankClass","wrapperClass","lazyPreloaderClass","lazyPreloadPrevNext","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","control"];function Ya(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)&&!e.__swiper__}function Wa(e,t){const n=["__proto__","constructor","prototype"];Object.keys(t).filter((e=>n.indexOf(e)<0)).forEach((n=>{void 0===e[n]?e[n]=t[n]:Ya(t[n])&&Ya(e[n])&&Object.keys(t[n]).length>0?t[n].__swiper__?e[n]=t[n]:Wa(e[n],t[n]):e[n]=t[n]}))}function qa(e){return void 0===e&&(e={}),e.navigation&&void 0===e.navigation.nextEl&&void 0===e.navigation.prevEl}function Xa(e){return void 0===e&&(e={}),e.pagination&&void 0===e.pagination.el}function Ua(e){return void 0===e&&(e={}),e.scrollbar&&void 0===e.scrollbar.el}function Za(e){void 0===e&&(e="");const t=e.split(" ").map((e=>e.trim())).filter((e=>!!e)),n=[];return t.forEach((e=>{n.indexOf(e)<0&&n.push(e)})),n.join(" ")}function Ka(e){return void 0===e&&(e=""),e?e.includes("swiper-wrapper")?e:`swiper-wrapper ${e}`:"swiper-wrapper"}function Ja(){return Ja=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Ja.apply(this,arguments)}function Qa(e){return e.type&&e.type.displayName&&e.type.displayName.includes("SwiperSlide")}function er(e){const t=[];return a.Children.toArray(e).forEach((e=>{Qa(e)?t.push(e):e.props&&e.props.children&&er(e.props.children).forEach((e=>t.push(e)))})),t}function tr(e){const t=[],n={"container-start":[],"container-end":[],"wrapper-start":[],"wrapper-end":[]};return a.Children.toArray(e).forEach((e=>{if(Qa(e))t.push(e);else if(e.props&&e.props.slot&&n[e.props.slot])n[e.props.slot].push(e);else if(e.props&&e.props.children){const a=er(e.props.children);a.length>0?a.forEach((e=>t.push(e))):n["container-end"].push(e)}else n["container-end"].push(e)})),{slides:t,slots:n}}function nr(e,t){return"undefined"==typeof window?(0,a.useEffect)(e,t):(0,a.useLayoutEffect)(e,t)}const ar=(0,a.createContext)(null),rr=(0,a.createContext)(null),ir=(0,a.forwardRef)((function(e,t){let{className:n,tag:r="div",wrapperTag:i="div",children:s,onSwiper:o,...l}=void 0===e?{}:e,c=!1;const[d,u]=(0,a.useState)("swiper"),[f,p]=(0,a.useState)(null),[m,v]=(0,a.useState)(!1),g=(0,a.useRef)(!1),h=(0,a.useRef)(null),b=(0,a.useRef)(null),y=(0,a.useRef)(null),w=(0,a.useRef)(null),C=(0,a.useRef)(null),x=(0,a.useRef)(null),S=(0,a.useRef)(null),E=(0,a.useRef)(null),{params:k,passedParams:T,rest:P,events:O}=function(e,t){void 0===e&&(e={}),void 0===t&&(t=!0);const n={on:{}},a={},r={};Wa(n,Ra),n._emitClasses=!0,n.init=!1;const i={},s=Va.map((e=>e.replace(/_/,""))),o=Object.assign({},e);return Object.keys(o).forEach((o=>{void 0!==e[o]&&(s.indexOf(o)>=0?Ya(e[o])?(n[o]={},r[o]={},Wa(n[o],e[o]),Wa(r[o],e[o])):(n[o]=e[o],r[o]=e[o]):0===o.search(/on[A-Z]/)&&"function"==typeof e[o]?t?a[`${o[2].toLowerCase()}${o.substr(3)}`]=e[o]:n.on[`${o[2].toLowerCase()}${o.substr(3)}`]=e[o]:i[o]=e[o])})),["navigation","pagination","scrollbar"].forEach((e=>{!0===n[e]&&(n[e]={}),!1===n[e]&&delete n[e]})),{params:n,passedParams:r,rest:i,events:a}}(l),{slides:M,slots:_}=tr(s),L=()=>{v(!m)};Object.assign(k.on,{_containerClasses(e,t){u(t)}});const A=()=>{Object.assign(k.on,O),c=!0;const e={...k};if(delete e.wrapperClass,b.current=new Ha(e),b.current.virtual&&b.current.params.virtual.enabled){b.current.virtual.slides=M;const e={cache:!1,slides:M,renderExternal:p,renderExternalUpdate:!1};Wa(b.current.params.virtual,e),Wa(b.current.originalParams.virtual,e)}};return h.current||A(),b.current&&b.current.on("_beforeBreakpoint",L),(0,a.useEffect)((()=>()=>{b.current&&b.current.off("_beforeBreakpoint",L)})),(0,a.useEffect)((()=>{!g.current&&b.current&&(b.current.emitSlidesClasses(),g.current=!0)})),nr((()=>{if(t&&(t.current=h.current),h.current)return b.current.destroyed&&A(),function(e,t){let{el:n,nextEl:a,prevEl:r,paginationEl:i,scrollbarEl:s,swiper:o}=e;qa(t)&&a&&r&&(o.params.navigation.nextEl=a,o.originalParams.navigation.nextEl=a,o.params.navigation.prevEl=r,o.originalParams.navigation.prevEl=r),Xa(t)&&i&&(o.params.pagination.el=i,o.originalParams.pagination.el=i),Ua(t)&&s&&(o.params.scrollbar.el=s,o.originalParams.scrollbar.el=s),o.init(n)}({el:h.current,nextEl:C.current,prevEl:x.current,paginationEl:S.current,scrollbarEl:E.current,swiper:b.current},k),o&&!b.current.destroyed&&o(b.current),()=>{b.current&&!b.current.destroyed&&b.current.destroy(!0,!1)}}),[]),nr((()=>{!c&&O&&b.current&&Object.keys(O).forEach((e=>{b.current.on(e,O[e])}));const e=function(e,t,n,a,r){const i=[];if(!t)return i;const s=e=>{i.indexOf(e)<0&&i.push(e)};if(n&&a){const e=a.map(r),t=n.map(r);e.join("")!==t.join("")&&s("children"),a.length!==n.length&&s("children")}return Va.filter((e=>"_"===e[0])).map((e=>e.replace(/_/,""))).forEach((n=>{if(n in e&&n in t)if(Ya(e[n])&&Ya(t[n])){const a=Object.keys(e[n]),r=Object.keys(t[n]);a.length!==r.length?s(n):(a.forEach((a=>{e[n][a]!==t[n][a]&&s(n)})),r.forEach((a=>{e[n][a]!==t[n][a]&&s(n)})))}else e[n]!==t[n]&&s(n)})),i}(T,y.current,M,w.current,(e=>e.key));return y.current=T,w.current=M,e.length&&b.current&&!b.current.destroyed&&function(e){let{swiper:t,slides:n,passedParams:a,changedParams:r,nextEl:i,prevEl:s,scrollbarEl:o,paginationEl:l}=e;const c=r.filter((e=>"children"!==e&&"direction"!==e&&"wrapperClass"!==e)),{params:d,pagination:u,navigation:f,scrollbar:p,virtual:m,thumbs:v}=t;let g,h,b,y,w,C,x,S;r.includes("thumbs")&&a.thumbs&&a.thumbs.swiper&&d.thumbs&&!d.thumbs.swiper&&(g=!0),r.includes("controller")&&a.controller&&a.controller.control&&d.controller&&!d.controller.control&&(h=!0),r.includes("pagination")&&a.pagination&&(a.pagination.el||l)&&(d.pagination||!1===d.pagination)&&u&&!u.el&&(b=!0),r.includes("scrollbar")&&a.scrollbar&&(a.scrollbar.el||o)&&(d.scrollbar||!1===d.scrollbar)&&p&&!p.el&&(y=!0),r.includes("navigation")&&a.navigation&&(a.navigation.prevEl||s)&&(a.navigation.nextEl||i)&&(d.navigation||!1===d.navigation)&&f&&!f.prevEl&&!f.nextEl&&(w=!0);const E=e=>{t[e]&&(t[e].destroy(),"navigation"===e?(t.isElement&&(t[e].prevEl.remove(),t[e].nextEl.remove()),d[e].prevEl=void 0,d[e].nextEl=void 0,t[e].prevEl=void 0,t[e].nextEl=void 0):(t.isElement&&t[e].el.remove(),d[e].el=void 0,t[e].el=void 0))};r.includes("loop")&&t.isElement&&(d.loop&&!a.loop?C=!0:!d.loop&&a.loop?x=!0:S=!0),c.forEach((e=>{if(Ya(d[e])&&Ya(a[e]))Object.assign(d[e],a[e]),"navigation"!==e&&"pagination"!==e&&"scrollbar"!==e||!("enabled"in a[e])||a[e].enabled||E(e);else{const t=a[e];!0!==t&&!1!==t||"navigation"!==e&&"pagination"!==e&&"scrollbar"!==e?d[e]=a[e]:!1===t&&E(e)}})),c.includes("controller")&&!h&&t.controller&&t.controller.control&&d.controller&&d.controller.control&&(t.controller.control=d.controller.control),r.includes("children")&&n&&m&&d.virtual.enabled?(m.slides=n,m.update(!0)):r.includes("virtual")&&m&&d.virtual.enabled&&(n&&(m.slides=n),m.update(!0)),r.includes("children")&&n&&d.loop&&(S=!0),g&&v.init()&&v.update(!0),h&&(t.controller.control=d.controller.control),b&&(!t.isElement||l&&"string"!=typeof l||(l=document.createElement("div"),l.classList.add("swiper-pagination"),l.part.add("pagination"),t.el.appendChild(l)),l&&(d.pagination.el=l),u.init(),u.render(),u.update()),y&&(!t.isElement||o&&"string"!=typeof o||(o=document.createElement("div"),o.classList.add("swiper-scrollbar"),o.part.add("scrollbar"),t.el.appendChild(o)),o&&(d.scrollbar.el=o),p.init(),p.updateSize(),p.setTranslate()),w&&(t.isElement&&(i&&"string"!=typeof i||(i=document.createElement("div"),i.classList.add("swiper-button-next"),i.innerHTML=t.hostEl.constructor.nextButtonSvg,i.part.add("button-next"),t.el.appendChild(i)),s&&"string"!=typeof s||(s=document.createElement("div"),s.classList.add("swiper-button-prev"),s.innerHTML=t.hostEl.constructor.prevButtonSvg,s.part.add("button-prev"),t.el.appendChild(s))),i&&(d.navigation.nextEl=i),s&&(d.navigation.prevEl=s),f.init(),f.update()),r.includes("allowSlideNext")&&(t.allowSlideNext=a.allowSlideNext),r.includes("allowSlidePrev")&&(t.allowSlidePrev=a.allowSlidePrev),r.includes("direction")&&t.changeDirection(a.direction,!1),(C||S)&&t.loopDestroy(),(x||S)&&t.loopCreate(),t.update()}({swiper:b.current,slides:M,passedParams:T,changedParams:e,nextEl:C.current,prevEl:x.current,scrollbarEl:E.current,paginationEl:S.current}),()=>{O&&b.current&&Object.keys(O).forEach((e=>{b.current.off(e,O[e])}))}})),nr((()=>{var e;!(e=b.current)||e.destroyed||!e.params.virtual||e.params.virtual&&!e.params.virtual.enabled||(e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.parallax&&e.params.parallax&&e.params.parallax.enabled&&e.parallax.setTranslate())}),[f]),a.createElement(r,Ja({ref:h,className:Za(`${d}${n?` ${n}`:""}`)},P),a.createElement(rr.Provider,{value:b.current},_["container-start"],a.createElement(i,{className:Ka(k.wrapperClass)},_["wrapper-start"],k.virtual?function(e,t,n){if(!n)return null;const r=e=>{let n=e;return e<0?n=t.length+e:n>=t.length&&(n-=t.length),n},i=e.isHorizontal()?{[e.rtlTranslate?"right":"left"]:`${n.offset}px`}:{top:`${n.offset}px`},{from:s,to:o}=n,l=e.params.loop?-t.length:0,c=e.params.loop?2*t.length:t.length,d=[];for(let e=l;e<c;e+=1)e>=s&&e<=o&&d.push(t[r(e)]);return d.map(((t,n)=>a.cloneElement(t,{swiper:e,style:i,key:t.props.virtualIndex||t.key||`slide-${n}`})))}(b.current,M,f):M.map(((e,t)=>a.cloneElement(e,{swiper:b.current,swiperSlideIndex:t}))),_["wrapper-end"]),qa(k)&&a.createElement(a.Fragment,null,a.createElement("div",{ref:x,className:"swiper-button-prev"}),a.createElement("div",{ref:C,className:"swiper-button-next"})),Ua(k)&&a.createElement("div",{ref:E,className:"swiper-scrollbar"}),Xa(k)&&a.createElement("div",{ref:S,className:"swiper-pagination"}),_["container-end"]))}));ir.displayName="Swiper";const sr=(0,a.forwardRef)((function(e,t){let{tag:n="div",children:r,className:i="",swiper:s,zoom:o,lazy:l,virtualIndex:c,swiperSlideIndex:d,...u}=void 0===e?{}:e;const f=(0,a.useRef)(null),[p,m]=(0,a.useState)("swiper-slide"),[v,g]=(0,a.useState)(!1);function h(e,t,n){t===f.current&&m(n)}nr((()=>{if(void 0!==d&&(f.current.swiperSlideIndex=d),t&&(t.current=f.current),f.current&&s){if(!s.destroyed)return s.on("_slideClass",h),()=>{s&&s.off("_slideClass",h)};"swiper-slide"!==p&&m("swiper-slide")}})),nr((()=>{s&&f.current&&!s.destroyed&&m(s.getSlideClasses(f.current))}),[s]);const b={isActive:p.indexOf("swiper-slide-active")>=0,isVisible:p.indexOf("swiper-slide-visible")>=0,isPrev:p.indexOf("swiper-slide-prev")>=0,isNext:p.indexOf("swiper-slide-next")>=0},y=()=>"function"==typeof r?r(b):r;return a.createElement(n,Ja({ref:f,className:Za(`${p}${i?` ${i}`:""}`),"data-swiper-slide-index":c,onLoad:()=>{g(!0)}},u),o&&a.createElement(ar.Provider,{value:b},a.createElement("div",{className:"swiper-zoom-container","data-swiper-zoom":"number"==typeof o?o:void 0},y(),l&&!v&&a.createElement("div",{className:"swiper-lazy-preloader"}))),!o&&a.createElement(ar.Provider,{value:b},y(),l&&!v&&a.createElement("div",{className:"swiper-lazy-preloader"})))}));function or(e){return void 0===e&&(e=""),`.${e.trim().replace(/([\.:!+\/])/g,"\\$1").replace(/ /g,".")}`}function lr(e){let{swiper:t,extendParams:n,on:a,emit:r}=e;const i="swiper-pagination";let s;n({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:e=>e,formatFractionTotal:e=>e,bulletClass:`${i}-bullet`,bulletActiveClass:`${i}-bullet-active`,modifierClass:`${i}-`,currentClass:`${i}-current`,totalClass:`${i}-total`,hiddenClass:`${i}-hidden`,progressbarFillClass:`${i}-progressbar-fill`,progressbarOppositeClass:`${i}-progressbar-opposite`,clickableClass:`${i}-clickable`,lockClass:`${i}-lock`,horizontalClass:`${i}-horizontal`,verticalClass:`${i}-vertical`,paginationDisabledClass:`${i}-disabled`}}),t.pagination={el:null,bullets:[]};let o=0;function l(){return!t.params.pagination.el||!t.pagination.el||Array.isArray(t.pagination.el)&&0===t.pagination.el.length}function c(e,n){const{bulletActiveClass:a}=t.params.pagination;e&&(e=e[("prev"===n?"previous":"next")+"ElementSibling"])&&(e.classList.add(`${a}-${n}`),(e=e[("prev"===n?"previous":"next")+"ElementSibling"])&&e.classList.add(`${a}-${n}-${n}`))}function d(e){const n=e.target.closest(or(t.params.pagination.bulletClass));if(!n)return;e.preventDefault();const a=ca(n)*t.params.slidesPerGroup;if(t.params.loop){if(t.realIndex===a)return;t.slideToLoop(a)}else t.slideTo(a)}function u(){const e=t.rtl,n=t.params.pagination;if(l())return;let a,i,d=t.pagination.el;d=fa(d);const u=t.virtual&&t.params.virtual.enabled?t.virtual.slides.length:t.slides.length,f=t.params.loop?Math.ceil(u/t.params.slidesPerGroup):t.snapGrid.length;if(t.params.loop?(i=t.previousRealIndex||0,a=t.params.slidesPerGroup>1?Math.floor(t.realIndex/t.params.slidesPerGroup):t.realIndex):void 0!==t.snapIndex?(a=t.snapIndex,i=t.previousSnapIndex):(i=t.previousIndex||0,a=t.activeIndex||0),"bullets"===n.type&&t.pagination.bullets&&t.pagination.bullets.length>0){const r=t.pagination.bullets;let l,u,f;if(n.dynamicBullets&&(s=ua(r[0],t.isHorizontal()?"width":"height",!0),d.forEach((e=>{e.style[t.isHorizontal()?"width":"height"]=s*(n.dynamicMainBullets+4)+"px"})),n.dynamicMainBullets>1&&void 0!==i&&(o+=a-(i||0),o>n.dynamicMainBullets-1?o=n.dynamicMainBullets-1:o<0&&(o=0)),l=Math.max(a-o,0),u=l+(Math.min(r.length,n.dynamicMainBullets)-1),f=(u+l)/2),r.forEach((e=>{const t=[...["","-next","-next-next","-prev","-prev-prev","-main"].map((e=>`${n.bulletActiveClass}${e}`))].map((e=>"string"==typeof e&&e.includes(" ")?e.split(" "):e)).flat();e.classList.remove(...t)})),d.length>1)r.forEach((e=>{const r=ca(e);r===a?e.classList.add(...n.bulletActiveClass.split(" ")):t.isElement&&e.setAttribute("part","bullet"),n.dynamicBullets&&(r>=l&&r<=u&&e.classList.add(...`${n.bulletActiveClass}-main`.split(" ")),r===l&&c(e,"prev"),r===u&&c(e,"next"))}));else{const e=r[a];if(e&&e.classList.add(...n.bulletActiveClass.split(" ")),t.isElement&&r.forEach(((e,t)=>{e.setAttribute("part",t===a?"bullet-active":"bullet")})),n.dynamicBullets){const e=r[l],t=r[u];for(let e=l;e<=u;e+=1)r[e]&&r[e].classList.add(...`${n.bulletActiveClass}-main`.split(" "));c(e,"prev"),c(t,"next")}}if(n.dynamicBullets){const a=Math.min(r.length,n.dynamicMainBullets+4),i=(s*a-s)/2-f*s,o=e?"right":"left";r.forEach((e=>{e.style[t.isHorizontal()?o:"top"]=`${i}px`}))}}d.forEach(((e,i)=>{if("fraction"===n.type&&(e.querySelectorAll(or(n.currentClass)).forEach((e=>{e.textContent=n.formatFractionCurrent(a+1)})),e.querySelectorAll(or(n.totalClass)).forEach((e=>{e.textContent=n.formatFractionTotal(f)}))),"progressbar"===n.type){let r;r=n.progressbarOpposite?t.isHorizontal()?"vertical":"horizontal":t.isHorizontal()?"horizontal":"vertical";const i=(a+1)/f;let s=1,o=1;"horizontal"===r?s=i:o=i,e.querySelectorAll(or(n.progressbarFillClass)).forEach((e=>{e.style.transform=`translate3d(0,0,0) scaleX(${s}) scaleY(${o})`,e.style.transitionDuration=`${t.params.speed}ms`}))}"custom"===n.type&&n.renderCustom?(e.innerHTML=n.renderCustom(t,a+1,f),0===i&&r("paginationRender",e)):(0===i&&r("paginationRender",e),r("paginationUpdate",e)),t.params.watchOverflow&&t.enabled&&e.classList[t.isLocked?"add":"remove"](n.lockClass)}))}function f(){const e=t.params.pagination;if(l())return;const n=t.virtual&&t.params.virtual.enabled?t.virtual.slides.length:t.grid&&t.params.grid.rows>1?t.slides.length/Math.ceil(t.params.grid.rows):t.slides.length;let a=t.pagination.el;a=fa(a);let i="";if("bullets"===e.type){let a=t.params.loop?Math.ceil(n/t.params.slidesPerGroup):t.snapGrid.length;t.params.freeMode&&t.params.freeMode.enabled&&a>n&&(a=n);for(let n=0;n<a;n+=1)e.renderBullet?i+=e.renderBullet.call(t,n,e.bulletClass):i+=`<${e.bulletElement} ${t.isElement?'part="bullet"':""} class="${e.bulletClass}"></${e.bulletElement}>`}"fraction"===e.type&&(i=e.renderFraction?e.renderFraction.call(t,e.currentClass,e.totalClass):`<span class="${e.currentClass}"></span> / <span class="${e.totalClass}"></span>`),"progressbar"===e.type&&(i=e.renderProgressbar?e.renderProgressbar.call(t,e.progressbarFillClass):`<span class="${e.progressbarFillClass}"></span>`),t.pagination.bullets=[],a.forEach((n=>{"custom"!==e.type&&(n.innerHTML=i||""),"bullets"===e.type&&t.pagination.bullets.push(...n.querySelectorAll(or(e.bulletClass)))})),"custom"!==e.type&&r("paginationRender",a[0])}function p(){t.params.pagination=function(e,t,n,a){return e.params.createElements&&Object.keys(a).forEach((r=>{if(!n[r]&&!0===n.auto){let i=ia(e.el,`.${a[r]}`)[0];i||(i=oa("div",a[r]),i.className=a[r],e.el.append(i)),n[r]=i,t[r]=i}})),n}(t,t.originalParams.pagination,t.params.pagination,{el:"swiper-pagination"});const e=t.params.pagination;if(!e.el)return;let n;"string"==typeof e.el&&t.isElement&&(n=t.el.querySelector(e.el)),n||"string"!=typeof e.el||(n=[...document.querySelectorAll(e.el)]),n||(n=e.el),n&&0!==n.length&&(t.params.uniqueNavElements&&"string"==typeof e.el&&Array.isArray(n)&&n.length>1&&(n=[...t.el.querySelectorAll(e.el)],n.length>1&&(n=n.filter((e=>da(e,".swiper")[0]===t.el))[0])),Array.isArray(n)&&1===n.length&&(n=n[0]),Object.assign(t.pagination,{el:n}),n=fa(n),n.forEach((n=>{"bullets"===e.type&&e.clickable&&n.classList.add(...(e.clickableClass||"").split(" ")),n.classList.add(e.modifierClass+e.type),n.classList.add(t.isHorizontal()?e.horizontalClass:e.verticalClass),"bullets"===e.type&&e.dynamicBullets&&(n.classList.add(`${e.modifierClass}${e.type}-dynamic`),o=0,e.dynamicMainBullets<1&&(e.dynamicMainBullets=1)),"progressbar"===e.type&&e.progressbarOpposite&&n.classList.add(e.progressbarOppositeClass),e.clickable&&n.addEventListener("click",d),t.enabled||n.classList.add(e.lockClass)})))}function m(){const e=t.params.pagination;if(l())return;let n=t.pagination.el;n&&(n=fa(n),n.forEach((n=>{n.classList.remove(e.hiddenClass),n.classList.remove(e.modifierClass+e.type),n.classList.remove(t.isHorizontal()?e.horizontalClass:e.verticalClass),e.clickable&&(n.classList.remove(...(e.clickableClass||"").split(" ")),n.removeEventListener("click",d))}))),t.pagination.bullets&&t.pagination.bullets.forEach((t=>t.classList.remove(...e.bulletActiveClass.split(" "))))}a("changeDirection",(()=>{if(!t.pagination||!t.pagination.el)return;const e=t.params.pagination;let{el:n}=t.pagination;n=fa(n),n.forEach((n=>{n.classList.remove(e.horizontalClass,e.verticalClass),n.classList.add(t.isHorizontal()?e.horizontalClass:e.verticalClass)}))})),a("init",(()=>{!1===t.params.pagination.enabled?v():(p(),f(),u())})),a("activeIndexChange",(()=>{void 0===t.snapIndex&&u()})),a("snapIndexChange",(()=>{u()})),a("snapGridLengthChange",(()=>{f(),u()})),a("destroy",(()=>{m()})),a("enable disable",(()=>{let{el:e}=t.pagination;e&&(e=fa(e),e.forEach((e=>e.classList[t.enabled?"remove":"add"](t.params.pagination.lockClass))))})),a("lock unlock",(()=>{u()})),a("click",((e,n)=>{const a=n.target,i=fa(t.pagination.el);if(t.params.pagination.el&&t.params.pagination.hideOnClick&&i&&i.length>0&&!a.classList.contains(t.params.pagination.bulletClass)){if(t.navigation&&(t.navigation.nextEl&&a===t.navigation.nextEl||t.navigation.prevEl&&a===t.navigation.prevEl))return;const e=i[0].classList.contains(t.params.pagination.hiddenClass);r(!0===e?"paginationShow":"paginationHide"),i.forEach((e=>e.classList.toggle(t.params.pagination.hiddenClass)))}}));const v=()=>{t.el.classList.add(t.params.pagination.paginationDisabledClass);let{el:e}=t.pagination;e&&(e=fa(e),e.forEach((e=>e.classList.add(t.params.pagination.paginationDisabledClass)))),m()};Object.assign(t.pagination,{enable:()=>{t.el.classList.remove(t.params.pagination.paginationDisabledClass);let{el:e}=t.pagination;e&&(e=fa(e),e.forEach((e=>e.classList.remove(t.params.pagination.paginationDisabledClass)))),p(),f(),u()},disable:v,render:f,update:u,init:p,destroy:m})}sr.displayName="SwiperSlide";const cr=window.wp.blockEditor,dr=({slideIndex:e,slide:t,slides:n,setAttributes:r})=>(0,a.createElement)(a.Fragment,null,(0,a.createElement)("div",{className:"wp-block-masterstudy-testimonials__item"},(0,a.createElement)("div",{className:"wp-block-masterstudy-testimonials__rating"},(0,a.createElement)(Gn,{icon:Yn}),(0,a.createElement)(Gn,{icon:Yn}),(0,a.createElement)(Gn,{icon:Yn}),(0,a.createElement)(Gn,{icon:Yn}),(0,a.createElement)(Gn,{icon:Yn})),(0,a.createElement)(cr.RichText,{value:t.content,identifier:`testimonial-content-${t.id}`,tagName:"p",withoutInteractiveFormatting:!0,className:"wp-block-masterstudy-testimonials__review",onChange:a=>(a=>{const i=[...n];i[e]={...t,content:a},r({slides:i})})(a),placeholder:s.__("Add testimonial text…","masterstudy-lms-learning-management-system")}),(0,a.createElement)(cr.RichText,{value:t.reviewer,identifier:`testimonial-reviewer-${t.id}`,tagName:"p",withoutInteractiveFormatting:!0,className:"wp-block-masterstudy-testimonials__reviewer",onChange:a=>(a=>{const i=[...n];i[e]={...t,reviewer:a},r({slides:i})})(a),placeholder:s.__("Add reviewer…","masterstudy-lms-learning-management-system")}))),ur=JSON.parse('{"UU":"masterstudy/testimonials"}');(0,i.registerBlockType)(ur.UU,{icon:{src:(0,a.createElement)("svg",{width:"512",height:"513",viewBox:"0 0 512 513",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,a.createElement)("g",{clipPath:"url(#clip0_710_5)"},(0,a.createElement)("path",{opacity:"0.3",d:"M11.7053 29.1697C11.7053 18.124 20.6596 9.16968 31.7053 9.16968H474.156C485.202 9.16968 494.156 18.124 494.156 29.1697V180.746C494.156 191.792 485.202 200.746 474.156 200.746H442.554C436.682 200.746 431.106 203.327 427.306 207.804L407.715 230.887C397.468 242.959 377.922 238.902 373.327 223.749L370.656 214.942C368.099 206.511 360.327 200.746 351.517 200.746H31.7053C20.6596 200.746 11.7053 191.792 11.7053 180.746V29.1697Z",fill:"#227AFF"}),(0,a.createElement)("path",{d:"M156.907 79.2356L128.427 75.7156L116.267 49.6886C115.404 47.8726 114.044 46.338 112.345 45.2621C110.646 44.1862 108.678 43.6129 106.667 43.6086C104.631 43.6247 102.641 44.2135 100.925 45.3077C99.208 46.4018 97.8341 47.9571 96.96 49.7956L84.907 75.8216L56.427 79.3416C54.4113 79.5798 52.5059 80.3903 50.9364 81.6774C49.3669 82.9644 48.1988 84.6741 47.5705 86.6041C46.9422 88.5342 46.8798 90.6038 47.3908 92.5682C47.9018 94.5325 48.9648 96.3094 50.454 97.6886L71.467 117.209L66.027 145.369C65.6562 147.358 65.8493 149.411 66.5844 151.296C67.3196 153.181 68.5675 154.823 70.187 156.036C71.8441 157.235 73.8108 157.932 75.8531 158.046C77.8954 158.159 79.9272 157.684 81.707 156.676L106.667 142.809L131.733 156.782C133.511 157.755 135.529 158.202 137.552 158.069C139.574 157.936 141.517 157.229 143.151 156.031C144.786 154.833 146.045 153.193 146.781 151.305C147.517 149.416 147.699 147.357 147.306 145.369L141.866 117.209L162.879 97.6886C164.363 96.2982 165.422 94.514 165.93 92.5445C166.438 90.5751 166.375 88.5016 165.749 86.5665C165.123 84.6314 163.958 82.9144 162.393 81.6163C160.827 80.3182 158.925 79.4925 156.907 79.2356ZM122.987 105.582C121.662 106.809 120.672 108.355 120.112 110.072C119.552 111.79 119.44 113.622 119.787 115.395L122.027 126.915L111.787 121.262C110.183 120.381 108.389 119.905 106.56 119.875C104.726 119.867 102.922 120.346 101.333 121.262L91.093 126.915L93.333 115.395C93.6881 113.622 93.5801 111.787 93.0196 110.069C92.4591 108.35 91.4649 106.804 90.133 105.582L81.6 97.5816L93.227 96.1946C95.0273 95.9644 96.7413 95.2875 98.2132 94.2256C99.685 93.1637 100.868 91.7505 101.654 90.1146L106.667 79.5546L111.574 90.1146C112.348 91.7597 113.527 93.1809 115.001 94.2446C116.475 95.3082 118.196 95.9793 120.001 96.1946L131.628 97.5816L122.987 105.582ZM301.867 79.2346L273.387 75.7146L261.333 49.6886C260.45 47.8582 259.067 46.3154 257.343 45.2394C255.619 44.1635 253.625 43.5983 251.593 43.6095C249.56 43.6207 247.573 44.2078 245.861 45.3027C244.149 46.3977 242.783 47.9555 241.92 49.7956L229.867 75.8216L201.387 79.3416C199.371 79.5798 197.466 80.3903 195.896 81.6774C194.327 82.9644 193.159 84.6741 192.531 86.6041C191.902 88.5342 191.84 90.6038 192.351 92.5682C192.862 94.5325 193.925 96.3094 195.414 97.6886L216.427 117.209L210.987 145.369C210.616 147.358 210.809 149.411 211.544 151.296C212.28 153.181 213.527 154.823 215.147 156.036C216.804 157.235 218.771 157.932 220.813 158.046C222.855 158.159 224.887 157.684 226.667 156.676L251.733 142.702L276.8 156.675C278.578 157.648 280.596 158.095 282.619 157.962C284.641 157.829 286.584 157.122 288.218 155.924C289.853 154.726 291.112 153.086 291.848 151.198C292.584 149.309 292.766 147.25 292.373 145.262L286.933 117.102L307.946 97.5816C309.442 96.1974 310.508 94.4117 311.017 92.4378C311.526 90.4639 311.455 88.3853 310.814 86.4504C310.173 84.5155 308.988 82.8061 307.401 81.5266C305.814 80.247 303.893 79.4513 301.866 79.2346H301.867ZM267.947 105.582C266.622 106.809 265.632 108.355 265.072 110.072C264.512 111.79 264.4 113.622 264.747 115.395L266.987 126.915L256.747 121.262C255.143 120.381 253.349 119.905 251.52 119.875C249.686 119.867 247.882 120.346 246.293 121.262L236.053 126.915L238.293 115.395C238.648 113.622 238.54 111.787 237.98 110.069C237.419 108.35 236.425 106.804 235.093 105.582L226.56 97.5816L238.187 96.1946C239.987 95.9644 241.701 95.2875 243.173 94.2256C244.645 93.1637 245.828 91.7505 246.614 90.1146L251.521 79.5546L256.428 90.1146C257.202 91.7597 258.381 93.1809 259.855 94.2446C261.329 95.3082 263.05 95.9793 264.855 96.1946L276.482 97.5816L267.947 105.582ZM446.827 79.2346L418.347 75.7146L406.294 49.6886C405.411 47.8582 404.028 46.3154 402.304 45.2394C400.58 44.1635 398.586 43.5983 396.554 43.6095C394.521 43.6207 392.534 44.2078 390.822 45.3027C389.11 46.3977 387.744 47.9555 386.881 49.7956L374.827 75.8216L346.347 79.3416C344.331 79.5798 342.426 80.3903 340.856 81.6774C339.287 82.9644 338.119 84.6741 337.491 86.6041C336.862 88.5342 336.8 90.6038 337.311 92.5682C337.822 94.5325 338.885 96.3094 340.374 97.6886L361.387 117.209L355.947 145.369C355.554 147.357 355.736 149.416 356.472 151.305C357.208 153.193 358.467 154.833 360.102 156.031C361.736 157.229 363.679 157.936 365.701 158.069C367.724 158.202 369.742 157.755 371.52 156.782L396.587 142.809L421.654 156.782C423.258 157.662 425.052 158.138 426.881 158.169C428.456 158.178 430.012 157.836 431.438 157.168C432.864 156.5 434.124 155.522 435.125 154.307C436.125 153.091 436.843 151.668 437.225 150.14C437.607 148.613 437.644 147.019 437.333 145.476L431.893 117.316L452.906 97.7956C454.38 96.4044 455.433 94.6257 455.942 92.6635C456.452 90.7014 456.397 88.6354 455.786 86.7026C455.147 84.7411 453.967 82.9997 452.382 81.6788C450.797 80.3579 448.872 79.5101 446.827 79.2346ZM412.907 105.582C411.582 106.809 410.592 108.355 410.032 110.072C409.472 111.79 409.36 113.622 409.707 115.395L411.947 126.915L401.707 121.262C400.103 120.381 398.309 119.905 396.48 119.875C394.646 119.867 392.842 120.346 391.253 121.262L381.013 126.915L383.253 115.395C383.608 113.622 383.5 111.787 382.94 110.069C382.379 108.35 381.385 106.804 380.053 105.582L371.52 97.5816L383.147 96.1946C384.947 95.9644 386.661 95.2875 388.133 94.2256C389.605 93.1637 390.788 91.7505 391.574 90.1146L396.481 79.5546L401.388 90.1146C402.162 91.7597 403.341 93.1809 404.815 94.2446C406.289 95.3082 408.01 95.9793 409.815 96.1946L421.442 97.5816L412.907 105.582Z",fill:"black"}),(0,a.createElement)("path",{d:"M480 0.94165H32C23.5207 0.966449 15.3958 4.34583 9.39998 10.3416C3.40418 16.3374 0.0247988 24.4623 0 32.9417V182.275C0.0247988 190.754 3.40418 198.879 9.39998 204.875C15.3958 210.87 23.5207 214.25 32 214.275H355.413L374.08 260.888C374.764 262.623 375.893 264.148 377.354 265.309C378.814 266.47 380.555 267.226 382.4 267.501C382.928 267.589 383.464 267.625 384 267.608C385.602 267.6 387.182 267.232 388.622 266.53C390.063 265.829 391.327 264.812 392.32 263.555L431.787 214.275H480C488.479 214.25 496.604 210.87 502.6 204.875C508.596 198.879 511.975 190.754 512 182.275V32.9417C511.975 24.4623 508.596 16.3374 502.6 10.3416C496.604 4.34583 488.479 0.966449 480 0.94165V0.94165ZM490.667 182.275C490.658 185.101 489.532 187.809 487.533 189.808C485.535 191.806 482.826 192.933 480 192.942H426.667C426.454 192.942 426.347 193.049 426.134 193.049C425.406 193.088 424.688 193.231 424.001 193.476C423.361 193.689 422.721 193.796 422.188 194.009C421.631 194.327 421.096 194.683 420.588 195.076C419.973 195.479 419.401 195.943 418.881 196.463C418.774 196.57 418.561 196.676 418.454 196.783L387.2 235.822L372.693 199.449C372.694 199.434 372.692 199.42 372.687 199.406C372.682 199.393 372.674 199.381 372.664 199.371C372.654 199.361 372.642 199.353 372.628 199.348C372.615 199.343 372.6 199.341 372.586 199.342C372.179 198.448 371.64 197.621 370.986 196.889C370.773 196.509 370.523 196.151 370.239 195.822C369.783 195.441 369.282 195.118 368.746 194.862C368.149 194.374 367.506 193.946 366.826 193.582C366.399 193.369 365.973 193.475 365.546 193.262C364.693 193.262 363.733 192.942 362.773 192.942H32C29.1736 192.933 26.4655 191.806 24.4669 189.808C22.4683 187.809 21.3417 185.101 21.333 182.275V32.9417C21.3417 30.1153 22.4683 27.4071 24.4669 25.4086C26.4655 23.41 29.1736 22.2834 32 22.2747H480C482.826 22.2834 485.535 23.41 487.533 25.4086C489.532 27.4071 490.658 30.1153 490.667 32.9417V182.275ZM309.334 342.275C309.334 331.726 306.206 321.415 300.345 312.645C294.485 303.874 286.155 297.039 276.41 293.002C266.665 288.966 255.942 287.91 245.596 289.967C235.251 292.025 225.748 297.105 218.289 304.564C210.83 312.022 205.751 321.525 203.693 331.871C201.635 342.216 202.691 352.94 206.728 362.685C210.764 372.43 217.6 380.76 226.37 386.62C235.141 392.48 245.452 395.608 256 395.609C270.143 395.603 283.706 389.983 293.707 379.982C303.707 369.981 309.328 356.418 309.333 342.275H309.334ZM224.001 342.275C224.001 335.946 225.878 329.759 229.394 324.496C232.91 319.234 237.908 315.133 243.755 312.711C249.602 310.289 256.037 309.655 262.244 310.89C268.451 312.124 274.153 315.172 278.628 319.647C283.104 324.123 286.151 329.824 287.386 336.032C288.621 342.239 287.987 348.673 285.565 354.521C283.143 360.368 279.042 365.365 273.779 368.882C268.517 372.398 262.33 374.275 256.001 374.275C247.522 374.25 239.396 370.871 233.4 364.875C227.404 358.879 224.025 350.754 224 342.275H224.001ZM149.334 342.275C149.334 331.726 146.206 321.415 140.345 312.645C134.485 303.874 126.155 297.039 116.41 293.002C106.665 288.966 95.9415 287.91 85.596 289.967C75.2505 292.025 65.7476 297.105 58.2889 304.564C50.8302 312.022 45.7507 321.525 43.6928 331.871C41.6349 342.216 42.691 352.94 46.7275 362.685C50.7641 372.43 57.5997 380.76 66.3701 386.62C75.1406 392.48 85.4518 395.608 96 395.609C110.143 395.603 123.706 389.983 133.707 379.982C143.707 369.981 149.328 356.418 149.333 342.275H149.334ZM64.001 342.275C64.001 335.946 65.8778 329.759 69.394 324.496C72.9102 319.234 77.9079 315.133 83.7551 312.711C89.6024 310.289 96.0365 309.655 102.244 310.89C108.451 312.124 114.153 315.172 118.628 319.647C123.104 324.123 126.151 329.824 127.386 336.032C128.621 342.239 127.987 348.673 125.565 354.521C123.143 360.368 119.042 365.365 113.779 368.882C108.517 372.398 102.33 374.275 96.001 374.275C87.5215 374.25 79.3963 370.871 73.4003 364.875C67.4043 358.879 64.0248 350.754 64 342.275H64.001ZM469.334 342.275C469.334 331.726 466.206 321.415 460.345 312.645C454.485 303.874 446.155 297.039 436.41 293.002C426.665 288.966 415.942 287.91 405.596 289.967C395.251 292.025 385.748 297.105 378.289 304.564C370.83 312.022 365.751 321.525 363.693 331.871C361.635 342.216 362.691 352.94 366.728 362.685C370.764 372.43 377.6 380.76 386.37 386.62C395.141 392.48 405.452 395.608 416 395.609C430.143 395.603 443.706 389.983 453.707 379.982C463.707 369.981 469.328 356.418 469.333 342.275H469.334ZM384.001 342.275C384.001 335.946 385.878 329.759 389.394 324.496C392.91 319.234 397.908 315.133 403.755 312.711C409.602 310.289 416.037 309.655 422.244 310.89C428.451 312.124 434.153 315.172 438.628 319.647C443.104 324.123 446.151 329.824 447.386 336.032C448.621 342.239 447.987 348.673 445.565 354.521C443.143 360.368 439.042 365.365 433.779 368.882C428.517 372.398 422.33 374.275 416.001 374.275C407.522 374.25 399.396 370.871 393.4 364.875C387.404 358.879 384.025 350.754 384 342.275H384.001ZM448.001 406.275H384C374.891 406.246 365.882 408.173 357.582 411.927C349.282 415.682 341.886 421.175 335.893 428.035C329.925 421.2 322.563 415.723 314.302 411.969C306.042 408.216 297.073 406.274 288 406.275H224C214.927 406.274 205.958 408.216 197.698 411.969C189.437 415.723 182.075 421.2 176.107 428.035C170.114 421.175 162.718 415.682 154.418 411.927C146.118 408.173 137.109 406.246 128 406.275H64C47.0303 406.288 30.7595 413.035 18.7601 425.035C6.76068 437.034 0.0135066 453.305 0 470.275V502.275C0.00870354 505.101 1.13534 507.809 3.13391 509.808C5.13247 511.806 7.84061 512.933 10.667 512.942H501.333C504.159 512.933 506.868 511.806 508.866 509.808C510.865 507.809 511.991 505.101 512 502.275V470.275C511.986 453.305 505.239 437.034 493.24 425.035C481.241 413.035 464.97 406.288 448 406.275H448.001ZM21.334 470.275C21.3326 464.671 22.4352 459.123 24.5788 453.945C26.7225 448.768 29.8652 444.064 33.8274 440.102C37.7896 436.14 42.4936 432.997 47.6707 430.853C52.8478 428.71 58.3966 427.607 64 427.609H128C135.247 427.601 142.376 429.449 148.706 432.978C155.036 436.508 160.357 441.6 164.16 447.769C161.419 454.956 160.01 462.582 160 470.275V491.608H21.333L21.334 470.275ZM181.334 491.608V470.275C181.333 464.671 182.435 459.123 184.579 453.945C186.722 448.768 189.865 444.064 193.827 440.102C197.79 436.14 202.494 432.997 207.671 430.853C212.848 428.71 218.397 427.607 224 427.609H288C293.604 427.607 299.153 428.71 304.33 430.853C309.507 432.997 314.211 436.14 318.174 440.102C322.136 444.064 325.279 448.769 327.422 453.946C329.566 459.123 330.669 464.672 330.667 470.276V491.609L181.334 491.608ZM490.667 491.608H352V470.275C351.991 462.582 350.581 454.955 347.84 447.768C351.644 441.599 356.964 436.507 363.294 432.978C369.624 429.449 376.753 427.601 384 427.609H448C453.604 427.607 459.153 428.71 464.33 430.853C469.507 432.997 474.211 436.14 478.174 440.102C482.136 444.064 485.279 448.769 487.422 453.946C489.566 459.123 490.669 464.672 490.667 470.276V491.608Z",fill:"black"})),(0,a.createElement)("defs",null,(0,a.createElement)("clipPath",{id:"clip0_710_5"},(0,a.createElement)("rect",{width:"512",height:"512",fill:"white",transform:"translate(0 0.94165)"}))))},edit:function({attributes:e,setAttributes:t,clientId:n}){const{title:r,slides:i}=e,[o,l]=(0,f.useState)("new"),[c,m]=(0,f.useState)(0),[v,g]=(0,f.useState)(""),[h,b]=(0,f.useState)(null),[y,w]=(0,f.useState)(0);((e,t,n)=>{(0,f.useEffect)((()=>{(!e||e&&e!==n)&&t({clientId:n})}),[e,t,n])})(e.clientId,t,n),(0,f.useEffect)((()=>{0===i.length?t({slides:[...e.slides,{id:d(),content:"",rating:"",imgUrl:""}]}):g(i[c].imgUrl)}),[e.slides,t,c,i]);const{blockClassName:C,blockStyleVariables:x}=u("testimonials",{titleColor:e.titleColor,ratingColor:e.ratingColor,textColor:e.textColor,reviewerColor:e.reviewerColor,bgColor:e.bgColor,iconBgColor:e.iconBgColor,iconColor:e.iconColor,avatarIconColor:e.avatarIconColor,avatarBgColor:e.avatarBgColor,activeAvatarBgColor:e.activeAvatarBgColor,avatarBorderColor:e.avatarBorderColor}),S=(0,cr.useBlockProps)({className:`${C} ${C}-${e.clientId}`}),E={clickable:!0,renderBullet(e,t){const n=i[e]?i[e].imgUrl:"";return(0,f.renderToString)((0,a.createElement)(a.Fragment,null,(0,a.createElement)("span",{className:t,style:{display:"flex"}},n&&(0,a.createElement)("img",{src:n,alt:`reviewer-avatar-${e}`}),!n&&(0,a.createElement)(Gn,{icon:Vn})),1===i.length&&(0,a.createElement)("span",{className:t,style:{display:"none"}})))}},k=()=>{c>=0&&(i[c].imgUrl="",t({slides:i}),g(""),w((e=>e+1)))};return(0,a.createElement)(a.Fragment,null,(0,a.createElement)(cr.InspectorControls,null,(0,a.createElement)(p.Panel,null,(0,a.createElement)(p.PanelBody,{title:s.__("Testimonial slide","masterstudy-lms-learning-management-system")},(0,a.createElement)(p.__experimentalToggleGroupControl,{isBlock:!0,size:"default",value:o,onChange:e=>l(e)},(0,a.createElement)(p.__experimentalToggleGroupControlOption,{label:s.__("New Slide","masterstudy-lms-learning-management-system"),value:"new"}),(0,a.createElement)(p.__experimentalToggleGroupControlOption,{label:s.__("Current Slide","masterstudy-lms-learning-management-system"),value:"current"})),"new"===o&&(0,a.createElement)(p.ToolbarButton,{icon:"plus",onClick:()=>{const n=[...e.slides,{id:d(),content:"",rating:""}];t({slides:n}),m(n.length-1),setTimeout((()=>{h.slideTo(n.length)}),100)},className:`${C}__slide--add-btn is-primary`},(0,a.createElement)("span",null,s.__("Add Slide","masterstudy-lms-learning-management-system"))),"current"===o&&(0,a.createElement)(a.Fragment,null,(0,a.createElement)("div",{className:`${C}__slide-image`},v&&(0,a.createElement)("img",{src:v,alt:`reviewer-avatar-${c}`}),!v&&(0,a.createElement)(Gn,{icon:Vn})),(0,a.createElement)(cr.MediaUploadCheck,null,(0,a.createElement)(cr.MediaUpload,{onSelect:e=>{if(c>=0){let n=e.url?e.url:"";e.sizes.full.url&&(n=e.sizes.full.url),i[c].imgUrl=n,t({slides:i}),g(n),w((e=>e+1))}},allowedTypes:["image"],render:({open:e})=>(0,a.createElement)(p.PanelRow,{className:`${C}__panel-row`},(0,a.createElement)(p.ToolbarButton,{onClick:e,className:`${C}__slide--add-btn is-primary`},!v&&(0,a.createElement)("span",null,s.__("Add image","masterstudy-lms-learning-management-system")),v&&(0,a.createElement)("span",null,s.__("Change image","masterstudy-lms-learning-management-system"))),v&&(0,a.createElement)(p.ToolbarButton,{onClick:k,className:`${C}__slide--add-btn is-destructive is-primary`},(0,a.createElement)("span",null,s.__("Remove image","masterstudy-lms-learning-management-system"))))})),(0,a.createElement)(p.ToolbarButton,{onClick:()=>{const n=i.filter(((e,t)=>t!==c));t({slides:n}),n.length<0?t({slides:[...e.slides,{id:d(),content:"",rating:"",imgUrl:""}]}):m(n.length-1)},className:`${C}__slide--add-btn is-destructive is-secondary`},(0,a.createElement)("span",null,s.__("Remove Slide","masterstudy-lms-learning-management-system")))))),(0,a.createElement)(p.Panel,null,(0,a.createElement)("div",{style:{borderBottom:"1px solid #e0e0e0"}},(0,a.createElement)(cr.PanelColorSettings,{enableAlpha:!0,disableCustomColors:!1,__experimentalHasMultipleOrigins:!0,__experimentalIsRenderedInSidebar:!0,title:s.__("Colors","masterstudy-lms-learning-management-system"),colorSettings:[{value:e.iconColor,onChange:e=>t({iconColor:e}),label:s.__("Icon color","masterstudy-lms-learning-management-system")},{value:e.iconBgColor,onChange:e=>t({iconBgColor:e}),label:s.__("Icon background color","masterstudy-lms-learning-management-system")},{value:e.titleColor,onChange:e=>t({titleColor:e}),label:s.__("Title color","masterstudy-lms-learning-management-system")},{value:e.ratingColor,onChange:e=>t({ratingColor:e}),label:s.__("Rating color","masterstudy-lms-learning-management-system")},{value:e.textColor,onChange:e=>t({textColor:e}),label:s.__("Text color","masterstudy-lms-learning-management-system")},{value:e.reviewerColor,onChange:e=>t({reviewerColor:e}),label:s.__("Reviewer color","masterstudy-lms-learning-management-system")},{value:e.bgColor,onChange:e=>t({bgColor:e}),label:s.__("Background color","masterstudy-lms-learning-management-system")},{value:e.avatarIconColor,onChange:e=>t({avatarIconColor:e}),label:s.__("Avatar icon color","masterstudy-lms-learning-management-system")},{value:e.avatarBgColor,onChange:e=>t({avatarBgColor:e}),label:s.__("Avatar icon background color","masterstudy-lms-learning-management-system")},{value:e.activeAvatarBgColor,onChange:e=>t({activeAvatarBgColor:e}),label:s.__("Active avatar background color","masterstudy-lms-learning-management-system")},{value:e.avatarBorderColor,onChange:e=>t({avatarBorderColor:e}),label:s.__("Avatar border color","masterstudy-lms-learning-management-system")}]})))),(0,a.createElement)("div",{...S},(0,a.createElement)("div",{className:`${C}__icon`},(0,a.createElement)(Gn,{icon:Wn})),(0,a.createElement)(cr.RichText,{value:r,identifier:"testimonial-title",tagName:"p",withoutInteractiveFormatting:!0,className:`${C}__title`,placeholder:s.__("Add title…","masterstudy-lms-learning-management-system"),onChange:e=>t({title:e})}),(0,a.createElement)(ir,{key:y,pagination:E,slidesPerView:1,grabCursor:!1,keyboard:!1,className:`${C}__swiper`,initialSlide:c,modules:[lr],onSlideChange:e=>m(e.activeIndex),onSwiper:e=>b(e),slidesPerColumn:1,mousewheel:!1,mousewheelControl:!1,simulateTouch:!1},i.map(((e,n)=>(0,a.createElement)(sr,{key:n},(0,a.createElement)(dr,{slideIndex:n,slide:e,slides:i,setAttributes:t})))))),(0,a.createElement)("style",null,`.${C}-${e.clientId} { ${x} }`))},save:function({attributes:e}){const{title:t,slides:n}=e,{blockClassName:r,blockStyleObject:i}=u("testimonials",{titleColor:e.titleColor,ratingColor:e.ratingColor,textColor:e.textColor,reviewerColor:e.reviewerColor,bgColor:e.bgColor,iconBgColor:e.iconBgColor,iconColor:e.iconColor,avatarIconColor:e.avatarIconColor,avatarBgColor:e.avatarBgColor,activeAvatarBgColor:e.activeAvatarBgColor,avatarBorderColor:e.avatarBorderColor}),s=cr.useBlockProps.save({className:`${r} ${r}-${e.clientId}`,style:i});return(0,a.createElement)("div",{...s},(0,a.createElement)("div",{className:`${r}__icon`},(0,a.createElement)(Gn,{icon:Wn})),(0,a.createElement)(cr.RichText.Content,{value:t,tagName:"p",className:`${r}__title`}),(0,a.createElement)("div",{className:`swiper ${r}__swiper`},(0,a.createElement)("div",{className:"swiper-wrapper"},n.map(((e,t)=>(0,a.createElement)("div",{key:t,className:"swiper-slide "+(0===t?"swiper-slide-active":"")},(0,a.createElement)("div",{className:"wp-block-masterstudy-testimonials__item"},(0,a.createElement)("div",{className:"wp-block-masterstudy-testimonials__rating"},(0,a.createElement)(Gn,{icon:Yn}),(0,a.createElement)(Gn,{icon:Yn}),(0,a.createElement)(Gn,{icon:Yn}),(0,a.createElement)(Gn,{icon:Yn}),(0,a.createElement)(Gn,{icon:Yn})),(0,a.createElement)(cr.RichText.Content,{value:e.content,tagName:"p",className:`${r}__review`}),e.reviewer&&(0,a.createElement)(cr.RichText.Content,{value:e.reviewer,tagName:"p",className:"wp-block-masterstudy-testimonials__reviewer"})))))),(0,a.createElement)("div",{className:"swiper-pagination"})),(0,a.createElement)("script",null,`var masterstudyTestimonialsSlides=${JSON.stringify(n)}`))}})},2694:(e,t,n)=>{"use strict";var a=n(6925);function r(){}function i(){}i.resetWarningCache=r,e.exports=function(){function e(e,t,n,r,i,s){if(s!==a){var o=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw o.name="Invariant Violation",o}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:r};return n.PropTypes=n,n}},5556:(e,t,n)=>{e.exports=n(2694)()},6925:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"}},n={};function a(e){var r=n[e];if(void 0!==r)return r.exports;var i=n[e]={exports:{}};return t[e](i,i.exports,a),i.exports}a.m=t,e=[],a.O=(t,n,r,i)=>{if(!n){var s=1/0;for(d=0;d<e.length;d++){for(var[n,r,i]=e[d],o=!0,l=0;l<n.length;l++)(!1&i||s>=i)&&Object.keys(a.O).every((e=>a.O[e](n[l])))?n.splice(l--,1):(o=!1,i<s&&(s=i));if(o){e.splice(d--,1);var c=r();void 0!==c&&(t=c)}}return t}i=i||0;for(var d=e.length;d>0&&e[d-1][2]>i;d--)e[d]=e[d-1];e[d]=[n,r,i]},a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={6058:0,8862:0};a.O.j=t=>0===e[t];var t=(t,n)=>{var r,i,[s,o,l]=n,c=0;if(s.some((t=>0!==e[t]))){for(r in o)a.o(o,r)&&(a.m[r]=o[r]);if(l)var d=l(a)}for(t&&t(n);c<s.length;c++)i=s[c],a.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return a.O(d)},n=globalThis.webpackChunkmasterstudy_lms_learning_management_system=globalThis.webpackChunkmasterstudy_lms_learning_management_system||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})();var r=a.O(void 0,[8862],(()=>a(5227)));r=a.O(r)})();