(()=>{var e,t={1357:(e,t,n)=>{"use strict";const a=window.React,l=window.wp.blocks,s=window.wp.i18n,r=window.wp.blockEditor,i=window.wp.components;var o=n(6942),m=n.n(o);const c=({options:e,children:t,defaultValue:n,buttonClassName:l,...s})=>(0,a.createElement)("div",{...s},t,e.map((({value:e,label:t})=>(0,a.createElement)("button",{type:"button",className:m()([l],"lms-courses-tab-button","lms-courses-tab-sorter",{"is-selected":n&&n===e}),"data-value":e,key:e},t)))),d=({options:e,children:t,defaultValue:n,...l})=>(0,a.createElement)("div",{...l},(0,a.createElement)("div",{className:`${l.className}__start`},s.__("Sort by","masterstudy-lms-learning-management-system"),":"),(0,a.createElement)("div",{className:`${l.className}__end`},(0,a.createElement)("select",{className:`${l.className}-options disable-select`},t,e.map((({value:e,label:t},s)=>(0,a.createElement)("option",{className:`${l.className}-option lms-courses-tab-option lms-courses-tab-sorter`,selected:n&&n===e,"data-value":e,key:e},t)))))),u=({options:e,children:t,defaultValue:n,tabClassName:l,...s})=>(0,a.createElement)("div",{...s},t,e.map((({value:e,label:t})=>(0,a.createElement)("button",{type:"button",className:m()([l],"lms-courses-tab-tab","lms-courses-tab-sorter",{"is-selected":n&&n===e}),"data-value":e,key:e},t)))),g=({message:e})=>(0,a.createElement)("div",{className:"lms-courses-tab__empty"},(0,a.createElement)("div",{className:"lms-courses-tab__empty-message"},e)),p=({condition:e,fallback:t=null,children:n})=>(0,a.createElement)(a.Fragment,null,e?n:t),C=(e,t)=>{if(typeof e!=typeof t)return!1;if(Number.isNaN(e)&&Number.isNaN(t))return!0;if("object"!=typeof e||null===e||null===t)return e===t;if(Object.keys(e).length!==Object.keys(t).length)return!1;if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;const n=e.slice().sort(),a=t.slice().sort();return n.every(((e,t)=>C(e,a[t])))}for(const n of Object.keys(e))if(!C(e[n],t[n]))return!1;return!0},h=(e=[])=>e.map((e=>({label:e.name,value:e.term_id})));let b=function(e){return e.ALL="all",e.SOME="some",e}({}),v=function(e){return e.NORMAL="Normal",e.HOVER="Hover",e.ACTIVE="Active",e.FOCUS="Focus",e}({}),y=function(e){return e.DESKTOP="Desktop",e.TABLET="Tablet",e.MOBILE="Mobile",e}({}),_=function(e){return e.TOP_lEFT="top-left",e.TOP_CENTER="top-center",e.TOP_RIGHT="top-right",e.BOTTOM_lEFT="bottom-left",e.BOTTOM_CENTER="bottom-center",e.BOTTOM_RIGHT="bottom-right",e}({});const E=["",null,void 0,"null","undefined"],f=[".jpg",".jpeg",".png",".gif"],N=e=>E.includes(e),w=(e,t,n="")=>{const a=e[t];return"object"==typeof a&&null!==a?((e,t)=>{return n=e,Object.values(n).every((e=>E.includes(e)))?null:((e,t="")=>{const n=Object.entries(e).reduce(((e,[n,a])=>(e[n]=(a||"0")+t,e)),{});return`${n.top} ${n.right} ${n.bottom} ${n.left}`})(e,t);var n})(a,n):((e,t)=>N(e)?e:(e=>{if("string"!=typeof e)return!1;const t=e.slice(e.lastIndexOf("."));return f.includes(t)||e.startsWith("blob")})(e)?`url('${e}')`:String(e+t))(a,n)},S=(e,t,n)=>{const a={};return n.forEach((({isAdaptive:n,hasHover:l,unit:s},r)=>{if(t.hasOwnProperty(r)){const{unitMeasureDesktop:o,unitMeasureTablet:m,unitMeasureMobile:c}=((e,t)=>{var n;return{unitMeasureDesktop:null!==(n=e[t])&&void 0!==n?n:"",unitMeasureTablet:t?e[t+"Tablet"]:"",unitMeasureMobile:t?e[t+"Mobile"]:""}})(t,s);if(n&&l){const{desktopHoverPropertyName:n,mobileHoverPropertyName:l,tabletHoverPropertyName:s}=(e=>{const t=e+v.HOVER;return{desktopHoverPropertyName:t,tabletHoverPropertyName:t+"Tablet",mobileHoverPropertyName:t+"Mobile"}})(r),i=w(t,n,o);N(i)||(a[`--lms-${e}-${n}`]=i);const d=w(t,s,m);N(d)||(a[`--lms-${e}-${s}`]=d);const u=w(t,l,c);N(u)||(a[`--lms-${e}-${l}`]=u)}if(l){const n=r+v.HOVER,l=w(t,n,o);N(l)||(a[`--lms-${e}-${n}`]=l)}if(n){const{desktopPropertyName:n,mobilePropertyName:l,tabletPropertyName:s}={desktopPropertyName:i=r,tabletPropertyName:i+"Tablet",mobilePropertyName:i+"Mobile"},d=w(t,n,o);N(d)||(a[`--lms-${e}-${n}`]=d);const u=w(t,s,m);N(u)||(a[`--lms-${e}-${s}`]=u);const g=w(t,l,c);N(g)||(a[`--lms-${e}-${l}`]=g)}const d=w(t,r,o);N(d)||(a[`--lms-${e}-${r}`]=d)}var i})),a},M=(s.__("Small","masterstudy-lms-learning-management-system"),s.__("Normal","masterstudy-lms-learning-management-system"),s.__("Large","masterstudy-lms-learning-management-system"),s.__("Extra Large","masterstudy-lms-learning-management-system"),"wp-block-masterstudy-settings__"),x={top:"",right:"",bottom:"",left:""};function T(e){return Array.isArray(e)?e.map((e=>M+e)):M+e}_.TOP_lEFT,_.TOP_CENTER,_.TOP_RIGHT,_.BOTTOM_lEFT,_.BOTTOM_CENTER,_.BOTTOM_RIGHT,s.__("Newest","masterstudy-lms-learning-management-system"),s.__("Oldest","masterstudy-lms-learning-management-system"),s.__("Overall rating","masterstudy-lms-learning-management-system"),s.__("Popular","masterstudy-lms-learning-management-system"),s.__("Price low","masterstudy-lms-learning-management-system"),s.__("Price high","masterstudy-lms-learning-management-system");const O=window.wp.element,A=window.wp.data,k=()=>(0,A.useSelect)((e=>e("core/editor").getDeviceType()||e("core/edit-site")?.__experimentalGetPreviewDeviceType()||e("core/edit-post")?.__experimentalGetPreviewDeviceType()||e("masterstudy/store")?.getDeviceType()),[])||"Desktop",D=(e=!1)=>{const[t,n]=(0,O.useState)(e),a=(0,O.useCallback)((()=>{n(!0)}),[]);return{isOpen:t,onClose:(0,O.useCallback)((()=>{n(!1)}),[]),onOpen:a,onToggle:(0,O.useCallback)((()=>{n((e=>!e))}),[])}},R=(0,O.createContext)(null),H=({children:e,...t})=>(0,a.createElement)(R.Provider,{value:{...t}},e),V=()=>{const e=(0,O.useContext)(R);if(!e)throw new Error("No settings context provided");return e},L=(e="")=>{const{attributes:t,setAttributes:n,onResetByFieldName:a,changedFieldsByName:l}=V();return{value:t[e],onChange:t=>n({[e]:t}),onReset:a.get(e),isChanged:l.get(e)}},P=(e,t=!1,n=!1)=>{const{hoverName:a,onChangeHoverName:l}=(()=>{const[e,t]=(0,O.useState)(v.NORMAL);return{hoverName:e,onChangeHoverName:(0,O.useCallback)((e=>{t(e)}),[])}})(),s=k();return{fieldName:(0,O.useMemo)((()=>{const l=a===v.HOVER?a:"",r=s===y.DESKTOP?"":s;return n&&t?e+l+r:n&&!t?e+l:t&&!n?e+r:e}),[e,n,t,a,s]),hoverName:a,onChangeHoverName:l}},B=(e,t=!1,n="Normal")=>{const a=k(),l=(0,O.useMemo)((()=>{const l=n===v.NORMAL?"":n,s=a===y.DESKTOP?"":a;return l&&t?e+l+s:l&&!t?e+l:t&&!l?e+s:e}),[e,t,n,a]),{value:s,isChanged:r,onReset:i}=L(l);return{fieldName:l,value:s,isChanged:r,onReset:i}},F=(e=[],t=b.ALL)=>{const{attributes:n}=V();return!e.length||(t===b.ALL?e.every((({name:e,value:t})=>{const a=n[e];return Array.isArray(t)?Array.isArray(a)?C(t,a):t.includes(a):t===a})):t!==b.SOME||e.some((({name:e,value:t})=>{const a=n[e];return Array.isArray(t)?Array.isArray(a)?C(t,a):t.includes(a):t===a})))},U=e=>{const t=(0,O.useRef)(null);return(0,O.useEffect)((()=>{const n=n=>{t.current&&!t.current.contains(n.target)&&e()};return document.addEventListener("click",n),()=>{document.removeEventListener("click",n)}}),[t,e]),t},I=e=>(0,a.createElement)(i.SVG,{width:"16",height:"16",viewBox:"0 0 12 13",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(i.Path,{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})),Z=e=>(0,a.createElement)(i.SVG,{width:"16",height:"16",viewBox:"0 0 14 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(i.G,{"clip-path":"url(#clip0_1068_38993)"},(0,a.createElement)(i.Path,{d:"M11.1973 8.60005L8.74967 8.11005V5.67171C8.74967 5.517 8.68822 5.36863 8.57882 5.25923C8.46942 5.14984 8.32105 5.08838 8.16634 5.08838H6.99967C6.84496 5.08838 6.69659 5.14984 6.5872 5.25923C6.4778 5.36863 6.41634 5.517 6.41634 5.67171V8.58838H5.24967C5.15021 8.58844 5.05241 8.61391 4.96555 8.66238C4.87869 8.71084 4.80565 8.78068 4.75336 8.86529C4.70106 8.9499 4.67125 9.04646 4.66674 9.14582C4.66223 9.24518 4.68317 9.34405 4.72759 9.43305L6.47759 12.933C6.57676 13.1302 6.77859 13.255 6.99967 13.255H11.083C11.2377 13.255 11.3861 13.1936 11.4955 13.0842C11.6049 12.9748 11.6663 12.8264 11.6663 12.6717V9.17171C11.6665 9.03685 11.6198 8.90612 11.5343 8.80186C11.4487 8.69759 11.3296 8.62626 11.1973 8.60005Z"}),(0,a.createElement)(i.Path,{d:"M10.4997 1.58838H3.49967C2.85626 1.58838 2.33301 2.11221 2.33301 2.75505V6.83838C2.33301 7.4818 2.85626 8.00505 3.49967 8.00505H5.24967V6.83838H3.49967V2.75505H10.4997V6.83838H11.6663V2.75505C11.6663 2.11221 11.1431 1.58838 10.4997 1.58838Z"})),(0,a.createElement)("defs",null,(0,a.createElement)("clipPath",{id:"clip0_1068_38993"},(0,a.createElement)(i.Rect,{width:"14",height:"14",transform:"translate(0 0.421875)"})))),z=[{value:v.NORMAL,label:s.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(I,{onClick:e})},{value:v.HOVER,label:s.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(Z,{onClick:e})},{value:v.ACTIVE,label:s.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(Z,{onClick:e})},{value:v.FOCUS,label:s.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(Z,{onClick:e})}],G={[v.NORMAL]:{icon:(0,a.createElement)(I,null),label:s.__("Normal State","masterstudy-lms-learning-management-system")},[v.HOVER]:{icon:(0,a.createElement)(Z,null),label:s.__("Hovered State","masterstudy-lms-learning-management-system")},[v.ACTIVE]:{icon:(0,a.createElement)(Z,null),label:s.__("Active State","masterstudy-lms-learning-management-system")},[v.FOCUS]:{icon:(0,a.createElement)(Z,null),label:s.__("Focus State","masterstudy-lms-learning-management-system")}},$=(e,t)=>{let n=[];return n=e.length?z.filter((t=>e.includes(t.value))):z,n=n.filter((e=>e.value!==t)),{ICONS_MAP:G,options:n}},[j,W,K,X,Y]=T(["hover-state","hover-state__selected","hover-state__selected__opened-menu","hover-state__menu","hover-state__menu__item"]),q=({stateOptions:e,currentState:t,onSelect:n})=>{const{isOpen:l,onOpen:s,onClose:r}=D(),i=U(r),{ICONS_MAP:o,options:c}=$(e,t);return(0,a.createElement)("div",{className:j,ref:i},(0,a.createElement)("div",{className:m()([W],{[K]:l}),onClick:s,title:o[t]?.label},o[t]?.icon),(0,a.createElement)(p,{condition:l},(0,a.createElement)("div",{className:X},c.map((({value:e,icon:t,label:l})=>(0,a.createElement)("div",{key:e,className:Y,title:l},t((()=>n(e)))))))))},J=T("color-indicator"),Q=(0,O.memo)((({color:e,onChange:t})=>(0,a.createElement)("div",{className:J},(0,a.createElement)(r.PanelColorSettings,{enableAlpha:!0,disableCustomColors:!1,__experimentalHasMultipleOrigins:!0,__experimentalIsRenderedInSidebar:!0,colorSettings:[{label:"",value:e,onChange:t}]}))));var ee;function te(){return te=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},te.apply(null,arguments)}var ne,ae,le=function(e){return a.createElement("svg",te({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 12 13"},e),ee||(ee=a.createElement("path",{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})))};function se(){return se=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},se.apply(null,arguments)}var re=function(e){return a.createElement("svg",se({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 14 15"},e),ne||(ne=a.createElement("g",{clipPath:"url(#state-hover_svg__a)"},a.createElement("path",{d:"M11.197 8.6 8.75 8.11V5.672a.583.583 0 0 0-.584-.584H7a.583.583 0 0 0-.584.584v2.916H5.25a.584.584 0 0 0-.522.845l1.75 3.5c.099.197.3.322.522.322h4.083a.583.583 0 0 0 .583-.583v-3.5a.58.58 0 0 0-.469-.572"}),a.createElement("path",{d:"M10.5 1.588h-7c-.644 0-1.167.524-1.167 1.167v4.083c0 .644.523 1.167 1.167 1.167h1.75V6.838H3.5V2.755h7v4.083h1.166V2.755c0-.643-.523-1.167-1.166-1.167"}))),ae||(ae=a.createElement("defs",null,a.createElement("clipPath",{id:"state-hover_svg__a"},a.createElement("path",{d:"M0 .422h14v14H0z"})))))};const ie=[{value:v.NORMAL,label:s.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(le,{onClick:e})},{value:v.HOVER,label:s.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(re,{onClick:e})}],oe={[v.NORMAL]:{icon:(0,a.createElement)(le,null),label:s.__("Normal State","masterstudy-lms-learning-management-system")},[v.HOVER]:{icon:(0,a.createElement)(re,null),label:s.__("Hovered State","masterstudy-lms-learning-management-system")}},me=T("hover-state"),ce=T("hover-state__selected"),de=T("hover-state__selected__opened-menu"),ue=T("has-changes"),ge=T("hover-state__menu"),pe=T("hover-state__menu__item"),Ce=(0,O.memo)((e=>{const{hoverName:t,onChangeHoverName:n,fieldName:l}=e,{changedFieldsByName:s}=V(),r=s.get(l),{isOpen:i,onOpen:o,onClose:c}=D(),d=U(c),{ICONS_MAP:u,options:g}=(e=>{const t=(0,O.useMemo)((()=>ie.filter((t=>t.value!==e))),[e]);return{ICONS_MAP:oe,options:t}})(t),C=(0,O.useCallback)((e=>{n(e),c()}),[n,c]);return(0,a.createElement)("div",{className:me,ref:d},(0,a.createElement)("div",{className:m()([ce],{[de]:i,[ue]:r}),onClick:o,title:u[t]?.label},u[t]?.icon),(0,a.createElement)(p,{condition:i},(0,a.createElement)("div",{className:ge},g.map((({value:e,icon:t,label:n})=>(0,a.createElement)("div",{key:e,className:pe,title:n},t((()=>C(e)))))))))})),he={Desktop:{icon:"desktop",label:s.__("Desktop","masterstudy-lms-learning-management-system")},Tablet:{icon:"tablet",label:s.__("Tablet","masterstudy-lms-learning-management-system")},Mobile:{icon:"smartphone",label:s.__("Mobile","masterstudy-lms-learning-management-system")}},be=[{value:y.DESKTOP,icon:"desktop",label:s.__("Desktop","masterstudy-lms-learning-management-system")},{value:y.TABLET,icon:"tablet",label:s.__("Tablet","masterstudy-lms-learning-management-system")},{value:y.MOBILE,icon:"smartphone",label:s.__("Mobile","masterstudy-lms-learning-management-system")}],ve=T("device-picker"),ye=T("device-picker__selected"),_e=T("device-picker__selected__opened-menu"),Ee=T("device-picker__menu"),fe=T("device-picker__menu__item"),Ne=()=>{const{isOpen:e,onOpen:t,onClose:n}=D(),{value:l,onChange:s}=(e=>{const t=k(),n=(0,A.useDispatch)();return{value:(0,O.useMemo)((()=>he[t]),[t]),onChange:t=>{n("core/edit-site")&&n("core/edit-site").__experimentalSetPreviewDeviceType?n("core/edit-site").__experimentalSetPreviewDeviceType(t):n("core/edit-post")&&n("core/edit-post").__experimentalSetPreviewDeviceType?n("core/edit-post").__experimentalSetPreviewDeviceType(t):n("masterstudy/store").setDeviceType(t),e()}}})(n),r=(e=>(0,O.useMemo)((()=>be.filter((t=>t.icon!==e))),[e]))(l.icon),o=U(n);return(0,a.createElement)("div",{className:ve,ref:o},(0,a.createElement)(i.Dashicon,{className:m()([ye],{[_e]:e}),icon:l.icon,size:16,onClick:t,title:l.label}),(0,a.createElement)(p,{condition:e},(0,a.createElement)("div",{className:Ee},r.map((e=>(0,a.createElement)(i.Dashicon,{key:e.value,icon:e.icon,size:16,onClick:()=>s(e.value),className:fe,title:e.label}))))))},we=T("reset-button"),Se=({onReset:e})=>(0,a.createElement)(i.Dashicon,{icon:"undo",onClick:e,className:we,size:16}),Me=[{label:"PX",value:"px"},{label:"%",value:"%"},{label:"EM",value:"em"},{label:"REM",value:"rem"},{label:"VW",value:"vw"},{label:"VH",value:"vh"}],xe=T("unit"),Te=T("unit__single"),Oe=T("unit__list"),Ae=({name:e,isAdaptive:t})=>{const{isOpen:n,onOpen:l,onClose:s}=D(),{fieldName:r}=P(e,t),{value:i,onChange:o}=L(r),m=U(s);return(0,a.createElement)("div",{className:xe,ref:m},(0,a.createElement)("div",{className:Te,onClick:l},i),(0,a.createElement)(p,{condition:n},(0,a.createElement)("div",{className:Oe},Me.map((({value:e,label:t})=>(0,a.createElement)("div",{key:e,onClick:()=>(o(e),void s())},t))))))},ke=T("popover-modal"),De=T("popover-modal__close dashicon dashicons dashicons-no-alt"),Re=e=>{const{isOpen:t,onClose:n,popoverContent:l}=e;return(0,a.createElement)(p,{condition:t},(0,a.createElement)(i.Popover,{position:"middle left",onClose:n,className:ke},l,(0,a.createElement)("span",{onClick:n,className:De})))},He=T("setting-label"),Ve=T("setting-label__content"),Le=e=>{const{label:t,isChanged:n=!1,onReset:l,showDevicePicker:s=!0,HoverStateControl:r=null,unitName:i,popoverContent:o=null,dependencies:m}=e,{isOpen:c,onClose:d,onToggle:u}=D();return F(m)?(0,a.createElement)("div",{className:He},(0,a.createElement)("div",{className:Ve},(0,a.createElement)("div",{onClick:u},t),(0,a.createElement)(p,{condition:Boolean(o)},(0,a.createElement)(Re,{isOpen:c,onClose:d,popoverContent:o})),(0,a.createElement)(p,{condition:s},(0,a.createElement)(Ne,null)),(0,a.createElement)(p,{condition:Boolean(r)},r)),(0,a.createElement)(p,{condition:Boolean(i)},(0,a.createElement)(Ae,{name:i,isAdaptive:s})),(0,a.createElement)(p,{condition:n},(0,a.createElement)(Se,{onReset:l}))):null},Pe=T("suffix"),Be=()=>(0,a.createElement)("div",{className:Pe},(0,a.createElement)(i.Dashicon,{icon:"color-picker",size:16})),Fe=T("color-picker"),Ue=e=>{const{name:t,label:n,placeholder:l,dependencyMode:s,dependencies:r,isAdaptive:o=!1,hasHover:m=!1}=e,{fieldName:c,hoverName:d,onChangeHoverName:u}=P(t,o,m),{value:g,isChanged:C,onChange:h,onReset:b}=L(c);return F(r,s)?(0,a.createElement)("div",{className:Fe},(0,a.createElement)(p,{condition:Boolean(n)},(0,a.createElement)(Le,{label:n,isChanged:C,onReset:b,showDevicePicker:o,HoverStateControl:(0,a.createElement)(p,{condition:m},(0,a.createElement)(Ce,{hoverName:d,onChangeHoverName:u,fieldName:c}))})),(0,a.createElement)(i.__experimentalInputControl,{prefix:(0,a.createElement)(Q,{color:g,onChange:h}),suffix:(0,a.createElement)(Be,null),onChange:h,value:g,placeholder:l})):null},Ie=T("number-steppers"),Ze=T("indent-steppers"),ze=T("indent-stepper-plus"),Ge=T("indent-stepper-minus"),$e=({onIncrement:e,onDecrement:t,withArrows:n=!1})=>n?(0,a.createElement)("span",{className:Ze},(0,a.createElement)("button",{onClick:e,className:ze}),(0,a.createElement)("button",{onClick:t,className:Ge})):(0,a.createElement)("span",{className:Ie},(0,a.createElement)("button",{onClick:e},"+"),(0,a.createElement)("button",{onClick:t},"-")),[je,We]=T(["indents","indents-control"]),Ke=({name:e,label:t,unitName:n,popoverContent:l,dependencyMode:r,dependencies:o,isAdaptive:m=!1})=>{const{fieldName:c}=P(e,m),{value:d,onResetSegmentedBox:u,hasChanges:g,handleInputIncrement:C,handleInputDecrement:h,updateDirectionsValues:b,lastFieldValue:v}=((e,t)=>{const{value:n,isChanged:a,onChange:l,onReset:s}=L(e),{onResetByFieldName:r,changedFieldsByName:i}=V(),o=a||i.get(t),m=e=>{l({...n,...e})},[c,d]=(0,O.useState)(!1);return{value:n,onResetSegmentedBox:()=>{s(),r.get(t)()},hasChanges:o,handleInputIncrement:e=>Number(n[e])+1,handleInputDecrement:e=>Number(n[e])-1,updateDirectionsValues:(e,t,n)=>{e?(d(!1),m({top:n,right:n,bottom:n,left:n})):(d(n),m({[t]:n}))},lastFieldValue:c}})(c,n),[y,_]=(0,O.useState)((()=>{const{left:e,right:t,top:n,bottom:a}=d;return""!==e&&e===t&&t===n&&n===a})),E=e=>{const[t,n]=Object.entries(e)[0];b(y,t,n)},f=e=>()=>{const t=C(e);b(y,e,String(t))},N=e=>()=>{const t=h(e);b(y,e,String(t))};return F(o,r)?(0,a.createElement)("div",{className:je},(0,a.createElement)(p,{condition:Boolean(t)},(0,a.createElement)(Le,{label:null!=t?t:"",isChanged:g,onReset:u,unitName:n,popoverContent:l,showDevicePicker:m})),(0,a.createElement)("div",{className:`${We} ${y?"active":""}`},(0,a.createElement)("div",null,(0,a.createElement)(i.__experimentalNumberControl,{value:d.top,onChange:e=>{E({top:e})},spinControls:"none",suffix:(0,a.createElement)($e,{onIncrement:f("top"),onDecrement:N("top"),withArrows:!0})}),(0,a.createElement)("div",null,s.__("Top","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(i.__experimentalNumberControl,{value:d.right,onChange:e=>{E({right:e})},spinControls:"none",suffix:(0,a.createElement)($e,{onIncrement:f("right"),onDecrement:N("right"),withArrows:!0})}),(0,a.createElement)("div",null,s.__("Right","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(i.__experimentalNumberControl,{value:d.bottom,onChange:e=>{E({bottom:e})},spinControls:"none",suffix:(0,a.createElement)($e,{onIncrement:f("bottom"),onDecrement:N("bottom"),withArrows:!0})}),(0,a.createElement)("div",null,s.__("Bottom","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(i.__experimentalNumberControl,{value:d.left,onChange:e=>{E({left:e})},spinControls:"none",suffix:(0,a.createElement)($e,{onIncrement:f("left"),onDecrement:N("left"),withArrows:!0})}),(0,a.createElement)("div",null,s.__("Left","masterstudy-lms-learning-management-system"))),(0,a.createElement)(i.Dashicon,{icon:"dashicons "+(y?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{y||!1===v||b(!0,"left",v),_((e=>!e))}}))):null},[Xe,Ye,qe,Je]=T(["toggle-group-wrapper","toggle-group","toggle-group__toggle","toggle-group__active-toggle"]),Qe=e=>{const{name:t,options:n,label:l,isAdaptive:s=!1,dependencyMode:r,dependencies:i}=e,{fieldName:o}=P(t,s),{value:c,isChanged:d,onChange:u,onReset:g}=L(o);return F(i,r)?(0,a.createElement)("div",{className:Xe},(0,a.createElement)(p,{condition:Boolean(l)},(0,a.createElement)(Le,{label:l,isChanged:d,onReset:g,showDevicePicker:s})),(0,a.createElement)("div",{className:Ye},n.map((e=>(0,a.createElement)("div",{key:e.value,className:m()([qe],{[Je]:e.value===c}),onClick:()=>u(e.value)},e.label))))):null},[et,tt,nt,at]=T(["border-control","border-control-solid","border-control-dashed","border-control-dotted"]),lt=e=>{const{label:t,borderStyleName:n,borderColorName:l,borderWidthName:r,dependencyMode:i,dependencies:o,isAdaptive:c=!1,hasHover:d=!1}=e,[u,g]=(0,O.useState)("Normal"),{fieldName:C,value:h,isChanged:b,onReset:v}=B(n,c,u),{fieldName:y,isChanged:_,onReset:E}=B(l,c,u),{fieldName:f,isChanged:N,onReset:w}=B(r,c,u);if(!F(o,i))return null;const S=b||_||N;return(0,a.createElement)("div",{className:m()([et],{"has-reset-button":S})},(0,a.createElement)(Le,{label:t,isChanged:S,onReset:()=>{v(),E(),w()},showDevicePicker:c,HoverStateControl:(0,a.createElement)(p,{condition:d},(0,a.createElement)(q,{stateOptions:["Normal","Hover"],currentState:u,onSelect:g}))}),(0,a.createElement)(Qe,{options:[{label:(0,a.createElement)("span",null,s.__("None","masterstudy-lms-learning-management-system")),value:"none"},{label:(0,a.createElement)("span",{className:tt}),value:"solid"},{label:(0,a.createElement)("span",{className:nt},(0,a.createElement)("span",null)),value:"dashed"},{label:(0,a.createElement)("span",{className:at},(0,a.createElement)("span",null,(0,a.createElement)("span",null))),value:"dotted"}],name:C}),(0,a.createElement)(p,{condition:"none"!==h},(0,a.createElement)(Ue,{name:y,placeholder:s.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Ke,{name:f})))},st=T("border-radius"),rt=T("border-radius-control"),it=({name:e,label:t,unitName:n,popoverContent:l,dependencyMode:s,dependencies:r,isAdaptive:o=!1,hasHover:c=!1})=>{const{fieldName:d}=P(e,o,c),{value:u,onResetBorderRadius:g,hasChanges:p,handleInputIncrement:C,handleInputDecrement:h,updateDirectionsValues:b,lastFieldValue:v}=((e,t)=>{const[n,a]=(0,O.useState)(!1),{value:l,isChanged:s,onChange:r,onReset:i}=L(e),{onResetByFieldName:o,changedFieldsByName:m}=V(),c=s||m.get(t),d=e=>{r({...l,...e})};return{value:l,onResetBorderRadius:()=>{i(),o.get(t)()},hasChanges:c,handleInputIncrement:e=>Number(l[e])+1,handleInputDecrement:e=>Number(l[e])-1,updateDirectionsValues:(e,t,n)=>{e?(d({top:n,right:n,bottom:n,left:n}),a(!1)):(d({[t]:n}),a(n))},lastFieldValue:n}})(d,n),[y,_]=(0,O.useState)((()=>{const{left:e,right:t,top:n,bottom:a}=u;return""!==e&&e===t&&t===n&&n===a})),E=e=>{const[t,n]=Object.entries(e)[0];b(y,t,n)},f=e=>()=>{const t=C(e);b(y,e,String(t))},N=e=>()=>{const t=h(e);b(y,e,String(t))};return F(r,s)?(0,a.createElement)("div",{className:st},(0,a.createElement)(Le,{label:t,isChanged:p,onReset:g,unitName:n,popoverContent:l,showDevicePicker:o}),(0,a.createElement)("div",{className:m()([rt],{"has-reset-button":p,active:y})},(0,a.createElement)("div",{className:"number-control-top"},(0,a.createElement)(i.__experimentalNumberControl,{value:u.top,onChange:e=>{E({top:e})},spinControls:"none",suffix:(0,a.createElement)($e,{onIncrement:f("top"),onDecrement:N("top"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-right"},(0,a.createElement)(i.__experimentalNumberControl,{value:u.right,onChange:e=>{E({right:e})},spinControls:"none",suffix:(0,a.createElement)($e,{onIncrement:f("right"),onDecrement:N("right"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-left"},(0,a.createElement)(i.__experimentalNumberControl,{value:u.left,onChange:e=>{E({left:e})},spinControls:"none",suffix:(0,a.createElement)($e,{onIncrement:f("left"),onDecrement:N("left"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-bottom"},(0,a.createElement)(i.__experimentalNumberControl,{value:u.bottom,onChange:e=>{E({bottom:e})},spinControls:"none",suffix:(0,a.createElement)($e,{onIncrement:f("bottom"),onDecrement:N("bottom"),withArrows:!0})})),(0,a.createElement)(i.Dashicon,{icon:"dashicons "+(y?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{y||!1===v||b(!0,"left",v),_((e=>!e))}}))):null},ot=(T("box-shadow-preset"),T("presets")),mt=T("presets__item-wrapper"),ct=T("presets__item-wrapper__preset"),dt=T("presets__item-wrapper__name"),ut=((0,O.memo)((e=>{const{presets:t,activePreset:n,onSelectPreset:l,PresetItem:s,detectIsActive:r,detectByIndex:i=!1}=e;return(0,a.createElement)("div",{className:ot},t.map((({name:e,...t},o)=>(0,a.createElement)("div",{key:o,className:m()([mt],{active:r(n,i?o:t)}),onClick:()=>l(t)},(0,a.createElement)("div",{className:ct},(0,a.createElement)(s,{preset:t})),(0,a.createElement)("span",{className:dt},e)))))})),T("range-control")),gt=e=>{const{name:t,label:n,min:l,max:s,unitName:r,dependencyMode:o,dependencies:m,isAdaptive:c=!1}=e,{fieldName:d}=P(t,c),{value:u,onChange:g,onResetNumberField:C,hasChanges:h}=((e,t)=>{const{value:n,isChanged:a,onChange:l,onReset:s}=L(e),{onResetByFieldName:r,changedFieldsByName:i}=V();return{value:n,onChange:l,onResetNumberField:()=>{s(),r.get(t)()},hasChanges:a||i.get(t)}})(d,r);return F(m,o)?(0,a.createElement)("div",{className:ut},(0,a.createElement)(p,{condition:Boolean(n)},(0,a.createElement)(Le,{label:n,isChanged:h,onReset:C,unitName:r,showDevicePicker:c})),(0,a.createElement)(i.RangeControl,{value:u,onChange:g,min:l,max:s})):null},pt=T("switch"),Ct=e=>{const{name:t,label:n,dependencyMode:l,dependencies:s,isAdaptive:r=!1}=e,{fieldName:o}=P(t,r),{value:m,onChange:c}=L(o);return F(s,l)?(0,a.createElement)("div",{className:pt,"data-has-label":Boolean(n).toString()},(0,a.createElement)(i.ToggleControl,{label:n,checked:m,onChange:c}),(0,a.createElement)(p,{condition:r},(0,a.createElement)(Ne,null))):null},ht=(T("box-shadow-settings"),T("box-shadow-presets-title"),T("input-field"),T("input-field-control"),T("number-field")),bt=T("number-field-control"),vt=e=>{const{name:t,label:n,unitName:l,help:s,popoverContent:r,dependencyMode:o,dependencies:m,isAdaptive:c=!1}=e,{fieldName:d}=P(t,c),{value:u,onResetNumberField:g,hasChanges:p,handleIncrement:C,handleDecrement:h,handleInputChange:b}=((e,t)=>{const{value:n,isChanged:a,onChange:l,onReset:s}=L(e),{onResetByFieldName:r,changedFieldsByName:i}=V(),o=a||i.get(t);return{value:n,onResetNumberField:()=>{s(),r.get(t)()},hasChanges:o,handleIncrement:()=>{l(n+1)},handleDecrement:()=>{l(n-1)},handleInputChange:e=>{const t=Number(""===e?0:e);l(t)}}})(d,l);return F(m,o)?(0,a.createElement)("div",{className:ht},(0,a.createElement)(Le,{label:n,isChanged:p,onReset:g,unitName:l,showDevicePicker:c,popoverContent:r}),(0,a.createElement)("div",{className:bt},(0,a.createElement)(i.__experimentalNumberControl,{value:u,onChange:b,spinControls:"none",suffix:(0,a.createElement)($e,{onIncrement:C,onDecrement:h})})),s&&(0,a.createElement)("small",null,s)):null},yt=({className:e})=>(0,a.createElement)("div",{className:e},s.__("No options","masterstudy-lms-learning-management-system")),_t=T("select__single-item"),Et=T("select__container"),ft=T("select__container__multi-item"),Nt=({multiple:e,value:t,options:n,onChange:l})=>{const{singleValue:s,multipleValue:r}=((e,t,n)=>({singleValue:(0,O.useMemo)((()=>t?null:n.find((t=>t.value===e))?.label),[t,e,n]),multipleValue:(0,O.useMemo)((()=>t?e:null),[t,e])}))(t,e,n);return(0,a.createElement)(p,{condition:e,fallback:(0,a.createElement)("div",{className:_t},s)},(0,a.createElement)("div",{className:Et},r?.map((e=>{const t=n.find((t=>t.value===e));return t?(0,a.createElement)("div",{key:t.value,className:ft},(0,a.createElement)("div",null,t.label),(0,a.createElement)(i.Dashicon,{icon:"no-alt",onClick:()=>l(t.value),size:16})):null}))))},wt=T("select"),St=T("select__select-box"),Mt=T("select__placeholder"),xt=T("select__select-box-multiple"),Tt=T("select__menu"),Ot=T("select__menu__options-container"),At=T("select__menu__item"),kt=e=>{const{options:t,multiple:n=!1,placeholder:l="Select",value:s,onSelect:r}=e,{isOpen:o,onToggle:c,onClose:d}=D(),u=U(d),g=((e,t,n)=>(0,O.useMemo)((()=>n&&Array.isArray(e)?t.filter((t=>!e.includes(t.value))):t.filter((t=>t.value!==e))),[e,t,n]))(s,t,n),C=((e,t,n,a)=>(0,O.useCallback)((l=>{if(t&&Array.isArray(e)){const t=e.includes(l)?e.filter((e=>e!==l)):[...e,l];n(t)}else n(l),a()}),[t,e,n,a]))(s,n,r,d),h=((e,t)=>(0,O.useMemo)((()=>t&&Array.isArray(e)?Boolean(e.length):Boolean(e)),[t,e]))(s,n),b=n&&Array.isArray(s)&&s?.length>0;return(0,a.createElement)("div",{className:wt,ref:u},(0,a.createElement)("div",{className:m()([St],{[xt]:b}),onClick:c},(0,a.createElement)(p,{condition:h,fallback:(0,a.createElement)("div",{className:Mt},l)},(0,a.createElement)(Nt,{onChange:C,options:t,multiple:n,value:s})),(0,a.createElement)(i.Dashicon,{icon:o?"arrow-up-alt2":"arrow-down-alt2",size:16,style:{color:"#4D5E6F"}})),(0,a.createElement)(p,{condition:o},(0,a.createElement)("div",{className:Tt},(0,a.createElement)(p,{condition:Boolean(g.length),fallback:(0,a.createElement)(yt,{className:At})},(0,a.createElement)("div",{className:Ot},g.map((e=>(0,a.createElement)("div",{key:e.value,onClick:()=>C(e.value),className:At},e.label))))))))},Dt=T("setting-select"),Rt=e=>{const{name:t,options:n,label:l,multiple:s=!1,placeholder:r,isAdaptive:i=!1,dependencyMode:o,dependencies:m}=e,{fieldName:c}=P(t,i),{value:d,isChanged:u,onChange:g,onReset:C}=L(c);return F(m,o)?(0,a.createElement)("div",{className:Dt},(0,a.createElement)(p,{condition:Boolean(l)},(0,a.createElement)(Le,{label:l,isChanged:u,onReset:C,showDevicePicker:i})),(0,a.createElement)(kt,{options:n,value:d,onSelect:g,multiple:s,placeholder:r})):null},Ht=(T("row-select"),T("row-select__label"),T("row-select__control"),T("typography-select")),Vt=T("typography-select-label"),Lt=e=>{const{name:t,label:n,options:l,isAdaptive:s=!1}=e,{fieldName:r}=P(t,s),{isChanged:i,onReset:o}=L(r);return(0,a.createElement)("div",{className:Ht},(0,a.createElement)("div",{className:Vt},(0,a.createElement)("div",null,n),(0,a.createElement)(p,{condition:s},(0,a.createElement)(Ne,null))),(0,a.createElement)(Rt,{name:t,options:l,isAdaptive:s}),(0,a.createElement)(p,{condition:i},(0,a.createElement)(Se,{onReset:o})))},Pt=T("typography"),Bt=e=>{const{fontSizeName:t,fontWeightName:n,textTransformName:l,fontStyleName:r,textDecorationName:i,lineHeightName:o,letterSpacingName:m,wordSpacingName:c,fontSizeUnitName:d,lineHeightUnitName:u,letterSpacingUnitName:g,wordSpacingUnitName:p,dependencyMode:C,dependencies:h,isAdaptive:b=!1}=e,{fontWeightOptions:v,textTransformOptions:y,fontStyleOptions:_,textDecorationOptions:E}={fontWeightOptions:[{label:s.__("100 (Thin)","masterstudy-lms-learning-management-system"),value:"100"},{label:s.__("200 (Extra Light)","masterstudy-lms-learning-management-system"),value:"200"},{label:s.__("300 (Light)","masterstudy-lms-learning-management-system"),value:"300"},{label:s.__("400 (Normal)","masterstudy-lms-learning-management-system"),value:"400"},{label:s.__("500 (Medium)","masterstudy-lms-learning-management-system"),value:"500"},{label:s.__("600 (Semi Bold)","masterstudy-lms-learning-management-system"),value:"600"},{label:s.__("700 (Bold)","masterstudy-lms-learning-management-system"),value:"700"},{label:s.__("800 (Extra Bold)","masterstudy-lms-learning-management-system"),value:"800"},{label:s.__("900 (Extra)","masterstudy-lms-learning-management-system"),value:"900"}],textTransformOptions:[{label:s.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:s.__("Uppercase","masterstudy-lms-learning-management-system"),value:"uppercase"},{label:s.__("Lowercase","masterstudy-lms-learning-management-system"),value:"lowercase"},{label:s.__("Capitalize","masterstudy-lms-learning-management-system"),value:"capitalize"},{label:s.__("Normal","masterstudy-lms-learning-management-system"),value:"none"}],fontStyleOptions:[{label:s.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:s.__("Normal","masterstudy-lms-learning-management-system"),value:"none"},{label:s.__("Italic","masterstudy-lms-learning-management-system"),value:"italic"},{label:s.__("Oblique","masterstudy-lms-learning-management-system"),value:"oblique"}],textDecorationOptions:[{label:s.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:s.__("Underline","masterstudy-lms-learning-management-system"),value:"underline"},{label:s.__("Line Through","masterstudy-lms-learning-management-system"),value:"line-through"},{label:s.__("None","masterstudy-lms-learning-management-system"),value:"none"}]};return F(h,C)?(0,a.createElement)("div",{className:Pt},(0,a.createElement)(gt,{name:t,label:s.__("Size","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:d,isAdaptive:b}),(0,a.createElement)(Lt,{name:n,label:s.__("Weight","masterstudy-lms-learning-management-system"),options:v}),(0,a.createElement)(Lt,{name:l,label:s.__("Transform","masterstudy-lms-learning-management-system"),options:y}),(0,a.createElement)(Lt,{name:r,label:s.__("Style","masterstudy-lms-learning-management-system"),options:_}),(0,a.createElement)(Lt,{name:i,label:s.__("Decoration","masterstudy-lms-learning-management-system"),options:E}),(0,a.createElement)(gt,{name:o,label:s.__("Line Height","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:u,isAdaptive:b}),(0,a.createElement)(gt,{name:m,label:s.__("Letter Spacing","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:g,isAdaptive:b}),c&&(0,a.createElement)(gt,{name:c,label:s.__("Word Spacing","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:p,isAdaptive:b})):null},Ft=(T("file-upload"),T("file-upload__wrap"),T("file-upload__image"),T("file-upload__remove"),T("file-upload__replace"),(0,O.createContext)({activeTab:0,setActiveTab:()=>{}})),Ut=()=>{const e=(0,O.useContext)(Ft);if(!e)throw new Error("useTabs should be used inside Tabs");return e},It=({children:e})=>{const[t,n]=(0,O.useState)(0);return(0,a.createElement)(Ft.Provider,{value:{activeTab:t,setActiveTab:n}},(0,a.createElement)("div",{className:`active-tab-${t}`},e))},Zt=T("tab-list"),zt=({children:e})=>(0,a.createElement)("div",{className:Zt},O.Children.map(e,((e,t)=>(0,O.cloneElement)(e,{index:t})))),Gt=T("tab"),$t=T("tab-active"),jt=T("content"),Wt=({index:e,title:t,icon:n})=>{const{activeTab:l,setActiveTab:s}=Ut();return(0,a.createElement)("div",{className:m()([Gt],{[$t]:l===e}),onClick:()=>s(e)},(0,a.createElement)("div",{className:jt},(0,a.createElement)("div",null,n),(0,a.createElement)("div",null,t)))},Kt=({children:e})=>(0,a.createElement)("div",null,O.Children.map(e,((e,t)=>(0,O.cloneElement)(e,{index:t})))),Xt=T("tab-panel"),Yt=({index:e,children:t})=>{const{activeTab:n}=Ut();return n===e?(0,a.createElement)("div",{className:Xt},t):null},qt=({generalTab:e,styleTab:t,advancedTab:n})=>(0,a.createElement)(It,null,(0,a.createElement)(zt,null,(0,a.createElement)(Wt,{title:s.__("General","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(i.Dashicon,{icon:"layout"})}),(0,a.createElement)(Wt,{title:s.__("Style","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(i.Dashicon,{icon:"admin-appearance"})}),(0,a.createElement)(Wt,{title:s.__("Advanced","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(i.Dashicon,{icon:"admin-settings"})})),(0,a.createElement)(Kt,null,(0,a.createElement)(Yt,null,e),(0,a.createElement)(Yt,null,t),(0,a.createElement)(Yt,null,n)));window.ReactDOM;"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement;function Jt(e){const t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function Qt(e){return"nodeType"in e}function en(e){var t,n;return e?Jt(e)?e:Qt(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function tn(e){const{Document:t}=en(e);return e instanceof t}function nn(e){return!Jt(e)&&e instanceof en(e).HTMLElement}function an(e){return e instanceof en(e).SVGElement}function ln(e){return e?Jt(e)?e.document:Qt(e)?tn(e)?e:nn(e)||an(e)?e.ownerDocument:document:document:document}function sn(e){return function(t){for(var n=arguments.length,a=new Array(n>1?n-1:0),l=1;l<n;l++)a[l-1]=arguments[l];return a.reduce(((t,n)=>{const a=Object.entries(n);for(const[n,l]of a){const a=t[n];null!=a&&(t[n]=a+e*l)}return t}),{...t})}}const rn=sn(-1);function on(e){if(function(e){if(!e)return!1;const{TouchEvent:t}=en(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return function(e){return"clientX"in e&&"clientY"in e}(e)?{x:e.clientX,y:e.clientY}:null}var mn;!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(mn||(mn={}));const cn=Object.freeze({x:0,y:0});var dn,un,gn,pn;!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(dn||(dn={}));class Cn{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach((e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)}))},this.target=e}add(e,t,n){var a;null==(a=this.target)||a.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function hn(e,t){const n=Math.abs(e.x),a=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+a**2)>t:"x"in t&&"y"in t?n>t.x&&a>t.y:"x"in t?n>t.x:"y"in t&&a>t.y}function bn(e){e.preventDefault()}function vn(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(un||(un={})),(pn=gn||(gn={})).Space="Space",pn.Down="ArrowDown",pn.Right="ArrowRight",pn.Left="ArrowLeft",pn.Up="ArrowUp",pn.Esc="Escape",pn.Enter="Enter";gn.Space,gn.Enter,gn.Esc,gn.Space,gn.Enter;function yn(e){return Boolean(e&&"distance"in e)}function En(e){return Boolean(e&&"delay"in e)}class fn{constructor(e,t,n){var a;void 0===n&&(n=function(e){const{EventTarget:t}=en(e);return e instanceof t?e:ln(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;const{event:l}=e,{target:s}=l;this.props=e,this.events=t,this.document=ln(s),this.documentListeners=new Cn(this.document),this.listeners=new Cn(n),this.windowListeners=new Cn(en(s)),this.initialCoordinates=null!=(a=on(l))?a:cn,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),this.windowListeners.add(un.Resize,this.handleCancel),this.windowListeners.add(un.DragStart,bn),this.windowListeners.add(un.VisibilityChange,this.handleCancel),this.windowListeners.add(un.ContextMenu,bn),this.documentListeners.add(un.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(En(t))return void(this.timeoutId=setTimeout(this.handleStart,t.delay));if(yn(t))return}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handleStart(){const{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(un.Click,vn,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(un.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;const{activated:n,initialCoordinates:a,props:l}=this,{onMove:s,options:{activationConstraint:r}}=l;if(!a)return;const i=null!=(t=on(e))?t:cn,o=rn(a,i);if(!n&&r){if(yn(r)){if(null!=r.tolerance&&hn(o,r.tolerance))return this.handleCancel();if(hn(o,r.distance))return this.handleStart()}return En(r)&&hn(o,r.tolerance)?this.handleCancel():void 0}e.cancelable&&e.preventDefault(),s(i)}handleEnd(){const{onEnd:e}=this.props;this.detach(),e()}handleCancel(){const{onCancel:e}=this.props;this.detach(),e()}handleKeydown(e){e.code===gn.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}const Nn={move:{name:"pointermove"},end:{name:"pointerup"}};(class extends fn{constructor(e){const{event:t}=e,n=ln(t.target);super(e,Nn,n)}}).activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;return!(!n.isPrimary||0!==n.button||(null==a||a({event:n}),0))}}];const wn={move:{name:"mousemove"},end:{name:"mouseup"}};var Sn;!function(e){e[e.RightClick=2]="RightClick"}(Sn||(Sn={})),class extends fn{constructor(e){super(e,wn,ln(e.event.target))}}.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;return n.button!==Sn.RightClick&&(null==a||a({event:n}),!0)}}];const Mn={move:{name:"touchmove"},end:{name:"touchend"}};var xn,Tn,On,An,kn;(class extends fn{constructor(e){super(e,Mn)}static setup(){return window.addEventListener(Mn.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(Mn.move.name,e)};function e(){}}}).activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;const{touches:l}=n;return!(l.length>1||(null==a||a({event:n}),0))}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(xn||(xn={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(Tn||(Tn={})),dn.Backward,dn.Forward,dn.Backward,dn.Forward,function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(On||(On={})),function(e){e.Optimized="optimized"}(An||(An={})),On.WhileDragging,An.Optimized,Map,function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(kn||(kn={})),gn.Down,gn.Right,gn.Up,gn.Left,s.__("Lectures","masterstudy-lms-learning-management-system"),s.__("Duration","masterstudy-lms-learning-management-system"),s.__("Views","masterstudy-lms-learning-management-system"),s.__("Level","masterstudy-lms-learning-management-system"),s.__("Members","masterstudy-lms-learning-management-system"),s.__("Empty","masterstudy-lms-learning-management-system"),T("sortable__item"),T("sortable__item__disabled"),T("sortable__item__content"),T("sortable__item__content__drag-item"),T("sortable__item__content__drag-item__disabled"),T("sortable__item__content__title"),T("sortable__item__control"),T("sortable__item__icon"),T("nested-sortable"),T("nested-sortable__item"),T("sortable");const Dn=T("accordion"),Rn=T("accordion__header"),Hn=T("accordion__header-flex"),Vn=T("accordion__content"),Ln=T("accordion__icon"),Pn=T("accordion__title"),Bn=T("accordion__title-disabled"),Fn=T("accordion__indicator"),Un=T("accordion__controls"),In=T("accordion__controls-disabled"),Zn=({title:e,children:t,accordionFields:n,switchName:l,visible:s=!0,isDefaultOpen:r=!1})=>{const{isOpen:o,onToggle:c,disabled:d,onReset:u,hasChanges:g,onClose:h}=((e,t,n)=>{var a;const{isOpen:l,onToggle:s,onClose:r}=D(t),{defaultValues:i,attributes:o,setAttributes:m}=V(),c=((e,t,n)=>{for(const a of n)if(!C(e[a],t[a]))return!0;return!1})(i,o,e);return{isOpen:l,onToggle:s,disabled:!(null===(a=o[n])||void 0===a||a),hasChanges:c,onReset:t=>{t.stopPropagation(),m(e.reduce(((e,t)=>(e[t]=i[t],e)),{}))},onClose:r}})(n,r,l);return((e,t)=>{const{attributes:n}=V(),a=!n[t];(0,O.useEffect)((()=>{a&&e()}),[a,e])})(h,l),s?(0,a.createElement)("div",{className:Dn},(0,a.createElement)("div",{className:Rn},(0,a.createElement)("div",{className:Hn,onClick:d?null:c},(0,a.createElement)("div",{className:m()(Pn,{[Bn]:d,"with-switch":Boolean(l)})},(0,a.createElement)("div",null,e),(0,a.createElement)(p,{condition:g&&!d},(0,a.createElement)("div",{className:Fn}))),(0,a.createElement)("div",{className:m()(Un,{[In]:d})},(0,a.createElement)(i.Dashicon,{icon:o?"arrow-up-alt2":"arrow-down-alt2",className:Ln,size:16}))),(0,a.createElement)(p,{condition:Boolean(l)},(0,a.createElement)(Ct,{name:l})),(0,a.createElement)(p,{condition:g&&!d},(0,a.createElement)(Se,{onReset:u}))),o&&(0,a.createElement)("div",{className:Vn},t)):null};T("preset-picker"),T("preset-picker__label"),T("preset-picker__remove"),T("preset-picker__presets-list"),T("preset-picker__presets-list__item"),T("preset-picker__presets-list__item__preset"),T("preset-picker__presets-list__item__preset-active");const zn=window.wp.apiFetch;var Gn=n.n(zn);const $n={tabCategoryIncluded:!0,tabCategoryValues:[],tabCategoryPosition:"center",tabCategoryViewType:"buttons"},jn=Object.keys($n),Wn={...$n},Kn={fontSize:14,fontSizeTablet:null,fontSizeMobile:null,fontSizeUnit:"px",fontSizeUnitTablet:"px",fontSizeUnitMobile:"px",fontWeight:"500",textTransform:"inherit",fontStyle:"inherit",textDecoration:"inherit",lineHeight:14,lineHeightTablet:null,lineHeightMobile:null,lineHeightUnit:"px",lineHeightUnitTablet:"px",lineHeightUnitMobile:"px",letterSpacing:0,letterSpacingTablet:null,letterSpacingMobile:null,letterSpacingUnit:"px",letterSpacingUnitTablet:"px",letterSpacingUnitMobile:"px",wordSpacing:0,wordSpacingTablet:null,wordSpacingMobile:null,wordSpacingUnit:"px",wordSpacingUnitTablet:"px",wordSpacingUnitMobile:"px",tabsBackground:"#DAE5F8",background:"#DAE5F8",backgroundHover:"#438EFF",color:"#227AFF",colorHover:"#ffffff",borderStyle:"none",borderStyleTablet:"",borderStyleMobile:"",borderColor:"",borderColorTablet:"",borderColorMobile:"",borderWidth:x,borderWidthTablet:x,borderWidthMobile:x,borderWidthUnit:"px",borderWidthUnitTablet:"px",borderWidthUnitMobile:"px",borderRadius:{top:"20",right:"20",bottom:"20",left:"20"},borderRadiusTablet:x,borderRadiusMobile:x,borderRadiusUnit:"px",borderRadiusUnitTablet:"px",borderRadiusUnitMobile:"px",margin:{top:"0",right:"0",bottom:"50",left:"0"},marginTablet:x,marginMobile:x,marginUnit:"px",marginUnitTablet:"px",marginUnitMobile:"px",paddingGroup:x,paddingGroupTablet:x,paddingGroupMobile:x,paddingGroupUnit:"px",paddingGroupUnitTablet:"px",paddingGroupUnitMobile:"px",padding:{top:"11",right:"20",bottom:"11",left:"20"},paddingTablet:x,paddingMobile:x,paddingUnit:"px",paddingUnitTablet:"px",paddingUnitMobile:"px",itemsGap:10,itemsGapTablet:null,itemsGapMobile:null,itemsGapUnit:"px",itemsGapUnitTablet:"px",itemsGapUnitMobile:"px"},Xn=Object.keys(Kn),Yn={...Wn,...{...Kn}},qn=new Map([["fontSize",{unit:"fontSizeUnit",isAdaptive:!0}],["fontWeight",{}],["textTransform",{}],["fontStyle",{}],["textDecoration",{}],["lineHeight",{unit:"lineHeightUnit",isAdaptive:!0}],["letterSpacing",{unit:"letterSpacingUnit",isAdaptive:!0}],["wordSpacing",{unit:"wordSpacingUnit",isAdaptive:!0}],["tabsBackground",{}],["background",{hasHover:!0}],["color",{hasHover:!0}],["borderStyle",{isAdaptive:!0}],["borderColor",{isAdaptive:!0}],["borderWidth",{isAdaptive:!0,unit:"borderWidthUnit"}],["borderRadius",{isAdaptive:!0,unit:"borderRadiusUnit"}],["margin",{unit:"marginUnit",isAdaptive:!0}],["paddingGroup",{unit:"paddingGroupUnit",isAdaptive:!0}],["padding",{unit:"paddingUnit",isAdaptive:!0}],["itemsGap",{unit:"itemsGapUnit",isAdaptive:!0}]]),Jn=({categories:e})=>(0,a.createElement)(Zn,{title:s.__("Filter by Category","masterstudy-lms-learning-management-system"),accordionFields:jn,isDefaultOpen:!0,switchName:"tabCategoryIncluded"},(0,a.createElement)(Qe,{name:"tabCategoryViewType",label:s.__("Filter","masterstudy-lms-learning-management-system"),options:[{label:"Buttons",value:"buttons"},{label:"Tabs",value:"tabs"},{label:"Select",value:"select"}]}),(0,a.createElement)(Rt,{name:"tabCategoryValues",label:s.__("Options","masterstudy-lms-learning-management-system"),options:e,multiple:!0}),(0,a.createElement)(Qe,{name:"tabCategoryPosition",label:s.__("Filter position","masterstudy-lms-learning-management-system"),options:[{label:(0,a.createElement)(i.Dashicon,{icon:"align-pull-left"}),value:"start"},{label:(0,a.createElement)(i.Dashicon,{icon:"align-center"}),value:"center"},{label:(0,a.createElement)(i.Dashicon,{icon:"align-pull-right"}),value:"end"}]})),Qn=()=>{const{attributes:e}=V();return(0,a.createElement)(Zn,{title:s.__("Filter by Category","masterstudy-lms-learning-management-system"),accordionFields:Xn,visible:Boolean(e.tabCategoryIncluded)},(0,a.createElement)(Bt,{fontSizeName:"fontSize",fontSizeUnitName:"fontSizeUnit",fontWeightName:"fontWeight",textTransformName:"textTransform",fontStyleName:"fontStyle",textDecorationName:"textDecoration",lineHeightName:"lineHeight",lineHeightUnitName:"lineHeightUnit",letterSpacingName:"letterSpacing",letterSpacingUnitName:"letterSpacingUnit",wordSpacingName:"wordSpacing",wordSpacingUnitName:"wordSpacingUnit",isAdaptive:!0}),(0,a.createElement)(Ue,{name:"tabsBackground",label:s.__("Tabs background","masterstudy-lms-learning-management-system"),placeholder:s.__("Select color","masterstudy-lms-learning-management-system"),dependencies:[{name:"tabCategoryViewType",value:"tabs"}]}),(0,a.createElement)(Ue,{name:"background",label:s.__("Background","masterstudy-lms-learning-management-system"),placeholder:s.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,a.createElement)(Ue,{name:"color",label:s.__("Color","masterstudy-lms-learning-management-system"),placeholder:s.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,a.createElement)(lt,{label:s.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"borderStyle",borderColorName:"borderColor",borderWidthName:"borderWidth",isAdaptive:!0}),(0,a.createElement)(it,{name:"borderRadius",label:s.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,a.createElement)(Ke,{name:"margin",label:s.__("Margin","masterstudy-lms-learning-management-system"),unitName:"marginUnit",isAdaptive:!0}),(0,a.createElement)(Ke,{name:"paddingGroup",label:s.__("Tabs Group Padding","masterstudy-lms-learning-management-system"),unitName:"paddingGroupUnit",isAdaptive:!0,dependencies:[{name:"tabCategoryViewType",value:"tabs"}]}),(0,a.createElement)(Ke,{name:"padding",label:s.__("Padding","masterstudy-lms-learning-management-system"),unitName:"paddingUnit",isAdaptive:!0}),(0,a.createElement)(vt,{name:"itemsGap",label:s.__("Space between items","masterstudy-lms-learning-management-system"),unitName:"itemsGapUnit",isAdaptive:!0,dependencies:[{name:"tabCategoryViewType",value:["tabs","buttons"]}]}))},ea=({attributes:e,setAttributes:t,categories:n})=>{const{onResetByFieldName:l,changedFieldsByName:s}=((e,t,n,a=[])=>{const l=(e=>{const t={};return Object.entries(e).forEach((([e,n])=>{e.includes("UAG")||(t[e]=n)})),t})(t),s=!C(e,l),r=((e,t,n)=>{const a=new Map;return n.forEach((n=>{a.has(n)||a.set(n,(()=>t({[n]:e[n]})))})),a})(e,n,a),i=((e,t,n)=>{const a=new Map;return n.forEach((n=>{a.has(n)?a.set(n,!1):a.set(n,!C(e[n],t[n]))})),a})(e,l,a);return{hasChanges:s,onResetByFieldName:r,changedFieldsByName:i}})(Yn,e,t,Object.keys(Yn));return(0,a.createElement)(r.InspectorControls,null,(0,a.createElement)(H,{attributes:e,setAttributes:t,defaultValues:Yn,onResetByFieldName:l,changedFieldsByName:s},(0,a.createElement)(qt,{generalTab:(0,a.createElement)(Jn,{categories:n}),styleTab:(0,a.createElement)(Qn,null),advancedTab:(0,a.createElement)(a.Fragment,null)})))},ta=JSON.parse('{"UU":"masterstudy/courses-tab-category"}');(0,l.registerBlockType)(ta.UU,{icon:{src:(0,a.createElement)("svg",{width:"512",height:"512",viewBox:"0 0 512 512",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,a.createElement)("rect",{opacity:"0.3",x:"38.5376",y:"234.079",width:"84.9566",height:"69.2645",rx:"20",fill:"#227AFF"}),(0,a.createElement)("rect",{opacity:"0.3",x:"164.538",y:"234.079",width:"84.9566",height:"69.2645",rx:"20",fill:"#227AFF"}),(0,a.createElement)("rect",{opacity:"0.3",x:"290.538",y:"234.079",width:"84.9566",height:"69.2645",rx:"20",fill:"#227AFF"}),(0,a.createElement)("rect",{opacity:"0.3",x:"416.538",y:"234.079",width:"84.9566",height:"69.2645",rx:"20",fill:"#227AFF"}),(0,a.createElement)("path",{d:"M146.748 302.806C144.065 302.165 141.643 300.716 139.808 298.655C137.212 295.635 136.1 292.033 136.1 288.086V230.883C136.1 226.141 135.57 221.028 137.61 216.631C139.941 211.571 145.318 208.684 150.749 208.658H234.976C235.033 208.658 235.089 208.658 235.146 208.658C235.259 208.658 235.371 208.661 235.481 208.668C239.205 208.82 242.73 210.39 245.332 213.058C247.935 215.726 249.418 219.288 249.477 223.015C249.53 226.564 249.477 230.114 249.477 233.663V288.827C249.478 291.936 248.488 294.964 246.648 297.47C244.809 299.976 242.218 301.83 239.252 302.761C237.292 303.344 235.305 303.344 233.292 303.344H151.146C149.663 303.349 148.185 303.168 146.748 302.806L146.748 302.806ZM151.04 292.746H232.205C233.185 292.746 234.192 292.773 235.173 292.746C235.333 292.746 235.831 292.686 235.895 292.695C235.998 292.672 236.086 292.652 236.126 292.64C236.245 292.61 236.623 292.464 236.805 292.402C236.992 292.298 237.196 292.186 237.265 292.136C237.348 292.07 237.431 292.004 237.521 291.944C237.72 291.744 237.983 291.455 238.035 291.421L238.093 291.369C238.258 291.092 238.393 290.785 238.565 290.516C238.598 290.45 238.625 290.38 238.645 290.309C238.708 290.103 238.762 289.897 238.812 289.691C238.808 289.551 238.859 289.306 238.859 289.223V288.879C238.859 288.824 238.859 288.772 238.859 288.722C238.859 288.222 238.859 287.512 238.859 286.535V222.962C238.859 222.899 238.856 222.835 238.852 222.772C238.751 222.489 238.742 222.12 238.674 221.849C238.621 221.69 238.567 221.505 238.515 221.346L238.511 221.329C238.356 221.071 238.172 220.792 238.144 220.736C238.129 220.718 238.115 220.698 238.103 220.678C237.855 220.473 237.626 220.245 237.421 219.997C237.399 219.983 237.382 219.973 237.375 219.968C237.317 219.939 237.02 219.751 236.754 219.594C236.587 219.553 236.422 219.5 236.262 219.437C236.118 219.401 235.947 219.376 235.775 219.347C235.576 219.372 235.376 219.384 235.176 219.384H150.457C150.44 219.384 150.122 219.373 149.9 219.38C149.823 219.395 149.746 219.414 149.663 219.437C149.455 219.5 149.115 219.677 148.85 219.737C148.748 219.802 148.636 219.858 148.523 219.914C148.364 219.993 148.231 220.101 148.072 220.179L148.066 220.183L148.047 220.207L148.028 220.231C148.032 220.221 148.036 220.212 148.039 220.201C147.852 220.322 147.839 220.396 147.884 220.389C147.819 220.455 147.749 220.517 147.676 220.576C147.664 220.588 147.592 220.66 147.517 220.737C147.356 221.011 147.171 221.285 147.003 221.559L146.988 221.609C146.916 221.923 146.821 222.237 146.745 222.551C146.745 222.584 146.748 222.623 146.749 222.668V289.039C146.79 289.284 146.803 289.533 146.788 289.781C146.792 289.798 146.797 289.816 146.802 289.834C146.865 290.042 147.042 290.382 147.102 290.646C147.167 290.753 147.226 290.862 147.279 290.974C147.359 291.133 147.466 291.265 147.544 291.424L147.547 291.43L147.57 291.451L147.594 291.47L147.565 291.458C147.686 291.645 147.76 291.659 147.752 291.614C147.819 291.679 147.881 291.749 147.939 291.822C147.952 291.834 148.023 291.906 148.1 291.98C148.375 292.141 148.648 292.327 148.922 292.495L148.973 292.51C149.287 292.582 149.6 292.676 149.914 292.753C149.948 292.753 149.987 292.749 150.032 292.748L151.04 292.746ZM148.311 292.163C148.325 292.156 148.268 292.112 148.186 292.058C148.223 292.098 148.265 292.134 148.311 292.164V292.163ZM237.291 292.136C237.308 292.145 237.38 292.082 237.473 291.991C237.363 292.065 237.274 292.129 237.291 292.138V292.136ZM147.014 290.521C147.03 290.541 147.046 290.562 147.061 290.583C147.016 290.467 146.954 290.358 146.879 290.259C146.912 290.352 146.957 290.441 147.014 290.522V290.521ZM238.587 221.483C238.642 221.645 238.7 221.806 238.722 221.796C238.753 221.776 238.678 221.624 238.573 221.442L238.587 221.483ZM147.332 220.949C147.338 220.962 147.381 220.906 147.435 220.825C147.396 220.862 147.361 220.904 147.332 220.949ZM237.983 220.5C237.945 220.432 237.9 220.37 237.847 220.313C237.812 220.313 237.887 220.401 237.983 220.5ZM237.796 220.26C237.733 220.199 237.663 220.147 237.588 220.103C237.696 220.209 237.796 220.297 237.796 220.261V220.26ZM148.974 219.653C148.954 219.668 148.934 219.684 148.913 219.699C148.96 219.681 149.002 219.66 149.043 219.639C149.113 219.61 149.178 219.569 149.234 219.517C149.143 219.554 149.056 219.599 148.974 219.653ZM236.312 219.386C236.288 219.409 236.488 219.485 236.667 219.547C236.561 219.477 236.445 219.421 236.323 219.382C236.319 219.382 236.315 219.383 236.312 219.386ZM149.689 219.412C149.751 219.409 149.813 219.399 149.872 219.382C149.767 219.386 149.689 219.395 149.689 219.412ZM108.871 303.318H24.7097C19.4385 303.29 14.2192 300.641 11.7293 295.82C10.3784 293.171 10.0078 290.416 10.0078 287.529V224.048C10.0078 220.445 10.829 217.079 13.0802 214.167C15.9683 210.432 20.3388 208.656 24.9745 208.656H107.014C108.895 208.656 110.749 208.63 112.629 209.106C119.014 210.668 123.305 216.684 123.358 223.121C123.385 227.174 123.358 231.2 123.358 235.226V285.78C123.358 286.84 123.385 287.926 123.358 288.986C123.309 292.704 121.834 296.262 119.24 298.926C116.645 301.59 113.127 303.157 109.411 303.304C109.301 303.312 109.191 303.316 109.081 303.316H109.054H108.96L108.871 303.318ZM24.3119 292.721H109.05C109.05 292.721 109.796 292.716 109.822 292.562C109.703 292.575 109.586 292.597 109.47 292.626C109.21 292.681 108.956 292.736 109.318 292.642C109.663 292.535 110.007 292.483 110.352 292.377C110.422 292.358 110.492 292.335 110.559 292.305C110.793 292.165 111.068 292.051 111.305 291.9C111.324 291.885 111.344 291.871 111.365 291.859C111.569 291.612 111.797 291.384 112.045 291.181C112.059 291.158 112.069 291.141 112.074 291.134C112.197 290.909 112.319 290.653 112.453 290.426C112.493 290.347 112.525 290.265 112.55 290.18C112.613 289.974 112.667 289.768 112.718 289.562C112.714 289.422 112.765 289.177 112.765 289.094V288.697C112.765 288.601 112.765 288.512 112.765 288.431C112.765 287.912 112.765 287.069 112.765 285.77V222.881C112.765 222.741 112.705 222.252 112.714 222.186C112.69 222.083 112.67 221.995 112.658 221.954C112.634 221.856 112.529 221.58 112.459 221.384C112.371 221.218 112.275 221.034 112.235 220.975C112.157 220.837 112.048 220.701 111.957 220.553C111.757 220.355 111.474 220.097 111.442 220.047C111.421 220.025 111.403 220.005 111.387 219.986C111.142 219.835 110.88 219.698 110.633 219.548L110.54 219.491C110.16 219.26 110.274 219.368 110.477 219.476C110.502 219.489 110.528 219.502 110.555 219.515C110.49 219.483 110.423 219.457 110.354 219.438C110.148 219.375 109.942 219.321 109.736 219.27C109.596 219.275 109.351 219.224 109.268 219.224H109.081C105.717 219.171 102.379 219.224 99.0412 219.224H24.3119C24.5422 219.224 24.3274 219.231 24.1 219.238C23.8192 219.332 23.4641 219.343 23.1997 219.409C23.0406 219.462 22.8553 219.515 22.6962 219.568H22.691C22.4763 219.693 22.2591 219.832 22.2194 219.858C22.1918 219.877 22.1127 219.924 22.0214 219.98C21.8113 220.237 21.5762 220.472 21.3195 220.682C21.3055 220.705 21.2957 220.722 21.2915 220.729C21.1614 220.967 21.0318 221.24 20.8872 221.478C20.8511 221.6 20.7992 221.727 20.7613 221.842C20.6963 222.103 20.6677 222.45 20.5849 222.731C20.5797 222.806 20.5755 222.881 20.5755 222.956V288.981C20.5755 289.044 20.5788 289.106 20.5825 289.168C20.6832 289.451 20.693 289.82 20.7613 290.091C20.8137 290.25 20.867 290.436 20.9199 290.595V290.601C21.0453 290.815 21.1838 291.032 21.21 291.072C21.2288 291.099 21.276 291.178 21.3322 291.27C21.5889 291.48 21.8241 291.715 22.0341 291.972C22.057 291.986 22.0743 291.996 22.0809 292.001C22.3186 292.13 22.5918 292.26 22.8295 292.404C22.9517 292.44 23.0804 292.492 23.195 292.531C23.4547 292.596 23.8033 292.624 24.0813 292.707C24.1617 292.717 24.2371 292.721 24.3119 292.721ZM21.663 291.714C21.7258 291.774 21.7957 291.827 21.8712 291.87C21.8141 291.804 21.7462 291.748 21.6705 291.705C21.6658 291.705 21.663 291.708 21.663 291.714ZM111.541 291.744C111.609 291.706 111.672 291.66 111.728 291.608C111.728 291.601 111.725 291.599 111.721 291.599C111.653 291.636 111.592 291.685 111.541 291.744ZM21.6125 291.661C21.6532 291.661 21.5493 291.548 21.4328 291.427C21.51 291.553 21.5858 291.661 21.6125 291.661ZM111.782 291.556C111.843 291.493 111.896 291.423 111.939 291.347C111.832 291.456 111.744 291.556 111.782 291.556ZM20.7098 290.152C20.6883 290.173 20.7566 290.314 20.8502 290.479L20.8446 290.463C20.818 290.352 20.773 290.246 20.7117 290.15L20.7098 290.152ZM112.153 220.789C112.161 220.772 112.098 220.7 112.006 220.606C112.08 220.717 112.144 220.807 112.153 220.789ZM21.4281 220.52C21.5343 220.411 21.6218 220.312 21.5839 220.312C21.5237 220.375 21.4714 220.445 21.4281 220.52ZM21.6368 220.26C21.6368 220.3 21.7496 220.196 21.8679 220.08C21.7444 220.159 21.6368 220.235 21.6368 220.261V220.26ZM22.8188 219.499C22.9854 219.443 23.1576 219.381 23.1463 219.359C23.1426 219.356 23.1379 219.355 23.1332 219.355C23.0219 219.387 22.9159 219.436 22.8188 219.499ZM147.594 291.469C147.699 291.516 147.747 291.579 147.753 291.612C147.701 291.562 147.648 291.513 147.594 291.469ZM148.028 220.231C147.981 220.337 147.916 220.385 147.884 220.39C147.935 220.338 147.984 220.285 148.028 220.231ZM110.552 219.515C110.578 219.526 110.603 219.538 110.629 219.548C110.603 219.538 110.577 219.527 110.552 219.515Z",fill:"black"}),(0,a.createElement)("path",{d:"M399.241 302.806C396.557 302.165 394.135 300.716 392.3 298.655C389.704 295.635 388.592 292.033 388.592 288.086V230.883C388.592 226.141 388.062 221.028 390.102 216.631C392.433 211.571 397.811 208.684 403.241 208.658H487.468C487.525 208.658 487.581 208.658 487.638 208.658C487.751 208.658 487.863 208.661 487.974 208.668C491.697 208.82 495.222 210.39 497.825 213.058C500.427 215.726 501.91 219.288 501.969 223.015C502.022 226.564 501.969 230.114 501.969 233.663V288.827C501.971 291.936 500.98 294.964 499.141 297.47C497.302 299.976 494.71 301.83 491.744 302.761C489.784 303.344 487.797 303.344 485.784 303.344H403.638C402.155 303.349 400.678 303.168 399.24 302.806L399.241 302.806ZM403.532 292.746H484.698C485.678 292.746 486.684 292.773 487.665 292.746C487.825 292.746 488.323 292.686 488.387 292.695C488.49 292.672 488.578 292.652 488.618 292.64C488.737 292.61 489.115 292.464 489.297 292.402C489.484 292.298 489.688 292.186 489.757 292.136C489.84 292.07 489.924 292.004 490.013 291.944C490.212 291.744 490.475 291.455 490.528 291.421L490.586 291.369C490.75 291.092 490.885 290.785 491.057 290.516C491.09 290.45 491.117 290.38 491.137 290.309C491.2 290.103 491.254 289.897 491.304 289.691C491.3 289.551 491.351 289.306 491.351 289.223V288.879C491.351 288.824 491.351 288.772 491.351 288.722C491.351 288.222 491.351 287.512 491.351 286.535V222.962C491.351 222.899 491.348 222.835 491.344 222.772C491.244 222.489 491.234 222.12 491.166 221.849C491.113 221.69 491.06 221.505 491.007 221.346L491.003 221.329C490.848 221.071 490.664 220.792 490.636 220.736C490.621 220.718 490.608 220.698 490.595 220.678C490.347 220.473 490.119 220.245 489.914 219.997C489.891 219.983 489.874 219.973 489.867 219.968C489.809 219.939 489.512 219.751 489.246 219.594C489.079 219.553 488.915 219.5 488.754 219.437C488.61 219.401 488.439 219.376 488.267 219.347C488.069 219.372 487.868 219.384 487.668 219.384H402.949C402.932 219.384 402.614 219.373 402.393 219.38C402.315 219.395 402.238 219.414 402.155 219.437C401.947 219.5 401.607 219.677 401.343 219.737C401.24 219.802 401.128 219.858 401.015 219.914C400.856 219.993 400.724 220.101 400.564 220.179L400.558 220.183L400.539 220.207L400.52 220.231C400.524 220.221 400.528 220.212 400.532 220.201C400.345 220.322 400.331 220.396 400.376 220.389C400.311 220.455 400.241 220.517 400.169 220.576C400.156 220.588 400.084 220.66 400.009 220.737C399.849 221.011 399.663 221.285 399.495 221.559L399.48 221.609C399.408 221.923 399.314 222.237 399.237 222.551C399.237 222.584 399.241 222.623 399.242 222.668V289.039C399.282 289.284 399.295 289.533 399.28 289.781C399.285 289.798 399.289 289.816 399.294 289.834C399.357 290.042 399.535 290.382 399.594 290.646C399.659 290.753 399.718 290.862 399.771 290.974C399.851 291.133 399.958 291.265 400.036 291.424L400.039 291.43L400.062 291.451L400.087 291.47L400.057 291.458C400.178 291.645 400.252 291.659 400.244 291.614C400.311 291.679 400.373 291.749 400.432 291.822C400.444 291.834 400.515 291.906 400.593 291.98C400.867 292.141 401.14 292.327 401.415 292.495L401.465 292.51C401.779 292.582 402.092 292.676 402.406 292.753C402.44 292.753 402.479 292.749 402.524 292.748L403.532 292.746ZM400.804 292.163C400.818 292.156 400.76 292.112 400.678 292.058C400.716 292.098 400.758 292.134 400.804 292.164V292.163ZM489.784 292.136C489.8 292.145 489.872 292.082 489.965 291.991C489.855 292.065 489.766 292.129 489.784 292.138V292.136ZM399.506 290.521C399.522 290.541 399.538 290.562 399.553 290.583C399.508 290.467 399.447 290.358 399.371 290.259C399.404 290.352 399.449 290.441 399.506 290.522V290.521ZM491.079 221.483C491.134 221.645 491.193 221.806 491.214 221.796C491.245 221.776 491.171 221.624 491.065 221.442L491.079 221.483ZM399.824 220.949C399.83 220.962 399.873 220.906 399.927 220.825C399.888 220.862 399.853 220.904 399.824 220.949ZM490.475 220.5C490.438 220.432 490.392 220.37 490.339 220.313C490.304 220.313 490.379 220.401 490.475 220.5ZM490.288 220.26C490.225 220.199 490.155 220.147 490.08 220.103C490.188 220.209 490.288 220.297 490.288 220.261V220.26ZM401.466 219.653C401.446 219.668 401.426 219.684 401.405 219.699C401.452 219.681 401.494 219.66 401.535 219.639C401.605 219.61 401.67 219.569 401.726 219.517C401.635 219.554 401.548 219.599 401.466 219.653ZM488.804 219.386C488.78 219.409 488.98 219.485 489.159 219.547C489.053 219.477 488.937 219.421 488.815 219.382C488.811 219.382 488.807 219.383 488.804 219.386ZM402.182 219.412C402.243 219.409 402.305 219.399 402.365 219.382C402.259 219.386 402.182 219.395 402.182 219.412ZM361.364 303.318H277.202C271.931 303.29 266.711 300.641 264.222 295.82C262.871 293.171 262.5 290.416 262.5 287.529V224.048C262.5 220.445 263.321 217.079 265.572 214.167C268.46 210.432 272.831 208.656 277.467 208.656H359.506C361.387 208.656 363.241 208.63 365.122 209.106C371.506 210.668 375.797 216.684 375.85 223.121C375.877 227.174 375.85 231.2 375.85 235.226V285.78C375.85 286.84 375.877 287.926 375.85 288.986C375.801 292.704 374.327 296.262 371.732 298.926C369.137 301.59 365.619 303.157 361.903 303.304C361.793 303.312 361.683 303.316 361.573 303.316H361.546H361.453L361.364 303.318ZM276.804 292.721H361.542C361.542 292.721 362.288 292.716 362.314 292.562C362.196 292.575 362.078 292.597 361.963 292.626C361.702 292.681 361.448 292.736 361.811 292.642C362.155 292.535 362.499 292.483 362.844 292.377C362.915 292.358 362.984 292.335 363.051 292.305C363.285 292.165 363.56 292.051 363.797 291.9C363.816 291.885 363.836 291.871 363.857 291.859C364.062 291.612 364.29 291.384 364.538 291.181C364.552 291.158 364.561 291.141 364.566 291.134C364.689 290.909 364.811 290.653 364.946 290.426C364.985 290.347 365.017 290.265 365.043 290.18C365.106 289.974 365.159 289.768 365.21 289.562C365.206 289.422 365.257 289.177 365.257 289.094V288.697C365.257 288.601 365.257 288.512 365.257 288.431C365.257 287.912 365.257 287.069 365.257 285.77V222.881C365.257 222.741 365.197 222.252 365.206 222.186C365.182 222.083 365.162 221.995 365.151 221.954C365.126 221.856 365.021 221.58 364.951 221.384C364.863 221.218 364.767 221.034 364.727 220.975C364.649 220.837 364.54 220.701 364.449 220.553C364.249 220.355 363.966 220.097 363.934 220.047C363.913 220.025 363.895 220.005 363.879 219.986C363.634 219.835 363.372 219.698 363.125 219.548L363.032 219.491C362.652 219.26 362.766 219.368 362.97 219.476C362.994 219.489 363.02 219.502 363.047 219.515C362.983 219.483 362.916 219.457 362.847 219.438C362.64 219.375 362.434 219.321 362.228 219.27C362.088 219.275 361.843 219.224 361.76 219.224H361.573C358.209 219.171 354.871 219.224 351.533 219.224H276.804C277.034 219.224 276.82 219.231 276.592 219.238C276.311 219.332 275.956 219.343 275.692 219.409C275.533 219.462 275.347 219.515 275.188 219.568H275.183C274.968 219.693 274.751 219.832 274.712 219.858C274.684 219.877 274.605 219.924 274.514 219.98C274.303 220.237 274.068 220.472 273.812 220.682C273.798 220.705 273.788 220.722 273.784 220.729C273.654 220.967 273.524 221.24 273.379 221.478C273.343 221.6 273.291 221.727 273.253 221.842C273.188 222.103 273.16 222.45 273.077 222.731C273.072 222.806 273.068 222.881 273.068 222.956V288.981C273.068 289.044 273.071 289.106 273.075 289.168C273.175 289.451 273.185 289.82 273.253 290.091C273.306 290.25 273.359 290.436 273.412 290.595V290.601C273.538 290.815 273.676 291.032 273.702 291.072C273.721 291.099 273.768 291.178 273.824 291.27C274.081 291.48 274.316 291.715 274.526 291.972C274.549 291.986 274.566 291.996 274.573 292.001C274.811 292.13 275.084 292.26 275.322 292.404C275.444 292.44 275.573 292.492 275.687 292.531C275.947 292.596 276.295 292.624 276.573 292.707C276.654 292.717 276.729 292.721 276.804 292.721ZM274.155 291.714C274.218 291.774 274.288 291.827 274.363 291.87C274.306 291.804 274.238 291.748 274.163 291.705C274.158 291.705 274.155 291.708 274.155 291.714ZM364.033 291.744C364.101 291.706 364.164 291.66 364.22 291.608C364.22 291.601 364.218 291.599 364.213 291.599C364.145 291.636 364.084 291.685 364.033 291.744ZM274.105 291.661C274.145 291.661 274.041 291.548 273.925 291.427C274.002 291.553 274.078 291.661 274.105 291.661ZM364.275 291.556C364.335 291.493 364.388 291.423 364.431 291.347C364.324 291.456 364.236 291.556 364.275 291.556ZM273.202 290.152C273.18 290.173 273.249 290.314 273.342 290.479L273.337 290.463C273.31 290.352 273.265 290.246 273.204 290.15L273.202 290.152ZM364.645 220.789C364.653 220.772 364.59 220.7 364.498 220.606C364.572 220.717 364.636 220.807 364.645 220.789ZM273.92 220.52C274.027 220.411 274.114 220.312 274.076 220.312C274.016 220.375 273.964 220.445 273.92 220.52ZM274.129 220.26C274.129 220.3 274.242 220.196 274.36 220.08C274.237 220.159 274.129 220.235 274.129 220.261V220.26ZM275.311 219.499C275.478 219.443 275.65 219.381 275.639 219.359C275.635 219.356 275.63 219.355 275.625 219.355C275.514 219.387 275.408 219.436 275.311 219.499ZM400.086 291.469C400.191 291.516 400.24 291.579 400.245 291.612C400.193 291.562 400.14 291.513 400.086 291.469ZM400.52 220.231C400.473 220.337 400.409 220.385 400.376 220.39C400.427 220.338 400.476 220.285 400.52 220.231ZM363.044 219.515C363.07 219.526 363.095 219.538 363.121 219.548C363.095 219.538 363.069 219.527 363.044 219.515Z",fill:"black"}))},edit:({attributes:e,setAttributes:t})=>{const{isFetching:n,categories:l}=((e,t)=>{const{isFetching:n,categories:a,categoriesMap:l}=((e=!1)=>{const[t,n]=(0,O.useState)([]),[a,l]=(0,O.useState)({}),[s,r]=(0,O.useState)({}),{setIsFetching:i,setError:o,isFetching:m,error:c}=(()=>{const[e,t]=(0,O.useState)(!0),[n,a]=(0,O.useState)("");return{isFetching:e,setIsFetching:t,error:n,setError:a}})();return(0,O.useEffect)((()=>{i(!0),(async(e=!1)=>{try{let t="?children=true";return e&&(t+="&details=true"),await Gn()({path:`masterstudy-lms/v2/course-categories${t}`})}catch(e){throw new Error(e)}})(e).then((({categories:e})=>{n((e=>e.map((e=>({label:e.name,value:e.id,image:e.image,icon:e.icon,color:e.color,children:e.children?h(e.children):[]}))))(e)),l(e.reduce(((e,t)=>(e[String(t.id)]=t.name,e)),{})),r(e.reduce(((e,t)=>(e[String(t.id)]={label:t.name,value:t.id,image:t.image,icon:t.icon,color:t.color,courses:t.courses,children:t.children},e)),{}))})).catch((e=>{o(e.message)})).finally((()=>{i(!1)}))}),[]),{categories:t,categoriesMap:a,categoriesMapFull:s,isFetching:m,error:c}})();return(0,O.useEffect)((()=>{e.tabCategoryValues.length?t({tabCategoryOptions:e.tabCategoryValues.map((e=>({value:e,label:l[e]})))}):t({tabCategoryOptions:[]})}),[e.tabCategoryValues,l,t]),{isFetching:n,categories:a}})(e,t),o=(0,r.useBlockProps)({className:`lms-courses-tab-container align-items-${e.tabCategoryPosition||"center"}`,style:S("fcat",e,qn)});return(0,a.createElement)(a.Fragment,null,(0,a.createElement)(ea,{attributes:e,setAttributes:t,categories:l}),e.tabCategoryIncluded&&(0,a.createElement)("div",{...o},(0,a.createElement)(p,{condition:!n,fallback:(0,a.createElement)(i.Spinner,null)},(0,a.createElement)(p,{condition:e.tabCategoryOptions.length>0,fallback:(0,a.createElement)(g,{message:s.__("There are no categories selected to display here","masterstudy-lms-learning-management-system")})},(()=>{switch(e.tabCategoryViewType){case"tabs":return(0,a.createElement)(u,{options:e.tabCategoryOptions,className:"lms-courses-category-tabs-container",tabClassName:"lms-courses-category-tab"},(0,a.createElement)("button",{type:"button",className:"lms-courses-tab-tab lms-courses-tab-sorter lms-courses-category-tab is-selected","data-value":e.tabCategoryValues},s.__("All Categories","masterstudy-lms-learning-management-system")));case"select":return(0,a.createElement)(d,{options:e.tabCategoryOptions,className:"lms-courses-category-select"},(0,a.createElement)("option",{className:"lms-courses-category-select-option lms-courses-tab-option lms-courses-tab-sorter",selected:!0,"data-value":e.tabCategoryValues},s.__("All Categories","masterstudy-lms-learning-management-system")));default:return(0,a.createElement)(c,{options:e.tabCategoryOptions,className:"lms-courses-category-buttons-container lms-courses-tab-buttons",buttonClassName:"lms-courses-category-button"},(0,a.createElement)("button",{type:"button",className:"lms-courses-tab-button lms-courses-tab-sorter lms-courses-category-button is-selected","data-value":e.tabCategoryValues},s.__("All Categories","masterstudy-lms-learning-management-system")))}})()))))},save:({attributes:e})=>{if(!e.tabCategoryIncluded||e.tabCategoryOptions.length<1)return null;const t=r.useBlockProps.save({className:`lms-courses-tab-container align-items-${e.tabCategoryPosition||"center"}`,style:S("fcat",e,qn)}),n=e.tabCategoryOptions.map((e=>e.value)).toString();return(0,a.createElement)("div",{...t},(()=>{switch(e.tabCategoryViewType){case"tabs":return(0,a.createElement)(u,{options:e.tabCategoryOptions,className:"lms-courses-category-tabs-container",tabClassName:"lms-courses-category-tab"},(0,a.createElement)("button",{type:"button",className:"lms-courses-tab-tab lms-courses-tab-sorter lms-courses-category-tab is-selected","data-value":n},s.__("All Categories","masterstudy-lms-learning-management-system")));case"select":return(0,a.createElement)(d,{options:e.tabCategoryOptions,className:"lms-courses-category-select"},(0,a.createElement)("option",{className:"lms-courses-category-select-option lms-courses-tab-option lms-courses-tab-sorter",selected:!0,"data-value":n},s.__("All Categories","masterstudy-lms-learning-management-system")));default:return(0,a.createElement)(c,{options:e.tabCategoryOptions,className:"lms-courses-category-buttons-container lms-courses-tab-buttons",buttonClassName:"lms-courses-category-button"},(0,a.createElement)("button",{type:"button",className:"lms-courses-tab-button lms-courses-tab-sorter lms-courses-category-button is-selected","data-value":n},s.__("All Categories","masterstudy-lms-learning-management-system")))}})())}})},6942:(e,t)=>{var n;!function(){"use strict";var a={}.hasOwnProperty;function l(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=r(e,s(n)))}return e}function s(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return l.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)a.call(e,n)&&e[n]&&(t=r(t,n));return t}function r(e,t){return t?e?e+" "+t:e+t:e}e.exports?(l.default=l,e.exports=l):void 0===(n=function(){return l}.apply(t,[]))||(e.exports=n)}()}},n={};function a(e){var l=n[e];if(void 0!==l)return l.exports;var s=n[e]={exports:{}};return t[e](s,s.exports,a),s.exports}a.m=t,e=[],a.O=(t,n,l,s)=>{if(!n){var r=1/0;for(c=0;c<e.length;c++){for(var[n,l,s]=e[c],i=!0,o=0;o<n.length;o++)(!1&s||r>=s)&&Object.keys(a.O).every((e=>a.O[e](n[o])))?n.splice(o--,1):(i=!1,s<r&&(r=s));if(i){e.splice(c--,1);var m=l();void 0!==m&&(t=m)}}return t}s=s||0;for(var c=e.length;c>0&&e[c-1][2]>s;c--)e[c]=e[c-1];e[c]=[n,l,s]},a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={5194:0,78:0};a.O.j=t=>0===e[t];var t=(t,n)=>{var l,s,[r,i,o]=n,m=0;if(r.some((t=>0!==e[t]))){for(l in i)a.o(i,l)&&(a.m[l]=i[l]);if(o)var c=o(a)}for(t&&t(n);m<r.length;m++)s=r[m],a.o(e,s)&&e[s]&&e[s][0](),e[s]=0;return a.O(c)},n=globalThis.webpackChunkmasterstudy_lms_learning_management_system=globalThis.webpackChunkmasterstudy_lms_learning_management_system||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})();var l=a.O(void 0,[78],(()=>a(1357)));l=a.O(l)})();