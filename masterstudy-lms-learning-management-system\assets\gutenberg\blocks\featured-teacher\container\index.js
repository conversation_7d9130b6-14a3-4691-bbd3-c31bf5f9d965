(()=>{var e,t={2313:(e,t,n)=>{"use strict";const a=window.React,i=window.wp.blocks,r=window.wp.blockEditor,l=()=>(0,a.createElement)(a.Fragment,null),o=window.wp.i18n;let s=function(e){return e.ALL="all",e.SOME="some",e}({}),m=function(e){return e.NORMAL="Normal",e.HOVER="Hover",e.ACTIVE="Active",e.FOCUS="Focus",e}({}),c=function(e){return e.DESKTOP="Desktop",e.TABLET="Tablet",e.MOBILE="Mobile",e}({}),u=function(e){return e.TOP_lEFT="top-left",e.TOP_CENTER="top-center",e.TOP_RIGHT="top-right",e.BOTTOM_lEFT="bottom-left",e.BOTTOM_CENTER="bottom-center",e.BOTTOM_RIGHT="bottom-right",e}({});o.__("Small","masterstudy-lms-learning-management-system"),o.__("Normal","masterstudy-lms-learning-management-system"),o.__("Large","masterstudy-lms-learning-management-system"),o.__("Extra Large","masterstudy-lms-learning-management-system");const d="wp-block-masterstudy-settings__",g={top:"",right:"",bottom:"",left:""},p=(u.TOP_lEFT,u.TOP_CENTER,u.TOP_RIGHT,u.BOTTOM_lEFT,u.BOTTOM_CENTER,u.BOTTOM_RIGHT,[{label:o.__("Newest","masterstudy-lms-learning-management-system"),value:"date_high"},{label:o.__("Oldest","masterstudy-lms-learning-management-system"),value:"date_low"},{label:o.__("Overall rating","masterstudy-lms-learning-management-system"),value:"rating"},{label:o.__("Popular","masterstudy-lms-learning-management-system"),value:"popular"},{label:o.__("Price low","masterstudy-lms-learning-management-system"),value:"price_low"},{label:o.__("Price high","masterstudy-lms-learning-management-system"),value:"price_high"}]),b={coursesPerPage:4,coursesPerRow:4,coursesPerRowTablet:2,coursesPerRowMobile:1,teacherId:1,orderBy:"date_high",category:[],showLabel:!0,label:"Teacher of Month",showPosition:!0,showBiography:!0,showViewAllButton:!0,buttonText:"View All",buttonUrl:""},h=Object.keys(b),y={layoutMargin:{top:"0",right:"0",bottom:"0",left:"0"},layoutMarginTablet:g,layoutMarginMobile:g,layoutMarginUnit:"px",layoutMarginUnitTablet:"px",layoutMarginUnitMobile:"px",layoutPadding:{top:"0",right:"0",bottom:"40",left:"0"},layoutPaddingTablet:g,layoutPaddingMobile:g,layoutPaddingUnit:"px",layoutPaddingUnitTablet:"px",layoutPaddingUnitMobile:"px",layoutBackground:"#001931",layoutBorderStyle:"none",layoutBorderStyleTablet:"",layoutBorderStyleMobile:"",layoutBorderColor:"",layoutBorderColorTablet:"",layoutBorderColorMobile:"",layoutBorderWidth:g,layoutBorderWidthTablet:g,layoutBorderWidthMobile:g,layoutBorderWidthUnit:"px",layoutBorderWidthUnitTablet:"px",layoutBorderWidthUnitMobile:"px",layoutBorderRadius:g,layoutBorderRadiusTablet:g,layoutBorderRadiusMobile:g,layoutBorderRadiusUnit:"px",layoutBorderRadiusUnitTablet:"px",layoutBorderRadiusUnitMobile:"px",layoutWidth:"alignfull",layoutZIndex:0,layoutZIndexTablet:null,layoutZIndexMobile:null},v=Object.keys(y),C={instructorContainerBackground:"#EEF1F7",instructorContainerBackgroundImage:"",instructorContainerBackgroundImageTablet:"",instructorContainerBackgroundImageMobile:"",instructorContainerBackgroundSize:"",instructorContainerBackgroundPosition:"",instructorContainerPadding:{top:"40",right:"15",bottom:"0",left:"15"},instructorContainerPaddingTablet:g,instructorContainerPaddingMobile:{top:"40",right:"15",bottom:"",left:"15"},instructorContainerPaddingUnit:"px",instructorContainerPaddingUnitTablet:"px",instructorContainerPaddingUnitMobile:"px",instructorContainerMargin:g,instructorContainerMarginTablet:g,instructorContainerMarginMobile:g,instructorContainerMarginUnit:"px",instructorContainerMarginUnitTablet:"px",instructorContainerMarginUnitMobile:"px",instructorBorderRadius:g,instructorBorderRadiusTablet:g,instructorBorderRadiusMobile:g,instructorBorderRadiusUnit:"px",instructorBorderRadiusUnitTablet:"px",instructorBorderRadiusUnitMobile:"px"},_=Object.keys(C),E={instructorLabelFontSize:12,instructorLabelFontSizeTablet:null,instructorLabelFontSizeMobile:null,instructorLabelFontSizeUnit:"px",instructorLabelFontSizeUnitTablet:"px",instructorLabelFontSizeUnitMobile:"px",instructorLabelFontWeight:"700",instructorLabelTextTransform:"uppercase",instructorLabelFontStyle:"inherit",instructorLabelTextDecoration:"inherit",instructorLabelLineHeight:12,instructorLabelLineHeightTablet:null,instructorLabelLineHeightMobile:null,instructorLabelLineHeightUnit:"px",instructorLabelLineHeightUnitTablet:"px",instructorLabelLineHeightUnitMobile:"px",instructorLabelLetterSpacing:0,instructorLabelLetterSpacingTablet:null,instructorLabelLetterSpacingMobile:null,instructorLabelLetterSpacingUnit:"px",instructorLabelLetterSpacingUnitTablet:"px",instructorLabelLetterSpacingUnitMobile:"px",instructorLabelWordSpacing:0,instructorLabelWordSpacingTablet:null,instructorLabelWordSpacingMobile:null,instructorLabelWordSpacingUnit:"px",instructorLabelWordSpacingUnitTablet:"px",instructorLabelWordSpacingUnitMobile:"px",instructorLabelPadding:{top:"7",right:"15",bottom:"7",left:"15"},instructorLabelPaddingTablet:g,instructorLabelPaddingMobile:g,instructorLabelPaddingUnit:"px",instructorLabelPaddingUnitTablet:"px",instructorLabelPaddingUnitMobile:"px",instructorLabelMargin:{top:"120",right:"0",bottom:"10",left:"0"},instructorLabelMarginTablet:g,instructorLabelMarginMobile:g,instructorLabelMarginUnit:"px",instructorLabelMarginUnitTablet:"px",instructorLabelMarginUnitMobile:"px",instructorLabelBackground:"#227AFF",instructorLabelColor:"#ffffff",instructorLabelBorderStyle:"none",instructorLabelBorderColor:"",instructorLabelBorderWidth:g,instructorLabelBorderWidthUnit:"px",instructorLabelBorderRadius:{top:"20",right:"20",bottom:"20",left:"20"},instructorLabelBorderRadiusUnit:"px"},L=Object.keys(E),S={buttonFontSize:14,buttonFontSizeTablet:null,buttonFontSizeMobile:null,buttonFontSizeUnit:"px",buttonFontSizeUnitTablet:"px",buttonFontSizeUnitMobile:"px",buttonFontWeight:"500",buttonTextTransform:"inherit",buttonFontStyle:"inherit",buttonTextDecoration:"inherit",buttonLineHeight:14,buttonLineHeightTablet:null,buttonLineHeightMobile:null,buttonLineHeightUnit:"px",buttonLineHeightUnitTablet:"px",buttonLineHeightUnitMobile:"px",buttonLetterSpacing:0,buttonLetterSpacingTablet:null,buttonLetterSpacingMobile:null,buttonLetterSpacingUnit:"px",buttonLetterSpacingUnitTablet:"px",buttonLetterSpacingUnitMobile:"px",buttonWordSpacing:0,buttonWordSpacingTablet:null,buttonWordSpacingMobile:null,buttonWordSpacingUnit:"px",buttonWordSpacingUnitTablet:"px",buttonWordSpacingUnitMobile:"px",buttonPadding:{top:"11",right:"20",bottom:"11",left:"20"},buttonPaddingTablet:g,buttonPaddingMobile:g,buttonPaddingUnit:"px",buttonPaddingUnitTablet:"px",buttonPaddingUnitMobile:"px",buttonMargin:{top:"40",right:"0",bottom:"0",left:"0"},buttonMarginTablet:g,buttonMarginMobile:g,buttonMarginUnit:"px",buttonMarginUnitTablet:"px",buttonMarginUnitMobile:"px",buttonBackground:"#227AFF",buttonBackgroundHover:"#438EFF",buttonColor:"#ffffff",buttonColorHover:"#ffffff",buttonBorderStyle:"none",buttonBorderStyleTablet:"",buttonBorderStyleMobile:"",buttonBorderColor:"",buttonBorderColorTablet:"",buttonBorderColorMobile:"",buttonBorderWidth:g,buttonBorderWidthTablet:g,buttonBorderWidthMobile:g,buttonBorderWidthUnit:"px",buttonBorderWidthUnitTablet:"px",buttonBorderWidthUnitMobile:"px",buttonBorderRadius:{top:"20",right:"20",bottom:"20",left:"20"},buttonBorderRadiusTablet:g,buttonBorderRadiusMobile:g,buttonBorderRadiusUnit:"px",buttonBorderRadiusUnitTablet:"px",buttonBorderRadiusUnitMobile:"px"},f=Object.keys(S),T={nameFontSize:65,nameFontSizeTablet:48,nameFontSizeMobile:36,nameFontSizeUnit:"px",nameFontSizeUnitTablet:"px",nameFontSizeUnitMobile:"px",nameFontWeight:"700",nameTextTransform:"inherit",nameFontStyle:"inherit",nameTextDecoration:"inherit",nameLineHeight:65,nameLineHeightTablet:null,nameLineHeightMobile:null,nameLineHeightUnit:"px",nameLineHeightUnitTablet:"px",nameLineHeightUnitMobile:"px",nameLetterSpacing:0,nameLetterSpacingTablet:null,nameLetterSpacingMobile:null,nameLetterSpacingUnit:"px",nameLetterSpacingUnitTablet:"px",nameLetterSpacingUnitMobile:"px",nameWordSpacing:0,nameWordSpacingTablet:null,nameWordSpacingMobile:null,nameWordSpacingUnit:"px",nameWordSpacingUnitTablet:"px",nameWordSpacingUnitMobile:"px",namePadding:g,namePaddingTablet:g,namePaddingMobile:g,namePaddingUnit:"px",namePaddingUnitTablet:"px",namePaddingUnitMobile:"px",nameMargin:{top:"0",right:"0",bottom:"10",left:"0"},nameMarginTablet:g,nameMarginMobile:g,nameMarginUnit:"px",nameMarginUnitTablet:"px",nameMarginUnitMobile:"px",nameColor:"#001931"},N=Object.keys(T),M={positionFontSize:18,positionFontSizeTablet:null,positionFontSizeMobile:null,positionFontSizeUnit:"px",positionFontSizeUnitTablet:"px",positionFontSizeUnitMobile:"px",positionFontWeight:"700",positionTextTransform:"inherit",positionFontStyle:"inherit",positionTextDecoration:"inherit",positionLineHeight:18,positionLineHeightTablet:null,positionLineHeightMobile:null,positionLineHeightUnit:"px",positionLineHeightUnitTablet:"px",positionLineHeightUnitMobile:"px",positionLetterSpacing:0,positionLetterSpacingTablet:null,positionLetterSpacingMobile:null,positionLetterSpacingUnit:"px",positionLetterSpacingUnitTablet:"px",positionLetterSpacingUnitMobile:"px",positionWordSpacing:0,positionWordSpacingTablet:null,positionWordSpacingMobile:null,positionWordSpacingUnit:"px",positionWordSpacingUnitTablet:"px",positionWordSpacingUnitMobile:"px",positionMargin:g,positionMarginTablet:g,positionMarginMobile:g,positionMarginUnit:"px",positionMarginUnitTablet:"px",positionMarginUnitMobile:"px",positionColor:"#4D5E6F"},x=Object.keys(M),U={biographyFontSize:18,biographyFontSizeTablet:null,biographyFontSizeMobile:null,biographyFontSizeUnit:"px",biographyFontSizeUnitTablet:"px",biographyFontSizeUnitMobile:"px",biographyFontWeight:"400",biographyTextTransform:"inherit",biographyFontStyle:"inherit",biographyTextDecoration:"inherit",biographyLineHeight:28,biographyLineHeightTablet:null,biographyLineHeightMobile:null,biographyLineHeightUnit:"px",biographyLineHeightUnitTablet:"px",biographyLineHeightUnitMobile:"px",biographyLetterSpacing:0,biographyLetterSpacingTablet:null,biographyLetterSpacingMobile:null,biographyLetterSpacingUnit:"px",biographyLetterSpacingUnitTablet:"px",biographyLetterSpacingUnitMobile:"px",biographyWordSpacing:0,biographyWordSpacingTablet:null,biographyWordSpacingMobile:null,biographyWordSpacingUnit:"px",biographyWordSpacingUnitTablet:"px",biographyWordSpacingUnitMobile:"px",biographyMargin:{top:"50",right:"0",bottom:"50",left:"0"},biographyMarginTablet:g,biographyMarginMobile:g,biographyMarginUnit:"px",biographyMarginUnitTablet:"px",biographyMarginUnitMobile:"px",biographyColor:"#4D5E6F"},w=Object.keys(U),B={courseTitleFontSize:24,courseTitleFontSizeTablet:null,courseTitleFontSizeMobile:null,courseTitleFontSizeUnit:"px",courseTitleFontSizeUnitTablet:"px",courseTitleFontSizeUnitMobile:"px",courseTitleFontWeight:"700",courseTitleTextTransform:"inherit",courseTitleFontStyle:"inherit",courseTitleTextDecoration:"inherit",courseTitleLineHeight:24,courseTitleLineHeightTablet:null,courseTitleLineHeightMobile:null,courseTitleLineHeightUnit:"px",courseTitleLineHeightUnitTablet:"px",courseTitleLineHeightUnitMobile:"px",courseTitleLetterSpacing:0,courseTitleLetterSpacingTablet:null,courseTitleLetterSpacingMobile:null,courseTitleLetterSpacingUnit:"px",courseTitleLetterSpacingUnitTablet:"px",courseTitleLetterSpacingUnitMobile:"px",courseTitleWordSpacing:0,courseTitleWordSpacingTablet:null,courseTitleWordSpacingMobile:null,courseTitleWordSpacingUnit:"px",courseTitleWordSpacingUnitTablet:"px",courseTitleWordSpacingUnitMobile:"px",courseTitleMargin:{top:"0",right:"0",bottom:"30",left:"0"},courseTitleMarginTablet:g,courseTitleMarginMobile:g,courseTitleMarginUnit:"px",courseTitleMarginUnitTablet:"px",courseTitleMarginUnitMobile:"px",courseTitleColor:"#001931"},A=Object.keys(B),H={...y,...C,...E,...S,...T,...M,...U,...B},k={...b,...H},F=new Map([["layoutMargin",{unit:"layoutMarginUnit",isAdaptive:!0}],["layoutPadding",{unit:"layoutPaddingUnit",isAdaptive:!0}],["layoutBackground",{}],["layoutBorderStyle",{isAdaptive:!0}],["layoutBorderColor",{isAdaptive:!0}],["layoutBorderWidth",{isAdaptive:!0,unit:"layoutBorderWidthUnit"}],["layoutBorderRadius",{isAdaptive:!0,unit:"layoutBorderRadiusUnit"}],["layoutZIndex",{isAdaptive:!0}],["instructorContainerBackground",{}],["instructorContainerBackgroundImage",{isAdaptive:!0}],["instructorContainerBackgroundSize",{}],["instructorContainerBackgroundPosition",{}],["instructorContainerPadding",{unit:"instructorContainerPaddingUnit",isAdaptive:!0}],["instructorContainerMargin",{unit:"instructorContainerMarginUnit",isAdaptive:!0}],["instructorLabelFontSize",{unit:"instructorLabelFontSizeUnit",isAdaptive:!0}],["instructorLabelFontWeight",{}],["instructorLabelTextTransform",{}],["instructorLabelFontStyle",{}],["instructorLabelTextDecoration",{}],["instructorLabelLineHeight",{unit:"instructorLabelLineHeightUnit",isAdaptive:!0}],["instructorLabelLetterSpacing",{unit:"instructorLabelLetterSpacingUnit",isAdaptive:!0}],["instructorLabelWordSpacing",{unit:"instructorLabelWordSpacingUnit",isAdaptive:!0}],["instructorLabelPadding",{unit:"instructorLabelPaddingUnit",isAdaptive:!0}],["instructorLabelMargin",{unit:"instructorLabelMarginUnit",isAdaptive:!0}],["instructorLabelBackground",{}],["instructorLabelColor",{}],["instructorLabelBorderStyle",{}],["instructorLabelBorderColor",{}],["instructorLabelBorderWidth",{unit:"instructorLabelBorderWidthUnit"}],["instructorLabelBorderRadius",{unit:"instructorLabelBorderRadiusUnit"}],["instructorBorderRadius",{isAdaptive:!0,unit:"instructorBorderRadiusUnit"}],["buttonFontSize",{unit:"buttonFontSizeUnit",isAdaptive:!0}],["buttonFontWeight",{}],["buttonTextTransform",{}],["buttonFontStyle",{}],["buttonTextDecoration",{}],["buttonLineHeight",{unit:"buttonLineHeightUnit",isAdaptive:!0}],["buttonLetterSpacing",{unit:"buttonLetterSpacingUnit",isAdaptive:!0}],["buttonWordSpacing",{unit:"buttonWordSpacingUnit",isAdaptive:!0}],["buttonPadding",{unit:"buttonPaddingUnit",isAdaptive:!0}],["buttonMargin",{unit:"buttonMarginUnit",isAdaptive:!0}],["buttonBackground",{hasHover:!0}],["buttonColor",{hasHover:!0}],["buttonBorderStyle",{isAdaptive:!0}],["buttonBorderColor",{isAdaptive:!0}],["buttonBorderWidth",{isAdaptive:!0,unit:"buttonBorderWidthUnit"}],["buttonBorderRadius",{isAdaptive:!0,unit:"buttonBorderRadiusUnit"}],["nameFontSize",{unit:"nameFontSizeUnit",isAdaptive:!0}],["nameFontWeight",{}],["nameTextTransform",{}],["nameFontStyle",{}],["nameTextDecoration",{}],["nameLineHeight",{unit:"nameLineHeightUnit",isAdaptive:!0}],["nameLetterSpacing",{unit:"nameLetterSpacingUnit",isAdaptive:!0}],["nameWordSpacing",{unit:"nameWordSpacingUnit",isAdaptive:!0}],["namePadding",{unit:"namePaddingUnit",isAdaptive:!0}],["nameMargin",{unit:"nameMarginUnit",isAdaptive:!0}],["nameColor",{}],["positionFontSize",{unit:"positionFontSizeUnit",isAdaptive:!0}],["positionFontWeight",{}],["positionTextTransform",{}],["positionFontStyle",{}],["positionTextDecoration",{}],["positionLineHeight",{unit:"positionLineHeightUnit",isAdaptive:!0}],["positionLetterSpacing",{unit:"positionLetterSpacingUnit",isAdaptive:!0}],["positionWordSpacing",{unit:"positionWordSpacingUnit",isAdaptive:!0}],["positionMargin",{unit:"positionMarginUnit",isAdaptive:!0}],["positionColor",{}],["biographyFontSize",{unit:"biographyFontSizeUnit",isAdaptive:!0}],["biographyFontWeight",{}],["biographyTextTransform",{}],["biographyFontStyle",{}],["biographyTextDecoration",{}],["biographyLineHeight",{unit:"biographyLineHeightUnit",isAdaptive:!0}],["biographyLetterSpacing",{unit:"biographyLetterSpacingUnit",isAdaptive:!0}],["biographyWordSpacing",{unit:"biographyWordSpacingUnit",isAdaptive:!0}],["biographyMargin",{unit:"biographyMarginUnit",isAdaptive:!0}],["biographyColor",{}],["courseTitleFontSize",{unit:"courseTitleFontSizeUnit",isAdaptive:!0}],["courseTitleFontWeight",{}],["courseTitleTextTransform",{}],["courseTitleFontStyle",{}],["courseTitleTextDecoration",{}],["courseTitleLineHeight",{unit:"courseTitleLineHeightUnit",isAdaptive:!0}],["courseTitleLetterSpacing",{unit:"courseTitleLetterSpacingUnit",isAdaptive:!0}],["courseTitleWordSpacing",{unit:"courseTitleWordSpacingUnit",isAdaptive:!0}],["courseTitleMargin",{unit:"courseTitleMarginUnit",isAdaptive:!0}],["courseTitleColor",{}]]);var P=n(6942),O=n.n(P);const R=window.wp.components,V=({condition:e,fallback:t=null,children:n})=>(0,a.createElement)(a.Fragment,null,e?n:t),D=(e,t)=>{if(typeof e!=typeof t)return!1;if(Number.isNaN(e)&&Number.isNaN(t))return!0;if("object"!=typeof e||null===e||null===t)return e===t;if(Object.keys(e).length!==Object.keys(t).length)return!1;if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;const n=e.slice().sort(),a=t.slice().sort();return n.every(((e,t)=>D(e,a[t])))}for(const n of Object.keys(e))if(!D(e[n],t[n]))return!1;return!0},W=(e=[])=>e.map((e=>({label:e.name,value:e.term_id}))),z=["",null,void 0,"null","undefined"],I=[".jpg",".jpeg",".png",".gif"],Z=e=>z.includes(e),j=(e,t,n="")=>{const a=e[t];return"object"==typeof a&&null!==a?((e,t)=>{return n=e,Object.values(n).every((e=>z.includes(e)))?null:((e,t="")=>{const n=Object.entries(e).reduce(((e,[n,a])=>(e[n]=(a||"0")+t,e)),{});return`${n.top} ${n.right} ${n.bottom} ${n.left}`})(e,t);var n})(a,n):((e,t)=>Z(e)?e:(e=>{if("string"!=typeof e)return!1;const t=e.slice(e.lastIndexOf("."));return I.includes(t)||e.startsWith("blob")})(e)?`url('${e}')`:String(e+t))(a,n)},$=(e,t,n)=>{const a={};return n.forEach((({isAdaptive:n,hasHover:i,unit:r},l)=>{if(t.hasOwnProperty(l)){const{unitMeasureDesktop:s,unitMeasureTablet:c,unitMeasureMobile:u}=((e,t)=>{var n;return{unitMeasureDesktop:null!==(n=e[t])&&void 0!==n?n:"",unitMeasureTablet:t?e[t+"Tablet"]:"",unitMeasureMobile:t?e[t+"Mobile"]:""}})(t,r);if(n&&i){const{desktopHoverPropertyName:n,mobileHoverPropertyName:i,tabletHoverPropertyName:r}=(e=>{const t=e+m.HOVER;return{desktopHoverPropertyName:t,tabletHoverPropertyName:t+"Tablet",mobileHoverPropertyName:t+"Mobile"}})(l),o=j(t,n,s);Z(o)||(a[`--lms-${e}-${n}`]=o);const d=j(t,r,c);Z(d)||(a[`--lms-${e}-${r}`]=d);const g=j(t,i,u);Z(g)||(a[`--lms-${e}-${i}`]=g)}if(i){const n=l+m.HOVER,i=j(t,n,s);Z(i)||(a[`--lms-${e}-${n}`]=i)}if(n){const{desktopPropertyName:n,mobilePropertyName:i,tabletPropertyName:r}={desktopPropertyName:o=l,tabletPropertyName:o+"Tablet",mobilePropertyName:o+"Mobile"},m=j(t,n,s);Z(m)||(a[`--lms-${e}-${n}`]=m);const d=j(t,r,c);Z(d)||(a[`--lms-${e}-${r}`]=d);const g=j(t,i,u);Z(g)||(a[`--lms-${e}-${i}`]=g)}const d=j(t,l,s);Z(d)||(a[`--lms-${e}-${l}`]=d)}var o})),a};function G(e){return Array.isArray(e)?e.map((e=>d+e)):d+e}const K=window.wp.element,X=window.wp.data,Y=()=>(0,X.useSelect)((e=>e("core/editor").getDeviceType()||e("core/edit-site")?.__experimentalGetPreviewDeviceType()||e("core/edit-post")?.__experimentalGetPreviewDeviceType()||e("masterstudy/store")?.getDeviceType()),[])||"Desktop",q=(e=!1)=>{const[t,n]=(0,K.useState)(e),a=(0,K.useCallback)((()=>{n(!0)}),[]);return{isOpen:t,onClose:(0,K.useCallback)((()=>{n(!1)}),[]),onOpen:a,onToggle:(0,K.useCallback)((()=>{n((e=>!e))}),[])}},J=(0,K.createContext)(null),Q=({children:e,...t})=>(0,a.createElement)(J.Provider,{value:{...t}},e),ee=()=>{const e=(0,K.useContext)(J);if(!e)throw new Error("No settings context provided");return e},te=(e="")=>{const{attributes:t,setAttributes:n,onResetByFieldName:a,changedFieldsByName:i}=ee();return{value:t[e],onChange:t=>n({[e]:t}),onReset:a.get(e),isChanged:i.get(e)}},ne=(e,t=!1,n=!1)=>{const{hoverName:a,onChangeHoverName:i}=(()=>{const[e,t]=(0,K.useState)(m.NORMAL);return{hoverName:e,onChangeHoverName:(0,K.useCallback)((e=>{t(e)}),[])}})(),r=Y();return{fieldName:(0,K.useMemo)((()=>{const i=a===m.HOVER?a:"",l=r===c.DESKTOP?"":r;return n&&t?e+i+l:n&&!t?e+i:t&&!n?e+l:e}),[e,n,t,a,r]),hoverName:a,onChangeHoverName:i}},ae=(e,t=!1,n="Normal")=>{const a=Y(),i=(0,K.useMemo)((()=>{const i=n===m.NORMAL?"":n,r=a===c.DESKTOP?"":a;return i&&t?e+i+r:i&&!t?e+i:t&&!i?e+r:e}),[e,t,n,a]),{value:r,isChanged:l,onReset:o}=te(i);return{fieldName:i,value:r,isChanged:l,onReset:o}},ie=(e=[],t=s.ALL)=>{const{attributes:n}=ee();return!e.length||(t===s.ALL?e.every((({name:e,value:t})=>{const a=n[e];return Array.isArray(t)?Array.isArray(a)?D(t,a):t.includes(a):t===a})):t!==s.SOME||e.some((({name:e,value:t})=>{const a=n[e];return Array.isArray(t)?Array.isArray(a)?D(t,a):t.includes(a):t===a})))},re=e=>{const t=(0,K.useRef)(null);return(0,K.useEffect)((()=>{const n=n=>{t.current&&!t.current.contains(n.target)&&e()};return document.addEventListener("click",n),()=>{document.removeEventListener("click",n)}}),[t,e]),t},le=()=>{const e=[{label:o.__("Auto","masterstudy-lms-learning-management-system"),value:"alignauto"},{label:o.__("Full width","masterstudy-lms-learning-management-system"),value:"alignfull"}],t=[{label:o.__("Cover","masterstudy-lms-learning-management-system"),value:"cover"},{label:o.__("Contain","masterstudy-lms-learning-management-system"),value:"contain"},{label:o.__("Inherit","masterstudy-lms-learning-management-system"),value:"inherit"},{label:o.__("Initial","masterstudy-lms-learning-management-system"),value:"initial"},{label:o.__("Revert","masterstudy-lms-learning-management-system"),value:"revert"},{label:o.__("Revert-layer","masterstudy-lms-learning-management-system"),value:"revert-layer"},{label:o.__("Unset","masterstudy-lms-learning-management-system"),value:"unset"}],n=[{label:o.__("Center center","masterstudy-lms-learning-management-system"),value:"center center"},{label:o.__("Center left","masterstudy-lms-learning-management-system"),value:"center left"},{label:o.__("Center right","masterstudy-lms-learning-management-system"),value:"center right"},{label:o.__("Top center","masterstudy-lms-learning-management-system"),value:"top center"},{label:o.__("Top left","masterstudy-lms-learning-management-system"),value:"top left"},{label:o.__("Top right","masterstudy-lms-learning-management-system"),value:"top right"},{label:o.__("Bottom center","masterstudy-lms-learning-management-system"),value:"bottom center"},{label:o.__("Bottom left","masterstudy-lms-learning-management-system"),value:"bottom left"},{label:o.__("Bottom right","masterstudy-lms-learning-management-system"),value:"bottom right"}],a=[{label:o.__("Center","masterstudy-lms-learning-management-system"),value:"center"},{label:o.__("Start","masterstudy-lms-learning-management-system"),value:"flex-start"},{label:o.__("End","masterstudy-lms-learning-management-system"),value:"flex-end"},{label:o.__("Space Between","masterstudy-lms-learning-management-system"),value:"space-between"},{label:o.__("Space Around","masterstudy-lms-learning-management-system"),value:"space-around"},{label:o.__("Space Evenly","masterstudy-lms-learning-management-system"),value:"space-evenly"}];return{filterOptions:p,widthOptions:e,backgroundSizeOptions:t,backgroundPositionOptions:n,alignContentOptions:a}},oe=e=>(0,a.createElement)(R.SVG,{width:"16",height:"16",viewBox:"0 0 12 13",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(R.Path,{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})),se=e=>(0,a.createElement)(R.SVG,{width:"16",height:"16",viewBox:"0 0 14 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(R.G,{"clip-path":"url(#clip0_1068_38993)"},(0,a.createElement)(R.Path,{d:"M11.1973 8.60005L8.74967 8.11005V5.67171C8.74967 5.517 8.68822 5.36863 8.57882 5.25923C8.46942 5.14984 8.32105 5.08838 8.16634 5.08838H6.99967C6.84496 5.08838 6.69659 5.14984 6.5872 5.25923C6.4778 5.36863 6.41634 5.517 6.41634 5.67171V8.58838H5.24967C5.15021 8.58844 5.05241 8.61391 4.96555 8.66238C4.87869 8.71084 4.80565 8.78068 4.75336 8.86529C4.70106 8.9499 4.67125 9.04646 4.66674 9.14582C4.66223 9.24518 4.68317 9.34405 4.72759 9.43305L6.47759 12.933C6.57676 13.1302 6.77859 13.255 6.99967 13.255H11.083C11.2377 13.255 11.3861 13.1936 11.4955 13.0842C11.6049 12.9748 11.6663 12.8264 11.6663 12.6717V9.17171C11.6665 9.03685 11.6198 8.90612 11.5343 8.80186C11.4487 8.69759 11.3296 8.62626 11.1973 8.60005Z"}),(0,a.createElement)(R.Path,{d:"M10.4997 1.58838H3.49967C2.85626 1.58838 2.33301 2.11221 2.33301 2.75505V6.83838C2.33301 7.4818 2.85626 8.00505 3.49967 8.00505H5.24967V6.83838H3.49967V2.75505H10.4997V6.83838H11.6663V2.75505C11.6663 2.11221 11.1431 1.58838 10.4997 1.58838Z"})),(0,a.createElement)("defs",null,(0,a.createElement)("clipPath",{id:"clip0_1068_38993"},(0,a.createElement)(R.Rect,{width:"14",height:"14",transform:"translate(0 0.421875)"})))),me=[{value:m.NORMAL,label:o.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(oe,{onClick:e})},{value:m.HOVER,label:o.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(se,{onClick:e})},{value:m.ACTIVE,label:o.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(se,{onClick:e})},{value:m.FOCUS,label:o.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(se,{onClick:e})}],ce={[m.NORMAL]:{icon:(0,a.createElement)(oe,null),label:o.__("Normal State","masterstudy-lms-learning-management-system")},[m.HOVER]:{icon:(0,a.createElement)(se,null),label:o.__("Hovered State","masterstudy-lms-learning-management-system")},[m.ACTIVE]:{icon:(0,a.createElement)(se,null),label:o.__("Active State","masterstudy-lms-learning-management-system")},[m.FOCUS]:{icon:(0,a.createElement)(se,null),label:o.__("Focus State","masterstudy-lms-learning-management-system")}},ue=(e,t)=>{let n=[];return n=e.length?me.filter((t=>e.includes(t.value))):me,n=n.filter((e=>e.value!==t)),{ICONS_MAP:ce,options:n}},[de,ge,pe,be,he]=G(["hover-state","hover-state__selected","hover-state__selected__opened-menu","hover-state__menu","hover-state__menu__item"]),ye=({stateOptions:e,currentState:t,onSelect:n})=>{const{isOpen:i,onOpen:r,onClose:l}=q(),o=re(l),{ICONS_MAP:s,options:m}=ue(e,t);return(0,a.createElement)("div",{className:de,ref:o},(0,a.createElement)("div",{className:O()([ge],{[pe]:i}),onClick:r,title:s[t]?.label},s[t]?.icon),(0,a.createElement)(V,{condition:i},(0,a.createElement)("div",{className:be},m.map((({value:e,icon:t,label:i})=>(0,a.createElement)("div",{key:e,className:he,title:i},t((()=>n(e)))))))))},ve=G("color-indicator"),Ce=(0,K.memo)((({color:e,onChange:t})=>(0,a.createElement)("div",{className:ve},(0,a.createElement)(r.PanelColorSettings,{enableAlpha:!0,disableCustomColors:!1,__experimentalHasMultipleOrigins:!0,__experimentalIsRenderedInSidebar:!0,colorSettings:[{label:"",value:e,onChange:t}]}))));var _e;function Ee(){return Ee=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Ee.apply(null,arguments)}var Le,Se,fe=function(e){return a.createElement("svg",Ee({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 12 13"},e),_e||(_e=a.createElement("path",{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})))};function Te(){return Te=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Te.apply(null,arguments)}var Ne=function(e){return a.createElement("svg",Te({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 14 15"},e),Le||(Le=a.createElement("g",{clipPath:"url(#state-hover_svg__a)"},a.createElement("path",{d:"M11.197 8.6 8.75 8.11V5.672a.583.583 0 0 0-.584-.584H7a.583.583 0 0 0-.584.584v2.916H5.25a.584.584 0 0 0-.522.845l1.75 3.5c.099.197.3.322.522.322h4.083a.583.583 0 0 0 .583-.583v-3.5a.58.58 0 0 0-.469-.572"}),a.createElement("path",{d:"M10.5 1.588h-7c-.644 0-1.167.524-1.167 1.167v4.083c0 .644.523 1.167 1.167 1.167h1.75V6.838H3.5V2.755h7v4.083h1.166V2.755c0-.643-.523-1.167-1.166-1.167"}))),Se||(Se=a.createElement("defs",null,a.createElement("clipPath",{id:"state-hover_svg__a"},a.createElement("path",{d:"M0 .422h14v14H0z"})))))};const Me=[{value:m.NORMAL,label:o.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(fe,{onClick:e})},{value:m.HOVER,label:o.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(Ne,{onClick:e})}],xe={[m.NORMAL]:{icon:(0,a.createElement)(fe,null),label:o.__("Normal State","masterstudy-lms-learning-management-system")},[m.HOVER]:{icon:(0,a.createElement)(Ne,null),label:o.__("Hovered State","masterstudy-lms-learning-management-system")}},Ue=G("hover-state"),we=G("hover-state__selected"),Be=G("hover-state__selected__opened-menu"),Ae=G("has-changes"),He=G("hover-state__menu"),ke=G("hover-state__menu__item"),Fe=(0,K.memo)((e=>{const{hoverName:t,onChangeHoverName:n,fieldName:i}=e,{changedFieldsByName:r}=ee(),l=r.get(i),{isOpen:o,onOpen:s,onClose:m}=q(),c=re(m),{ICONS_MAP:u,options:d}=(e=>{const t=(0,K.useMemo)((()=>Me.filter((t=>t.value!==e))),[e]);return{ICONS_MAP:xe,options:t}})(t),g=(0,K.useCallback)((e=>{n(e),m()}),[n,m]);return(0,a.createElement)("div",{className:Ue,ref:c},(0,a.createElement)("div",{className:O()([we],{[Be]:o,[Ae]:l}),onClick:s,title:u[t]?.label},u[t]?.icon),(0,a.createElement)(V,{condition:o},(0,a.createElement)("div",{className:He},d.map((({value:e,icon:t,label:n})=>(0,a.createElement)("div",{key:e,className:ke,title:n},t((()=>g(e)))))))))})),Pe={Desktop:{icon:"desktop",label:o.__("Desktop","masterstudy-lms-learning-management-system")},Tablet:{icon:"tablet",label:o.__("Tablet","masterstudy-lms-learning-management-system")},Mobile:{icon:"smartphone",label:o.__("Mobile","masterstudy-lms-learning-management-system")}},Oe=[{value:c.DESKTOP,icon:"desktop",label:o.__("Desktop","masterstudy-lms-learning-management-system")},{value:c.TABLET,icon:"tablet",label:o.__("Tablet","masterstudy-lms-learning-management-system")},{value:c.MOBILE,icon:"smartphone",label:o.__("Mobile","masterstudy-lms-learning-management-system")}],Re=G("device-picker"),Ve=G("device-picker__selected"),De=G("device-picker__selected__opened-menu"),We=G("device-picker__menu"),ze=G("device-picker__menu__item"),Ie=()=>{const{isOpen:e,onOpen:t,onClose:n}=q(),{value:i,onChange:r}=(e=>{const t=Y(),n=(0,X.useDispatch)();return{value:(0,K.useMemo)((()=>Pe[t]),[t]),onChange:t=>{n("core/edit-site")&&n("core/edit-site").__experimentalSetPreviewDeviceType?n("core/edit-site").__experimentalSetPreviewDeviceType(t):n("core/edit-post")&&n("core/edit-post").__experimentalSetPreviewDeviceType?n("core/edit-post").__experimentalSetPreviewDeviceType(t):n("masterstudy/store").setDeviceType(t),e()}}})(n),l=(e=>(0,K.useMemo)((()=>Oe.filter((t=>t.icon!==e))),[e]))(i.icon),o=re(n);return(0,a.createElement)("div",{className:Re,ref:o},(0,a.createElement)(R.Dashicon,{className:O()([Ve],{[De]:e}),icon:i.icon,size:16,onClick:t,title:i.label}),(0,a.createElement)(V,{condition:e},(0,a.createElement)("div",{className:We},l.map((e=>(0,a.createElement)(R.Dashicon,{key:e.value,icon:e.icon,size:16,onClick:()=>r(e.value),className:ze,title:e.label}))))))},Ze=G("reset-button"),je=({onReset:e})=>(0,a.createElement)(R.Dashicon,{icon:"undo",onClick:e,className:Ze,size:16}),$e=[{label:"PX",value:"px"},{label:"%",value:"%"},{label:"EM",value:"em"},{label:"REM",value:"rem"},{label:"VW",value:"vw"},{label:"VH",value:"vh"}],Ge=G("unit"),Ke=G("unit__single"),Xe=G("unit__list"),Ye=({name:e,isAdaptive:t})=>{const{isOpen:n,onOpen:i,onClose:r}=q(),{fieldName:l}=ne(e,t),{value:o,onChange:s}=te(l),m=re(r);return(0,a.createElement)("div",{className:Ge,ref:m},(0,a.createElement)("div",{className:Ke,onClick:i},o),(0,a.createElement)(V,{condition:n},(0,a.createElement)("div",{className:Xe},$e.map((({value:e,label:t})=>(0,a.createElement)("div",{key:e,onClick:()=>(s(e),void r())},t))))))},qe=G("popover-modal"),Je=G("popover-modal__close dashicon dashicons dashicons-no-alt"),Qe=e=>{const{isOpen:t,onClose:n,popoverContent:i}=e;return(0,a.createElement)(V,{condition:t},(0,a.createElement)(R.Popover,{position:"middle left",onClose:n,className:qe},i,(0,a.createElement)("span",{onClick:n,className:Je})))},et=G("setting-label"),tt=G("setting-label__content"),nt=e=>{const{label:t,isChanged:n=!1,onReset:i,showDevicePicker:r=!0,HoverStateControl:l=null,unitName:o,popoverContent:s=null,dependencies:m}=e,{isOpen:c,onClose:u,onToggle:d}=q();return ie(m)?(0,a.createElement)("div",{className:et},(0,a.createElement)("div",{className:tt},(0,a.createElement)("div",{onClick:d},t),(0,a.createElement)(V,{condition:Boolean(s)},(0,a.createElement)(Qe,{isOpen:c,onClose:u,popoverContent:s})),(0,a.createElement)(V,{condition:r},(0,a.createElement)(Ie,null)),(0,a.createElement)(V,{condition:Boolean(l)},l)),(0,a.createElement)(V,{condition:Boolean(o)},(0,a.createElement)(Ye,{name:o,isAdaptive:r})),(0,a.createElement)(V,{condition:n},(0,a.createElement)(je,{onReset:i}))):null},at=G("suffix"),it=()=>(0,a.createElement)("div",{className:at},(0,a.createElement)(R.Dashicon,{icon:"color-picker",size:16})),rt=G("color-picker"),lt=e=>{const{name:t,label:n,placeholder:i,dependencyMode:r,dependencies:l,isAdaptive:o=!1,hasHover:s=!1}=e,{fieldName:m,hoverName:c,onChangeHoverName:u}=ne(t,o,s),{value:d,isChanged:g,onChange:p,onReset:b}=te(m);return ie(l,r)?(0,a.createElement)("div",{className:rt},(0,a.createElement)(V,{condition:Boolean(n)},(0,a.createElement)(nt,{label:n,isChanged:g,onReset:b,showDevicePicker:o,HoverStateControl:(0,a.createElement)(V,{condition:s},(0,a.createElement)(Fe,{hoverName:c,onChangeHoverName:u,fieldName:m}))})),(0,a.createElement)(R.__experimentalInputControl,{prefix:(0,a.createElement)(Ce,{color:d,onChange:p}),suffix:(0,a.createElement)(it,null),onChange:p,value:d,placeholder:i})):null},ot=G("number-steppers"),st=G("indent-steppers"),mt=G("indent-stepper-plus"),ct=G("indent-stepper-minus"),ut=({onIncrement:e,onDecrement:t,withArrows:n=!1})=>n?(0,a.createElement)("span",{className:st},(0,a.createElement)("button",{onClick:e,className:mt}),(0,a.createElement)("button",{onClick:t,className:ct})):(0,a.createElement)("span",{className:ot},(0,a.createElement)("button",{onClick:e},"+"),(0,a.createElement)("button",{onClick:t},"-")),[dt,gt]=G(["indents","indents-control"]),pt=({name:e,label:t,unitName:n,popoverContent:i,dependencyMode:r,dependencies:l,isAdaptive:s=!1})=>{const{fieldName:m}=ne(e,s),{value:c,onResetSegmentedBox:u,hasChanges:d,handleInputIncrement:g,handleInputDecrement:p,updateDirectionsValues:b,lastFieldValue:h}=((e,t)=>{const{value:n,isChanged:a,onChange:i,onReset:r}=te(e),{onResetByFieldName:l,changedFieldsByName:o}=ee(),s=a||o.get(t),m=e=>{i({...n,...e})},[c,u]=(0,K.useState)(!1);return{value:n,onResetSegmentedBox:()=>{r(),l.get(t)()},hasChanges:s,handleInputIncrement:e=>Number(n[e])+1,handleInputDecrement:e=>Number(n[e])-1,updateDirectionsValues:(e,t,n)=>{e?(u(!1),m({top:n,right:n,bottom:n,left:n})):(u(n),m({[t]:n}))},lastFieldValue:c}})(m,n),[y,v]=(0,K.useState)((()=>{const{left:e,right:t,top:n,bottom:a}=c;return""!==e&&e===t&&t===n&&n===a})),C=e=>{const[t,n]=Object.entries(e)[0];b(y,t,n)},_=e=>()=>{const t=g(e);b(y,e,String(t))},E=e=>()=>{const t=p(e);b(y,e,String(t))};return ie(l,r)?(0,a.createElement)("div",{className:dt},(0,a.createElement)(V,{condition:Boolean(t)},(0,a.createElement)(nt,{label:null!=t?t:"",isChanged:d,onReset:u,unitName:n,popoverContent:i,showDevicePicker:s})),(0,a.createElement)("div",{className:`${gt} ${y?"active":""}`},(0,a.createElement)("div",null,(0,a.createElement)(R.__experimentalNumberControl,{value:c.top,onChange:e=>{C({top:e})},spinControls:"none",suffix:(0,a.createElement)(ut,{onIncrement:_("top"),onDecrement:E("top"),withArrows:!0})}),(0,a.createElement)("div",null,o.__("Top","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(R.__experimentalNumberControl,{value:c.right,onChange:e=>{C({right:e})},spinControls:"none",suffix:(0,a.createElement)(ut,{onIncrement:_("right"),onDecrement:E("right"),withArrows:!0})}),(0,a.createElement)("div",null,o.__("Right","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(R.__experimentalNumberControl,{value:c.bottom,onChange:e=>{C({bottom:e})},spinControls:"none",suffix:(0,a.createElement)(ut,{onIncrement:_("bottom"),onDecrement:E("bottom"),withArrows:!0})}),(0,a.createElement)("div",null,o.__("Bottom","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(R.__experimentalNumberControl,{value:c.left,onChange:e=>{C({left:e})},spinControls:"none",suffix:(0,a.createElement)(ut,{onIncrement:_("left"),onDecrement:E("left"),withArrows:!0})}),(0,a.createElement)("div",null,o.__("Left","masterstudy-lms-learning-management-system"))),(0,a.createElement)(R.Dashicon,{icon:"dashicons "+(y?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{y||!1===h||b(!0,"left",h),v((e=>!e))}}))):null},[bt,ht,yt,vt]=G(["toggle-group-wrapper","toggle-group","toggle-group__toggle","toggle-group__active-toggle"]),Ct=e=>{const{name:t,options:n,label:i,isAdaptive:r=!1,dependencyMode:l,dependencies:o}=e,{fieldName:s}=ne(t,r),{value:m,isChanged:c,onChange:u,onReset:d}=te(s);return ie(o,l)?(0,a.createElement)("div",{className:bt},(0,a.createElement)(V,{condition:Boolean(i)},(0,a.createElement)(nt,{label:i,isChanged:c,onReset:d,showDevicePicker:r})),(0,a.createElement)("div",{className:ht},n.map((e=>(0,a.createElement)("div",{key:e.value,className:O()([yt],{[vt]:e.value===m}),onClick:()=>u(e.value)},e.label))))):null},[_t,Et,Lt,St]=G(["border-control","border-control-solid","border-control-dashed","border-control-dotted"]),ft=e=>{const{label:t,borderStyleName:n,borderColorName:i,borderWidthName:r,dependencyMode:l,dependencies:s,isAdaptive:m=!1,hasHover:c=!1}=e,[u,d]=(0,K.useState)("Normal"),{fieldName:g,value:p,isChanged:b,onReset:h}=ae(n,m,u),{fieldName:y,isChanged:v,onReset:C}=ae(i,m,u),{fieldName:_,isChanged:E,onReset:L}=ae(r,m,u);if(!ie(s,l))return null;const S=b||v||E;return(0,a.createElement)("div",{className:O()([_t],{"has-reset-button":S})},(0,a.createElement)(nt,{label:t,isChanged:S,onReset:()=>{h(),C(),L()},showDevicePicker:m,HoverStateControl:(0,a.createElement)(V,{condition:c},(0,a.createElement)(ye,{stateOptions:["Normal","Hover"],currentState:u,onSelect:d}))}),(0,a.createElement)(Ct,{options:[{label:(0,a.createElement)("span",null,o.__("None","masterstudy-lms-learning-management-system")),value:"none"},{label:(0,a.createElement)("span",{className:Et}),value:"solid"},{label:(0,a.createElement)("span",{className:Lt},(0,a.createElement)("span",null)),value:"dashed"},{label:(0,a.createElement)("span",{className:St},(0,a.createElement)("span",null,(0,a.createElement)("span",null))),value:"dotted"}],name:g}),(0,a.createElement)(V,{condition:"none"!==p},(0,a.createElement)(lt,{name:y,placeholder:o.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(pt,{name:_})))},Tt=G("border-radius"),Nt=G("border-radius-control"),Mt=({name:e,label:t,unitName:n,popoverContent:i,dependencyMode:r,dependencies:l,isAdaptive:o=!1,hasHover:s=!1})=>{const{fieldName:m}=ne(e,o,s),{value:c,onResetBorderRadius:u,hasChanges:d,handleInputIncrement:g,handleInputDecrement:p,updateDirectionsValues:b,lastFieldValue:h}=((e,t)=>{const[n,a]=(0,K.useState)(!1),{value:i,isChanged:r,onChange:l,onReset:o}=te(e),{onResetByFieldName:s,changedFieldsByName:m}=ee(),c=r||m.get(t),u=e=>{l({...i,...e})};return{value:i,onResetBorderRadius:()=>{o(),s.get(t)()},hasChanges:c,handleInputIncrement:e=>Number(i[e])+1,handleInputDecrement:e=>Number(i[e])-1,updateDirectionsValues:(e,t,n)=>{e?(u({top:n,right:n,bottom:n,left:n}),a(!1)):(u({[t]:n}),a(n))},lastFieldValue:n}})(m,n),[y,v]=(0,K.useState)((()=>{const{left:e,right:t,top:n,bottom:a}=c;return""!==e&&e===t&&t===n&&n===a})),C=e=>{const[t,n]=Object.entries(e)[0];b(y,t,n)},_=e=>()=>{const t=g(e);b(y,e,String(t))},E=e=>()=>{const t=p(e);b(y,e,String(t))};return ie(l,r)?(0,a.createElement)("div",{className:Tt},(0,a.createElement)(nt,{label:t,isChanged:d,onReset:u,unitName:n,popoverContent:i,showDevicePicker:o}),(0,a.createElement)("div",{className:O()([Nt],{"has-reset-button":d,active:y})},(0,a.createElement)("div",{className:"number-control-top"},(0,a.createElement)(R.__experimentalNumberControl,{value:c.top,onChange:e=>{C({top:e})},spinControls:"none",suffix:(0,a.createElement)(ut,{onIncrement:_("top"),onDecrement:E("top"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-right"},(0,a.createElement)(R.__experimentalNumberControl,{value:c.right,onChange:e=>{C({right:e})},spinControls:"none",suffix:(0,a.createElement)(ut,{onIncrement:_("right"),onDecrement:E("right"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-left"},(0,a.createElement)(R.__experimentalNumberControl,{value:c.left,onChange:e=>{C({left:e})},spinControls:"none",suffix:(0,a.createElement)(ut,{onIncrement:_("left"),onDecrement:E("left"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-bottom"},(0,a.createElement)(R.__experimentalNumberControl,{value:c.bottom,onChange:e=>{C({bottom:e})},spinControls:"none",suffix:(0,a.createElement)(ut,{onIncrement:_("bottom"),onDecrement:E("bottom"),withArrows:!0})})),(0,a.createElement)(R.Dashicon,{icon:"dashicons "+(y?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{y||!1===h||b(!0,"left",h),v((e=>!e))}}))):null},xt=(G("box-shadow-preset"),G("presets")),Ut=G("presets__item-wrapper"),wt=G("presets__item-wrapper__preset"),Bt=G("presets__item-wrapper__name"),At=((0,K.memo)((e=>{const{presets:t,activePreset:n,onSelectPreset:i,PresetItem:r,detectIsActive:l,detectByIndex:o=!1}=e;return(0,a.createElement)("div",{className:xt},t.map((({name:e,...t},s)=>(0,a.createElement)("div",{key:s,className:O()([Ut],{active:l(n,o?s:t)}),onClick:()=>i(t)},(0,a.createElement)("div",{className:wt},(0,a.createElement)(r,{preset:t})),(0,a.createElement)("span",{className:Bt},e)))))})),G("range-control")),Ht=e=>{const{name:t,label:n,min:i,max:r,unitName:l,dependencyMode:o,dependencies:s,isAdaptive:m=!1}=e,{fieldName:c}=ne(t,m),{value:u,onChange:d,onResetNumberField:g,hasChanges:p}=((e,t)=>{const{value:n,isChanged:a,onChange:i,onReset:r}=te(e),{onResetByFieldName:l,changedFieldsByName:o}=ee();return{value:n,onChange:i,onResetNumberField:()=>{r(),l.get(t)()},hasChanges:a||o.get(t)}})(c,l);return ie(s,o)?(0,a.createElement)("div",{className:At},(0,a.createElement)(V,{condition:Boolean(n)},(0,a.createElement)(nt,{label:n,isChanged:p,onReset:g,unitName:l,showDevicePicker:m})),(0,a.createElement)(R.RangeControl,{value:u,onChange:d,min:i,max:r})):null},kt=G("switch"),Ft=e=>{const{name:t,label:n,dependencyMode:i,dependencies:r,isAdaptive:l=!1}=e,{fieldName:o}=ne(t,l),{value:s,onChange:m}=te(o);return ie(r,i)?(0,a.createElement)("div",{className:kt,"data-has-label":Boolean(n).toString()},(0,a.createElement)(R.ToggleControl,{label:n,checked:s,onChange:m}),(0,a.createElement)(V,{condition:l},(0,a.createElement)(Ie,null))):null},Pt=(G("box-shadow-settings"),G("box-shadow-presets-title"),G("input-field")),Ot=G("input-field-control"),Rt=e=>{const{name:t,label:n,placeholder:i,popoverContent:r,dependencyMode:l,dependencies:o,isAdaptive:s=!1}=e,{fieldName:m}=ne(t,s),{value:c,isChanged:u,onChange:d,onReset:g}=te(m);return ie(o,l)?(0,a.createElement)("div",{className:Pt},(0,a.createElement)(nt,{label:n,isChanged:u,onReset:g,showDevicePicker:s,popoverContent:r}),(0,a.createElement)("div",{className:Ot},(0,a.createElement)(R.__experimentalInputControl,{value:c,onChange:d,placeholder:i}))):null},Vt=(G("number-field"),G("number-field-control"),({className:e})=>(0,a.createElement)("div",{className:e},o.__("No options","masterstudy-lms-learning-management-system"))),Dt=G("select__single-item"),Wt=G("select__container"),zt=G("select__container__multi-item"),It=({multiple:e,value:t,options:n,onChange:i})=>{const{singleValue:r,multipleValue:l}=((e,t,n)=>({singleValue:(0,K.useMemo)((()=>t?null:n.find((t=>t.value===e))?.label),[t,e,n]),multipleValue:(0,K.useMemo)((()=>t?e:null),[t,e])}))(t,e,n);return(0,a.createElement)(V,{condition:e,fallback:(0,a.createElement)("div",{className:Dt},r)},(0,a.createElement)("div",{className:Wt},l?.map((e=>{const t=n.find((t=>t.value===e));return t?(0,a.createElement)("div",{key:t.value,className:zt},(0,a.createElement)("div",null,t.label),(0,a.createElement)(R.Dashicon,{icon:"no-alt",onClick:()=>i(t.value),size:16})):null}))))},Zt=G("select"),jt=G("select__select-box"),$t=G("select__placeholder"),Gt=G("select__select-box-multiple"),Kt=G("select__menu"),Xt=G("select__menu__options-container"),Yt=G("select__menu__item"),qt=e=>{const{options:t,multiple:n=!1,placeholder:i="Select",value:r,onSelect:l}=e,{isOpen:o,onToggle:s,onClose:m}=q(),c=re(m),u=((e,t,n)=>(0,K.useMemo)((()=>n&&Array.isArray(e)?t.filter((t=>!e.includes(t.value))):t.filter((t=>t.value!==e))),[e,t,n]))(r,t,n),d=((e,t,n,a)=>(0,K.useCallback)((i=>{if(t&&Array.isArray(e)){const t=e.includes(i)?e.filter((e=>e!==i)):[...e,i];n(t)}else n(i),a()}),[t,e,n,a]))(r,n,l,m),g=((e,t)=>(0,K.useMemo)((()=>t&&Array.isArray(e)?Boolean(e.length):Boolean(e)),[t,e]))(r,n),p=n&&Array.isArray(r)&&r?.length>0;return(0,a.createElement)("div",{className:Zt,ref:c},(0,a.createElement)("div",{className:O()([jt],{[Gt]:p}),onClick:s},(0,a.createElement)(V,{condition:g,fallback:(0,a.createElement)("div",{className:$t},i)},(0,a.createElement)(It,{onChange:d,options:t,multiple:n,value:r})),(0,a.createElement)(R.Dashicon,{icon:o?"arrow-up-alt2":"arrow-down-alt2",size:16,style:{color:"#4D5E6F"}})),(0,a.createElement)(V,{condition:o},(0,a.createElement)("div",{className:Kt},(0,a.createElement)(V,{condition:Boolean(u.length),fallback:(0,a.createElement)(Vt,{className:Yt})},(0,a.createElement)("div",{className:Xt},u.map((e=>(0,a.createElement)("div",{key:e.value,onClick:()=>d(e.value),className:Yt},e.label))))))))},Jt=G("setting-select"),Qt=e=>{const{name:t,options:n,label:i,multiple:r=!1,placeholder:l,isAdaptive:o=!1,dependencyMode:s,dependencies:m}=e,{fieldName:c}=ne(t,o),{value:u,isChanged:d,onChange:g,onReset:p}=te(c);return ie(m,s)?(0,a.createElement)("div",{className:Jt},(0,a.createElement)(V,{condition:Boolean(i)},(0,a.createElement)(nt,{label:i,isChanged:d,onReset:p,showDevicePicker:o})),(0,a.createElement)(qt,{options:n,value:u,onSelect:g,multiple:r,placeholder:l})):null},en=G("row-select"),tn=G("row-select__label"),nn=G("row-select__control"),an=e=>{const{name:t,label:n,options:i,isAdaptive:r=!1}=e,{fieldName:l}=ne(t,r),{isChanged:o,onReset:s}=te(l);return(0,a.createElement)("div",{className:en},(0,a.createElement)("div",{className:tn},(0,a.createElement)("div",null,n),(0,a.createElement)(V,{condition:r},(0,a.createElement)(Ie,null))),(0,a.createElement)("div",{className:nn},(0,a.createElement)(Qt,{name:t,options:i,isAdaptive:r}),(0,a.createElement)(V,{condition:o},(0,a.createElement)(je,{onReset:s}))))},rn=G("typography-select"),ln=G("typography-select-label"),on=e=>{const{name:t,label:n,options:i,isAdaptive:r=!1}=e,{fieldName:l}=ne(t,r),{isChanged:o,onReset:s}=te(l);return(0,a.createElement)("div",{className:rn},(0,a.createElement)("div",{className:ln},(0,a.createElement)("div",null,n),(0,a.createElement)(V,{condition:r},(0,a.createElement)(Ie,null))),(0,a.createElement)(Qt,{name:t,options:i,isAdaptive:r}),(0,a.createElement)(V,{condition:o},(0,a.createElement)(je,{onReset:s})))},sn=G("typography"),mn=e=>{const{fontSizeName:t,fontWeightName:n,textTransformName:i,fontStyleName:r,textDecorationName:l,lineHeightName:s,letterSpacingName:m,wordSpacingName:c,fontSizeUnitName:u,lineHeightUnitName:d,letterSpacingUnitName:g,wordSpacingUnitName:p,dependencyMode:b,dependencies:h,isAdaptive:y=!1}=e,{fontWeightOptions:v,textTransformOptions:C,fontStyleOptions:_,textDecorationOptions:E}={fontWeightOptions:[{label:o.__("100 (Thin)","masterstudy-lms-learning-management-system"),value:"100"},{label:o.__("200 (Extra Light)","masterstudy-lms-learning-management-system"),value:"200"},{label:o.__("300 (Light)","masterstudy-lms-learning-management-system"),value:"300"},{label:o.__("400 (Normal)","masterstudy-lms-learning-management-system"),value:"400"},{label:o.__("500 (Medium)","masterstudy-lms-learning-management-system"),value:"500"},{label:o.__("600 (Semi Bold)","masterstudy-lms-learning-management-system"),value:"600"},{label:o.__("700 (Bold)","masterstudy-lms-learning-management-system"),value:"700"},{label:o.__("800 (Extra Bold)","masterstudy-lms-learning-management-system"),value:"800"},{label:o.__("900 (Extra)","masterstudy-lms-learning-management-system"),value:"900"}],textTransformOptions:[{label:o.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:o.__("Uppercase","masterstudy-lms-learning-management-system"),value:"uppercase"},{label:o.__("Lowercase","masterstudy-lms-learning-management-system"),value:"lowercase"},{label:o.__("Capitalize","masterstudy-lms-learning-management-system"),value:"capitalize"},{label:o.__("Normal","masterstudy-lms-learning-management-system"),value:"none"}],fontStyleOptions:[{label:o.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:o.__("Normal","masterstudy-lms-learning-management-system"),value:"none"},{label:o.__("Italic","masterstudy-lms-learning-management-system"),value:"italic"},{label:o.__("Oblique","masterstudy-lms-learning-management-system"),value:"oblique"}],textDecorationOptions:[{label:o.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:o.__("Underline","masterstudy-lms-learning-management-system"),value:"underline"},{label:o.__("Line Through","masterstudy-lms-learning-management-system"),value:"line-through"},{label:o.__("None","masterstudy-lms-learning-management-system"),value:"none"}]};return ie(h,b)?(0,a.createElement)("div",{className:sn},(0,a.createElement)(Ht,{name:t,label:o.__("Size","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:u,isAdaptive:y}),(0,a.createElement)(on,{name:n,label:o.__("Weight","masterstudy-lms-learning-management-system"),options:v}),(0,a.createElement)(on,{name:i,label:o.__("Transform","masterstudy-lms-learning-management-system"),options:C}),(0,a.createElement)(on,{name:r,label:o.__("Style","masterstudy-lms-learning-management-system"),options:_}),(0,a.createElement)(on,{name:l,label:o.__("Decoration","masterstudy-lms-learning-management-system"),options:E}),(0,a.createElement)(Ht,{name:s,label:o.__("Line Height","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:d,isAdaptive:y}),(0,a.createElement)(Ht,{name:m,label:o.__("Letter Spacing","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:g,isAdaptive:y}),c&&(0,a.createElement)(Ht,{name:c,label:o.__("Word Spacing","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:p,isAdaptive:y})):null},cn=G("file-upload"),un=G("file-upload__wrap"),dn=G("file-upload__image"),gn=G("file-upload__remove"),pn=G("file-upload__replace"),bn=e=>{const{name:t,label:n,dependencyMode:i,dependencies:l,isAdaptive:s}=e,{fieldName:m}=ne(t,s),{value:c,isChanged:u,onReset:d,onSelectImage:g,onRemoveImage:p,altText:b}=(e=>{const{value:t,isChanged:n,onChange:a,onReset:i}=te(e),[r,l]=(0,K.useState)("");return{value:t,isChanged:n,onReset:i,onSelectImage:e=>{e.url&&(a(e.url),l(e.alt||""))},onRemoveImage:()=>{a(""),l("")},altText:r}})(m);return ie(l,i)?(0,a.createElement)("div",{className:cn},(0,a.createElement)(V,{condition:Boolean(n)},(0,a.createElement)(nt,{label:n,isChanged:u,onReset:d})),(0,a.createElement)(r.MediaUploadCheck,null,(0,a.createElement)(V,{condition:Boolean(c),fallback:(0,a.createElement)(r.MediaPlaceholder,{onSelect:g,allowedTypes:["image"],multiple:!1})},(0,a.createElement)("div",{className:un},(0,a.createElement)("div",{className:dn},(0,a.createElement)("img",{src:c,alt:b}),(0,a.createElement)("button",{className:gn,onClick:p})),(0,a.createElement)(r.MediaUpload,{onSelect:g,allowedTypes:["image"],render:({open:e})=>(0,a.createElement)("button",{onClick:e,className:pn},o.__("Replace Image","masterstudy-lms-learning-management-system"))}))))):null},hn=(0,K.createContext)({activeTab:0,setActiveTab:()=>{}}),yn=()=>{const e=(0,K.useContext)(hn);if(!e)throw new Error("useTabs should be used inside Tabs");return e},vn=({children:e})=>{const[t,n]=(0,K.useState)(0);return(0,a.createElement)(hn.Provider,{value:{activeTab:t,setActiveTab:n}},(0,a.createElement)("div",{className:`active-tab-${t}`},e))},Cn=G("tab-list"),En=({children:e})=>(0,a.createElement)("div",{className:Cn},K.Children.map(e,((e,t)=>(0,K.cloneElement)(e,{index:t})))),Ln=G("tab"),Sn=G("tab-active"),fn=G("content"),Tn=({index:e,title:t,icon:n})=>{const{activeTab:i,setActiveTab:r}=yn();return(0,a.createElement)("div",{className:O()([Ln],{[Sn]:i===e}),onClick:()=>r(e)},(0,a.createElement)("div",{className:fn},(0,a.createElement)("div",null,n),(0,a.createElement)("div",null,t)))},Nn=({children:e})=>(0,a.createElement)("div",null,K.Children.map(e,((e,t)=>(0,K.cloneElement)(e,{index:t})))),Mn=G("tab-panel"),xn=({index:e,children:t})=>{const{activeTab:n}=yn();return n===e?(0,a.createElement)("div",{className:Mn},t):null},Un=({generalTab:e,styleTab:t,advancedTab:n})=>(0,a.createElement)(vn,null,(0,a.createElement)(En,null,(0,a.createElement)(Tn,{title:o.__("General","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(R.Dashicon,{icon:"layout"})}),(0,a.createElement)(Tn,{title:o.__("Style","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(R.Dashicon,{icon:"admin-appearance"})}),(0,a.createElement)(Tn,{title:o.__("Advanced","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(R.Dashicon,{icon:"admin-settings"})})),(0,a.createElement)(Nn,null,(0,a.createElement)(xn,null,e),(0,a.createElement)(xn,null,t),(0,a.createElement)(xn,null,n)));window.ReactDOM;"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement;function wn(e){const t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function Bn(e){return"nodeType"in e}function An(e){var t,n;return e?wn(e)?e:Bn(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function Hn(e){const{Document:t}=An(e);return e instanceof t}function kn(e){return!wn(e)&&e instanceof An(e).HTMLElement}function Fn(e){return e instanceof An(e).SVGElement}function Pn(e){return e?wn(e)?e.document:Bn(e)?Hn(e)?e:kn(e)||Fn(e)?e.ownerDocument:document:document:document}function On(e){return function(t){for(var n=arguments.length,a=new Array(n>1?n-1:0),i=1;i<n;i++)a[i-1]=arguments[i];return a.reduce(((t,n)=>{const a=Object.entries(n);for(const[n,i]of a){const a=t[n];null!=a&&(t[n]=a+e*i)}return t}),{...t})}}const Rn=On(-1);function Vn(e){if(function(e){if(!e)return!1;const{TouchEvent:t}=An(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return function(e){return"clientX"in e&&"clientY"in e}(e)?{x:e.clientX,y:e.clientY}:null}var Dn;!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(Dn||(Dn={}));const Wn=Object.freeze({x:0,y:0});var zn,In,Zn,jn;!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(zn||(zn={}));class $n{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach((e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)}))},this.target=e}add(e,t,n){var a;null==(a=this.target)||a.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function Gn(e,t){const n=Math.abs(e.x),a=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+a**2)>t:"x"in t&&"y"in t?n>t.x&&a>t.y:"x"in t?n>t.x:"y"in t&&a>t.y}function Kn(e){e.preventDefault()}function Xn(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(In||(In={})),(jn=Zn||(Zn={})).Space="Space",jn.Down="ArrowDown",jn.Right="ArrowRight",jn.Left="ArrowLeft",jn.Up="ArrowUp",jn.Esc="Escape",jn.Enter="Enter";Zn.Space,Zn.Enter,Zn.Esc,Zn.Space,Zn.Enter;function Yn(e){return Boolean(e&&"distance"in e)}function qn(e){return Boolean(e&&"delay"in e)}class Jn{constructor(e,t,n){var a;void 0===n&&(n=function(e){const{EventTarget:t}=An(e);return e instanceof t?e:Pn(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;const{event:i}=e,{target:r}=i;this.props=e,this.events=t,this.document=Pn(r),this.documentListeners=new $n(this.document),this.listeners=new $n(n),this.windowListeners=new $n(An(r)),this.initialCoordinates=null!=(a=Vn(i))?a:Wn,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),this.windowListeners.add(In.Resize,this.handleCancel),this.windowListeners.add(In.DragStart,Kn),this.windowListeners.add(In.VisibilityChange,this.handleCancel),this.windowListeners.add(In.ContextMenu,Kn),this.documentListeners.add(In.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(qn(t))return void(this.timeoutId=setTimeout(this.handleStart,t.delay));if(Yn(t))return}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handleStart(){const{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(In.Click,Xn,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(In.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;const{activated:n,initialCoordinates:a,props:i}=this,{onMove:r,options:{activationConstraint:l}}=i;if(!a)return;const o=null!=(t=Vn(e))?t:Wn,s=Rn(a,o);if(!n&&l){if(Yn(l)){if(null!=l.tolerance&&Gn(s,l.tolerance))return this.handleCancel();if(Gn(s,l.distance))return this.handleStart()}return qn(l)&&Gn(s,l.tolerance)?this.handleCancel():void 0}e.cancelable&&e.preventDefault(),r(o)}handleEnd(){const{onEnd:e}=this.props;this.detach(),e()}handleCancel(){const{onCancel:e}=this.props;this.detach(),e()}handleKeydown(e){e.code===Zn.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}const Qn={move:{name:"pointermove"},end:{name:"pointerup"}};(class extends Jn{constructor(e){const{event:t}=e,n=Pn(t.target);super(e,Qn,n)}}).activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;return!(!n.isPrimary||0!==n.button||(null==a||a({event:n}),0))}}];const ea={move:{name:"mousemove"},end:{name:"mouseup"}};var ta;!function(e){e[e.RightClick=2]="RightClick"}(ta||(ta={})),class extends Jn{constructor(e){super(e,ea,Pn(e.event.target))}}.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;return n.button!==ta.RightClick&&(null==a||a({event:n}),!0)}}];const na={move:{name:"touchmove"},end:{name:"touchend"}};var aa,ia,ra,la,oa;(class extends Jn{constructor(e){super(e,na)}static setup(){return window.addEventListener(na.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(na.move.name,e)};function e(){}}}).activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;const{touches:i}=n;return!(i.length>1||(null==a||a({event:n}),0))}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(aa||(aa={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(ia||(ia={})),zn.Backward,zn.Forward,zn.Backward,zn.Forward,function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(ra||(ra={})),function(e){e.Optimized="optimized"}(la||(la={})),ra.WhileDragging,la.Optimized,Map,function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(oa||(oa={})),Zn.Down,Zn.Right,Zn.Up,Zn.Left,o.__("Lectures","masterstudy-lms-learning-management-system"),o.__("Duration","masterstudy-lms-learning-management-system"),o.__("Views","masterstudy-lms-learning-management-system"),o.__("Level","masterstudy-lms-learning-management-system"),o.__("Members","masterstudy-lms-learning-management-system"),o.__("Empty","masterstudy-lms-learning-management-system"),G("sortable__item"),G("sortable__item__disabled"),G("sortable__item__content"),G("sortable__item__content__drag-item"),G("sortable__item__content__drag-item__disabled"),G("sortable__item__content__title"),G("sortable__item__control"),G("sortable__item__icon"),G("nested-sortable"),G("nested-sortable__item"),G("sortable");const sa=G("accordion"),ma=G("accordion__header"),ca=G("accordion__header-flex"),ua=G("accordion__content"),da=G("accordion__icon"),ga=G("accordion__title"),pa=G("accordion__title-disabled"),ba=G("accordion__indicator"),ha=G("accordion__controls"),ya=G("accordion__controls-disabled"),va=({title:e,children:t,accordionFields:n,switchName:i,visible:r=!0,isDefaultOpen:l=!1})=>{const{isOpen:o,onToggle:s,disabled:m,onReset:c,hasChanges:u,onClose:d}=((e,t,n)=>{var a;const{isOpen:i,onToggle:r,onClose:l}=q(t),{defaultValues:o,attributes:s,setAttributes:m}=ee(),c=((e,t,n)=>{for(const a of n)if(!D(e[a],t[a]))return!0;return!1})(o,s,e);return{isOpen:i,onToggle:r,disabled:!(null===(a=s[n])||void 0===a||a),hasChanges:c,onReset:t=>{t.stopPropagation(),m(e.reduce(((e,t)=>(e[t]=o[t],e)),{}))},onClose:l}})(n,l,i);return((e,t)=>{const{attributes:n}=ee(),a=!n[t];(0,K.useEffect)((()=>{a&&e()}),[a,e])})(d,i),r?(0,a.createElement)("div",{className:sa},(0,a.createElement)("div",{className:ma},(0,a.createElement)("div",{className:ca,onClick:m?null:s},(0,a.createElement)("div",{className:O()(ga,{[pa]:m,"with-switch":Boolean(i)})},(0,a.createElement)("div",null,e),(0,a.createElement)(V,{condition:u&&!m},(0,a.createElement)("div",{className:ba}))),(0,a.createElement)("div",{className:O()(ha,{[ya]:m})},(0,a.createElement)(R.Dashicon,{icon:o?"arrow-up-alt2":"arrow-down-alt2",className:da,size:16}))),(0,a.createElement)(V,{condition:Boolean(i)},(0,a.createElement)(Ft,{name:i})),(0,a.createElement)(V,{condition:u&&!m},(0,a.createElement)(je,{onReset:c}))),o&&(0,a.createElement)("div",{className:ua},t)):null};G("preset-picker"),G("preset-picker__label"),G("preset-picker__remove"),G("preset-picker__presets-list"),G("preset-picker__presets-list__item"),G("preset-picker__presets-list__item__preset"),G("preset-picker__presets-list__item__preset-active");const Ca=({categories:e,instructorOptions:t})=>{const{filterOptions:n}=le(),{min:i,max:r}=((e,t=!1)=>{const n=Y(),[a,i]=(0,K.useState)(e.default||{min:3,max:6});return(0,K.useEffect)((()=>{if(n===c.DESKTOP){const n=e.desktop||{min:t?2:3,max:6};i(n)}if(n===c.TABLET){const n=e.tablet||{min:t?1:2,max:3};i(n)}if(n===c.MOBILE){const t=e.mobile||{min:1,max:1};i(t)}}),[n,t,e]),a})({default:{min:3,max:6}});return(0,a.createElement)(a.Fragment,null,(0,a.createElement)(va,{title:o.__("Instructor","masterstudy-lms-learning-management-system"),accordionFields:h},(0,a.createElement)(Ht,{name:"coursesPerPage",label:o.__("Courses per page","masterstudy-lms-learning-management-system"),min:2,max:24}),(0,a.createElement)(Ht,{name:"coursesPerRow",label:o.__("Courses per row","masterstudy-lms-learning-management-system"),min:i,max:r,isAdaptive:!0}),(0,a.createElement)(Qt,{name:"teacherId",label:o.__("Instructor","masterstudy-lms-learning-management-system"),options:t}),(0,a.createElement)(Qt,{name:"orderBy",label:o.__("Sort By","masterstudy-lms-learning-management-system"),options:n}),(0,a.createElement)(Qt,{name:"category",multiple:!0,label:o.__("Course Category","masterstudy-lms-learning-management-system"),options:e}),(0,a.createElement)(Ft,{name:"showLabel",label:o.__("Label","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Rt,{name:"label",label:o.__("Label text","masterstudy-lms-learning-management-system"),placeholder:o.__("Enter text","masterstudy-lms-learning-management-system"),dependencies:[{name:"showLabel",value:!0}]}),(0,a.createElement)(Ft,{name:"showPosition",label:o.__("Position","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Ft,{name:"showBiography",label:o.__("Biography","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Ft,{name:"showViewAllButton",label:o.__('"View All" Button',"masterstudy-lms-learning-management-system")}),(0,a.createElement)(Rt,{name:"buttonText",label:o.__("Button text","masterstudy-lms-learning-management-system"),placeholder:o.__("Enter text","masterstudy-lms-learning-management-system"),dependencies:[{name:"showViewAllButton",value:!0}]}),(0,a.createElement)(Rt,{name:"buttonUrl",label:o.__("Button URL","masterstudy-lms-learning-management-system"),placeholder:o.__("Enter url","masterstudy-lms-learning-management-system"),dependencies:[{name:"showViewAllButton",value:!0}]})))},_a=()=>{const{attributes:e}=ee(),{backgroundSizeOptions:t,backgroundPositionOptions:n,widthOptions:i}=le();return(0,a.createElement)(a.Fragment,null,(0,a.createElement)(va,{title:o.__("Layout","masterstudy-lms-learning-management-system"),accordionFields:v},(0,a.createElement)(pt,{name:"layoutMargin",label:o.__("Margin","masterstudy-lms-learning-management-system"),unitName:"layoutMarginUnit",isAdaptive:!0,dependencies:[{name:"layoutWidth",value:"alignauto"}]}),(0,a.createElement)(pt,{name:"layoutPadding",label:o.__("Padding","masterstudy-lms-learning-management-system"),unitName:"layoutPaddingUnit",isAdaptive:!0}),(0,a.createElement)(lt,{name:"layoutBackground",label:o.__("Background","masterstudy-lms-learning-management-system"),placeholder:o.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(ft,{label:o.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"layoutBorderStyle",borderColorName:"layoutBorderColor",borderWidthName:"layoutBorderWidth",isAdaptive:!0}),(0,a.createElement)(Mt,{name:"layoutBorderRadius",label:o.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,a.createElement)(an,{name:"layoutWidth",label:o.__("Width","masterstudy-lms-learning-management-system"),options:i}),(0,a.createElement)(Ht,{name:"layoutZIndex",label:o.__("Z-Index","masterstudy-lms-learning-management-system"),min:0,max:100,isAdaptive:!0})),(0,a.createElement)(va,{title:o.__("Instructor Container","masterstudy-lms-learning-management-system"),accordionFields:_},(0,a.createElement)(lt,{name:"instructorContainerBackground",label:o.__("Background","masterstudy-lms-learning-management-system"),placeholder:o.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(bn,{name:"instructorContainerBackgroundImage",label:o.__("Background image","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,a.createElement)(Qt,{name:"instructorContainerBackgroundSize",label:o.__("Background Size","masterstudy-lms-learning-management-system"),options:t}),(0,a.createElement)(Qt,{name:"instructorContainerBackgroundPosition",label:o.__("Background Position","masterstudy-lms-learning-management-system"),options:n}),(0,a.createElement)(pt,{name:"instructorContainerPadding",label:o.__("Padding","masterstudy-lms-learning-management-system"),unitName:"instructorContainerPaddingUnit",isAdaptive:!0}),(0,a.createElement)(pt,{name:"instructorContainerMargin",label:o.__("Margin","masterstudy-lms-learning-management-system"),unitName:"instructorContainerMarginUnit",isAdaptive:!0}),(0,a.createElement)(Mt,{name:"instructorBorderRadius",label:o.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0})),(0,a.createElement)(va,{title:o.__("Instructor Label","masterstudy-lms-learning-management-system"),accordionFields:L,visible:!!e.showLabel},(0,a.createElement)(mn,{fontSizeName:"instructorLabelFontSize",fontSizeUnitName:"instructorLabelFontSizeUnit",fontWeightName:"instructorLabelFontWeight",textTransformName:"instructorLabelTextTransform",fontStyleName:"instructorLabelFontStyle",textDecorationName:"instructorLabelTextDecoration",lineHeightName:"instructorLabelLineHeight",lineHeightUnitName:"instructorLabelLineHeightUnit",letterSpacingName:"instructorLabelLetterSpacing",letterSpacingUnitName:"instructorLabelLetterSpacingUnit",wordSpacingName:"instructorLabelWordSpacing",wordSpacingUnitName:"instructorLabelWordSpacingUnit",isAdaptive:!0}),(0,a.createElement)(pt,{name:"instructorLabelPadding",label:o.__("Padding","masterstudy-lms-learning-management-system"),unitName:"instructorLabelPaddingUnit",isAdaptive:!0}),(0,a.createElement)(pt,{name:"instructorLabelMargin",label:o.__("Margin","masterstudy-lms-learning-management-system"),unitName:"instructorLabelMarginUnit",isAdaptive:!0}),(0,a.createElement)(lt,{name:"instructorLabelBackground",label:o.__("Background","masterstudy-lms-learning-management-system"),placeholder:o.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(lt,{name:"instructorLabelColor",label:o.__("Color","masterstudy-lms-learning-management-system"),placeholder:o.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(ft,{label:o.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"instructorLabelBorderStyle",borderColorName:"instructorLabelBorderColor",borderWidthName:"instructorLabelBorderWidth"}),(0,a.createElement)(Mt,{name:"instructorLabelBorderRadius",label:o.__("Border radius","masterstudy-lms-learning-management-system")})),(0,a.createElement)(va,{title:o.__('Instructor "View All" Button',"masterstudy-lms-learning-management-system"),accordionFields:f,visible:!!e.showViewAllButton},(0,a.createElement)(mn,{fontSizeName:"buttonFontSize",fontSizeUnitName:"buttonFontSizeUnit",fontWeightName:"buttonFontWeight",textTransformName:"buttonTextTransform",fontStyleName:"buttonFontStyle",textDecorationName:"buttonTextDecoration",lineHeightName:"buttonLineHeight",lineHeightUnitName:"buttonLineHeightUnit",letterSpacingName:"buttonLetterSpacing",letterSpacingUnitName:"buttonLetterSpacingUnit",wordSpacingName:"buttonWordSpacing",wordSpacingUnitName:"buttonWordSpacingUnit",isAdaptive:!0}),(0,a.createElement)(pt,{name:"buttonPadding",label:o.__("Padding","masterstudy-lms-learning-management-system"),unitName:"buttonPaddingUnit",isAdaptive:!0}),(0,a.createElement)(pt,{name:"buttonMargin",label:o.__("Margin","masterstudy-lms-learning-management-system"),unitName:"buttonMarginUnit",isAdaptive:!0}),(0,a.createElement)(lt,{name:"buttonBackground",label:o.__("Background","masterstudy-lms-learning-management-system"),placeholder:o.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,a.createElement)(lt,{name:"buttonColor",label:o.__("Color","masterstudy-lms-learning-management-system"),placeholder:o.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,a.createElement)(ft,{label:o.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"buttonBorderStyle",borderColorName:"buttonBorderColor",borderWidthName:"buttonBorderWidth",isAdaptive:!0}),(0,a.createElement)(Mt,{name:"buttonBorderRadius",label:o.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0})),(0,a.createElement)(va,{title:o.__("Instructor Name","masterstudy-lms-learning-management-system"),accordionFields:N},(0,a.createElement)(mn,{fontSizeName:"nameFontSize",fontSizeUnitName:"nameFontSizeUnit",fontWeightName:"nameFontWeight",textTransformName:"nameTextTransform",fontStyleName:"nameFontStyle",textDecorationName:"nameTextDecoration",lineHeightName:"nameLineHeight",lineHeightUnitName:"nameLineHeightUnit",letterSpacingName:"nameLetterSpacing",letterSpacingUnitName:"nameLetterSpacingUnit",wordSpacingName:"nameWordSpacing",wordSpacingUnitName:"nameWordSpacingUnit",isAdaptive:!0}),(0,a.createElement)(pt,{name:"namePadding",label:o.__("Padding","masterstudy-lms-learning-management-system"),unitName:"namePaddingUnit",isAdaptive:!0}),(0,a.createElement)(pt,{name:"nameMargin",label:o.__("Margin","masterstudy-lms-learning-management-system"),unitName:"nameMarginUnit",isAdaptive:!0}),(0,a.createElement)(lt,{name:"nameColor",label:o.__("Color","masterstudy-lms-learning-management-system"),placeholder:o.__("Select color","masterstudy-lms-learning-management-system")})),(0,a.createElement)(va,{title:o.__("Instructor Position","masterstudy-lms-learning-management-system"),accordionFields:x,visible:!!e.showPosition},(0,a.createElement)(mn,{fontSizeName:"positionFontSize",fontSizeUnitName:"positionFontSizeUnit",fontWeightName:"positionFontWeight",textTransformName:"positionTextTransform",fontStyleName:"positionFontStyle",textDecorationName:"positionTextDecoration",lineHeightName:"positionLineHeight",lineHeightUnitName:"positionLineHeightUnit",letterSpacingName:"positionLetterSpacing",letterSpacingUnitName:"positionLetterSpacingUnit",wordSpacingName:"positionWordSpacing",wordSpacingUnitName:"positionWordSpacingUnit",isAdaptive:!0}),(0,a.createElement)(pt,{name:"positionMargin",label:o.__("Margin","masterstudy-lms-learning-management-system"),unitName:"positionMarginUnit",isAdaptive:!0}),(0,a.createElement)(lt,{name:"positionColor",label:o.__("Color","masterstudy-lms-learning-management-system"),placeholder:o.__("Select color","masterstudy-lms-learning-management-system")})),(0,a.createElement)(va,{title:o.__("Instructor Biography","masterstudy-lms-learning-management-system"),accordionFields:w,visible:!!e.showBiography},(0,a.createElement)(mn,{fontSizeName:"biographyFontSize",fontSizeUnitName:"biographyFontSizeUnit",fontWeightName:"biographyFontWeight",textTransformName:"biographyTextTransform",fontStyleName:"biographyFontStyle",textDecorationName:"biographyTextDecoration",lineHeightName:"biographyLineHeight",lineHeightUnitName:"biographyLineHeightUnit",letterSpacingName:"biographyLetterSpacing",letterSpacingUnitName:"biographyLetterSpacingUnit",wordSpacingName:"biographyWordSpacing",wordSpacingUnitName:"biographyWordSpacingUnit",isAdaptive:!0}),(0,a.createElement)(pt,{name:"biographyMargin",label:o.__("Margin","masterstudy-lms-learning-management-system"),unitName:"biographyMarginUnit",isAdaptive:!0}),(0,a.createElement)(lt,{name:"biographyColor",label:o.__("Color","masterstudy-lms-learning-management-system"),placeholder:o.__("Select color","masterstudy-lms-learning-management-system")})),(0,a.createElement)(va,{title:o.__("Instructor Courses Title","masterstudy-lms-learning-management-system"),accordionFields:A},(0,a.createElement)(mn,{fontSizeName:"courseTitleFontSize",fontSizeUnitName:"courseTitleFontSizeUnit",fontWeightName:"courseTitleFontWeight",textTransformName:"courseTitleTextTransform",fontStyleName:"courseTitleFontStyle",textDecorationName:"courseTitleTextDecoration",lineHeightName:"courseTitleLineHeight",lineHeightUnitName:"courseTitleLineHeightUnit",letterSpacingName:"courseTitleLetterSpacing",letterSpacingUnitName:"courseTitleLetterSpacingUnit",wordSpacingName:"courseTitleWordSpacing",wordSpacingUnitName:"courseTitleWordSpacingUnit",isAdaptive:!0}),(0,a.createElement)(pt,{name:"courseTitleMargin",label:o.__("Margin","masterstudy-lms-learning-management-system"),unitName:"courseTitleMarginUnit",isAdaptive:!0}),(0,a.createElement)(lt,{name:"courseTitleColor",label:o.__("Color","masterstudy-lms-learning-management-system"),placeholder:o.__("Select color","masterstudy-lms-learning-management-system")})))},Ea=({attributes:e,setAttributes:t,categories:n,instructorOptions:i})=>{const{onResetByFieldName:o,changedFieldsByName:s}=((e,t,n,a=[])=>{const i=(e=>{const t={};return Object.entries(e).forEach((([e,n])=>{e.includes("UAG")||(t[e]=n)})),t})(t),r=!D(e,i),l=((e,t,n)=>{const a=new Map;return n.forEach((n=>{a.has(n)||a.set(n,(()=>t({[n]:e[n]})))})),a})(e,n,a),o=((e,t,n)=>{const a=new Map;return n.forEach((n=>{a.has(n)?a.set(n,!1):a.set(n,!D(e[n],t[n]))})),a})(e,i,a);return{hasChanges:r,onResetByFieldName:l,changedFieldsByName:o}})(k,e,t,Object.keys(k));return(0,a.createElement)(r.InspectorControls,null,(0,a.createElement)(Q,{attributes:e,setAttributes:t,defaultValues:k,onResetByFieldName:o,changedFieldsByName:s},(0,a.createElement)(Un,{generalTab:(0,a.createElement)(Ca,{categories:n,instructorOptions:i}),styleTab:(0,a.createElement)(_a,null),advancedTab:(0,a.createElement)(l,null)})))},La=window.wp.apiFetch;var Sa=n.n(La);const fa=()=>{const[e,t]=(0,K.useState)(!0),[n,a]=(0,K.useState)("");return{isFetching:e,setIsFetching:t,error:n,setError:a}},Ta=[["masterstudy/featured-teacher-about"],["core/group",{className:"lms-courses-group-presets",style:{spacing:{margin:{top:"0px",bottom:"0px"}}}},[["masterstudy/courses-preset",{cntrMargin:{top:"-150",right:"",bottom:"",left:""},cntrPaddingMobile:{top:"",left:"30",bottom:"",right:"30"}}]]],["masterstudy/featured-teacher-button"]],Na=JSON.parse('{"UU":"masterstudy/featured-teacher"}');(0,i.registerBlockType)(Na.UU,{icon:{src:(0,a.createElement)("svg",{width:"512",height:"513",viewBox:"0 0 512 513",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,a.createElement)("g",{clipPath:"url(#clip0_708_21913)"},(0,a.createElement)("rect",{opacity:"0.3",x:"223.095",y:"41.4032",width:"247.312",height:"244",rx:"20",fill:"#227AFF"}),(0,a.createElement)("path",{d:"M9.60263 475.434V486.312C9.60263 500.418 20.9898 511.805 35.0964 511.805H43.5943C46.5686 511.805 49.5429 510.956 52.0923 509.426C54.6416 510.956 57.6159 511.805 60.5902 511.805H162.565C171.913 511.805 179.561 504.157 179.561 494.81V392.665L188.144 408.726C191.883 417.563 199.616 424.022 208.964 426.061C218.057 428.186 227.574 425.891 234.628 419.773L290.459 378.303C293.433 375.669 295.303 371.93 295.558 367.851C302.186 362.327 309.749 358.163 317.907 355.444C320.287 354.764 322.496 353.659 324.536 352.299C331.249 348.05 336.008 341.252 337.538 333.434C338.047 330.545 338.047 327.57 337.623 324.681H485.486C499.593 324.681 510.98 313.294 510.98 299.187V27.2538C510.98 13.1472 499.593 1.76001 485.486 1.76001H128.574C114.467 1.76001 103.08 13.1472 103.08 27.2538V95.7471C64.4142 100.081 35.1814 132.798 35.0964 171.719V197.212C35.0964 208.175 42.1497 217.947 52.5172 221.431C54.5567 238.172 63.7344 253.299 77.586 262.986V275.478L49.4579 281.767C21.1598 287.97 0.934742 313.039 1.1047 342.017V460.733C1.1047 466.851 4.33391 472.375 9.60263 475.434ZM35.0964 494.81C30.4225 494.81 26.5985 490.986 26.5985 486.312V477.814H43.5943V494.81H35.0964ZM60.5902 477.814H103.08V494.81H60.5902V477.814ZM120.076 494.81V477.814H162.565V494.81H120.076ZM265.475 334.114C261.481 333.689 257.487 334.794 254.343 337.343L234.543 353.829L223.665 324.851H274.908C271.934 328.08 268.789 331.225 265.475 334.114ZM315.188 338.108C314.508 338.533 313.828 338.873 313.064 339.128C303.886 342.017 295.303 346.436 287.655 352.299L279.922 344.056C287.4 337.258 293.518 329.27 298.192 320.347C298.702 319.497 299.467 318.903 300.317 318.393C306.435 314.569 314.508 316.013 318.927 321.707C320.712 324.171 321.392 327.231 320.797 330.29C320.117 333.519 318.077 336.408 315.188 338.108ZM128.574 18.9258H485.486C490.16 18.9258 493.984 22.7499 493.984 27.4237V299.357C493.984 304.031 490.16 307.855 485.486 307.855H329.125C325.301 304.286 320.627 301.737 315.528 300.462V239.872C315.528 235.198 311.704 231.374 307.03 231.374C302.356 231.374 298.532 235.198 298.532 239.872V300.887C295.983 301.652 293.518 302.756 291.309 304.116C289.609 305.136 288.08 306.411 286.635 307.855H214.488C205.82 296.213 193.243 288.055 179.051 284.911L145.569 275.818V263.071C159.421 253.384 168.599 238.342 170.638 221.516C181.006 218.032 188.059 208.345 188.059 197.297V171.803C187.974 132.883 158.741 100.166 120.076 95.832V27.4237C120.076 22.6649 123.815 18.9258 128.574 18.9258ZM128.404 398.273L111.578 409.49L94.7518 398.273L101.975 350.26H121.35L128.404 398.273ZM82.1749 291.879C85.744 296.468 90.2479 300.207 95.3467 302.841C92.2874 307.175 89.993 311.934 88.5484 317.033C88.2084 318.053 87.4436 318.818 86.4239 319.158C85.4041 319.497 84.2144 319.327 83.2796 318.733C75.5465 312.614 69.428 304.711 65.519 295.618L82.1749 291.879ZM100.7 333.349L99.6806 330.375C102.06 327.995 103.845 325.106 104.779 321.792C105.969 317.203 108.264 313.039 111.578 309.64C114.807 313.039 117.101 317.118 118.291 321.707C119.226 325.021 121.01 327.995 123.475 330.375L122.455 333.349H100.7ZM139.791 318.733C138.856 319.412 137.751 319.497 136.647 319.158C135.627 318.818 134.777 317.968 134.522 316.948C133.077 311.934 130.783 307.175 127.809 302.926C132.823 300.292 137.241 296.638 140.811 292.134L157.127 296.553C153.218 305.221 147.269 312.869 139.791 318.733ZM111.578 112.403C144.38 112.403 171.063 139.086 171.063 171.888V173.418C170.723 173.333 170.468 173.163 170.128 173.078C166.219 159.227 151.773 151.154 137.836 155.063C135.372 155.742 132.908 156.847 130.783 158.292C125.004 161.861 118.376 163.645 111.578 163.476C104.779 163.645 98.151 161.861 92.3724 158.292C80.2204 150.304 63.9894 153.788 56.1713 165.94C54.7266 168.149 53.7069 170.529 52.9421 172.993C52.6021 173.078 52.3472 173.248 52.0073 173.333V171.803C52.0923 139.001 78.6908 112.403 111.578 112.403ZM60.5902 205.88C55.9163 205.88 52.0923 202.056 52.0923 197.382C52.0923 192.708 55.9163 188.884 60.5902 188.884C65.349 188.544 69.0881 184.55 69.0881 179.707C69.1731 176.477 70.9576 173.503 73.8469 172.058C75.2066 171.379 76.6513 170.954 78.1809 170.954C79.8805 170.954 81.5801 171.464 82.9397 172.398C91.4377 177.837 101.38 180.556 111.493 180.386C121.605 180.641 131.548 177.837 140.046 172.398C142.765 170.614 146.334 170.444 149.224 172.058C152.283 173.673 154.152 176.902 153.982 180.386C153.982 185.06 157.806 188.884 162.48 188.884C167.154 188.884 170.978 192.708 170.978 197.382C170.978 202.056 167.154 205.88 162.48 205.88C157.806 205.88 153.982 209.704 153.982 214.378C153.982 237.832 134.947 256.868 111.493 256.868C88.0385 256.868 69.0031 237.832 69.0031 214.378C69.0881 209.619 65.264 205.88 60.5902 205.88ZM111.578 273.864C117.356 273.864 123.05 273.014 128.574 271.399V280.237C124.665 285.931 118.461 289.755 111.578 290.774C104.694 289.755 98.5759 285.931 94.5819 280.237V271.399C100.021 273.014 105.799 273.779 111.578 273.864ZM18.1006 342.017C18.0156 322.727 30.5075 305.646 48.7781 299.697C53.8768 312.614 62.2898 323.831 73.1671 332.414C76.2264 334.624 79.8805 335.983 83.7045 336.153L85.829 342.527L77.671 396.829C76.8212 402.692 79.4556 408.556 84.3844 411.87L102.91 424.192C108.094 427.676 114.892 427.676 120.076 424.192L138.601 411.87C143.53 408.556 146.164 402.692 145.314 396.744L137.156 342.527L139.281 336.238C143.105 335.983 146.759 334.709 149.818 332.414C160.356 324.171 168.599 313.379 173.698 300.972L174.972 301.312C189.759 304.541 201.911 315.079 207.179 329.27L218.567 359.693C221.796 368.276 231.398 372.609 239.981 369.465C241.596 368.87 243.126 367.936 244.57 366.916L263.691 351.62L277.712 366.576L223.92 406.516C220.861 409.236 216.697 410.34 212.703 409.405C208.454 408.386 205.055 405.411 203.44 401.332L178.541 354.679C176.332 350.515 171.148 348.985 167.069 351.195C164.265 352.639 162.565 355.529 162.565 358.673V460.648H60.5902V358.673C60.5902 353.999 56.7661 350.175 52.0923 350.175C47.4184 350.175 43.5943 353.999 43.5943 358.673V460.648H18.1006V342.017Z",fill:"black"}),(0,a.createElement)("path",{d:"M162.566 512.825H60.5905C57.6162 512.825 54.7269 512.06 52.0925 510.616C49.4582 512.06 46.5689 512.825 43.5946 512.825H35.0967C20.3953 512.825 8.49819 500.928 8.49819 486.312V476.029C3.22947 472.8 0.000261316 467.021 0.000261316 460.818V342.102C-0.0847179 312.784 20.5652 287.035 49.2032 280.747L76.4816 274.628V263.581C62.7999 253.893 53.7921 238.852 51.4977 222.281C41.0452 218.542 33.992 208.599 33.992 197.382V171.888C34.0769 132.713 63.2248 99.826 101.975 94.9822V27.4237C101.975 12.7223 113.872 0.825195 128.574 0.825195H485.487C500.103 0.825195 512.085 12.7223 512.085 27.4237V299.357C512.085 313.974 500.188 325.956 485.487 325.956H338.812C339.067 328.59 338.982 331.225 338.558 333.774C336.943 341.847 332.099 348.985 325.131 353.319C323.006 354.679 320.712 355.784 318.248 356.548C310.344 359.183 303.036 363.177 296.578 368.445C296.238 372.524 294.283 376.434 291.139 379.153L235.223 420.708C228 426.996 218.057 429.376 208.709 427.251C199.022 425.127 191.034 418.413 187.125 409.32L180.581 397.083V494.895C180.581 504.752 172.508 512.825 162.566 512.825ZM52.0925 508.151L52.6024 508.491C54.9818 509.936 57.7862 510.701 60.5905 510.701H162.566C171.318 510.701 178.542 503.562 178.542 494.725V388.501L189.164 408.301C192.818 416.884 200.296 423.087 209.304 425.042C218.057 427.081 227.32 424.787 234.033 418.923L289.864 377.368C292.584 374.989 294.283 371.42 294.538 367.766V367.341L294.878 367.086C301.592 361.477 309.24 357.228 317.568 354.509C319.862 353.829 321.987 352.809 323.941 351.535C330.4 347.456 334.988 340.912 336.433 333.434C336.943 330.715 336.943 327.825 336.518 325.021L336.348 323.746H485.402C498.828 323.746 509.876 312.784 509.876 299.272V27.3387C509.876 13.827 498.913 2.8647 485.402 2.8647H128.489C114.977 2.8647 104.015 13.827 104.015 27.3387V96.7668L103.08 96.8518C64.9244 101.186 36.1164 133.393 36.0315 171.803V197.297C36.0315 207.835 42.7448 217.182 52.7724 220.497L53.3672 220.667L53.4522 221.346C55.4917 237.832 64.4995 252.704 78.0962 262.136L78.5211 262.476V276.328L49.5432 282.786C21.8399 288.82 1.8698 313.719 2.03976 342.017V460.733C2.03976 466.342 5.09902 471.61 10.0278 474.415L10.5377 474.755V486.227C10.5377 499.653 21.5 510.701 35.0117 510.701H43.5096C46.3139 510.701 49.0333 509.936 51.4977 508.491L52.0925 508.151ZM163.585 495.829H118.971V476.709H163.585V495.829ZM121.096 493.705H161.461V478.834H121.096V493.705ZM104.1 495.829H59.4857V476.709H104.1V495.829ZM61.6102 493.705H101.975V478.834H61.6102V493.705ZM44.6144 495.829H35.0967C29.743 495.829 25.494 491.58 25.494 486.312V476.709H44.6144V495.829ZM27.6185 478.834V486.312C27.6185 490.391 30.9327 493.79 35.0967 493.79H42.4899V478.918H27.6185V478.834ZM163.585 461.838H59.4857V358.843C59.4857 354.764 56.1715 351.365 52.0076 351.365C47.8436 351.365 44.5294 354.679 44.5294 358.843V461.923H16.9961V342.102C16.9111 322.302 29.573 304.881 48.4384 298.762L49.3732 298.423L49.7131 299.357C54.7269 312.104 63.0549 323.236 73.7623 331.564C76.6516 333.689 80.1357 334.879 83.7048 335.134L84.3847 335.219L86.8491 342.527L78.691 397.083C77.9262 402.522 80.3906 408.046 84.9795 411.105L103.505 423.427C108.349 426.656 114.637 426.656 119.481 423.427L138.007 411.105C142.595 408.046 145.06 402.522 144.295 396.998L136.137 342.527L138.601 335.304L139.281 335.219C142.85 334.964 146.335 333.774 149.224 331.649C159.591 323.576 167.749 312.869 172.678 300.632L173.018 299.782L175.227 300.377C190.354 303.691 202.676 314.399 208.114 329.015L219.502 359.438C220.946 363.347 223.836 366.406 227.66 368.106C231.399 369.805 235.648 369.975 239.557 368.53C241.086 367.936 242.531 367.171 243.806 366.151L263.691 350.26L279.157 366.831L278.222 367.511L224.43 407.451C221.201 410.34 216.612 411.53 212.363 410.51C207.775 409.405 204.035 406.176 202.336 401.757L177.522 355.274C176.587 353.489 175.058 352.214 173.103 351.705C171.233 351.11 169.194 351.365 167.409 352.299C164.945 353.574 163.5 356.123 163.5 358.843V461.838H163.585ZM61.6102 459.713H161.461V358.843C161.461 355.274 163.415 352.044 166.475 350.43C168.684 349.24 171.318 348.985 173.783 349.665C176.247 350.43 178.202 352.044 179.476 354.339L204.375 400.993C205.82 404.817 209.049 407.621 212.873 408.556C216.527 409.405 220.351 408.386 223.156 405.921L276.013 366.661L263.521 353.319L245.165 368.021C243.721 369.21 242.106 370.145 240.322 370.74C235.903 372.439 231.144 372.27 226.81 370.315C222.476 368.36 219.247 364.876 217.547 360.457L206.16 330.035C200.976 316.098 189.249 305.901 174.718 302.757L174.293 302.672C169.194 314.824 160.951 325.531 150.499 333.689C147.439 335.898 143.87 337.258 140.131 337.683L138.346 343.122L146.419 397.083C147.354 403.372 144.465 409.745 139.196 413.23L120.671 425.552C115.062 429.291 107.924 429.291 102.315 425.552L83.7898 413.23C78.5211 409.66 75.7168 403.372 76.5666 397.083L84.6396 343.037L82.77 337.598C79.031 337.258 75.4618 335.898 72.4026 333.689C61.6102 325.276 53.1973 314.144 48.0135 301.482C30.5078 307.6 18.9506 323.916 18.9506 342.527V459.713H42.4899V358.843C42.4899 353.489 46.7388 349.24 52.0925 349.24C57.4462 349.24 61.6952 353.489 61.6952 358.843V459.713H61.6102ZM111.578 410.765L93.4774 398.783L100.956 349.24H122.115L122.285 350.175L129.594 398.868L111.578 410.765ZM95.8568 397.763L111.578 408.216L127.299 397.763L120.331 351.365H102.825L95.8568 397.763ZM234.033 355.614L222.136 323.746H277.288L275.758 325.531C272.784 328.845 269.554 332.074 266.24 334.964L265.9 335.304L265.39 335.219C261.651 334.794 257.997 335.813 255.023 338.193L234.033 355.614ZM225.195 325.871L235.053 352.044L253.663 336.578C256.893 334.029 261.057 332.754 265.136 333.094C267.685 330.8 270.149 328.42 272.529 325.956H225.195V325.871ZM287.485 353.744L278.392 343.971L279.157 343.207C286.55 336.493 292.584 328.59 297.173 319.752C297.853 318.733 298.702 317.883 299.722 317.373C306.265 313.294 314.848 314.824 319.607 320.942C321.562 323.661 322.327 327.061 321.732 330.375C320.967 333.944 318.757 337.088 315.698 338.958L314.084 339.892C313.829 339.977 313.574 340.062 313.319 340.147C304.226 343.037 295.728 347.371 288.25 353.234L287.485 353.744ZM281.367 344.141L287.74 350.94C295.303 345.246 303.716 340.997 312.724 338.108C313.404 337.938 313.999 337.598 314.593 337.173L315.783 336.408C317.823 334.794 319.182 332.499 319.692 330.035C320.202 327.4 319.607 324.511 317.993 322.302C313.914 317.033 306.52 315.758 300.827 319.327C300.062 319.752 299.467 320.262 299.042 320.942C294.538 329.61 288.59 337.428 281.367 344.141ZM123.135 334.369H99.8509L98.4062 330.035L98.9161 329.61C101.211 327.4 102.825 324.596 103.76 321.537C104.95 316.863 107.414 312.444 110.728 308.96L111.493 308.195L112.258 308.96C115.572 312.444 118.036 316.778 119.226 321.452C120.076 324.511 121.776 327.315 124.07 329.61L124.58 330.035L123.135 334.369ZM101.465 332.244H121.691L122.2 330.63C119.906 328.165 118.121 325.191 117.272 321.962C116.252 317.968 114.297 314.314 111.578 311.169C108.859 314.314 106.904 318.053 105.884 322.047C104.95 325.276 103.25 328.165 100.956 330.63L101.465 332.244ZM137.837 320.432C137.327 320.432 136.817 320.347 136.307 320.177C134.947 319.752 133.928 318.648 133.503 317.288C132.058 312.359 129.849 307.77 126.959 303.521L126.279 302.502L127.299 301.907C132.228 299.272 136.477 295.788 139.961 291.454L140.386 290.944L141.066 291.114L158.572 295.873L158.062 296.978C154.153 305.816 148.119 313.634 140.471 319.582L140.131 319.837C139.366 320.177 138.601 320.432 137.837 320.432ZM129.339 303.266C132.058 307.43 134.182 311.849 135.542 316.608C135.712 317.288 136.307 317.883 136.987 318.138C137.752 318.393 138.516 318.308 139.111 317.883L139.366 317.713C146.25 312.274 151.773 305.221 155.512 297.233L141.066 293.324C137.837 297.403 133.843 300.717 129.339 303.266ZM85.2344 320.432C84.2997 320.432 83.3649 320.177 82.6001 319.582C74.697 313.379 68.4086 305.221 64.4995 296.043L64.0746 294.853L82.6001 290.774L83.025 291.284C86.5091 295.788 90.8431 299.357 95.8568 301.992L96.9616 302.587L96.2817 303.606C93.3075 307.855 91.098 312.529 89.6534 317.458C89.2285 318.818 88.2087 319.837 86.8491 320.262C86.2542 320.347 85.7443 320.432 85.2344 320.432ZM66.9639 296.383C70.788 304.796 76.5666 312.189 83.8748 317.883C84.4696 318.308 85.3194 318.393 85.9993 318.138C86.6791 317.883 87.2739 317.373 87.4439 316.693C88.8886 311.934 90.9281 307.345 93.7324 303.181C89.1435 300.547 85.0645 297.148 81.7503 292.984L66.9639 296.383ZM485.487 308.875H328.615L328.275 308.62C324.621 305.136 320.032 302.672 315.188 301.482L314.423 301.227V239.787C314.423 235.708 311.109 232.309 306.945 232.309C302.781 232.309 299.467 235.623 299.467 239.787V301.567L298.702 301.822C296.238 302.502 293.859 303.606 291.734 304.966C290.119 305.986 288.59 307.175 287.315 308.535L286.975 308.875H213.893L213.553 308.45C205.055 297.063 192.648 288.99 178.797 285.931L144.465 276.668V262.561L144.89 262.221C158.487 252.789 167.494 237.917 169.534 221.431L169.619 220.752L170.214 220.582C180.241 217.267 186.955 207.92 186.955 197.382V171.888C186.87 133.478 158.062 101.271 119.906 96.9367L118.971 96.8518V27.4237C118.971 22.07 123.22 17.821 128.574 17.821H485.487C490.755 17.821 495.089 22.07 495.089 27.4237V299.357C495.004 304.626 490.755 308.875 485.487 308.875ZM329.465 306.751H485.402C489.481 306.751 492.88 303.436 492.88 299.272V27.3387C492.88 23.2597 489.566 19.8605 485.402 19.8605H128.489C124.41 19.8605 121.011 23.1747 121.011 27.3387V94.8972C159.761 99.7411 188.909 132.628 188.994 171.803V197.297C188.994 208.514 181.941 218.457 171.488 222.281C169.279 238.852 160.186 253.893 146.504 263.581V274.968L179.221 283.806C193.413 287.035 206.075 295.108 214.913 306.751H286.04C287.4 305.391 288.93 304.201 290.544 303.181C292.669 301.907 294.963 300.802 297.258 300.037V239.872C297.428 234.518 301.677 230.269 307.03 230.269C312.384 230.269 316.633 234.518 316.633 239.872V299.612C321.392 300.887 325.811 303.351 329.465 306.751ZM111.578 291.794H111.408C104.27 290.774 97.8114 286.78 93.7324 280.832L93.4774 280.492V269.87L94.8371 270.294C105.714 273.524 117.357 273.524 128.234 270.294L129.594 269.87V280.407L129.424 280.662C125.345 286.61 118.886 290.604 111.748 291.709L111.578 291.794ZM95.6019 279.812C99.341 285.081 105.12 288.65 111.578 289.585C117.951 288.565 123.73 284.996 127.554 279.727V272.759C117.102 275.648 106.139 275.648 95.6869 272.759V279.812H95.6019ZM111.578 257.887C87.5289 257.887 67.9837 238.342 67.9837 214.293C67.9837 210.214 64.6695 206.815 60.5055 206.815C55.2368 206.9 50.9878 202.651 50.9878 197.382C50.9878 192.114 55.2368 187.78 60.5905 187.78C64.6695 187.44 67.9837 183.955 67.9837 179.707C68.0686 176.052 70.1081 172.738 73.3373 171.124C74.782 170.359 76.4816 169.934 78.1812 169.934C80.0507 169.934 81.9202 170.529 83.5349 171.549C91.8628 176.902 101.635 179.537 111.493 179.367C121.521 179.622 131.123 176.902 139.536 171.549C142.595 169.509 146.504 169.424 149.734 171.209C153.133 172.993 155.257 176.647 155.087 180.471C155.087 184.55 158.402 187.865 162.566 187.865C167.834 187.865 172.168 192.113 172.168 197.467C172.168 202.821 167.919 207.07 162.566 207.07C158.487 207.07 155.087 210.384 155.087 214.548C155.087 238.342 135.542 257.887 111.578 257.887ZM78.1812 172.058C76.7365 171.973 75.4618 172.398 74.2721 172.993C71.7227 174.268 70.1081 176.902 70.0232 179.707C70.0232 184.975 65.8592 189.479 60.5055 189.904C56.3415 189.904 53.0273 193.218 53.0273 197.382C53.0273 201.546 56.4265 204.775 60.5905 204.775C65.8592 204.775 70.1931 209.024 70.1931 214.378C70.1931 237.237 88.8036 255.848 111.663 255.848C134.522 255.848 152.963 237.153 152.963 214.378C152.963 209.024 157.212 204.775 162.566 204.775C166.645 204.775 170.044 201.461 170.044 197.297C170.044 193.133 166.73 189.819 162.566 189.819C157.297 189.819 152.963 185.57 152.963 180.216C153.048 177.157 151.433 174.268 148.714 172.823C146.165 171.379 143.105 171.549 140.641 173.078C132.228 178.432 122.625 181.236 112.683 181.236C111.833 181.236 111.068 181.236 110.218 181.236C100.361 181.236 90.7581 178.432 82.3451 173.078C81.1554 172.483 79.7108 172.058 78.1812 172.058ZM50.9878 174.948V171.888C50.9878 138.492 78.1812 111.298 111.578 111.298C144.89 111.298 172.083 138.492 172.168 171.888V174.948L170.724 174.438C170.554 174.353 170.384 174.268 170.214 174.268C170.044 174.183 169.959 174.183 169.789 174.098L169.279 173.928L169.109 173.418C165.37 160.161 151.433 152.428 138.177 156.167C135.797 156.847 133.503 157.867 131.378 159.227C125.43 162.966 118.631 164.835 111.578 164.58C104.695 164.835 97.8114 162.966 91.7778 159.227C86.1692 155.572 79.4559 154.383 72.9125 155.742C66.369 157.187 60.7604 161.011 57.1063 166.62C55.7466 168.744 54.7269 170.954 54.0471 173.418L53.8771 173.928L53.3672 174.098C53.1973 174.183 53.0273 174.183 52.9423 174.268L50.9878 174.948ZM144.975 152.938C149.564 152.938 154.068 154.128 158.147 156.422C163.415 159.396 167.494 163.9 169.874 169.339C168.514 138.322 142.85 113.508 111.493 113.423C80.1357 113.423 54.472 138.322 53.1123 169.339C53.7071 167.979 54.387 166.62 55.2368 165.43C59.1458 159.312 65.2643 155.148 72.4026 153.618C79.5408 152.088 86.7641 153.448 92.8826 157.357C98.4912 160.841 104.95 162.626 111.493 162.371C118.121 162.541 124.58 160.841 130.188 157.357C132.483 155.912 134.947 154.808 137.497 154.043C140.046 153.278 142.51 152.938 144.975 152.938Z",fill:"black"})),(0,a.createElement)("defs",null,(0,a.createElement)("clipPath",{id:"clip0_708_21913"},(0,a.createElement)("rect",{width:"512",height:"512",fill:"white",transform:"translate(0 0.825195)"}))))},edit:({attributes:e,setAttributes:t})=>{const n=(0,r.useBlockProps)({className:`lms-teacher-container ${e.layoutWidth}`,style:$("teacher",e,F)}),{categories:i}=((e=!1)=>{const[t,n]=(0,K.useState)([]),[a,i]=(0,K.useState)({}),[r,l]=(0,K.useState)({}),{setIsFetching:o,setError:s,isFetching:m,error:c}=fa();return(0,K.useEffect)((()=>{o(!0),(async(e=!1)=>{try{let t="?children=true";return e&&(t+="&details=true"),await Sa()({path:`masterstudy-lms/v2/course-categories${t}`})}catch(e){throw new Error(e)}})(e).then((({categories:e})=>{n((e=>e.map((e=>({label:e.name,value:e.id,image:e.image,icon:e.icon,color:e.color,children:e.children?W(e.children):[]}))))(e)),i(e.reduce(((e,t)=>(e[String(t.id)]=t.name,e)),{})),l(e.reduce(((e,t)=>(e[String(t.id)]={label:t.name,value:t.id,image:t.image,icon:t.icon,color:t.color,courses:t.courses,children:t.children},e)),{}))})).catch((e=>{s(e.message)})).finally((()=>{o(!1)}))}),[]),{categories:t,categoriesMap:a,categoriesMapFull:r,isFetching:m,error:c}})(),{instructorOptions:l}=((e=!0)=>{const[t,n]=(0,K.useState)([]),{setIsFetching:a,setError:i,isFetching:r,error:l}=fa();return(0,K.useEffect)((()=>{a(!0),(async e=>{let t="stm_lms_instructor";e&&(t+=",administrator");try{return await Sa()({path:`masterstudy-lms/v2/users?roles=${t}&context=edit`})}catch(e){throw new Error(e)}})(e).then((e=>{n(e.map((e=>({label:e.name,value:e.id}))))})).catch((e=>{i(e.message)})).finally((()=>{a(!1)}))}),[]),{instructorOptions:t,isFetching:r,error:l}})(),o=(0,r.useInnerBlocksProps)({...n},{template:Ta,allowedBlocks:["masterstudy/featured-teacher-about","masterstudy/courses-preset","masterstudy/featured-teacher-button"],templateLock:"insert"});return(0,a.createElement)(a.Fragment,null,(0,a.createElement)(Ea,{attributes:e,setAttributes:t,categories:i,instructorOptions:l}),(0,a.createElement)("div",{...o}))},save:({attributes:e})=>{const t=r.useBlockProps.save({className:`lms-teacher-container ${e.layoutWidth}`,style:$("teacher",e,F)}),n=r.useInnerBlocksProps.save(t);return(0,a.createElement)("div",{...n})}})},6942:(e,t)=>{var n;!function(){"use strict";var a={}.hasOwnProperty;function i(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=l(e,r(n)))}return e}function r(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return i.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)a.call(e,n)&&e[n]&&(t=l(t,n));return t}function l(e,t){return t?e?e+" "+t:e+t:e}e.exports?(i.default=i,e.exports=i):void 0===(n=function(){return i}.apply(t,[]))||(e.exports=n)}()}},n={};function a(e){var i=n[e];if(void 0!==i)return i.exports;var r=n[e]={exports:{}};return t[e](r,r.exports,a),r.exports}a.m=t,e=[],a.O=(t,n,i,r)=>{if(!n){var l=1/0;for(c=0;c<e.length;c++){for(var[n,i,r]=e[c],o=!0,s=0;s<n.length;s++)(!1&r||l>=r)&&Object.keys(a.O).every((e=>a.O[e](n[s])))?n.splice(s--,1):(o=!1,r<l&&(l=r));if(o){e.splice(c--,1);var m=i();void 0!==m&&(t=m)}}return t}r=r||0;for(var c=e.length;c>0&&e[c-1][2]>r;c--)e[c]=e[c-1];e[c]=[n,i,r]},a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={2917:0,9625:0};a.O.j=t=>0===e[t];var t=(t,n)=>{var i,r,[l,o,s]=n,m=0;if(l.some((t=>0!==e[t]))){for(i in o)a.o(o,i)&&(a.m[i]=o[i]);if(s)var c=s(a)}for(t&&t(n);m<l.length;m++)r=l[m],a.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return a.O(c)},n=globalThis.webpackChunkmasterstudy_lms_learning_management_system=globalThis.webpackChunkmasterstudy_lms_learning_management_system||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})();var i=a.O(void 0,[9625],(()=>a(2313)));i=a.O(i)})();