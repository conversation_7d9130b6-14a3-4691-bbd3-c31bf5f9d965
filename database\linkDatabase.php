<?php
/**
 * Database functionality for Custom Linking Plugin
 *
 * This file links all database-related functionality
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Include database-related files
require_once plugin_dir_path(__FILE__) . 'Activator.php';
require_once plugin_dir_path(__FILE__) . 'Deactivator.php';

/**
 * Create the necessary database table
 * This function is called during plugin activation
 */
function create_linking_table() {
    Custom_Linking_Activator::activate();
}

/**
 * Handle plugin deactivation
 * This function is called during plugin deactivation
 */
function deactivate_linking_plugin() {
    Custom_Linking_Deactivator::deactivate();
}

/**
 * Get all linked products for a course
 *
 * @param int $course_id The course ID
 * @return array Array of linked product IDs
 */
function get_linked_products($course_id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'linking_table';
    
    $results = $wpdb->get_col(
        $wpdb->prepare(
            "SELECT product_id FROM $table_name WHERE course_id = %d",
            $course_id
        )
    );
    
    return array_map('intval', $results);
}

/**
 * Link a product to a course
 *
 * @param int $course_id The course ID
 * @param int $product_id The product ID to link
 * @param string $type The type of link (course or bundle)
 * @return bool|int The number of rows affected or false on error
 */
function link_product_to_course($course_id, $product_id, $type = 'course') {
    global $wpdb;
    $table_name = $wpdb->prefix . 'linking_table';
    
    // Validate and sanitize type
    $type = function_exists('custom_linking_sanitize_type') ? 
        custom_linking_sanitize_type($type) : 
        (in_array($type, array('course', 'bundle')) ? $type : 'course');
    
    return $wpdb->insert(
        $table_name,
        array(
            'course_id' => $course_id,
            'product_id' => $product_id,
            'type' => $type
        ),
        array('%d', '%d', '%s')
    );
}

/**
 * Unlink a product from a course
 *
 * @param int $course_id The course ID
 * @param int $product_id The product ID to unlink
 * @return bool|int The number of rows affected or false on error
 */
function unlink_product_from_course($course_id, $product_id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'linking_table';
    
    return $wpdb->delete(
        $table_name,
        array(
            'course_id' => $course_id,
            'product_id' => $product_id
        ),
        array('%d', '%d')
    );
}

/**
 * Check if a product is linked to a course
 *
 * @param int $course_id The course ID
 * @param int $product_id The product ID to check
 * @return bool True if linked, false otherwise
 */
function is_product_linked_to_course($course_id, $product_id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'linking_table';
    
    $count = $wpdb->get_var(
        $wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name WHERE course_id = %d AND product_id = %d",
            $course_id,
            $product_id
        )
    );
    
    return (int) $count > 0;
}

/**
 * Delete a course-product link by ID
 *
 * This function is used by the admin interface to delete links
 * based on the linking table primary key
 *
 * @param int $id The ID of the link to delete
 * @return bool|int The number of rows affected or false on error
 */
function unlink_product_from_course_by_id($id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'linking_table';
    
    return $wpdb->delete(
        $table_name,
        array('id' => $id),
        array('%d')
    );
}