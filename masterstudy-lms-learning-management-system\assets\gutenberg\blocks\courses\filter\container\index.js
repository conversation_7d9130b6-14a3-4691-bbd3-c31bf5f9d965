(()=>{var e,t={4280:(e,t,n)=>{"use strict";const r=window.React,l=window.wp.blocks,a=window.wp.i18n,i=window.wp.data,s=window.wp.element,o=window.wp.blockEditor,m=window.wp.components,c=window.wp.primitives,d=(0,r.createElement)(c.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(c.Path,{d:"M11 12.5V17.5H12.5V12.5H17.5V11H12.5V6H11V11H6V12.5H11Z"}));var u=n(6942),g=n.n(u);const p=(0,s.createContext)(null),f=({children:e,...t})=>(0,r.createElement)(p.Provider,{value:{...t}},e),h=({condition:e,fallback:t=null,children:n})=>(0,r.createElement)(r.Fragment,null,e?n:t),v=(e,t)=>{if(typeof e!=typeof t)return!1;if(Number.isNaN(e)&&Number.isNaN(t))return!0;if("object"!=typeof e||null===e||null===t)return e===t;if(Object.keys(e).length!==Object.keys(t).length)return!1;if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;const n=e.slice().sort(),r=t.slice().sort();return n.every(((e,t)=>v(e,r[t])))}for(const n of Object.keys(e))if(!v(e[n],t[n]))return!1;return!0};let _=function(e){return e.ALL="all",e.SOME="some",e}({}),b=function(e){return e.NORMAL="Normal",e.HOVER="Hover",e.ACTIVE="Active",e.FOCUS="Focus",e}({}),y=function(e){return e.DESKTOP="Desktop",e.TABLET="Tablet",e.MOBILE="Mobile",e}({}),E=function(e){return e.TOP_lEFT="top-left",e.TOP_CENTER="top-center",e.TOP_RIGHT="top-right",e.BOTTOM_lEFT="bottom-left",e.BOTTOM_CENTER="bottom-center",e.BOTTOM_RIGHT="bottom-right",e}({});const C=["",null,void 0,"null","undefined"],B=[".jpg",".jpeg",".png",".gif"],N=e=>C.includes(e),T=(e,t,n="")=>{const r=e[t];return"object"==typeof r&&null!==r?((e,t)=>{return n=e,Object.values(n).every((e=>C.includes(e)))?null:((e,t="")=>{const n=Object.entries(e).reduce(((e,[n,r])=>(e[n]=(r||"0")+t,e)),{});return`${n.top} ${n.right} ${n.bottom} ${n.left}`})(e,t);var n})(r,n):((e,t)=>N(e)?e:(e=>{if("string"!=typeof e)return!1;const t=e.slice(e.lastIndexOf("."));return B.includes(t)||e.startsWith("blob")})(e)?`url('${e}')`:String(e+t))(r,n)},S=(e,t,n)=>{const r={};return n.forEach((({isAdaptive:n,hasHover:l,unit:a},i)=>{if(t.hasOwnProperty(i)){const{unitMeasureDesktop:o,unitMeasureTablet:m,unitMeasureMobile:c}=((e,t)=>{var n;return{unitMeasureDesktop:null!==(n=e[t])&&void 0!==n?n:"",unitMeasureTablet:t?e[t+"Tablet"]:"",unitMeasureMobile:t?e[t+"Mobile"]:""}})(t,a);if(n&&l){const{desktopHoverPropertyName:n,mobileHoverPropertyName:l,tabletHoverPropertyName:a}=(e=>{const t=e+b.HOVER;return{desktopHoverPropertyName:t,tabletHoverPropertyName:t+"Tablet",mobileHoverPropertyName:t+"Mobile"}})(i),s=T(t,n,o);N(s)||(r[`--lms-${e}-${n}`]=s);const d=T(t,a,m);N(d)||(r[`--lms-${e}-${a}`]=d);const u=T(t,l,c);N(u)||(r[`--lms-${e}-${l}`]=u)}if(l){const n=i+b.HOVER,l=T(t,n,o);N(l)||(r[`--lms-${e}-${n}`]=l)}if(n){const{desktopPropertyName:n,mobilePropertyName:l,tabletPropertyName:a}={desktopPropertyName:s=i,tabletPropertyName:s+"Tablet",mobilePropertyName:s+"Mobile"},d=T(t,n,o);N(d)||(r[`--lms-${e}-${n}`]=d);const u=T(t,a,m);N(u)||(r[`--lms-${e}-${a}`]=u);const g=T(t,l,c);N(g)||(r[`--lms-${e}-${l}`]=g)}const d=T(t,i,o);N(d)||(r[`--lms-${e}-${i}`]=d)}var s})),r},R=(a.__("Small","masterstudy-lms-learning-management-system"),a.__("Normal","masterstudy-lms-learning-management-system"),a.__("Large","masterstudy-lms-learning-management-system"),a.__("Extra Large","masterstudy-lms-learning-management-system"),"wp-block-masterstudy-settings__"),w={top:"",right:"",bottom:"",left:""};function x(e){return Array.isArray(e)?e.map((e=>R+e)):R+e}E.TOP_lEFT,E.TOP_CENTER,E.TOP_RIGHT,E.BOTTOM_lEFT,E.BOTTOM_CENTER,E.BOTTOM_RIGHT,a.__("Newest","masterstudy-lms-learning-management-system"),a.__("Oldest","masterstudy-lms-learning-management-system"),a.__("Overall rating","masterstudy-lms-learning-management-system"),a.__("Popular","masterstudy-lms-learning-management-system"),a.__("Price low","masterstudy-lms-learning-management-system"),a.__("Price high","masterstudy-lms-learning-management-system");const M=()=>(0,i.useSelect)((e=>e("core/editor").getDeviceType()||e("core/edit-site")?.__experimentalGetPreviewDeviceType()||e("core/edit-post")?.__experimentalGetPreviewDeviceType()||e("masterstudy/store")?.getDeviceType()),[])||"Desktop",A=(e=!1)=>{const[t,n]=(0,s.useState)(e),r=(0,s.useCallback)((()=>{n(!0)}),[]);return{isOpen:t,onClose:(0,s.useCallback)((()=>{n(!1)}),[]),onOpen:r,onToggle:(0,s.useCallback)((()=>{n((e=>!e))}),[])}},U=()=>{const e=(0,s.useContext)(p);if(!e)throw new Error("No settings context provided");return e},k=(e="")=>{const{attributes:t,setAttributes:n,onResetByFieldName:r,changedFieldsByName:l}=U();return{value:t[e],onChange:t=>n({[e]:t}),onReset:r.get(e),isChanged:l.get(e)}},L=(e,t=!1,n=!1)=>{const{hoverName:r,onChangeHoverName:l}=(()=>{const[e,t]=(0,s.useState)(b.NORMAL);return{hoverName:e,onChangeHoverName:(0,s.useCallback)((e=>{t(e)}),[])}})(),a=M();return{fieldName:(0,s.useMemo)((()=>{const l=r===b.HOVER?r:"",i=a===y.DESKTOP?"":a;return n&&t?e+l+i:n&&!t?e+l:t&&!n?e+i:e}),[e,n,t,r,a]),hoverName:r,onChangeHoverName:l}},H=(e,t=!1,n="Normal")=>{const r=M(),l=(0,s.useMemo)((()=>{const l=n===b.NORMAL?"":n,a=r===y.DESKTOP?"":r;return l&&t?e+l+a:l&&!t?e+l:t&&!l?e+a:e}),[e,t,n,r]),{value:a,isChanged:i,onReset:o}=k(l);return{fieldName:l,value:a,isChanged:i,onReset:o}},O=(e=[],t=_.ALL)=>{const{attributes:n}=U();return!e.length||(t===_.ALL?e.every((({name:e,value:t})=>{const r=n[e];return Array.isArray(t)?Array.isArray(r)?v(t,r):t.includes(r):t===r})):t!==_.SOME||e.some((({name:e,value:t})=>{const r=n[e];return Array.isArray(t)?Array.isArray(r)?v(t,r):t.includes(r):t===r})))},P=e=>{const t=(0,s.useRef)(null);return(0,s.useEffect)((()=>{const n=n=>{t.current&&!t.current.contains(n.target)&&e()};return document.addEventListener("click",n),()=>{document.removeEventListener("click",n)}}),[t,e]),t},D=e=>(0,r.createElement)(m.SVG,{width:"16",height:"16",viewBox:"0 0 12 13",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,r.createElement)(m.Path,{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})),F=e=>(0,r.createElement)(m.SVG,{width:"16",height:"16",viewBox:"0 0 14 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,r.createElement)(m.G,{"clip-path":"url(#clip0_1068_38993)"},(0,r.createElement)(m.Path,{d:"M11.1973 8.60005L8.74967 8.11005V5.67171C8.74967 5.517 8.68822 5.36863 8.57882 5.25923C8.46942 5.14984 8.32105 5.08838 8.16634 5.08838H6.99967C6.84496 5.08838 6.69659 5.14984 6.5872 5.25923C6.4778 5.36863 6.41634 5.517 6.41634 5.67171V8.58838H5.24967C5.15021 8.58844 5.05241 8.61391 4.96555 8.66238C4.87869 8.71084 4.80565 8.78068 4.75336 8.86529C4.70106 8.9499 4.67125 9.04646 4.66674 9.14582C4.66223 9.24518 4.68317 9.34405 4.72759 9.43305L6.47759 12.933C6.57676 13.1302 6.77859 13.255 6.99967 13.255H11.083C11.2377 13.255 11.3861 13.1936 11.4955 13.0842C11.6049 12.9748 11.6663 12.8264 11.6663 12.6717V9.17171C11.6665 9.03685 11.6198 8.90612 11.5343 8.80186C11.4487 8.69759 11.3296 8.62626 11.1973 8.60005Z"}),(0,r.createElement)(m.Path,{d:"M10.4997 1.58838H3.49967C2.85626 1.58838 2.33301 2.11221 2.33301 2.75505V6.83838C2.33301 7.4818 2.85626 8.00505 3.49967 8.00505H5.24967V6.83838H3.49967V2.75505H10.4997V6.83838H11.6663V2.75505C11.6663 2.11221 11.1431 1.58838 10.4997 1.58838Z"})),(0,r.createElement)("defs",null,(0,r.createElement)("clipPath",{id:"clip0_1068_38993"},(0,r.createElement)(m.Rect,{width:"14",height:"14",transform:"translate(0 0.421875)"})))),W=[{value:b.NORMAL,label:a.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,r.createElement)(D,{onClick:e})},{value:b.HOVER,label:a.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,r.createElement)(F,{onClick:e})},{value:b.ACTIVE,label:a.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,r.createElement)(F,{onClick:e})},{value:b.FOCUS,label:a.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,r.createElement)(F,{onClick:e})}],I={[b.NORMAL]:{icon:(0,r.createElement)(D,null),label:a.__("Normal State","masterstudy-lms-learning-management-system")},[b.HOVER]:{icon:(0,r.createElement)(F,null),label:a.__("Hovered State","masterstudy-lms-learning-management-system")},[b.ACTIVE]:{icon:(0,r.createElement)(F,null),label:a.__("Active State","masterstudy-lms-learning-management-system")},[b.FOCUS]:{icon:(0,r.createElement)(F,null),label:a.__("Focus State","masterstudy-lms-learning-management-system")}},z=(e,t)=>{let n=[];return n=e.length?W.filter((t=>e.includes(t.value))):W,n=n.filter((e=>e.value!==t)),{ICONS_MAP:I,options:n}},[V,j,G,$,K]=x(["hover-state","hover-state__selected","hover-state__selected__opened-menu","hover-state__menu","hover-state__menu__item"]),Z=({stateOptions:e,currentState:t,onSelect:n})=>{const{isOpen:l,onOpen:a,onClose:i}=A(),s=P(i),{ICONS_MAP:o,options:m}=z(e,t);return(0,r.createElement)("div",{className:V,ref:s},(0,r.createElement)("div",{className:g()([j],{[G]:l}),onClick:a,title:o[t]?.label},o[t]?.icon),(0,r.createElement)(h,{condition:l},(0,r.createElement)("div",{className:$},m.map((({value:e,icon:t,label:l})=>(0,r.createElement)("div",{key:e,className:K,title:l},t((()=>n(e)))))))))},X=x("color-indicator"),Y=(0,s.memo)((({color:e,onChange:t})=>(0,r.createElement)("div",{className:X},(0,r.createElement)(o.PanelColorSettings,{enableAlpha:!0,disableCustomColors:!1,__experimentalHasMultipleOrigins:!0,__experimentalIsRenderedInSidebar:!0,colorSettings:[{label:"",value:e,onChange:t}]}))));var q;function J(){return J=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},J.apply(null,arguments)}var Q,ee,te=function(e){return r.createElement("svg",J({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 12 13"},e),q||(q=r.createElement("path",{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})))};function ne(){return ne=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ne.apply(null,arguments)}var re=function(e){return r.createElement("svg",ne({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 14 15"},e),Q||(Q=r.createElement("g",{clipPath:"url(#state-hover_svg__a)"},r.createElement("path",{d:"M11.197 8.6 8.75 8.11V5.672a.583.583 0 0 0-.584-.584H7a.583.583 0 0 0-.584.584v2.916H5.25a.584.584 0 0 0-.522.845l1.75 3.5c.099.197.3.322.522.322h4.083a.583.583 0 0 0 .583-.583v-3.5a.58.58 0 0 0-.469-.572"}),r.createElement("path",{d:"M10.5 1.588h-7c-.644 0-1.167.524-1.167 1.167v4.083c0 .644.523 1.167 1.167 1.167h1.75V6.838H3.5V2.755h7v4.083h1.166V2.755c0-.643-.523-1.167-1.166-1.167"}))),ee||(ee=r.createElement("defs",null,r.createElement("clipPath",{id:"state-hover_svg__a"},r.createElement("path",{d:"M0 .422h14v14H0z"})))))};const le=[{value:b.NORMAL,label:a.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,r.createElement)(te,{onClick:e})},{value:b.HOVER,label:a.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,r.createElement)(re,{onClick:e})}],ae={[b.NORMAL]:{icon:(0,r.createElement)(te,null),label:a.__("Normal State","masterstudy-lms-learning-management-system")},[b.HOVER]:{icon:(0,r.createElement)(re,null),label:a.__("Hovered State","masterstudy-lms-learning-management-system")}},ie=x("hover-state"),se=x("hover-state__selected"),oe=x("hover-state__selected__opened-menu"),me=x("has-changes"),ce=x("hover-state__menu"),de=x("hover-state__menu__item"),ue=(0,s.memo)((e=>{const{hoverName:t,onChangeHoverName:n,fieldName:l}=e,{changedFieldsByName:a}=U(),i=a.get(l),{isOpen:o,onOpen:m,onClose:c}=A(),d=P(c),{ICONS_MAP:u,options:p}=(e=>{const t=(0,s.useMemo)((()=>le.filter((t=>t.value!==e))),[e]);return{ICONS_MAP:ae,options:t}})(t),f=(0,s.useCallback)((e=>{n(e),c()}),[n,c]);return(0,r.createElement)("div",{className:ie,ref:d},(0,r.createElement)("div",{className:g()([se],{[oe]:o,[me]:i}),onClick:m,title:u[t]?.label},u[t]?.icon),(0,r.createElement)(h,{condition:o},(0,r.createElement)("div",{className:ce},p.map((({value:e,icon:t,label:n})=>(0,r.createElement)("div",{key:e,className:de,title:n},t((()=>f(e)))))))))})),ge={Desktop:{icon:"desktop",label:a.__("Desktop","masterstudy-lms-learning-management-system")},Tablet:{icon:"tablet",label:a.__("Tablet","masterstudy-lms-learning-management-system")},Mobile:{icon:"smartphone",label:a.__("Mobile","masterstudy-lms-learning-management-system")}},pe=[{value:y.DESKTOP,icon:"desktop",label:a.__("Desktop","masterstudy-lms-learning-management-system")},{value:y.TABLET,icon:"tablet",label:a.__("Tablet","masterstudy-lms-learning-management-system")},{value:y.MOBILE,icon:"smartphone",label:a.__("Mobile","masterstudy-lms-learning-management-system")}],fe=x("device-picker"),he=x("device-picker__selected"),ve=x("device-picker__selected__opened-menu"),_e=x("device-picker__menu"),be=x("device-picker__menu__item"),ye=()=>{const{isOpen:e,onOpen:t,onClose:n}=A(),{value:l,onChange:a}=(e=>{const t=M(),n=(0,i.useDispatch)();return{value:(0,s.useMemo)((()=>ge[t]),[t]),onChange:t=>{n("core/edit-site")&&n("core/edit-site").__experimentalSetPreviewDeviceType?n("core/edit-site").__experimentalSetPreviewDeviceType(t):n("core/edit-post")&&n("core/edit-post").__experimentalSetPreviewDeviceType?n("core/edit-post").__experimentalSetPreviewDeviceType(t):n("masterstudy/store").setDeviceType(t),e()}}})(n),o=(e=>(0,s.useMemo)((()=>pe.filter((t=>t.icon!==e))),[e]))(l.icon),c=P(n);return(0,r.createElement)("div",{className:fe,ref:c},(0,r.createElement)(m.Dashicon,{className:g()([he],{[ve]:e}),icon:l.icon,size:16,onClick:t,title:l.label}),(0,r.createElement)(h,{condition:e},(0,r.createElement)("div",{className:_e},o.map((e=>(0,r.createElement)(m.Dashicon,{key:e.value,icon:e.icon,size:16,onClick:()=>a(e.value),className:be,title:e.label}))))))},Ee=x("reset-button"),Ce=({onReset:e})=>(0,r.createElement)(m.Dashicon,{icon:"undo",onClick:e,className:Ee,size:16}),Be=[{label:"PX",value:"px"},{label:"%",value:"%"},{label:"EM",value:"em"},{label:"REM",value:"rem"},{label:"VW",value:"vw"},{label:"VH",value:"vh"}],Ne=x("unit"),Te=x("unit__single"),Se=x("unit__list"),Re=({name:e,isAdaptive:t})=>{const{isOpen:n,onOpen:l,onClose:a}=A(),{fieldName:i}=L(e,t),{value:s,onChange:o}=k(i),m=P(a);return(0,r.createElement)("div",{className:Ne,ref:m},(0,r.createElement)("div",{className:Te,onClick:l},s),(0,r.createElement)(h,{condition:n},(0,r.createElement)("div",{className:Se},Be.map((({value:e,label:t})=>(0,r.createElement)("div",{key:e,onClick:()=>(o(e),void a())},t))))))},we=x("popover-modal"),xe=x("popover-modal__close dashicon dashicons dashicons-no-alt"),Me=e=>{const{isOpen:t,onClose:n,popoverContent:l}=e;return(0,r.createElement)(h,{condition:t},(0,r.createElement)(m.Popover,{position:"middle left",onClose:n,className:we},l,(0,r.createElement)("span",{onClick:n,className:xe})))},Ae=x("setting-label"),Ue=x("setting-label__content"),ke=e=>{const{label:t,isChanged:n=!1,onReset:l,showDevicePicker:a=!0,HoverStateControl:i=null,unitName:s,popoverContent:o=null,dependencies:m}=e,{isOpen:c,onClose:d,onToggle:u}=A();return O(m)?(0,r.createElement)("div",{className:Ae},(0,r.createElement)("div",{className:Ue},(0,r.createElement)("div",{onClick:u},t),(0,r.createElement)(h,{condition:Boolean(o)},(0,r.createElement)(Me,{isOpen:c,onClose:d,popoverContent:o})),(0,r.createElement)(h,{condition:a},(0,r.createElement)(ye,null)),(0,r.createElement)(h,{condition:Boolean(i)},i)),(0,r.createElement)(h,{condition:Boolean(s)},(0,r.createElement)(Re,{name:s,isAdaptive:a})),(0,r.createElement)(h,{condition:n},(0,r.createElement)(Ce,{onReset:l}))):null},Le=x("suffix"),He=()=>(0,r.createElement)("div",{className:Le},(0,r.createElement)(m.Dashicon,{icon:"color-picker",size:16})),Oe=x("color-picker"),Pe=e=>{const{name:t,label:n,placeholder:l,dependencyMode:a,dependencies:i,isAdaptive:s=!1,hasHover:o=!1}=e,{fieldName:c,hoverName:d,onChangeHoverName:u}=L(t,s,o),{value:g,isChanged:p,onChange:f,onReset:v}=k(c);return O(i,a)?(0,r.createElement)("div",{className:Oe},(0,r.createElement)(h,{condition:Boolean(n)},(0,r.createElement)(ke,{label:n,isChanged:p,onReset:v,showDevicePicker:s,HoverStateControl:(0,r.createElement)(h,{condition:o},(0,r.createElement)(ue,{hoverName:d,onChangeHoverName:u,fieldName:c}))})),(0,r.createElement)(m.__experimentalInputControl,{prefix:(0,r.createElement)(Y,{color:g,onChange:f}),suffix:(0,r.createElement)(He,null),onChange:f,value:g,placeholder:l})):null},De=x("number-steppers"),Fe=x("indent-steppers"),We=x("indent-stepper-plus"),Ie=x("indent-stepper-minus"),ze=({onIncrement:e,onDecrement:t,withArrows:n=!1})=>n?(0,r.createElement)("span",{className:Fe},(0,r.createElement)("button",{onClick:e,className:We}),(0,r.createElement)("button",{onClick:t,className:Ie})):(0,r.createElement)("span",{className:De},(0,r.createElement)("button",{onClick:e},"+"),(0,r.createElement)("button",{onClick:t},"-")),[Ve,je]=x(["indents","indents-control"]),Ge=({name:e,label:t,unitName:n,popoverContent:l,dependencyMode:i,dependencies:o,isAdaptive:c=!1})=>{const{fieldName:d}=L(e,c),{value:u,onResetSegmentedBox:g,hasChanges:p,handleInputIncrement:f,handleInputDecrement:v,updateDirectionsValues:_,lastFieldValue:b}=((e,t)=>{const{value:n,isChanged:r,onChange:l,onReset:a}=k(e),{onResetByFieldName:i,changedFieldsByName:o}=U(),m=r||o.get(t),c=e=>{l({...n,...e})},[d,u]=(0,s.useState)(!1);return{value:n,onResetSegmentedBox:()=>{a(),i.get(t)()},hasChanges:m,handleInputIncrement:e=>Number(n[e])+1,handleInputDecrement:e=>Number(n[e])-1,updateDirectionsValues:(e,t,n)=>{e?(u(!1),c({top:n,right:n,bottom:n,left:n})):(u(n),c({[t]:n}))},lastFieldValue:d}})(d,n),[y,E]=(0,s.useState)((()=>{const{left:e,right:t,top:n,bottom:r}=u;return""!==e&&e===t&&t===n&&n===r})),C=e=>{const[t,n]=Object.entries(e)[0];_(y,t,n)},B=e=>()=>{const t=f(e);_(y,e,String(t))},N=e=>()=>{const t=v(e);_(y,e,String(t))};return O(o,i)?(0,r.createElement)("div",{className:Ve},(0,r.createElement)(h,{condition:Boolean(t)},(0,r.createElement)(ke,{label:null!=t?t:"",isChanged:p,onReset:g,unitName:n,popoverContent:l,showDevicePicker:c})),(0,r.createElement)("div",{className:`${je} ${y?"active":""}`},(0,r.createElement)("div",null,(0,r.createElement)(m.__experimentalNumberControl,{value:u.top,onChange:e=>{C({top:e})},spinControls:"none",suffix:(0,r.createElement)(ze,{onIncrement:B("top"),onDecrement:N("top"),withArrows:!0})}),(0,r.createElement)("div",null,a.__("Top","masterstudy-lms-learning-management-system"))),(0,r.createElement)("div",null,(0,r.createElement)(m.__experimentalNumberControl,{value:u.right,onChange:e=>{C({right:e})},spinControls:"none",suffix:(0,r.createElement)(ze,{onIncrement:B("right"),onDecrement:N("right"),withArrows:!0})}),(0,r.createElement)("div",null,a.__("Right","masterstudy-lms-learning-management-system"))),(0,r.createElement)("div",null,(0,r.createElement)(m.__experimentalNumberControl,{value:u.bottom,onChange:e=>{C({bottom:e})},spinControls:"none",suffix:(0,r.createElement)(ze,{onIncrement:B("bottom"),onDecrement:N("bottom"),withArrows:!0})}),(0,r.createElement)("div",null,a.__("Bottom","masterstudy-lms-learning-management-system"))),(0,r.createElement)("div",null,(0,r.createElement)(m.__experimentalNumberControl,{value:u.left,onChange:e=>{C({left:e})},spinControls:"none",suffix:(0,r.createElement)(ze,{onIncrement:B("left"),onDecrement:N("left"),withArrows:!0})}),(0,r.createElement)("div",null,a.__("Left","masterstudy-lms-learning-management-system"))),(0,r.createElement)(m.Dashicon,{icon:"dashicons "+(y?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{y||!1===b||_(!0,"left",b),E((e=>!e))}}))):null},[$e,Ke,Ze,Xe]=x(["toggle-group-wrapper","toggle-group","toggle-group__toggle","toggle-group__active-toggle"]),Ye=e=>{const{name:t,options:n,label:l,isAdaptive:a=!1,dependencyMode:i,dependencies:s}=e,{fieldName:o}=L(t,a),{value:m,isChanged:c,onChange:d,onReset:u}=k(o);return O(s,i)?(0,r.createElement)("div",{className:$e},(0,r.createElement)(h,{condition:Boolean(l)},(0,r.createElement)(ke,{label:l,isChanged:c,onReset:u,showDevicePicker:a})),(0,r.createElement)("div",{className:Ke},n.map((e=>(0,r.createElement)("div",{key:e.value,className:g()([Ze],{[Xe]:e.value===m}),onClick:()=>d(e.value)},e.label))))):null},[qe,Je,Qe,et]=x(["border-control","border-control-solid","border-control-dashed","border-control-dotted"]),tt=e=>{const{label:t,borderStyleName:n,borderColorName:l,borderWidthName:i,dependencyMode:o,dependencies:m,isAdaptive:c=!1,hasHover:d=!1}=e,[u,p]=(0,s.useState)("Normal"),{fieldName:f,value:v,isChanged:_,onReset:b}=H(n,c,u),{fieldName:y,isChanged:E,onReset:C}=H(l,c,u),{fieldName:B,isChanged:N,onReset:T}=H(i,c,u);if(!O(m,o))return null;const S=_||E||N;return(0,r.createElement)("div",{className:g()([qe],{"has-reset-button":S})},(0,r.createElement)(ke,{label:t,isChanged:S,onReset:()=>{b(),C(),T()},showDevicePicker:c,HoverStateControl:(0,r.createElement)(h,{condition:d},(0,r.createElement)(Z,{stateOptions:["Normal","Hover"],currentState:u,onSelect:p}))}),(0,r.createElement)(Ye,{options:[{label:(0,r.createElement)("span",null,a.__("None","masterstudy-lms-learning-management-system")),value:"none"},{label:(0,r.createElement)("span",{className:Je}),value:"solid"},{label:(0,r.createElement)("span",{className:Qe},(0,r.createElement)("span",null)),value:"dashed"},{label:(0,r.createElement)("span",{className:et},(0,r.createElement)("span",null,(0,r.createElement)("span",null))),value:"dotted"}],name:f}),(0,r.createElement)(h,{condition:"none"!==v},(0,r.createElement)(Pe,{name:y,placeholder:a.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(Ge,{name:B})))},nt=x("border-radius"),rt=x("border-radius-control"),lt=({name:e,label:t,unitName:n,popoverContent:l,dependencyMode:a,dependencies:i,isAdaptive:o=!1,hasHover:c=!1})=>{const{fieldName:d}=L(e,o,c),{value:u,onResetBorderRadius:p,hasChanges:f,handleInputIncrement:h,handleInputDecrement:v,updateDirectionsValues:_,lastFieldValue:b}=((e,t)=>{const[n,r]=(0,s.useState)(!1),{value:l,isChanged:a,onChange:i,onReset:o}=k(e),{onResetByFieldName:m,changedFieldsByName:c}=U(),d=a||c.get(t),u=e=>{i({...l,...e})};return{value:l,onResetBorderRadius:()=>{o(),m.get(t)()},hasChanges:d,handleInputIncrement:e=>Number(l[e])+1,handleInputDecrement:e=>Number(l[e])-1,updateDirectionsValues:(e,t,n)=>{e?(u({top:n,right:n,bottom:n,left:n}),r(!1)):(u({[t]:n}),r(n))},lastFieldValue:n}})(d,n),[y,E]=(0,s.useState)((()=>{const{left:e,right:t,top:n,bottom:r}=u;return""!==e&&e===t&&t===n&&n===r})),C=e=>{const[t,n]=Object.entries(e)[0];_(y,t,n)},B=e=>()=>{const t=h(e);_(y,e,String(t))},N=e=>()=>{const t=v(e);_(y,e,String(t))};return O(i,a)?(0,r.createElement)("div",{className:nt},(0,r.createElement)(ke,{label:t,isChanged:f,onReset:p,unitName:n,popoverContent:l,showDevicePicker:o}),(0,r.createElement)("div",{className:g()([rt],{"has-reset-button":f,active:y})},(0,r.createElement)("div",{className:"number-control-top"},(0,r.createElement)(m.__experimentalNumberControl,{value:u.top,onChange:e=>{C({top:e})},spinControls:"none",suffix:(0,r.createElement)(ze,{onIncrement:B("top"),onDecrement:N("top"),withArrows:!0})})),(0,r.createElement)("div",{className:"number-control-right"},(0,r.createElement)(m.__experimentalNumberControl,{value:u.right,onChange:e=>{C({right:e})},spinControls:"none",suffix:(0,r.createElement)(ze,{onIncrement:B("right"),onDecrement:N("right"),withArrows:!0})})),(0,r.createElement)("div",{className:"number-control-left"},(0,r.createElement)(m.__experimentalNumberControl,{value:u.left,onChange:e=>{C({left:e})},spinControls:"none",suffix:(0,r.createElement)(ze,{onIncrement:B("left"),onDecrement:N("left"),withArrows:!0})})),(0,r.createElement)("div",{className:"number-control-bottom"},(0,r.createElement)(m.__experimentalNumberControl,{value:u.bottom,onChange:e=>{C({bottom:e})},spinControls:"none",suffix:(0,r.createElement)(ze,{onIncrement:B("bottom"),onDecrement:N("bottom"),withArrows:!0})})),(0,r.createElement)(m.Dashicon,{icon:"dashicons "+(y?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{y||!1===b||_(!0,"left",b),E((e=>!e))}}))):null},at=(x("box-shadow-preset"),x("presets")),it=x("presets__item-wrapper"),st=x("presets__item-wrapper__preset"),ot=x("presets__item-wrapper__name"),mt=((0,s.memo)((e=>{const{presets:t,activePreset:n,onSelectPreset:l,PresetItem:a,detectIsActive:i,detectByIndex:s=!1}=e;return(0,r.createElement)("div",{className:at},t.map((({name:e,...t},o)=>(0,r.createElement)("div",{key:o,className:g()([it],{active:i(n,s?o:t)}),onClick:()=>l(t)},(0,r.createElement)("div",{className:st},(0,r.createElement)(a,{preset:t})),(0,r.createElement)("span",{className:ot},e)))))})),x("range-control")),ct=e=>{const{name:t,label:n,min:l,max:a,unitName:i,dependencyMode:s,dependencies:o,isAdaptive:c=!1}=e,{fieldName:d}=L(t,c),{value:u,onChange:g,onResetNumberField:p,hasChanges:f}=((e,t)=>{const{value:n,isChanged:r,onChange:l,onReset:a}=k(e),{onResetByFieldName:i,changedFieldsByName:s}=U();return{value:n,onChange:l,onResetNumberField:()=>{a(),i.get(t)()},hasChanges:r||s.get(t)}})(d,i);return O(o,s)?(0,r.createElement)("div",{className:mt},(0,r.createElement)(h,{condition:Boolean(n)},(0,r.createElement)(ke,{label:n,isChanged:f,onReset:p,unitName:i,showDevicePicker:c})),(0,r.createElement)(m.RangeControl,{value:u,onChange:g,min:l,max:a})):null},dt=x("switch"),ut=e=>{const{name:t,label:n,dependencyMode:l,dependencies:a,isAdaptive:i=!1}=e,{fieldName:s}=L(t,i),{value:o,onChange:c}=k(s);return O(a,l)?(0,r.createElement)("div",{className:dt,"data-has-label":Boolean(n).toString()},(0,r.createElement)(m.ToggleControl,{label:n,checked:o,onChange:c}),(0,r.createElement)(h,{condition:i},(0,r.createElement)(ye,null))):null},gt=(x("box-shadow-settings"),x("box-shadow-presets-title"),x("input-field"),x("input-field-control"),x("number-field")),pt=x("number-field-control"),ft=e=>{const{name:t,label:n,unitName:l,help:a,popoverContent:i,dependencyMode:s,dependencies:o,isAdaptive:c=!1}=e,{fieldName:d}=L(t,c),{value:u,onResetNumberField:g,hasChanges:p,handleIncrement:f,handleDecrement:h,handleInputChange:v}=((e,t)=>{const{value:n,isChanged:r,onChange:l,onReset:a}=k(e),{onResetByFieldName:i,changedFieldsByName:s}=U(),o=r||s.get(t);return{value:n,onResetNumberField:()=>{a(),i.get(t)()},hasChanges:o,handleIncrement:()=>{l(n+1)},handleDecrement:()=>{l(n-1)},handleInputChange:e=>{const t=Number(""===e?0:e);l(t)}}})(d,l);return O(o,s)?(0,r.createElement)("div",{className:gt},(0,r.createElement)(ke,{label:n,isChanged:p,onReset:g,unitName:l,showDevicePicker:c,popoverContent:i}),(0,r.createElement)("div",{className:pt},(0,r.createElement)(m.__experimentalNumberControl,{value:u,onChange:v,spinControls:"none",suffix:(0,r.createElement)(ze,{onIncrement:f,onDecrement:h})})),a&&(0,r.createElement)("small",null,a)):null},ht=({className:e})=>(0,r.createElement)("div",{className:e},a.__("No options","masterstudy-lms-learning-management-system")),vt=x("select__single-item"),_t=x("select__container"),bt=x("select__container__multi-item"),yt=({multiple:e,value:t,options:n,onChange:l})=>{const{singleValue:a,multipleValue:i}=((e,t,n)=>({singleValue:(0,s.useMemo)((()=>t?null:n.find((t=>t.value===e))?.label),[t,e,n]),multipleValue:(0,s.useMemo)((()=>t?e:null),[t,e])}))(t,e,n);return(0,r.createElement)(h,{condition:e,fallback:(0,r.createElement)("div",{className:vt},a)},(0,r.createElement)("div",{className:_t},i?.map((e=>{const t=n.find((t=>t.value===e));return t?(0,r.createElement)("div",{key:t.value,className:bt},(0,r.createElement)("div",null,t.label),(0,r.createElement)(m.Dashicon,{icon:"no-alt",onClick:()=>l(t.value),size:16})):null}))))},Et=x("select"),Ct=x("select__select-box"),Bt=x("select__placeholder"),Nt=x("select__select-box-multiple"),Tt=x("select__menu"),St=x("select__menu__options-container"),Rt=x("select__menu__item"),wt=e=>{const{options:t,multiple:n=!1,placeholder:l="Select",value:a,onSelect:i}=e,{isOpen:o,onToggle:c,onClose:d}=A(),u=P(d),p=((e,t,n)=>(0,s.useMemo)((()=>n&&Array.isArray(e)?t.filter((t=>!e.includes(t.value))):t.filter((t=>t.value!==e))),[e,t,n]))(a,t,n),f=((e,t,n,r)=>(0,s.useCallback)((l=>{if(t&&Array.isArray(e)){const t=e.includes(l)?e.filter((e=>e!==l)):[...e,l];n(t)}else n(l),r()}),[t,e,n,r]))(a,n,i,d),v=((e,t)=>(0,s.useMemo)((()=>t&&Array.isArray(e)?Boolean(e.length):Boolean(e)),[t,e]))(a,n),_=n&&Array.isArray(a)&&a?.length>0;return(0,r.createElement)("div",{className:Et,ref:u},(0,r.createElement)("div",{className:g()([Ct],{[Nt]:_}),onClick:c},(0,r.createElement)(h,{condition:v,fallback:(0,r.createElement)("div",{className:Bt},l)},(0,r.createElement)(yt,{onChange:f,options:t,multiple:n,value:a})),(0,r.createElement)(m.Dashicon,{icon:o?"arrow-up-alt2":"arrow-down-alt2",size:16,style:{color:"#4D5E6F"}})),(0,r.createElement)(h,{condition:o},(0,r.createElement)("div",{className:Tt},(0,r.createElement)(h,{condition:Boolean(p.length),fallback:(0,r.createElement)(ht,{className:Rt})},(0,r.createElement)("div",{className:St},p.map((e=>(0,r.createElement)("div",{key:e.value,onClick:()=>f(e.value),className:Rt},e.label))))))))},xt=x("setting-select"),Mt=e=>{const{name:t,options:n,label:l,multiple:a=!1,placeholder:i,isAdaptive:s=!1,dependencyMode:o,dependencies:m}=e,{fieldName:c}=L(t,s),{value:d,isChanged:u,onChange:g,onReset:p}=k(c);return O(m,o)?(0,r.createElement)("div",{className:xt},(0,r.createElement)(h,{condition:Boolean(l)},(0,r.createElement)(ke,{label:l,isChanged:u,onReset:p,showDevicePicker:s})),(0,r.createElement)(wt,{options:n,value:d,onSelect:g,multiple:a,placeholder:i})):null},At=(x("row-select"),x("row-select__label"),x("row-select__control"),x("typography-select")),Ut=x("typography-select-label"),kt=e=>{const{name:t,label:n,options:l,isAdaptive:a=!1}=e,{fieldName:i}=L(t,a),{isChanged:s,onReset:o}=k(i);return(0,r.createElement)("div",{className:At},(0,r.createElement)("div",{className:Ut},(0,r.createElement)("div",null,n),(0,r.createElement)(h,{condition:a},(0,r.createElement)(ye,null))),(0,r.createElement)(Mt,{name:t,options:l,isAdaptive:a}),(0,r.createElement)(h,{condition:s},(0,r.createElement)(Ce,{onReset:o})))},Lt=x("typography"),Ht=e=>{const{fontSizeName:t,fontWeightName:n,textTransformName:l,fontStyleName:i,textDecorationName:s,lineHeightName:o,letterSpacingName:m,wordSpacingName:c,fontSizeUnitName:d,lineHeightUnitName:u,letterSpacingUnitName:g,wordSpacingUnitName:p,dependencyMode:f,dependencies:h,isAdaptive:v=!1}=e,{fontWeightOptions:_,textTransformOptions:b,fontStyleOptions:y,textDecorationOptions:E}={fontWeightOptions:[{label:a.__("100 (Thin)","masterstudy-lms-learning-management-system"),value:"100"},{label:a.__("200 (Extra Light)","masterstudy-lms-learning-management-system"),value:"200"},{label:a.__("300 (Light)","masterstudy-lms-learning-management-system"),value:"300"},{label:a.__("400 (Normal)","masterstudy-lms-learning-management-system"),value:"400"},{label:a.__("500 (Medium)","masterstudy-lms-learning-management-system"),value:"500"},{label:a.__("600 (Semi Bold)","masterstudy-lms-learning-management-system"),value:"600"},{label:a.__("700 (Bold)","masterstudy-lms-learning-management-system"),value:"700"},{label:a.__("800 (Extra Bold)","masterstudy-lms-learning-management-system"),value:"800"},{label:a.__("900 (Extra)","masterstudy-lms-learning-management-system"),value:"900"}],textTransformOptions:[{label:a.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:a.__("Uppercase","masterstudy-lms-learning-management-system"),value:"uppercase"},{label:a.__("Lowercase","masterstudy-lms-learning-management-system"),value:"lowercase"},{label:a.__("Capitalize","masterstudy-lms-learning-management-system"),value:"capitalize"},{label:a.__("Normal","masterstudy-lms-learning-management-system"),value:"none"}],fontStyleOptions:[{label:a.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:a.__("Normal","masterstudy-lms-learning-management-system"),value:"none"},{label:a.__("Italic","masterstudy-lms-learning-management-system"),value:"italic"},{label:a.__("Oblique","masterstudy-lms-learning-management-system"),value:"oblique"}],textDecorationOptions:[{label:a.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:a.__("Underline","masterstudy-lms-learning-management-system"),value:"underline"},{label:a.__("Line Through","masterstudy-lms-learning-management-system"),value:"line-through"},{label:a.__("None","masterstudy-lms-learning-management-system"),value:"none"}]};return O(h,f)?(0,r.createElement)("div",{className:Lt},(0,r.createElement)(ct,{name:t,label:a.__("Size","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:d,isAdaptive:v}),(0,r.createElement)(kt,{name:n,label:a.__("Weight","masterstudy-lms-learning-management-system"),options:_}),(0,r.createElement)(kt,{name:l,label:a.__("Transform","masterstudy-lms-learning-management-system"),options:b}),(0,r.createElement)(kt,{name:i,label:a.__("Style","masterstudy-lms-learning-management-system"),options:y}),(0,r.createElement)(kt,{name:s,label:a.__("Decoration","masterstudy-lms-learning-management-system"),options:E}),(0,r.createElement)(ct,{name:o,label:a.__("Line Height","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:u,isAdaptive:v}),(0,r.createElement)(ct,{name:m,label:a.__("Letter Spacing","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:g,isAdaptive:v}),c&&(0,r.createElement)(ct,{name:c,label:a.__("Word Spacing","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:p,isAdaptive:v})):null},Ot=(x("file-upload"),x("file-upload__wrap"),x("file-upload__image"),x("file-upload__remove"),x("file-upload__replace"),(0,s.createContext)({activeTab:0,setActiveTab:()=>{}})),Pt=()=>{const e=(0,s.useContext)(Ot);if(!e)throw new Error("useTabs should be used inside Tabs");return e},Dt=({children:e})=>{const[t,n]=(0,s.useState)(0);return(0,r.createElement)(Ot.Provider,{value:{activeTab:t,setActiveTab:n}},(0,r.createElement)("div",{className:`active-tab-${t}`},e))},Ft=x("tab-list"),Wt=({children:e})=>(0,r.createElement)("div",{className:Ft},s.Children.map(e,((e,t)=>(0,s.cloneElement)(e,{index:t})))),It=x("tab"),zt=x("tab-active"),Vt=x("content"),jt=({index:e,title:t,icon:n})=>{const{activeTab:l,setActiveTab:a}=Pt();return(0,r.createElement)("div",{className:g()([It],{[zt]:l===e}),onClick:()=>a(e)},(0,r.createElement)("div",{className:Vt},(0,r.createElement)("div",null,n),(0,r.createElement)("div",null,t)))},Gt=({children:e})=>(0,r.createElement)("div",null,s.Children.map(e,((e,t)=>(0,s.cloneElement)(e,{index:t})))),$t=x("tab-panel"),Kt=({index:e,children:t})=>{const{activeTab:n}=Pt();return n===e?(0,r.createElement)("div",{className:$t},t):null},Zt=({generalTab:e,styleTab:t,advancedTab:n})=>(0,r.createElement)(Dt,null,(0,r.createElement)(Wt,null,(0,r.createElement)(jt,{title:a.__("General","masterstudy-lms-learning-management-system"),icon:(0,r.createElement)(m.Dashicon,{icon:"layout"})}),(0,r.createElement)(jt,{title:a.__("Style","masterstudy-lms-learning-management-system"),icon:(0,r.createElement)(m.Dashicon,{icon:"admin-appearance"})}),(0,r.createElement)(jt,{title:a.__("Advanced","masterstudy-lms-learning-management-system"),icon:(0,r.createElement)(m.Dashicon,{icon:"admin-settings"})})),(0,r.createElement)(Gt,null,(0,r.createElement)(Kt,null,e),(0,r.createElement)(Kt,null,t),(0,r.createElement)(Kt,null,n)));window.ReactDOM;"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement;function Xt(e){const t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function Yt(e){return"nodeType"in e}function qt(e){var t,n;return e?Xt(e)?e:Yt(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function Jt(e){const{Document:t}=qt(e);return e instanceof t}function Qt(e){return!Xt(e)&&e instanceof qt(e).HTMLElement}function en(e){return e instanceof qt(e).SVGElement}function tn(e){return e?Xt(e)?e.document:Yt(e)?Jt(e)?e:Qt(e)||en(e)?e.ownerDocument:document:document:document}function nn(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),l=1;l<n;l++)r[l-1]=arguments[l];return r.reduce(((t,n)=>{const r=Object.entries(n);for(const[n,l]of r){const r=t[n];null!=r&&(t[n]=r+e*l)}return t}),{...t})}}const rn=nn(-1);function ln(e){if(function(e){if(!e)return!1;const{TouchEvent:t}=qt(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return function(e){return"clientX"in e&&"clientY"in e}(e)?{x:e.clientX,y:e.clientY}:null}var an;!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(an||(an={}));const sn=Object.freeze({x:0,y:0});var on,mn,cn,dn;!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(on||(on={}));class un{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach((e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)}))},this.target=e}add(e,t,n){var r;null==(r=this.target)||r.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function gn(e,t){const n=Math.abs(e.x),r=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t&&r>t.y}function pn(e){e.preventDefault()}function fn(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(mn||(mn={})),(dn=cn||(cn={})).Space="Space",dn.Down="ArrowDown",dn.Right="ArrowRight",dn.Left="ArrowLeft",dn.Up="ArrowUp",dn.Esc="Escape",dn.Enter="Enter";cn.Space,cn.Enter,cn.Esc,cn.Space,cn.Enter;function hn(e){return Boolean(e&&"distance"in e)}function vn(e){return Boolean(e&&"delay"in e)}class bn{constructor(e,t,n){var r;void 0===n&&(n=function(e){const{EventTarget:t}=qt(e);return e instanceof t?e:tn(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;const{event:l}=e,{target:a}=l;this.props=e,this.events=t,this.document=tn(a),this.documentListeners=new un(this.document),this.listeners=new un(n),this.windowListeners=new un(qt(a)),this.initialCoordinates=null!=(r=ln(l))?r:sn,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),this.windowListeners.add(mn.Resize,this.handleCancel),this.windowListeners.add(mn.DragStart,pn),this.windowListeners.add(mn.VisibilityChange,this.handleCancel),this.windowListeners.add(mn.ContextMenu,pn),this.documentListeners.add(mn.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(vn(t))return void(this.timeoutId=setTimeout(this.handleStart,t.delay));if(hn(t))return}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handleStart(){const{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(mn.Click,fn,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(mn.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;const{activated:n,initialCoordinates:r,props:l}=this,{onMove:a,options:{activationConstraint:i}}=l;if(!r)return;const s=null!=(t=ln(e))?t:sn,o=rn(r,s);if(!n&&i){if(hn(i)){if(null!=i.tolerance&&gn(o,i.tolerance))return this.handleCancel();if(gn(o,i.distance))return this.handleStart()}return vn(i)&&gn(o,i.tolerance)?this.handleCancel():void 0}e.cancelable&&e.preventDefault(),a(s)}handleEnd(){const{onEnd:e}=this.props;this.detach(),e()}handleCancel(){const{onCancel:e}=this.props;this.detach(),e()}handleKeydown(e){e.code===cn.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}const yn={move:{name:"pointermove"},end:{name:"pointerup"}};(class extends bn{constructor(e){const{event:t}=e,n=tn(t.target);super(e,yn,n)}}).activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!(!n.isPrimary||0!==n.button||(null==r||r({event:n}),0))}}];const En={move:{name:"mousemove"},end:{name:"mouseup"}};var Cn;!function(e){e[e.RightClick=2]="RightClick"}(Cn||(Cn={})),class extends bn{constructor(e){super(e,En,tn(e.event.target))}}.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button!==Cn.RightClick&&(null==r||r({event:n}),!0)}}];const Bn={move:{name:"touchmove"},end:{name:"touchend"}};var Nn,Tn,Sn,Rn,wn;(class extends bn{constructor(e){super(e,Bn)}static setup(){return window.addEventListener(Bn.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(Bn.move.name,e)};function e(){}}}).activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;const{touches:l}=n;return!(l.length>1||(null==r||r({event:n}),0))}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(Nn||(Nn={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(Tn||(Tn={})),on.Backward,on.Forward,on.Backward,on.Forward,function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(Sn||(Sn={})),function(e){e.Optimized="optimized"}(Rn||(Rn={})),Sn.WhileDragging,Rn.Optimized,Map,function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(wn||(wn={})),cn.Down,cn.Right,cn.Up,cn.Left,a.__("Lectures","masterstudy-lms-learning-management-system"),a.__("Duration","masterstudy-lms-learning-management-system"),a.__("Views","masterstudy-lms-learning-management-system"),a.__("Level","masterstudy-lms-learning-management-system"),a.__("Members","masterstudy-lms-learning-management-system"),a.__("Empty","masterstudy-lms-learning-management-system"),x("sortable__item"),x("sortable__item__disabled"),x("sortable__item__content"),x("sortable__item__content__drag-item"),x("sortable__item__content__drag-item__disabled"),x("sortable__item__content__title"),x("sortable__item__control"),x("sortable__item__icon"),x("nested-sortable"),x("nested-sortable__item"),x("sortable");const xn=x("accordion"),Mn=x("accordion__header"),An=x("accordion__header-flex"),Un=x("accordion__content"),kn=x("accordion__icon"),Ln=x("accordion__title"),Hn=x("accordion__title-disabled"),On=x("accordion__indicator"),Pn=x("accordion__controls"),Dn=x("accordion__controls-disabled"),Fn=({title:e,children:t,accordionFields:n,switchName:l,visible:a=!0,isDefaultOpen:i=!1})=>{const{isOpen:o,onToggle:c,disabled:d,onReset:u,hasChanges:p,onClose:f}=((e,t,n)=>{var r;const{isOpen:l,onToggle:a,onClose:i}=A(t),{defaultValues:s,attributes:o,setAttributes:m}=U(),c=((e,t,n)=>{for(const r of n)if(!v(e[r],t[r]))return!0;return!1})(s,o,e);return{isOpen:l,onToggle:a,disabled:!(null===(r=o[n])||void 0===r||r),hasChanges:c,onReset:t=>{t.stopPropagation(),m(e.reduce(((e,t)=>(e[t]=s[t],e)),{}))},onClose:i}})(n,i,l);return((e,t)=>{const{attributes:n}=U(),r=!n[t];(0,s.useEffect)((()=>{r&&e()}),[r,e])})(f,l),a?(0,r.createElement)("div",{className:xn},(0,r.createElement)("div",{className:Mn},(0,r.createElement)("div",{className:An,onClick:d?null:c},(0,r.createElement)("div",{className:g()(Ln,{[Hn]:d,"with-switch":Boolean(l)})},(0,r.createElement)("div",null,e),(0,r.createElement)(h,{condition:p&&!d},(0,r.createElement)("div",{className:On}))),(0,r.createElement)("div",{className:g()(Pn,{[Dn]:d})},(0,r.createElement)(m.Dashicon,{icon:o?"arrow-up-alt2":"arrow-down-alt2",className:kn,size:16}))),(0,r.createElement)(h,{condition:Boolean(l)},(0,r.createElement)(ut,{name:l})),(0,r.createElement)(h,{condition:p&&!d},(0,r.createElement)(Ce,{onReset:u}))),o&&(0,r.createElement)("div",{className:Un},t)):null};x("preset-picker"),x("preset-picker__label"),x("preset-picker__remove"),x("preset-picker__presets-list"),x("preset-picker__presets-list__item"),x("preset-picker__presets-list__item__preset"),x("preset-picker__presets-list__item__preset-active");const Wn={filterPadding:w,filterPaddingTablet:w,filterPaddingMobile:w,filterPaddingUnit:"px",filterPaddingUnitTablet:"px",filterPaddingUnitMobile:"px",filterLabelGap:10,filterLabelGapTablet:null,filterLabelGapMobile:null,filterLabelGapUnit:"px",filterLabelGapUnitTablet:"px",filterLabelGapUnitMobile:"px",filterBgColor:"#ffffff",filterColor:"#001931",filterBorderStyle:"solid",filterBorderStyleTablet:"",filterBorderStyleMobile:"",filterBorderColor:"#dbe0e9",filterBorderColorTablet:"",filterBorderColorMobile:"",filterBorderWidth:{top:"1",right:"1",bottom:"1",left:"1"},filterBorderWidthTablet:w,filterBorderWidthMobile:w,filterBorderWidthUnit:"px",filterBorderWidthUnitTablet:"px",filterBorderWidthUnitMobile:"px",filterBorderRadius:{top:"4",right:"4",bottom:"4",left:"4"},filterBorderRadiusTablet:w,filterBorderRadiusMobile:w,filterBorderRadiusUnit:"px",filterBorderRadiusUnitTablet:"px",filterBorderRadiusUnitMobile:"px"},In=Object.keys(Wn),zn={filterButtonFontSize:14,filterButtonFontSizeTablet:null,filterButtonFontSizeMobile:null,filterButtonFontSizeUnit:"px",filterButtonFontSizeUnitTablet:"px",filterButtonFontSizeUnitMobile:"px",filterButtonFontWeight:"500",filterButtonTextTransform:"uppercase",filterButtonFontStyle:"inherit",filterButtonTextDecoration:"inherit",filterButtonLineHeight:16,filterButtonLineHeightTablet:null,filterButtonLineHeightMobile:null,filterButtonLineHeightUnit:"px",filterButtonLineHeightUnitTablet:"px",filterButtonLineHeightUnitMobile:"px",filterButtonLetterSpacing:0,filterButtonLetterSpacingTablet:null,filterButtonLetterSpacingMobile:null,filterButtonLetterSpacingUnit:"px",filterButtonLetterSpacingUnitTablet:"px",filterButtonLetterSpacingUnitMobile:"px",filterButtonWordSpacing:0,filterButtonWordSpacingTablet:null,filterButtonWordSpacingMobile:null,filterButtonWordSpacingUnit:"px",filterButtonWordSpacingUnitTablet:"px",filterButtonWordSpacingUnitMobile:"px",filterButtonPadding:{top:"13",left:"20",bottom:"13",right:"20"},filterButtonPaddingTablet:w,filterButtonPaddingMobile:w,filterButtonPaddingUnit:"px",filterButtonPaddingUnitTablet:"px",filterButtonPaddingUnitMobile:"px",filterButtonMargin:{top:"0",left:"0",bottom:"20",right:"0"},filterButtonMarginTablet:w,filterButtonMarginMobile:w,filterButtonMarginUnit:"px",filterButtonMarginUnitTablet:"px",filterButtonMarginUnitMobile:"px",filterButtonColor:"#ffffff",filterButtonColorHover:"#ffffff",filterButtonBgColor:"#227aff",filterButtonBgColorHover:"#227aff",filterButtonBorderStyle:"none",filterButtonBorderStyleTablet:"",filterButtonBorderStyleMobile:"",filterButtonBorderStyleHover:"",filterButtonBorderStyleHoverTablet:"",filterButtonBorderStyleHoverMobile:"",filterButtonBorderColor:"",filterButtonBorderColorTablet:"",filterButtonBorderColorMobile:"",filterButtonBorderColorHover:"",filterButtonBorderColorHoverTablet:"",filterButtonBorderColorHoverMobile:"",filterButtonBorderWidth:w,filterButtonBorderWidthTablet:w,filterButtonBorderWidthMobile:w,filterButtonBorderWidthHover:w,filterButtonBorderWidthHoverTablet:w,filterButtonBorderWidthHoverMobile:w,filterButtonBorderWidthUnit:"px",filterButtonBorderWidthUnitTablet:"px",filterButtonBorderWidthUnitMobile:"px",filterButtonBorderRadius:w,filterButtonBorderRadiusTablet:w,filterButtonBorderRadiusMobile:w,filterButtonBorderRadiusUnit:"px",filterButtonBorderRadiusUnitTablet:"px",filterButtonBorderRadiusUnitMobile:"px"},Vn=Object.keys(zn),jn={filterResetFontSize:15,filterResetFontSizeTablet:null,filterResetFontSizeMobile:null,filterResetFontSizeUnit:"px",filterResetFontSizeUnitTablet:"px",filterResetFontSizeUnitMobile:"px",filterResetFontWeight:"500",filterResetTextTransform:"inherit",filterResetFontStyle:"inherit",filterResetTextDecoration:"inherit",filterResetLineHeight:15,filterResetLineHeightTablet:null,filterResetLineHeightMobile:null,filterResetLineHeightUnit:"px",filterResetLineHeightUnitTablet:"px",filterResetLineHeightUnitMobile:"px",filterResetLetterSpacing:0,filterResetLetterSpacingTablet:null,filterResetLetterSpacingMobile:null,filterResetLetterSpacingUnit:"px",filterResetLetterSpacingUnitTablet:"px",filterResetLetterSpacingUnitMobile:"px",filterResetWordSpacing:0,filterResetWordSpacingTablet:null,filterResetWordSpacingMobile:null,filterResetWordSpacingUnit:"px",filterResetWordSpacingUnitTablet:"px",filterResetWordSpacingUnitMobile:"px",filterResetPadding:w,filterResetPaddingTablet:w,filterResetPaddingMobile:w,filterResetPaddingUnit:"px",filterResetPaddingUnitTablet:"px",filterResetPaddingUnitMobile:"px",filterResetMargin:w,filterResetMarginTablet:w,filterResetMarginMobile:w,filterResetMarginUnit:"px",filterResetMarginUnitTablet:"px",filterResetMarginUnitMobile:"px",filterResetColor:"#001931",filterResetColorHover:"#001931",filterResetBgColor:"",filterResetBgColorHover:"",filterResetBorderStyle:"none",filterResetBorderStyleTablet:"",filterResetBorderStyleMobile:"",filterResetBorderColor:"",filterResetBorderColorTablet:"",filterResetBorderColorMobile:"",filterResetBorderWidth:w,filterResetBorderWidthTablet:w,filterResetBorderWidthMobile:w,filterResetBorderWidthUnit:"px",filterResetBorderWidthUnitTablet:"px",filterResetBorderWidthUnitMobile:"px",filterResetBorderRadius:w,filterResetBorderRadiusTablet:w,filterResetBorderRadiusMobile:w,filterResetBorderRadiusUnit:"px",filterResetBorderRadiusUnitTablet:"px",filterResetBorderRadiusUnitMobile:"px"},Gn=Object.keys(jn),$n={filterTogglerFontSize:16,filterTogglerFontSizeTablet:null,filterTogglerFontSizeMobile:null,filterTogglerFontSizeUnit:"px",filterTogglerFontSizeUnitTablet:"px",filterTogglerFontSizeUnitMobile:"px",filterTogglerFontWeight:"700",filterTogglerTextTransform:"inherit",filterTogglerFontStyle:"inherit",filterTogglerTextDecoration:"inherit",filterTogglerLineHeight:16,filterTogglerLineHeightTablet:null,filterTogglerLineHeightMobile:null,filterTogglerLineHeightUnit:"px",filterTogglerLineHeightUnitTablet:"px",filterTogglerLineHeightUnitMobile:"px",filterTogglerLetterSpacing:0,filterTogglerLetterSpacingTablet:null,filterTogglerLetterSpacingMobile:null,filterTogglerLetterSpacingUnit:"px",filterTogglerLetterSpacingUnitTablet:"px",filterTogglerLetterSpacingUnitMobile:"px",filterTogglerWordSpacing:0,filterTogglerWordSpacingTablet:null,filterTogglerWordSpacingMobile:null,filterTogglerWordSpacingUnit:"px",filterTogglerWordSpacingUnitTablet:"px",filterTogglerWordSpacingUnitMobile:"px",filterTogglerPadding:{top:"14",left:"20",bottom:"14",right:"20"},filterTogglerPaddingTablet:w,filterTogglerPaddingMobile:w,filterTogglerPaddingUnit:"px",filterTogglerPaddingUnitTablet:"px",filterTogglerPaddingUnitMobile:"px",filterTogglerMargin:w,filterTogglerMarginTablet:w,filterTogglerMarginMobile:w,filterTogglerMarginUnit:"px",filterTogglerMarginUnitTablet:"px",filterTogglerMarginUnitMobile:"px",filterTogglerColor:"#227aff",filterTogglerColorHover:"#227aff",filterTogglerBgColor:"rgba(34, 122, 255, 0.1)",filterTogglerBgColorHover:"rgba(34, 122, 255, 0.1)"},Kn=Object.keys($n),Zn={...{...Wn,...zn,...jn,...$n}},Xn=new Map([["filterPadding",{unit:"filterPaddingUnit",isAdaptive:!0}],["filterLabelGap",{unit:"filterLabelGapUnit",isAdaptive:!0}],["filterBgColor",{}],["filterColor",{}],["filterBorderStyle",{isAdaptive:!0}],["filterBorderColor",{isAdaptive:!0}],["filterBorderWidth",{isAdaptive:!0,unit:"filterBorderWidthUnit"}],["filterBorderRadius",{isAdaptive:!0,unit:"filterBorderRadiusUnit"}],["filterButtonFontSize",{unit:"filterButtonFontSizeUnit",isAdaptive:!0}],["filterButtonFontWeight",{}],["filterButtonTextTransform",{}],["filterButtonFontStyle",{}],["filterButtonTextDecoration",{}],["filterButtonLineHeight",{unit:"filterButtonLineHeightUnit",isAdaptive:!0}],["filterButtonLetterSpacing",{unit:"filterButtonLetterSpacingUnit",isAdaptive:!0}],["filterButtonWordSpacing",{unit:"filterButtonWordSpacingUnit",isAdaptive:!0}],["filterButtonPadding",{unit:"filterButtonPaddingUnit",isAdaptive:!0}],["filterButtonMargin",{unit:"filterButtonMarginUnit",isAdaptive:!0}],["filterButtonColor",{hasHover:!0}],["filterButtonBgColor",{hasHover:!0}],["filterButtonBorderStyle",{isAdaptive:!0,hasHover:!0}],["filterButtonBorderColor",{isAdaptive:!0,hasHover:!0}],["filterButtonBorderWidth",{isAdaptive:!0,hasHover:!0,unit:"filterButtonBorderWidthUnit"}],["filterButtonBorderRadius",{isAdaptive:!0,unit:"filterButtonBorderRadiusUnit"}],["filterResetFontSize",{unit:"filterResetFontSizeUnit",isAdaptive:!0}],["filterResetFontWeight",{}],["filterResetTextTransform",{}],["filterResetFontStyle",{}],["filterResetTextDecoration",{}],["filterResetLineHeight",{unit:"filterResetLineHeightUnit",isAdaptive:!0}],["filterResetLetterSpacing",{unit:"filterResetLetterSpacingUnit",isAdaptive:!0}],["filterResetWordSpacing",{unit:"filterResetWordSpacingUnit",isAdaptive:!0}],["filterResetPadding",{unit:"filterResetPaddingUnit",isAdaptive:!0}],["filterResetMargin",{unit:"filterResetMarginUnit",isAdaptive:!0}],["filterResetColor",{hasHover:!0}],["filterResetBgColor",{hasHover:!0}],["filterResetBorderStyle",{isAdaptive:!0}],["filterResetBorderColor",{isAdaptive:!0}],["filterResetBorderWidth",{isAdaptive:!0,unit:"filterResetBorderWidthUnit"}],["filterResetBorderRadius",{isAdaptive:!0,unit:"filterResetBorderRadiusUnit"}],["filterTogglerFontSize",{unit:"filterTogglerFontSizeUnit",isAdaptive:!0}],["filterTogglerFontWeight",{}],["filterTogglerTextTransform",{}],["filterTogglerFontStyle",{}],["filterTogglerTextDecoration",{}],["filterTogglerLineHeight",{unit:"filterTogglerLineHeightUnit",isAdaptive:!0}],["filterTogglerLetterSpacing",{unit:"filterTogglerLetterSpacingUnit",isAdaptive:!0}],["filterTogglerWordSpacing",{unit:"filterTogglerWordSpacingUnit",isAdaptive:!0}],["filterTogglerPadding",{unit:"filterTogglerPaddingUnit",isAdaptive:!0}],["filterTogglerMargin",{unit:"filterTogglerMarginUnit",isAdaptive:!0}],["filterTogglerColor",{hasHover:!0}],["filterTogglerBgColor",{hasHover:!0}]]),Yn=()=>null,qn=()=>(0,r.createElement)(r.Fragment,null,(0,r.createElement)(Fn,{title:a.__("Filter","masterstudy-lms-learning-management-system"),accordionFields:In},(0,r.createElement)(Ge,{name:"filterPadding",label:a.__("Padding","masterstudy-lms-learning-management-system"),unitName:"filterPaddingUnit",isAdaptive:!0}),(0,r.createElement)(ft,{name:"filterLabelGap",label:a.__("Space between items","masterstudy-lms-learning-management-system"),unitName:"filterLabelGapUnit",isAdaptive:!0}),(0,r.createElement)(Pe,{name:"filterBgColor",label:a.__("Background","masterstudy-lms-learning-management-system"),placeholder:a.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(Pe,{name:"filterColor",label:a.__("Color","masterstudy-lms-learning-management-system"),placeholder:a.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(tt,{label:a.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"filterBorderStyle",borderColorName:"filterBorderColor",borderWidthName:"filterBorderWidth",isAdaptive:!0}),(0,r.createElement)(lt,{name:"filterBorderRadius",label:a.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0})),(0,r.createElement)(Fn,{title:a.__("Show result button","masterstudy-lms-learning-management-system"),accordionFields:Vn},(0,r.createElement)(Ht,{fontSizeName:"filterButtonFontSize",fontSizeUnitName:"filterButtonFontSizeUnit",fontWeightName:"filterButtonFontWeight",textTransformName:"filterButtonTextTransform",fontStyleName:"filterButtonFontStyle",textDecorationName:"filterButtonTextDecoration",lineHeightName:"filterButtonLineHeight",lineHeightUnitName:"filterButtonLineHeightUnit",letterSpacingName:"filterButtonLetterSpacing",letterSpacingUnitName:"filterButtonLetterSpacingUnit",wordSpacingName:"filterButtonWordSpacing",wordSpacingUnitName:"filterButtonWordSpacingUnit",isAdaptive:!0}),(0,r.createElement)(Ge,{name:"filterButtonPadding",label:a.__("Padding","masterstudy-lms-learning-management-system"),unitName:"filterButtonPaddingUnit",isAdaptive:!0}),(0,r.createElement)(Ge,{name:"filterButtonMargin",label:a.__("Margin","masterstudy-lms-learning-management-system"),unitName:"filterButtonMarginUnit",isAdaptive:!0}),(0,r.createElement)(Pe,{name:"filterButtonColor",label:a.__("Color","masterstudy-lms-learning-management-system"),placeholder:a.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,r.createElement)(Pe,{name:"filterButtonBgColor",label:a.__("Background Color","masterstudy-lms-learning-management-system"),placeholder:a.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,r.createElement)(tt,{label:a.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"filterButtonBorderStyle",borderColorName:"filterButtonBorderColor",borderWidthName:"filterButtonBorderWidth",hasHover:!0,isAdaptive:!0}),(0,r.createElement)(lt,{name:"filterButtonBorderRadius",label:a.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0})),(0,r.createElement)(Fn,{title:a.__("Reset filters button","masterstudy-lms-learning-management-system"),accordionFields:Gn},(0,r.createElement)(Ht,{fontSizeName:"filterResetFontSize",fontSizeUnitName:"filterResetFontSizeUnit",fontWeightName:"filterResetFontWeight",textTransformName:"filterResetTextTransform",fontStyleName:"filterResetFontStyle",textDecorationName:"filterResetTextDecoration",lineHeightName:"filterResetLineHeight",lineHeightUnitName:"filterResetLineHeightUnit",letterSpacingName:"filterResetLetterSpacing",letterSpacingUnitName:"filterResetLetterSpacingUnit",wordSpacingName:"filterResetWordSpacing",wordSpacingUnitName:"filterResetWordSpacingUnit",isAdaptive:!0}),(0,r.createElement)(Ge,{name:"filterResetPadding",label:a.__("Padding","masterstudy-lms-learning-management-system"),unitName:"filterResetPaddingUnit",isAdaptive:!0}),(0,r.createElement)(Ge,{name:"filterResetMargin",label:a.__("Margin","masterstudy-lms-learning-management-system"),unitName:"filterResetMarginUnit",isAdaptive:!0}),(0,r.createElement)(Pe,{name:"filterResetColor",label:a.__("Color","masterstudy-lms-learning-management-system"),placeholder:a.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,r.createElement)(Pe,{name:"filterResetBgColor",label:a.__("Background Color","masterstudy-lms-learning-management-system"),placeholder:a.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,r.createElement)(tt,{label:a.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"filterResetBorderStyle",borderColorName:"filterResetBorderColor",borderWidthName:"filterResetBorderWidth",isAdaptive:!0}),(0,r.createElement)(lt,{name:"filterResetBorderRadius",label:a.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0})),(0,r.createElement)(Fn,{title:a.__("Filter (Mobile Toggle)","masterstudy-lms-learning-management-system"),accordionFields:Kn},(0,r.createElement)(Ht,{fontSizeName:"filterTogglerFontSize",fontSizeUnitName:"filterTogglerFontSizeUnit",fontWeightName:"filterTogglerFontWeight",textTransformName:"filterTogglerTextTransform",fontStyleName:"filterTogglerFontStyle",textDecorationName:"filterTogglerTextDecoration",lineHeightName:"filterTogglerLineHeight",lineHeightUnitName:"filterTogglerLineHeightUnit",letterSpacingName:"filterTogglerLetterSpacing",letterSpacingUnitName:"filterTogglerLetterSpacingUnit",wordSpacingName:"filterTogglerWordSpacing",wordSpacingUnitName:"filterTogglerWordSpacingUnit",isAdaptive:!0}),(0,r.createElement)(Ge,{name:"filterTogglerPadding",label:a.__("Padding","masterstudy-lms-learning-management-system"),unitName:"filterTogglerPaddingUnit",isAdaptive:!0}),(0,r.createElement)(Ge,{name:"filterTogglerMargin",label:a.__("Margin","masterstudy-lms-learning-management-system"),unitName:"filterTogglerMarginUnit",isAdaptive:!0}),(0,r.createElement)(Pe,{name:"filterTogglerColor",label:a.__("Color","masterstudy-lms-learning-management-system"),placeholder:a.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,r.createElement)(Pe,{name:"filterTogglerBgColor",label:a.__("Background Color","masterstudy-lms-learning-management-system"),placeholder:a.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}))),Jn=({attributes:e,setAttributes:t})=>{const{onResetByFieldName:n,changedFieldsByName:l}=((e,t,n,r=[])=>{const l=(e=>{const t={};return Object.entries(e).forEach((([e,n])=>{e.includes("UAG")||(t[e]=n)})),t})(t),a=!v(e,l),i=((e,t,n)=>{const r=new Map;return n.forEach((n=>{r.has(n)||r.set(n,(()=>t({[n]:e[n]})))})),r})(e,n,r),s=((e,t,n)=>{const r=new Map;return n.forEach((n=>{r.has(n)?r.set(n,!1):r.set(n,!v(e[n],t[n]))})),r})(e,l,r);return{hasChanges:a,onResetByFieldName:i,changedFieldsByName:s}})(Zn,e,t,Object.keys(Zn));return(0,r.createElement)(o.InspectorControls,null,(0,r.createElement)(f,{attributes:e,setAttributes:t,defaultValues:Zn,onResetByFieldName:n,changedFieldsByName:l},(0,r.createElement)(Zt,{generalTab:(0,r.createElement)(Yn,null),styleTab:(0,r.createElement)(qn,null),advancedTab:(0,r.createElement)(r.Fragment,null)})))},Qn=window.wp.apiFetch;var er=n.n(Qn);const tr=["masterstudy/courses-filter-category","masterstudy/courses-filter-status","masterstudy/courses-filter-level","masterstudy/courses-filter-rating","masterstudy/courses-filter-price","masterstudy/courses-filter-availability"],nr=[["masterstudy/courses-filter-category"],["masterstudy/courses-filter-status"],["masterstudy/courses-filter-level"],["masterstudy/courses-filter-rating"],["masterstudy/courses-filter-price"]],rr=JSON.parse('{"UU":"masterstudy/courses-filter"}');(0,l.registerBlockType)(rr.UU,{edit:({attributes:e,setAttributes:t,clientId:n})=>{const{settings:c}=(()=>{const[e,t]=(0,s.useState)({coming_soon:!1}),{setIsFetching:n,setError:r,isFetching:l,error:a}=(()=>{const[e,t]=(0,s.useState)(!0),[n,r]=(0,s.useState)("");return{isFetching:e,setIsFetching:t,error:n,setError:r}})();return(0,s.useEffect)((()=>{n(!0),(async()=>{try{return await er()({path:"masterstudy-lms/v2/blocks/settings"})}catch(e){throw new Error(e)}})().then((e=>{t((e=>({coming_soon:e.addons.coming_soon||!1}))(e))})).catch((e=>{r(e.message)})).finally((()=>{n(!1)}))}),[r,n]),{settings:e,isFetching:l,error:a}})(),[u,p]=(0,s.useState)(!1),f=(0,o.useBlockProps)({className:g()("archive-courses-filter",{"filter-opened":u}),style:S("filter",e,Xn)}),{innerBlocksLength:h,innerBlockNames:v}=(0,i.useSelect)((e=>{const t=e(o.store).getBlock(n);return{innerBlocksLength:t.innerBlocks.length,innerBlockNames:t.innerBlocks.reduce(((e,t)=>(e[t.name]={clientId:t.clientId},e)),{})}}),[n]),{insertBlock:_}=(0,i.useDispatch)(o.store),b=e=>{const t=(0,l.createBlock)(e);_(t,h,n)},y=(0,o.useInnerBlocksProps)({className:"archive-courses-filter__inner"},{template:nr,allowedBlocks:tr,templateLock:!1,renderAppender:!1});return(0,r.createElement)(r.Fragment,null,(0,r.createElement)(Jn,{attributes:e,setAttributes:t}),(0,r.createElement)("div",{...f},(0,r.createElement)("div",{className:"archive-courses-filter__header",onClick:()=>p((e=>!e))},(0,r.createElement)("span",{className:"stmlms-filter"}),a.__("Filters","masterstudy-lms-learning-management-system"),(0,r.createElement)("div",{className:"archive-courses-filter__mobile-switcher"},(0,r.createElement)("div",{className:"archive-courses-filter__mobile-switcher__inner"},(0,r.createElement)("div",null)))),(0,r.createElement)("div",{className:"archive-courses-filter__inner-wrap"},(0,r.createElement)("div",{...y}),h<6-(c.coming_soon?0:1)&&(0,r.createElement)(m.DropdownMenu,{label:a.__("Add Filters.","masterstudy-lms-learning-management-system"),text:a.__("Add Filters.","masterstudy-lms-learning-management-system"),icon:d,className:"archive-courses-filter__appender"},(({onClose:e})=>(0,r.createElement)(m.MenuGroup,null,!v["masterstudy/courses-filter-category"]&&(0,r.createElement)(m.MenuItem,{onClick:()=>b("masterstudy/courses-filter-category")},a.__("Filter by Category","masterstudy-lms-learning-management-system")),!v["masterstudy/courses-filter-status"]&&(0,r.createElement)(m.MenuItem,{onClick:()=>b("masterstudy/courses-filter-status")},a.__("Filter by Status","masterstudy-lms-learning-management-system")),!v["masterstudy/courses-filter-level"]&&(0,r.createElement)(m.MenuItem,{onClick:()=>b("masterstudy/courses-filter-level")},a.__("Filter by Lavel","masterstudy-lms-learning-management-system")),!v["masterstudy/courses-filter-rating"]&&(0,r.createElement)(m.MenuItem,{onClick:()=>b("masterstudy/courses-filter-rating")},a.__("Filter by Rating","masterstudy-lms-learning-management-system")),!v["masterstudy/courses-filter-price"]&&(0,r.createElement)(m.MenuItem,{onClick:()=>b("masterstudy/courses-filter-price")},a.__("Filter by Price","masterstudy-lms-learning-management-system")),c.coming_soon&&!v["masterstudy/courses-filter-availability"]&&(0,r.createElement)(m.MenuItem,{onClick:()=>b("masterstudy/courses-filter-availability")},a.__("Filter by Availability","masterstudy-lms-learning-management-system"))))),(0,r.createElement)("div",{className:"archive-courses-filter__footer"},(0,r.createElement)("div",{className:"archive-courses-filter__update"},a.__("Show Results","masterstudy-lms-learning-management-system")),(0,r.createElement)("div",{className:"archive-courses-filter__reset"},(0,r.createElement)("span",{className:"stmlms-reset"}),(0,r.createElement)("em",null,a.__("Reset All","masterstudy-lms-learning-management-system")))))))},save:({attributes:e})=>{const t=o.useBlockProps.save({className:"archive-courses-filter",style:S("filter",e,Xn)}),n=o.useInnerBlocksProps.save({className:"archive-courses-filter__inner"});return(0,r.createElement)("div",{...t},(0,r.createElement)("div",{className:"archive-courses-filter__header"},(0,r.createElement)("span",{className:"stmlms-filter"}),a.__("Filters","masterstudy-lms-learning-management-system"),(0,r.createElement)("div",{className:"archive-courses-filter__mobile-switcher"},(0,r.createElement)("div",{className:"archive-courses-filter__mobile-switcher__inner"},(0,r.createElement)("div",null)))),(0,r.createElement)("div",{className:"archive-courses-filter__inner-wrap"},(0,r.createElement)("div",{...n}),(0,r.createElement)("div",{className:"archive-courses-filter__footer"},(0,r.createElement)("div",{className:"archive-courses-filter__update"},a.__("Show Results","masterstudy-lms-learning-management-system")),(0,r.createElement)("div",{className:"archive-courses-filter__reset"},(0,r.createElement)("span",{className:"stmlms-reset"}),(0,r.createElement)("em",null,a.__("Reset All","masterstudy-lms-learning-management-system"))))))},icon:(0,r.createElement)("svg",{width:"500",height:"512",viewBox:"0 0 500 512",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)("g",{clipPath:"url(#clip0_3567_45413)"},(0,r.createElement)("path",{d:"M403.142 100.818H86.6198C83.7084 100.812 80.852 101.686 78.3657 103.344C75.8793 105.001 73.8596 107.378 72.5292 110.211C71.1796 113.084 70.5814 116.309 70.8006 119.53C71.0198 122.752 72.0479 125.844 73.7715 128.467L197.064 298.504C197.105 298.568 197.148 298.633 197.191 298.696C201.67 305.315 204.093 313.33 204.106 321.567V476.506C204.094 478.777 204.492 481.028 205.278 483.131C206.063 485.233 207.221 487.144 208.684 488.755C210.148 490.366 211.888 491.644 213.804 492.517C215.72 493.389 217.775 493.839 219.851 493.839C221.982 493.835 224.091 493.372 226.059 492.476L295.339 463.571C301.547 461.499 305.667 455.089 305.667 447.372L296.667 321.567C296.679 313.332 299.101 305.316 303.579 298.696C303.622 298.633 303.664 298.57 303.705 298.505L415.991 128.461C417.715 125.839 418.743 122.748 418.962 119.528C419.182 116.308 418.585 113.084 417.237 110.211C415.906 107.377 413.886 105 411.399 103.343C408.912 101.685 406.054 100.812 403.142 100.818Z",fill:"#227AFF",fillOpacity:"0.3"}),(0,r.createElement)("path",{d:"M477.988 4.8334e-05H21.5159C17.5667 -0.00741546 13.6922 1.0759 10.3196 3.13054C6.94706 5.18518 4.20742 8.13131 2.40285 11.644C0.572161 15.2054 -0.239266 19.2031 0.0580649 23.1964C0.355396 27.1896 1.74994 31.0231 4.08785 34.2741L171.327 269.854C171.383 269.933 171.44 270.013 171.499 270.091C177.574 278.297 180.861 288.233 180.879 298.443V490.513C180.862 493.329 181.402 496.119 182.468 498.725C183.534 501.331 185.104 503.701 187.089 505.698C189.074 507.695 191.434 509.28 194.033 510.361C196.633 511.443 199.42 512 202.236 512C205.126 511.995 207.987 511.422 210.656 510.311L304.63 474.479C313.051 471.91 318.64 463.964 318.64 454.397V298.444C318.656 288.235 321.941 278.299 328.015 270.092C328.074 270.013 328.131 269.935 328.187 269.855L495.417 34.267C497.754 31.0173 499.149 27.1852 499.447 23.1934C499.745 19.2015 498.935 15.2048 497.106 11.644C495.301 8.13055 492.56 5.18392 489.187 3.12923C485.813 1.07455 481.938 -0.00833721 477.988 4.8334e-05ZM307.062 254.7C297.719 267.375 292.666 282.701 292.637 298.447V451.239L206.868 483.939V298.439C206.837 282.696 201.784 267.373 192.444 254.7L30.0879 25.991H469.418L307.062 254.7Z",fill:"black"})),(0,r.createElement)("defs",null,(0,r.createElement)("clipPath",{id:"clip0_3567_45413"},(0,r.createElement)("rect",{width:"499.508",height:"512",fill:"white"}))))})},6942:(e,t)=>{var n;!function(){"use strict";var r={}.hasOwnProperty;function l(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=i(e,a(n)))}return e}function a(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return l.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)r.call(e,n)&&e[n]&&(t=i(t,n));return t}function i(e,t){return t?e?e+" "+t:e+t:e}e.exports?(l.default=l,e.exports=l):void 0===(n=function(){return l}.apply(t,[]))||(e.exports=n)}()}},n={};function r(e){var l=n[e];if(void 0!==l)return l.exports;var a=n[e]={exports:{}};return t[e](a,a.exports,r),a.exports}r.m=t,e=[],r.O=(t,n,l,a)=>{if(!n){var i=1/0;for(c=0;c<e.length;c++){for(var[n,l,a]=e[c],s=!0,o=0;o<n.length;o++)(!1&a||i>=a)&&Object.keys(r.O).every((e=>r.O[e](n[o])))?n.splice(o--,1):(s=!1,a<i&&(i=a));if(s){e.splice(c--,1);var m=l();void 0!==m&&(t=m)}}return t}a=a||0;for(var c=e.length;c>0&&e[c-1][2]>a;c--)e[c]=e[c-1];e[c]=[n,l,a]},r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={5317:0,9113:0};r.O.j=t=>0===e[t];var t=(t,n)=>{var l,a,[i,s,o]=n,m=0;if(i.some((t=>0!==e[t]))){for(l in s)r.o(s,l)&&(r.m[l]=s[l]);if(o)var c=o(r)}for(t&&t(n);m<i.length;m++)a=i[m],r.o(e,a)&&e[a]&&e[a][0](),e[a]=0;return r.O(c)},n=globalThis.webpackChunkmasterstudy_lms_learning_management_system=globalThis.webpackChunkmasterstudy_lms_learning_management_system||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})();var l=r.O(void 0,[9113],(()=>r(4280)));l=r.O(l)})();