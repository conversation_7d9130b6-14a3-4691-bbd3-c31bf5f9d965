(()=>{var e,t={4725:(e,t,n)=>{"use strict";const a=window.React,l=window.wp.blocks,r=window.wp.blockEditor,i=window.wp.element,s=(0,i.createContext)(null),o=({children:e,...t})=>(0,a.createElement)(s.Provider,{value:{...t}},e);var m=n(6942),c=n.n(m);const d=window.wp.components,u=({condition:e,fallback:t=null,children:n})=>(0,a.createElement)(a.Fragment,null,e?n:t),g=(e,t)=>{if(typeof e!=typeof t)return!1;if(Number.isNaN(e)&&Number.isNaN(t))return!0;if("object"!=typeof e||null===e||null===t)return e===t;if(Object.keys(e).length!==Object.keys(t).length)return!1;if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;const n=e.slice().sort(),a=t.slice().sort();return n.every(((e,t)=>g(e,a[t])))}for(const n of Object.keys(e))if(!g(e[n],t[n]))return!1;return!0};let p=function(e){return e.ALL="all",e.SOME="some",e}({}),h=function(e){return e.NORMAL="Normal",e.HOVER="Hover",e.ACTIVE="Active",e.FOCUS="Focus",e}({}),v=function(e){return e.DESKTOP="Desktop",e.TABLET="Tablet",e.MOBILE="Mobile",e}({}),_=function(e){return e.TOP_lEFT="top-left",e.TOP_CENTER="top-center",e.TOP_RIGHT="top-right",e.BOTTOM_lEFT="bottom-left",e.BOTTOM_CENTER="bottom-center",e.BOTTOM_RIGHT="bottom-right",e}({});const y=["",null,void 0,"null","undefined"],b=[".jpg",".jpeg",".png",".gif"],E=e=>y.includes(e),f=(e,t,n="")=>{const a=e[t];return"object"==typeof a&&null!==a?((e,t)=>{return n=e,Object.values(n).every((e=>y.includes(e)))?null:((e,t="")=>{const n=Object.entries(e).reduce(((e,[n,a])=>(e[n]=(a||"0")+t,e)),{});return`${n.top} ${n.right} ${n.bottom} ${n.left}`})(e,t);var n})(a,n):((e,t)=>E(e)?e:(e=>{if("string"!=typeof e)return!1;const t=e.slice(e.lastIndexOf("."));return b.includes(t)||e.startsWith("blob")})(e)?`url('${e}')`:String(e+t))(a,n)},C=(e,t,n)=>{const a={};return n.forEach((({isAdaptive:n,hasHover:l,unit:r},i)=>{if(t.hasOwnProperty(i)){const{unitMeasureDesktop:o,unitMeasureTablet:m,unitMeasureMobile:c}=((e,t)=>{var n;return{unitMeasureDesktop:null!==(n=e[t])&&void 0!==n?n:"",unitMeasureTablet:t?e[t+"Tablet"]:"",unitMeasureMobile:t?e[t+"Mobile"]:""}})(t,r);if(n&&l){const{desktopHoverPropertyName:n,mobileHoverPropertyName:l,tabletHoverPropertyName:r}=(e=>{const t=e+h.HOVER;return{desktopHoverPropertyName:t,tabletHoverPropertyName:t+"Tablet",mobileHoverPropertyName:t+"Mobile"}})(i),s=f(t,n,o);E(s)||(a[`--lms-${e}-${n}`]=s);const d=f(t,r,m);E(d)||(a[`--lms-${e}-${r}`]=d);const u=f(t,l,c);E(u)||(a[`--lms-${e}-${l}`]=u)}if(l){const n=i+h.HOVER,l=f(t,n,o);E(l)||(a[`--lms-${e}-${n}`]=l)}if(n){const{desktopPropertyName:n,mobilePropertyName:l,tabletPropertyName:r}={desktopPropertyName:s=i,tabletPropertyName:s+"Tablet",mobilePropertyName:s+"Mobile"},d=f(t,n,o);E(d)||(a[`--lms-${e}-${n}`]=d);const u=f(t,r,m);E(u)||(a[`--lms-${e}-${r}`]=u);const g=f(t,l,c);E(g)||(a[`--lms-${e}-${l}`]=g)}const d=f(t,i,o);E(d)||(a[`--lms-${e}-${i}`]=d)}var s})),a},N=window.wp.i18n,w=(N.__("Small","masterstudy-lms-learning-management-system"),N.__("Normal","masterstudy-lms-learning-management-system"),N.__("Large","masterstudy-lms-learning-management-system"),N.__("Extra Large","masterstudy-lms-learning-management-system"),"wp-block-masterstudy-settings__"),S={top:"",right:"",bottom:"",left:""};function T(e){return Array.isArray(e)?e.map((e=>w+e)):w+e}_.TOP_lEFT,_.TOP_CENTER,_.TOP_RIGHT,_.BOTTOM_lEFT,_.BOTTOM_CENTER,_.BOTTOM_RIGHT,N.__("Newest","masterstudy-lms-learning-management-system"),N.__("Oldest","masterstudy-lms-learning-management-system"),N.__("Overall rating","masterstudy-lms-learning-management-system"),N.__("Popular","masterstudy-lms-learning-management-system"),N.__("Price low","masterstudy-lms-learning-management-system"),N.__("Price high","masterstudy-lms-learning-management-system");const x=window.wp.data,O=()=>(0,x.useSelect)((e=>e("core/editor").getDeviceType()||e("core/edit-site")?.__experimentalGetPreviewDeviceType()||e("core/edit-post")?.__experimentalGetPreviewDeviceType()||e("masterstudy/store")?.getDeviceType()),[])||"Desktop",k=(e=!1)=>{const[t,n]=(0,i.useState)(e),a=(0,i.useCallback)((()=>{n(!0)}),[]);return{isOpen:t,onClose:(0,i.useCallback)((()=>{n(!1)}),[]),onOpen:a,onToggle:(0,i.useCallback)((()=>{n((e=>!e))}),[])}},A=()=>{const e=(0,i.useContext)(s);if(!e)throw new Error("No settings context provided");return e},D=(e="")=>{const{attributes:t,setAttributes:n,onResetByFieldName:a,changedFieldsByName:l}=A();return{value:t[e],onChange:t=>n({[e]:t}),onReset:a.get(e),isChanged:l.get(e)}},L=(e,t=!1,n=!1)=>{const{hoverName:a,onChangeHoverName:l}=(()=>{const[e,t]=(0,i.useState)(h.NORMAL);return{hoverName:e,onChangeHoverName:(0,i.useCallback)((e=>{t(e)}),[])}})(),r=O();return{fieldName:(0,i.useMemo)((()=>{const l=a===h.HOVER?a:"",i=r===v.DESKTOP?"":r;return n&&t?e+l+i:n&&!t?e+l:t&&!n?e+i:e}),[e,n,t,a,r]),hoverName:a,onChangeHoverName:l}},M=(e=[],t=p.ALL)=>{const{attributes:n}=A();return!e.length||(t===p.ALL?e.every((({name:e,value:t})=>{const a=n[e];return Array.isArray(t)?Array.isArray(a)?g(t,a):t.includes(a):t===a})):t!==p.SOME||e.some((({name:e,value:t})=>{const a=n[e];return Array.isArray(t)?Array.isArray(a)?g(t,a):t.includes(a):t===a})))},R=e=>{const t=(0,i.useRef)(null);return(0,i.useEffect)((()=>{const n=n=>{t.current&&!t.current.contains(n.target)&&e()};return document.addEventListener("click",n),()=>{document.removeEventListener("click",n)}}),[t,e]),t},B=e=>(0,a.createElement)(d.SVG,{width:"16",height:"16",viewBox:"0 0 14 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(d.G,{"clip-path":"url(#clip0_1068_38993)"},(0,a.createElement)(d.Path,{d:"M11.1973 8.60005L8.74967 8.11005V5.67171C8.74967 5.517 8.68822 5.36863 8.57882 5.25923C8.46942 5.14984 8.32105 5.08838 8.16634 5.08838H6.99967C6.84496 5.08838 6.69659 5.14984 6.5872 5.25923C6.4778 5.36863 6.41634 5.517 6.41634 5.67171V8.58838H5.24967C5.15021 8.58844 5.05241 8.61391 4.96555 8.66238C4.87869 8.71084 4.80565 8.78068 4.75336 8.86529C4.70106 8.9499 4.67125 9.04646 4.66674 9.14582C4.66223 9.24518 4.68317 9.34405 4.72759 9.43305L6.47759 12.933C6.57676 13.1302 6.77859 13.255 6.99967 13.255H11.083C11.2377 13.255 11.3861 13.1936 11.4955 13.0842C11.6049 12.9748 11.6663 12.8264 11.6663 12.6717V9.17171C11.6665 9.03685 11.6198 8.90612 11.5343 8.80186C11.4487 8.69759 11.3296 8.62626 11.1973 8.60005Z"}),(0,a.createElement)(d.Path,{d:"M10.4997 1.58838H3.49967C2.85626 1.58838 2.33301 2.11221 2.33301 2.75505V6.83838C2.33301 7.4818 2.85626 8.00505 3.49967 8.00505H5.24967V6.83838H3.49967V2.75505H10.4997V6.83838H11.6663V2.75505C11.6663 2.11221 11.1431 1.58838 10.4997 1.58838Z"})),(0,a.createElement)("defs",null,(0,a.createElement)("clipPath",{id:"clip0_1068_38993"},(0,a.createElement)(d.Rect,{width:"14",height:"14",transform:"translate(0 0.421875)"})))),[H,P,F,U,z]=(h.NORMAL,N.__("Normal State","masterstudy-lms-learning-management-system"),h.HOVER,N.__("Hovered State","masterstudy-lms-learning-management-system"),h.ACTIVE,N.__("Hovered State","masterstudy-lms-learning-management-system"),h.FOCUS,N.__("Hovered State","masterstudy-lms-learning-management-system"),h.NORMAL,(0,a.createElement)((e=>(0,a.createElement)(d.SVG,{width:"16",height:"16",viewBox:"0 0 12 13",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(d.Path,{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"}))),null),N.__("Normal State","masterstudy-lms-learning-management-system"),h.HOVER,(0,a.createElement)(B,null),N.__("Hovered State","masterstudy-lms-learning-management-system"),h.ACTIVE,(0,a.createElement)(B,null),N.__("Active State","masterstudy-lms-learning-management-system"),h.FOCUS,(0,a.createElement)(B,null),N.__("Focus State","masterstudy-lms-learning-management-system"),T(["hover-state","hover-state__selected","hover-state__selected__opened-menu","hover-state__menu","hover-state__menu__item"])),V=T("color-indicator"),I=(0,i.memo)((({color:e,onChange:t})=>(0,a.createElement)("div",{className:V},(0,a.createElement)(r.PanelColorSettings,{enableAlpha:!0,disableCustomColors:!1,__experimentalHasMultipleOrigins:!0,__experimentalIsRenderedInSidebar:!0,colorSettings:[{label:"",value:e,onChange:t}]}))));var j;function W(){return W=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},W.apply(null,arguments)}var $,G,K=function(e){return a.createElement("svg",W({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 12 13"},e),j||(j=a.createElement("path",{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})))};function X(){return X=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},X.apply(null,arguments)}var Y=function(e){return a.createElement("svg",X({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 14 15"},e),$||($=a.createElement("g",{clipPath:"url(#state-hover_svg__a)"},a.createElement("path",{d:"M11.197 8.6 8.75 8.11V5.672a.583.583 0 0 0-.584-.584H7a.583.583 0 0 0-.584.584v2.916H5.25a.584.584 0 0 0-.522.845l1.75 3.5c.099.197.3.322.522.322h4.083a.583.583 0 0 0 .583-.583v-3.5a.58.58 0 0 0-.469-.572"}),a.createElement("path",{d:"M10.5 1.588h-7c-.644 0-1.167.524-1.167 1.167v4.083c0 .644.523 1.167 1.167 1.167h1.75V6.838H3.5V2.755h7v4.083h1.166V2.755c0-.643-.523-1.167-1.166-1.167"}))),G||(G=a.createElement("defs",null,a.createElement("clipPath",{id:"state-hover_svg__a"},a.createElement("path",{d:"M0 .422h14v14H0z"})))))};const Z=[{value:h.NORMAL,label:N.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(K,{onClick:e})},{value:h.HOVER,label:N.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(Y,{onClick:e})}],q={[h.NORMAL]:{icon:(0,a.createElement)(K,null),label:N.__("Normal State","masterstudy-lms-learning-management-system")},[h.HOVER]:{icon:(0,a.createElement)(Y,null),label:N.__("Hovered State","masterstudy-lms-learning-management-system")}},J=T("hover-state"),Q=T("hover-state__selected"),ee=T("hover-state__selected__opened-menu"),te=T("has-changes"),ne=T("hover-state__menu"),ae=T("hover-state__menu__item"),le=(0,i.memo)((e=>{const{hoverName:t,onChangeHoverName:n,fieldName:l}=e,{changedFieldsByName:r}=A(),s=r.get(l),{isOpen:o,onOpen:m,onClose:d}=k(),g=R(d),{ICONS_MAP:p,options:h}=(e=>{const t=(0,i.useMemo)((()=>Z.filter((t=>t.value!==e))),[e]);return{ICONS_MAP:q,options:t}})(t),v=(0,i.useCallback)((e=>{n(e),d()}),[n,d]);return(0,a.createElement)("div",{className:J,ref:g},(0,a.createElement)("div",{className:c()([Q],{[ee]:o,[te]:s}),onClick:m,title:p[t]?.label},p[t]?.icon),(0,a.createElement)(u,{condition:o},(0,a.createElement)("div",{className:ne},h.map((({value:e,icon:t,label:n})=>(0,a.createElement)("div",{key:e,className:ae,title:n},t((()=>v(e)))))))))})),re={Desktop:{icon:"desktop",label:N.__("Desktop","masterstudy-lms-learning-management-system")},Tablet:{icon:"tablet",label:N.__("Tablet","masterstudy-lms-learning-management-system")},Mobile:{icon:"smartphone",label:N.__("Mobile","masterstudy-lms-learning-management-system")}},ie=[{value:v.DESKTOP,icon:"desktop",label:N.__("Desktop","masterstudy-lms-learning-management-system")},{value:v.TABLET,icon:"tablet",label:N.__("Tablet","masterstudy-lms-learning-management-system")},{value:v.MOBILE,icon:"smartphone",label:N.__("Mobile","masterstudy-lms-learning-management-system")}],se=T("device-picker"),oe=T("device-picker__selected"),me=T("device-picker__selected__opened-menu"),ce=T("device-picker__menu"),de=T("device-picker__menu__item"),ue=()=>{const{isOpen:e,onOpen:t,onClose:n}=k(),{value:l,onChange:r}=(e=>{const t=O(),n=(0,x.useDispatch)();return{value:(0,i.useMemo)((()=>re[t]),[t]),onChange:t=>{n("core/edit-site")&&n("core/edit-site").__experimentalSetPreviewDeviceType?n("core/edit-site").__experimentalSetPreviewDeviceType(t):n("core/edit-post")&&n("core/edit-post").__experimentalSetPreviewDeviceType?n("core/edit-post").__experimentalSetPreviewDeviceType(t):n("masterstudy/store").setDeviceType(t),e()}}})(n),s=(e=>(0,i.useMemo)((()=>ie.filter((t=>t.icon!==e))),[e]))(l.icon),o=R(n);return(0,a.createElement)("div",{className:se,ref:o},(0,a.createElement)(d.Dashicon,{className:c()([oe],{[me]:e}),icon:l.icon,size:16,onClick:t,title:l.label}),(0,a.createElement)(u,{condition:e},(0,a.createElement)("div",{className:ce},s.map((e=>(0,a.createElement)(d.Dashicon,{key:e.value,icon:e.icon,size:16,onClick:()=>r(e.value),className:de,title:e.label}))))))},ge=T("reset-button"),pe=({onReset:e})=>(0,a.createElement)(d.Dashicon,{icon:"undo",onClick:e,className:ge,size:16}),he=[{label:"PX",value:"px"},{label:"%",value:"%"},{label:"EM",value:"em"},{label:"REM",value:"rem"},{label:"VW",value:"vw"},{label:"VH",value:"vh"}],ve=T("unit"),_e=T("unit__single"),ye=T("unit__list"),be=({name:e,isAdaptive:t})=>{const{isOpen:n,onOpen:l,onClose:r}=k(),{fieldName:i}=L(e,t),{value:s,onChange:o}=D(i),m=R(r);return(0,a.createElement)("div",{className:ve,ref:m},(0,a.createElement)("div",{className:_e,onClick:l},s),(0,a.createElement)(u,{condition:n},(0,a.createElement)("div",{className:ye},he.map((({value:e,label:t})=>(0,a.createElement)("div",{key:e,onClick:()=>(o(e),void r())},t))))))},Ee=T("popover-modal"),fe=T("popover-modal__close dashicon dashicons dashicons-no-alt"),Ce=e=>{const{isOpen:t,onClose:n,popoverContent:l}=e;return(0,a.createElement)(u,{condition:t},(0,a.createElement)(d.Popover,{position:"middle left",onClose:n,className:Ee},l,(0,a.createElement)("span",{onClick:n,className:fe})))},Ne=T("setting-label"),we=T("setting-label__content"),Se=e=>{const{label:t,isChanged:n=!1,onReset:l,showDevicePicker:r=!0,HoverStateControl:i=null,unitName:s,popoverContent:o=null,dependencies:m}=e,{isOpen:c,onClose:d,onToggle:g}=k();return M(m)?(0,a.createElement)("div",{className:Ne},(0,a.createElement)("div",{className:we},(0,a.createElement)("div",{onClick:g},t),(0,a.createElement)(u,{condition:Boolean(o)},(0,a.createElement)(Ce,{isOpen:c,onClose:d,popoverContent:o})),(0,a.createElement)(u,{condition:r},(0,a.createElement)(ue,null)),(0,a.createElement)(u,{condition:Boolean(i)},i)),(0,a.createElement)(u,{condition:Boolean(s)},(0,a.createElement)(be,{name:s,isAdaptive:r})),(0,a.createElement)(u,{condition:n},(0,a.createElement)(pe,{onReset:l}))):null},Te=T("suffix"),xe=()=>(0,a.createElement)("div",{className:Te},(0,a.createElement)(d.Dashicon,{icon:"color-picker",size:16})),Oe=T("color-picker"),ke=e=>{const{name:t,label:n,placeholder:l,dependencyMode:r,dependencies:i,isAdaptive:s=!1,hasHover:o=!1}=e,{fieldName:m,hoverName:c,onChangeHoverName:g}=L(t,s,o),{value:p,isChanged:h,onChange:v,onReset:_}=D(m);return M(i,r)?(0,a.createElement)("div",{className:Oe},(0,a.createElement)(u,{condition:Boolean(n)},(0,a.createElement)(Se,{label:n,isChanged:h,onReset:_,showDevicePicker:s,HoverStateControl:(0,a.createElement)(u,{condition:o},(0,a.createElement)(le,{hoverName:c,onChangeHoverName:g,fieldName:m}))})),(0,a.createElement)(d.__experimentalInputControl,{prefix:(0,a.createElement)(I,{color:p,onChange:v}),suffix:(0,a.createElement)(xe,null),onChange:v,value:p,placeholder:l})):null},Ae=T("number-steppers"),De=T("indent-steppers"),Le=T("indent-stepper-plus"),Me=T("indent-stepper-minus"),Re=({onIncrement:e,onDecrement:t,withArrows:n=!1})=>n?(0,a.createElement)("span",{className:De},(0,a.createElement)("button",{onClick:e,className:Le}),(0,a.createElement)("button",{onClick:t,className:Me})):(0,a.createElement)("span",{className:Ae},(0,a.createElement)("button",{onClick:e},"+"),(0,a.createElement)("button",{onClick:t},"-")),[Be,He]=T(["indents","indents-control"]),Pe=({name:e,label:t,unitName:n,popoverContent:l,dependencyMode:r,dependencies:s,isAdaptive:o=!1})=>{const{fieldName:m}=L(e,o),{value:c,onResetSegmentedBox:g,hasChanges:p,handleInputIncrement:h,handleInputDecrement:v,updateDirectionsValues:_,lastFieldValue:y}=((e,t)=>{const{value:n,isChanged:a,onChange:l,onReset:r}=D(e),{onResetByFieldName:s,changedFieldsByName:o}=A(),m=a||o.get(t),c=e=>{l({...n,...e})},[d,u]=(0,i.useState)(!1);return{value:n,onResetSegmentedBox:()=>{r(),s.get(t)()},hasChanges:m,handleInputIncrement:e=>Number(n[e])+1,handleInputDecrement:e=>Number(n[e])-1,updateDirectionsValues:(e,t,n)=>{e?(u(!1),c({top:n,right:n,bottom:n,left:n})):(u(n),c({[t]:n}))},lastFieldValue:d}})(m,n),[b,E]=(0,i.useState)((()=>{const{left:e,right:t,top:n,bottom:a}=c;return""!==e&&e===t&&t===n&&n===a})),f=e=>{const[t,n]=Object.entries(e)[0];_(b,t,n)},C=e=>()=>{const t=h(e);_(b,e,String(t))},w=e=>()=>{const t=v(e);_(b,e,String(t))};return M(s,r)?(0,a.createElement)("div",{className:Be},(0,a.createElement)(u,{condition:Boolean(t)},(0,a.createElement)(Se,{label:null!=t?t:"",isChanged:p,onReset:g,unitName:n,popoverContent:l,showDevicePicker:o})),(0,a.createElement)("div",{className:`${He} ${b?"active":""}`},(0,a.createElement)("div",null,(0,a.createElement)(d.__experimentalNumberControl,{value:c.top,onChange:e=>{f({top:e})},spinControls:"none",suffix:(0,a.createElement)(Re,{onIncrement:C("top"),onDecrement:w("top"),withArrows:!0})}),(0,a.createElement)("div",null,N.__("Top","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(d.__experimentalNumberControl,{value:c.right,onChange:e=>{f({right:e})},spinControls:"none",suffix:(0,a.createElement)(Re,{onIncrement:C("right"),onDecrement:w("right"),withArrows:!0})}),(0,a.createElement)("div",null,N.__("Right","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(d.__experimentalNumberControl,{value:c.bottom,onChange:e=>{f({bottom:e})},spinControls:"none",suffix:(0,a.createElement)(Re,{onIncrement:C("bottom"),onDecrement:w("bottom"),withArrows:!0})}),(0,a.createElement)("div",null,N.__("Bottom","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(d.__experimentalNumberControl,{value:c.left,onChange:e=>{f({left:e})},spinControls:"none",suffix:(0,a.createElement)(Re,{onIncrement:C("left"),onDecrement:w("left"),withArrows:!0})}),(0,a.createElement)("div",null,N.__("Left","masterstudy-lms-learning-management-system"))),(0,a.createElement)(d.Dashicon,{icon:"dashicons "+(b?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{b||!1===y||_(!0,"left",y),E((e=>!e))}}))):null},[Fe,Ue,ze,Ve]=T(["toggle-group-wrapper","toggle-group","toggle-group__toggle","toggle-group__active-toggle"]),[Ie,je,We,$e]=T(["border-control","border-control-solid","border-control-dashed","border-control-dotted"]),Ge=(T("border-radius"),T("border-radius-control"),T("box-shadow-preset"),T("presets")),Ke=T("presets__item-wrapper"),Xe=T("presets__item-wrapper__preset"),Ye=T("presets__item-wrapper__name"),Ze=((0,i.memo)((e=>{const{presets:t,activePreset:n,onSelectPreset:l,PresetItem:r,detectIsActive:i,detectByIndex:s=!1}=e;return(0,a.createElement)("div",{className:Ge},t.map((({name:e,...t},o)=>(0,a.createElement)("div",{key:o,className:c()([Ke],{active:i(n,s?o:t)}),onClick:()=>l(t)},(0,a.createElement)("div",{className:Xe},(0,a.createElement)(r,{preset:t})),(0,a.createElement)("span",{className:Ye},e)))))})),T("range-control")),qe=e=>{const{name:t,label:n,min:l,max:r,unitName:i,dependencyMode:s,dependencies:o,isAdaptive:m=!1}=e,{fieldName:c}=L(t,m),{value:g,onChange:p,onResetNumberField:h,hasChanges:v}=((e,t)=>{const{value:n,isChanged:a,onChange:l,onReset:r}=D(e),{onResetByFieldName:i,changedFieldsByName:s}=A();return{value:n,onChange:l,onResetNumberField:()=>{r(),i.get(t)()},hasChanges:a||s.get(t)}})(c,i);return M(o,s)?(0,a.createElement)("div",{className:Ze},(0,a.createElement)(u,{condition:Boolean(n)},(0,a.createElement)(Se,{label:n,isChanged:v,onReset:h,unitName:i,showDevicePicker:m})),(0,a.createElement)(d.RangeControl,{value:g,onChange:p,min:l,max:r})):null},Je=T("switch"),Qe=e=>{const{name:t,label:n,dependencyMode:l,dependencies:r,isAdaptive:i=!1}=e,{fieldName:s}=L(t,i),{value:o,onChange:m}=D(s);return M(r,l)?(0,a.createElement)("div",{className:Je,"data-has-label":Boolean(n).toString()},(0,a.createElement)(d.ToggleControl,{label:n,checked:o,onChange:m}),(0,a.createElement)(u,{condition:i},(0,a.createElement)(ue,null))):null},et=(T("box-shadow-settings"),T("box-shadow-presets-title"),T("input-field"),T("input-field-control"),T("number-field"),T("number-field-control"),({className:e})=>(0,a.createElement)("div",{className:e},N.__("No options","masterstudy-lms-learning-management-system"))),tt=T("select__single-item"),nt=T("select__container"),at=T("select__container__multi-item"),lt=({multiple:e,value:t,options:n,onChange:l})=>{const{singleValue:r,multipleValue:s}=((e,t,n)=>({singleValue:(0,i.useMemo)((()=>t?null:n.find((t=>t.value===e))?.label),[t,e,n]),multipleValue:(0,i.useMemo)((()=>t?e:null),[t,e])}))(t,e,n);return(0,a.createElement)(u,{condition:e,fallback:(0,a.createElement)("div",{className:tt},r)},(0,a.createElement)("div",{className:nt},s?.map((e=>{const t=n.find((t=>t.value===e));return t?(0,a.createElement)("div",{key:t.value,className:at},(0,a.createElement)("div",null,t.label),(0,a.createElement)(d.Dashicon,{icon:"no-alt",onClick:()=>l(t.value),size:16})):null}))))},rt=T("select"),it=T("select__select-box"),st=T("select__placeholder"),ot=T("select__select-box-multiple"),mt=T("select__menu"),ct=T("select__menu__options-container"),dt=T("select__menu__item"),ut=e=>{const{options:t,multiple:n=!1,placeholder:l="Select",value:r,onSelect:s}=e,{isOpen:o,onToggle:m,onClose:g}=k(),p=R(g),h=((e,t,n)=>(0,i.useMemo)((()=>n&&Array.isArray(e)?t.filter((t=>!e.includes(t.value))):t.filter((t=>t.value!==e))),[e,t,n]))(r,t,n),v=((e,t,n,a)=>(0,i.useCallback)((l=>{if(t&&Array.isArray(e)){const t=e.includes(l)?e.filter((e=>e!==l)):[...e,l];n(t)}else n(l),a()}),[t,e,n,a]))(r,n,s,g),_=((e,t)=>(0,i.useMemo)((()=>t&&Array.isArray(e)?Boolean(e.length):Boolean(e)),[t,e]))(r,n),y=n&&Array.isArray(r)&&r?.length>0;return(0,a.createElement)("div",{className:rt,ref:p},(0,a.createElement)("div",{className:c()([it],{[ot]:y}),onClick:m},(0,a.createElement)(u,{condition:_,fallback:(0,a.createElement)("div",{className:st},l)},(0,a.createElement)(lt,{onChange:v,options:t,multiple:n,value:r})),(0,a.createElement)(d.Dashicon,{icon:o?"arrow-up-alt2":"arrow-down-alt2",size:16,style:{color:"#4D5E6F"}})),(0,a.createElement)(u,{condition:o},(0,a.createElement)("div",{className:mt},(0,a.createElement)(u,{condition:Boolean(h.length),fallback:(0,a.createElement)(et,{className:dt})},(0,a.createElement)("div",{className:ct},h.map((e=>(0,a.createElement)("div",{key:e.value,onClick:()=>v(e.value),className:dt},e.label))))))))},gt=T("setting-select"),pt=e=>{const{name:t,options:n,label:l,multiple:r=!1,placeholder:i,isAdaptive:s=!1,dependencyMode:o,dependencies:m}=e,{fieldName:c}=L(t,s),{value:d,isChanged:g,onChange:p,onReset:h}=D(c);return M(m,o)?(0,a.createElement)("div",{className:gt},(0,a.createElement)(u,{condition:Boolean(l)},(0,a.createElement)(Se,{label:l,isChanged:g,onReset:h,showDevicePicker:s})),(0,a.createElement)(ut,{options:n,value:d,onSelect:p,multiple:r,placeholder:i})):null},ht=(T("row-select"),T("row-select__label"),T("row-select__control"),T("typography-select")),vt=T("typography-select-label"),_t=e=>{const{name:t,label:n,options:l,isAdaptive:r=!1}=e,{fieldName:i}=L(t,r),{isChanged:s,onReset:o}=D(i);return(0,a.createElement)("div",{className:ht},(0,a.createElement)("div",{className:vt},(0,a.createElement)("div",null,n),(0,a.createElement)(u,{condition:r},(0,a.createElement)(ue,null))),(0,a.createElement)(pt,{name:t,options:l,isAdaptive:r}),(0,a.createElement)(u,{condition:s},(0,a.createElement)(pe,{onReset:o})))},yt=T("typography"),bt=e=>{const{fontSizeName:t,fontWeightName:n,textTransformName:l,fontStyleName:r,textDecorationName:i,lineHeightName:s,letterSpacingName:o,wordSpacingName:m,fontSizeUnitName:c,lineHeightUnitName:d,letterSpacingUnitName:u,wordSpacingUnitName:g,dependencyMode:p,dependencies:h,isAdaptive:v=!1}=e,{fontWeightOptions:_,textTransformOptions:y,fontStyleOptions:b,textDecorationOptions:E}={fontWeightOptions:[{label:N.__("100 (Thin)","masterstudy-lms-learning-management-system"),value:"100"},{label:N.__("200 (Extra Light)","masterstudy-lms-learning-management-system"),value:"200"},{label:N.__("300 (Light)","masterstudy-lms-learning-management-system"),value:"300"},{label:N.__("400 (Normal)","masterstudy-lms-learning-management-system"),value:"400"},{label:N.__("500 (Medium)","masterstudy-lms-learning-management-system"),value:"500"},{label:N.__("600 (Semi Bold)","masterstudy-lms-learning-management-system"),value:"600"},{label:N.__("700 (Bold)","masterstudy-lms-learning-management-system"),value:"700"},{label:N.__("800 (Extra Bold)","masterstudy-lms-learning-management-system"),value:"800"},{label:N.__("900 (Extra)","masterstudy-lms-learning-management-system"),value:"900"}],textTransformOptions:[{label:N.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:N.__("Uppercase","masterstudy-lms-learning-management-system"),value:"uppercase"},{label:N.__("Lowercase","masterstudy-lms-learning-management-system"),value:"lowercase"},{label:N.__("Capitalize","masterstudy-lms-learning-management-system"),value:"capitalize"},{label:N.__("Normal","masterstudy-lms-learning-management-system"),value:"none"}],fontStyleOptions:[{label:N.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:N.__("Normal","masterstudy-lms-learning-management-system"),value:"none"},{label:N.__("Italic","masterstudy-lms-learning-management-system"),value:"italic"},{label:N.__("Oblique","masterstudy-lms-learning-management-system"),value:"oblique"}],textDecorationOptions:[{label:N.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:N.__("Underline","masterstudy-lms-learning-management-system"),value:"underline"},{label:N.__("Line Through","masterstudy-lms-learning-management-system"),value:"line-through"},{label:N.__("None","masterstudy-lms-learning-management-system"),value:"none"}]};return M(h,p)?(0,a.createElement)("div",{className:yt},(0,a.createElement)(qe,{name:t,label:N.__("Size","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:c,isAdaptive:v}),(0,a.createElement)(_t,{name:n,label:N.__("Weight","masterstudy-lms-learning-management-system"),options:_}),(0,a.createElement)(_t,{name:l,label:N.__("Transform","masterstudy-lms-learning-management-system"),options:y}),(0,a.createElement)(_t,{name:r,label:N.__("Style","masterstudy-lms-learning-management-system"),options:b}),(0,a.createElement)(_t,{name:i,label:N.__("Decoration","masterstudy-lms-learning-management-system"),options:E}),(0,a.createElement)(qe,{name:s,label:N.__("Line Height","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:d,isAdaptive:v}),(0,a.createElement)(qe,{name:o,label:N.__("Letter Spacing","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:u,isAdaptive:v}),m&&(0,a.createElement)(qe,{name:m,label:N.__("Word Spacing","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:g,isAdaptive:v})):null},Et=(T("file-upload"),T("file-upload__wrap"),T("file-upload__image"),T("file-upload__remove"),T("file-upload__replace"),(0,i.createContext)({activeTab:0,setActiveTab:()=>{}})),ft=()=>{const e=(0,i.useContext)(Et);if(!e)throw new Error("useTabs should be used inside Tabs");return e},Ct=({children:e})=>{const[t,n]=(0,i.useState)(0);return(0,a.createElement)(Et.Provider,{value:{activeTab:t,setActiveTab:n}},(0,a.createElement)("div",{className:`active-tab-${t}`},e))},Nt=T("tab-list"),wt=({children:e})=>(0,a.createElement)("div",{className:Nt},i.Children.map(e,((e,t)=>(0,i.cloneElement)(e,{index:t})))),St=T("tab"),Tt=T("tab-active"),xt=T("content"),Ot=({index:e,title:t,icon:n})=>{const{activeTab:l,setActiveTab:r}=ft();return(0,a.createElement)("div",{className:c()([St],{[Tt]:l===e}),onClick:()=>r(e)},(0,a.createElement)("div",{className:xt},(0,a.createElement)("div",null,n),(0,a.createElement)("div",null,t)))},kt=({children:e})=>(0,a.createElement)("div",null,i.Children.map(e,((e,t)=>(0,i.cloneElement)(e,{index:t})))),At=T("tab-panel"),Dt=({index:e,children:t})=>{const{activeTab:n}=ft();return n===e?(0,a.createElement)("div",{className:At},t):null},Lt=({generalTab:e,styleTab:t,advancedTab:n})=>(0,a.createElement)(Ct,null,(0,a.createElement)(wt,null,(0,a.createElement)(Ot,{title:N.__("General","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(d.Dashicon,{icon:"layout"})}),(0,a.createElement)(Ot,{title:N.__("Style","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(d.Dashicon,{icon:"admin-appearance"})}),(0,a.createElement)(Ot,{title:N.__("Advanced","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(d.Dashicon,{icon:"admin-settings"})})),(0,a.createElement)(kt,null,(0,a.createElement)(Dt,null,e),(0,a.createElement)(Dt,null,t),(0,a.createElement)(Dt,null,n)));window.ReactDOM;"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement;function Mt(e){const t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function Rt(e){return"nodeType"in e}function Bt(e){var t,n;return e?Mt(e)?e:Rt(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function Ht(e){const{Document:t}=Bt(e);return e instanceof t}function Pt(e){return!Mt(e)&&e instanceof Bt(e).HTMLElement}function Ft(e){return e instanceof Bt(e).SVGElement}function Ut(e){return e?Mt(e)?e.document:Rt(e)?Ht(e)?e:Pt(e)||Ft(e)?e.ownerDocument:document:document:document}function zt(e){return function(t){for(var n=arguments.length,a=new Array(n>1?n-1:0),l=1;l<n;l++)a[l-1]=arguments[l];return a.reduce(((t,n)=>{const a=Object.entries(n);for(const[n,l]of a){const a=t[n];null!=a&&(t[n]=a+e*l)}return t}),{...t})}}const Vt=zt(-1);function It(e){if(function(e){if(!e)return!1;const{TouchEvent:t}=Bt(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return function(e){return"clientX"in e&&"clientY"in e}(e)?{x:e.clientX,y:e.clientY}:null}var jt;!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(jt||(jt={}));const Wt=Object.freeze({x:0,y:0});var $t,Gt,Kt,Xt;!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}($t||($t={}));class Yt{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach((e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)}))},this.target=e}add(e,t,n){var a;null==(a=this.target)||a.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function Zt(e,t){const n=Math.abs(e.x),a=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+a**2)>t:"x"in t&&"y"in t?n>t.x&&a>t.y:"x"in t?n>t.x:"y"in t&&a>t.y}function qt(e){e.preventDefault()}function Jt(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(Gt||(Gt={})),(Xt=Kt||(Kt={})).Space="Space",Xt.Down="ArrowDown",Xt.Right="ArrowRight",Xt.Left="ArrowLeft",Xt.Up="ArrowUp",Xt.Esc="Escape",Xt.Enter="Enter";Kt.Space,Kt.Enter,Kt.Esc,Kt.Space,Kt.Enter;function Qt(e){return Boolean(e&&"distance"in e)}function en(e){return Boolean(e&&"delay"in e)}class tn{constructor(e,t,n){var a;void 0===n&&(n=function(e){const{EventTarget:t}=Bt(e);return e instanceof t?e:Ut(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;const{event:l}=e,{target:r}=l;this.props=e,this.events=t,this.document=Ut(r),this.documentListeners=new Yt(this.document),this.listeners=new Yt(n),this.windowListeners=new Yt(Bt(r)),this.initialCoordinates=null!=(a=It(l))?a:Wt,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),this.windowListeners.add(Gt.Resize,this.handleCancel),this.windowListeners.add(Gt.DragStart,qt),this.windowListeners.add(Gt.VisibilityChange,this.handleCancel),this.windowListeners.add(Gt.ContextMenu,qt),this.documentListeners.add(Gt.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(en(t))return void(this.timeoutId=setTimeout(this.handleStart,t.delay));if(Qt(t))return}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handleStart(){const{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(Gt.Click,Jt,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(Gt.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;const{activated:n,initialCoordinates:a,props:l}=this,{onMove:r,options:{activationConstraint:i}}=l;if(!a)return;const s=null!=(t=It(e))?t:Wt,o=Vt(a,s);if(!n&&i){if(Qt(i)){if(null!=i.tolerance&&Zt(o,i.tolerance))return this.handleCancel();if(Zt(o,i.distance))return this.handleStart()}return en(i)&&Zt(o,i.tolerance)?this.handleCancel():void 0}e.cancelable&&e.preventDefault(),r(s)}handleEnd(){const{onEnd:e}=this.props;this.detach(),e()}handleCancel(){const{onCancel:e}=this.props;this.detach(),e()}handleKeydown(e){e.code===Kt.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}const nn={move:{name:"pointermove"},end:{name:"pointerup"}};(class extends tn{constructor(e){const{event:t}=e,n=Ut(t.target);super(e,nn,n)}}).activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;return!(!n.isPrimary||0!==n.button||(null==a||a({event:n}),0))}}];const an={move:{name:"mousemove"},end:{name:"mouseup"}};var ln;!function(e){e[e.RightClick=2]="RightClick"}(ln||(ln={})),class extends tn{constructor(e){super(e,an,Ut(e.event.target))}}.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;return n.button!==ln.RightClick&&(null==a||a({event:n}),!0)}}];const rn={move:{name:"touchmove"},end:{name:"touchend"}};var sn,on,mn,cn,dn;(class extends tn{constructor(e){super(e,rn)}static setup(){return window.addEventListener(rn.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(rn.move.name,e)};function e(){}}}).activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;const{touches:l}=n;return!(l.length>1||(null==a||a({event:n}),0))}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(sn||(sn={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(on||(on={})),$t.Backward,$t.Forward,$t.Backward,$t.Forward,function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(mn||(mn={})),function(e){e.Optimized="optimized"}(cn||(cn={})),mn.WhileDragging,cn.Optimized,Map,function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(dn||(dn={})),Kt.Down,Kt.Right,Kt.Up,Kt.Left,N.__("Lectures","masterstudy-lms-learning-management-system"),N.__("Duration","masterstudy-lms-learning-management-system"),N.__("Views","masterstudy-lms-learning-management-system"),N.__("Level","masterstudy-lms-learning-management-system"),N.__("Members","masterstudy-lms-learning-management-system"),N.__("Empty","masterstudy-lms-learning-management-system"),T("sortable__item"),T("sortable__item__disabled"),T("sortable__item__content"),T("sortable__item__content__drag-item"),T("sortable__item__content__drag-item__disabled"),T("sortable__item__content__title"),T("sortable__item__control"),T("sortable__item__icon"),T("nested-sortable"),T("nested-sortable__item"),T("sortable");const un=T("accordion"),gn=T("accordion__header"),pn=T("accordion__header-flex"),hn=T("accordion__content"),vn=T("accordion__icon"),yn=T("accordion__title"),bn=T("accordion__title-disabled"),En=T("accordion__indicator"),fn=T("accordion__controls"),Cn=T("accordion__controls-disabled"),Nn=({title:e,children:t,accordionFields:n,switchName:l,visible:r=!0,isDefaultOpen:s=!1})=>{const{isOpen:o,onToggle:m,disabled:p,onReset:h,hasChanges:v,onClose:_}=((e,t,n)=>{var a;const{isOpen:l,onToggle:r,onClose:i}=k(t),{defaultValues:s,attributes:o,setAttributes:m}=A(),c=((e,t,n)=>{for(const a of n)if(!g(e[a],t[a]))return!0;return!1})(s,o,e);return{isOpen:l,onToggle:r,disabled:!(null===(a=o[n])||void 0===a||a),hasChanges:c,onReset:t=>{t.stopPropagation(),m(e.reduce(((e,t)=>(e[t]=s[t],e)),{}))},onClose:i}})(n,s,l);return((e,t)=>{const{attributes:n}=A(),a=!n[t];(0,i.useEffect)((()=>{a&&e()}),[a,e])})(_,l),r?(0,a.createElement)("div",{className:un},(0,a.createElement)("div",{className:gn},(0,a.createElement)("div",{className:pn,onClick:p?null:m},(0,a.createElement)("div",{className:c()(yn,{[bn]:p,"with-switch":Boolean(l)})},(0,a.createElement)("div",null,e),(0,a.createElement)(u,{condition:v&&!p},(0,a.createElement)("div",{className:En}))),(0,a.createElement)("div",{className:c()(fn,{[Cn]:p})},(0,a.createElement)(d.Dashicon,{icon:o?"arrow-up-alt2":"arrow-down-alt2",className:vn,size:16}))),(0,a.createElement)(u,{condition:Boolean(l)},(0,a.createElement)(Qe,{name:l})),(0,a.createElement)(u,{condition:v&&!p},(0,a.createElement)(pe,{onReset:h}))),o&&(0,a.createElement)("div",{className:hn},t)):null};T("preset-picker"),T("preset-picker__label"),T("preset-picker__remove"),T("preset-picker__presets-list"),T("preset-picker__presets-list__item"),T("preset-picker__presets-list__item__preset"),T("preset-picker__presets-list__item__preset-active");const wn={hideDefault:!1},Sn=Object.keys(wn),Tn={padding:S,paddingTablet:S,paddingMobile:S,paddingUnit:"px",paddingUnitTablet:"px",paddingUnitMobile:"px",background:""},xn=Object.keys(Tn),On={titleColor:"",titleFontSize:16,titleFontSizeUnit:"px",titleFontWeight:"700",titleTextTransform:"",titleFontStyle:"",titleTextDecoration:"",titleLineHeight:24,titleLineHeightUnit:"px",titleLetterSpacing:null,titleLetterSpacingUnit:"px",titleWordSpacing:null,titleWordSpacingUnit:"px",togglerColor:""},kn=Object.keys(On),An={itemColor:"",itemFontSize:14,itemFontSizeUnit:"px",itemFontWeight:"500",itemTextTransform:"",itemFontStyle:"",itemTextDecoration:"",itemLineHeight:16,itemLineHeightUnit:"px",itemLetterSpacing:null,itemLetterSpacingUnit:"px",itemWordSpacing:null,itemWordSpacingUnit:"px",inputColor:"",inputColorActive:"",inputBgColor:"",inputBgColorActive:"",inputBorderColor:"",inputBorderColorActive:""},Dn=Object.keys(An),Ln={...{...wn},...{...Tn,...On,...An}},Mn=new Map([["padding",{unit:"paddingUnit",isAdaptive:!0}],["background",{}],["titleColor",{}],["titleFontSize",{unit:"titleFontSizeUnit"}],["titleFontWeight",{}],["titleTextTransform",{}],["titleFontStyle",{}],["titleTextDecoration",{}],["titleLineHeight",{unit:"titleLineHeightUnit"}],["titleLetterSpacing",{unit:"titleLetterSpacingUnit"}],["titleWordSpacing",{unit:"titleWordSpacingUnit"}],["togglerColor",{}],["itemColor",{}],["itemFontSize",{unit:"itemFontSizeUnit"}],["itemFontWeight",{}],["itemTextTransform",{}],["itemFontStyle",{}],["itemTextDecoration",{}],["itemLineHeight",{unit:"itemLineHeightUnit"}],["itemLetterSpacing",{unit:"itemLetterSpacingUnit"}],["itemWordSpacing",{unit:"itemWordSpacingUnit"}],["inputColor",{}],["inputColorActive",{}],["inputBgColor",{}],["inputBgColorActive",{}],["inputBorderColor",{}],["inputBorderColorActive",{}]]),Rn=()=>(0,a.createElement)(a.Fragment,null,(0,a.createElement)(Nn,{title:N.__("Layout","masterstudy-lms-learning-management-system"),accordionFields:Sn},(0,a.createElement)(Qe,{name:"hideDefault",label:N.__("Hide default","masterstudy-lms-learning-management-system")}))),Bn=()=>(0,a.createElement)(a.Fragment,null,(0,a.createElement)(Nn,{title:N.__("Layout","masterstudy-lms-learning-management-system"),accordionFields:xn},(0,a.createElement)(Pe,{name:"padding",label:N.__("Padding","masterstudy-lms-learning-management-system"),unitName:"paddingUnit",isAdaptive:!0}),(0,a.createElement)(ke,{name:"background",label:N.__("Background","masterstudy-lms-learning-management-system"),placeholder:N.__("Select color","masterstudy-lms-learning-management-system")})),(0,a.createElement)(Nn,{title:N.__("Header","masterstudy-lms-learning-management-system"),accordionFields:kn},(0,a.createElement)(ke,{name:"titleColor",label:N.__("Title color","masterstudy-lms-learning-management-system"),placeholder:N.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(bt,{fontSizeName:"titleFontSize",fontSizeUnitName:"titleFontSizeUnit",fontWeightName:"titleFontWeight",textTransformName:"titleTextTransform",fontStyleName:"titleFontStyle",textDecorationName:"titleTextDecoration",lineHeightName:"titleLineHeight",lineHeightUnitName:"titleLineHeightUnit",letterSpacingName:"titleLetterSpacing",letterSpacingUnitName:"titleLetterSpacingUnit",wordSpacingName:"titleWordSpacing",wordSpacingUnitName:"titleWordSpacingUnit"}),(0,a.createElement)(ke,{name:"togglerColor",label:N.__("Toggler color","masterstudy-lms-learning-management-system"),placeholder:N.__("Select color","masterstudy-lms-learning-management-system")})),(0,a.createElement)(Nn,{title:N.__("Items","masterstudy-lms-learning-management-system"),accordionFields:Dn},(0,a.createElement)(ke,{name:"itemColor",label:N.__("Text color","masterstudy-lms-learning-management-system"),placeholder:N.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(bt,{fontSizeName:"itemFontSize",fontSizeUnitName:"itemFontSizeUnit",fontWeightName:"itemFontWeight",textTransformName:"itemTextTransform",fontStyleName:"itemFontStyle",textDecorationName:"itemTextDecoration",lineHeightName:"itemLineHeight",lineHeightUnitName:"itemLineHeightUnit",letterSpacingName:"itemLetterSpacing",letterSpacingUnitName:"itemLetterSpacingUnit",wordSpacingName:"itemWordSpacing",wordSpacingUnitName:"itemWordSpacingUnit"}),(0,a.createElement)(ke,{name:"inputColor",label:N.__("Radio button color","masterstudy-lms-learning-management-system"),placeholder:N.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(ke,{name:"inputColorActive",label:N.__("Radio button selected color","masterstudy-lms-learning-management-system"),placeholder:N.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(ke,{name:"inputBgColor",label:N.__("Radio button background color","masterstudy-lms-learning-management-system"),placeholder:N.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(ke,{name:"inputBgColorActive",label:N.__("Radio button selected background color","masterstudy-lms-learning-management-system"),placeholder:N.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(ke,{name:"inputBorderColor",label:N.__("Radio button border color","masterstudy-lms-learning-management-system"),placeholder:N.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(ke,{name:"inputBorderColorActive",label:N.__("Radio button selected border color","masterstudy-lms-learning-management-system"),placeholder:N.__("Select color","masterstudy-lms-learning-management-system")}))),Hn=({attributes:e,setAttributes:t})=>{const{onResetByFieldName:n,changedFieldsByName:l}=((e,t,n,a=[])=>{const l=(e=>{const t={};return Object.entries(e).forEach((([e,n])=>{e.includes("UAG")||(t[e]=n)})),t})(t),r=!g(e,l),i=((e,t,n)=>{const a=new Map;return n.forEach((n=>{a.has(n)||a.set(n,(()=>t({[n]:e[n]})))})),a})(e,n,a),s=((e,t,n)=>{const a=new Map;return n.forEach((n=>{a.has(n)?a.set(n,!1):a.set(n,!g(e[n],t[n]))})),a})(e,l,a);return{hasChanges:r,onResetByFieldName:i,changedFieldsByName:s}})(Ln,e,t,Object.keys(Ln));return(0,a.createElement)(r.InspectorControls,null,(0,a.createElement)(o,{attributes:e,setAttributes:t,defaultValues:Ln,onResetByFieldName:n,changedFieldsByName:l},(0,a.createElement)(Lt,{generalTab:(0,a.createElement)(Rn,null),styleTab:(0,a.createElement)(Bn,null),advancedTab:(0,a.createElement)(a.Fragment,null)})))},Pn=JSON.parse('{"UU":"masterstudy/courses-filter-availability"}');(0,l.registerBlockType)(Pn.UU,{edit:({attributes:e,setAttributes:t})=>{const n=(0,r.useBlockProps)({style:C("filter-item",e,Mn)}),l=(0,r.useInnerBlocksProps)({...n},{template:[["masterstudy/courses-filter-availability-inner"]],allowedBlocks:["masterstudy/courses-filter-availability-inner"],templateLock:"all",renderAppender:null});return(0,a.createElement)(a.Fragment,null,(0,a.createElement)(Hn,{attributes:e,setAttributes:t}),(0,a.createElement)("div",{...l}))},save:({attributes:e})=>{const t=r.useBlockProps.save({style:C("filter-item",e,Mn)}),n=r.useInnerBlocksProps.save(t);return(0,a.createElement)("div",{...n})},icon:(0,a.createElement)("svg",{width:"536",height:"512",viewBox:"0 0 536 512",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,a.createElement)("rect",{opacity:"0.3",x:"70.3647",y:"68.7286",width:"374.543",height:"374.543",rx:"187.271",fill:"#227AFF"}),(0,a.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M346.65 52.4216C302.779 32.8736 253.764 28.0309 206.915 38.6157C160.067 49.2005 117.895 74.6457 86.6889 111.156C55.4832 147.667 36.9157 193.287 33.7555 241.212C30.5953 289.138 43.0118 336.801 69.1532 377.093C95.2946 417.385 133.76 448.147 178.813 464.792C223.866 481.437 273.093 483.072 319.151 469.454C365.209 455.836 405.632 427.694 434.39 389.226C463.147 350.758 478.7 304.024 478.727 255.994V234.356C478.727 228.834 483.204 224.356 488.727 224.356C494.25 224.356 498.727 228.834 498.727 234.356V256.006C498.697 308.348 481.748 359.278 450.408 401.201C419.068 443.124 375.016 473.792 324.822 488.633C274.628 503.474 220.981 501.692 171.882 483.552C122.784 465.413 80.8639 431.888 52.3751 387.978C23.8864 344.068 10.3549 292.125 13.7989 239.896C17.2428 187.667 37.4776 137.951 71.4855 98.1619C105.493 58.3728 151.452 30.6427 202.507 19.1075C253.563 7.57218 306.979 12.8497 354.79 34.153C359.835 36.4008 362.102 42.3126 359.854 47.3573C357.607 52.402 351.695 54.6694 346.65 52.4216Z",fill:"black"}),(0,a.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M519.071 39.4745C522.976 43.3797 522.976 49.7114 519.071 53.6166L263.071 309.617C261.196 311.492 258.652 312.546 256 312.546C253.348 312.546 250.804 311.492 248.929 309.617L179.111 239.798C175.206 235.893 175.206 229.562 179.111 225.656C183.016 221.751 189.348 221.751 193.253 225.656L256 288.403L504.929 39.4745C508.834 35.5692 515.166 35.5692 519.071 39.4745Z",fill:"black"}))})},6942:(e,t)=>{var n;!function(){"use strict";var a={}.hasOwnProperty;function l(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=i(e,r(n)))}return e}function r(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return l.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)a.call(e,n)&&e[n]&&(t=i(t,n));return t}function i(e,t){return t?e?e+" "+t:e+t:e}e.exports?(l.default=l,e.exports=l):void 0===(n=function(){return l}.apply(t,[]))||(e.exports=n)}()}},n={};function a(e){var l=n[e];if(void 0!==l)return l.exports;var r=n[e]={exports:{}};return t[e](r,r.exports,a),r.exports}a.m=t,e=[],a.O=(t,n,l,r)=>{if(!n){var i=1/0;for(c=0;c<e.length;c++){for(var[n,l,r]=e[c],s=!0,o=0;o<n.length;o++)(!1&r||i>=r)&&Object.keys(a.O).every((e=>a.O[e](n[o])))?n.splice(o--,1):(s=!1,r<i&&(i=r));if(s){e.splice(c--,1);var m=l();void 0!==m&&(t=m)}}return t}r=r||0;for(var c=e.length;c>0&&e[c-1][2]>r;c--)e[c]=e[c-1];e[c]=[n,l,r]},a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={4799:0,503:0};a.O.j=t=>0===e[t];var t=(t,n)=>{var l,r,[i,s,o]=n,m=0;if(i.some((t=>0!==e[t]))){for(l in s)a.o(s,l)&&(a.m[l]=s[l]);if(o)var c=o(a)}for(t&&t(n);m<i.length;m++)r=i[m],a.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return a.O(c)},n=globalThis.webpackChunkmasterstudy_lms_learning_management_system=globalThis.webpackChunkmasterstudy_lms_learning_management_system||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})();var l=a.O(void 0,[503],(()=>a(4725)));l=a.O(l)})();