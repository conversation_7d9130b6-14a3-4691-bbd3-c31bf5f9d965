(()=>{"use strict";var s={n:e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},d:(e,t)=>{for(var n in t)s.o(t,n)&&!s.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o:(s,e)=>Object.prototype.hasOwnProperty.call(s,e)};const e=window.wp.apiFetch;var t=s.n(e);const n="masterstudy-lms/v2/",r=`${n}course-categories`,a=`${n}courses`,c=async(s=!1)=>{try{let e="?children=true";return s&&(e+="&details=true"),await t()({path:`${r}${e}`})}catch(s){throw new Error(s)}},o=async s=>{try{return await t()({path:`${a}?${s}`})}catch(s){throw new Error(s)}};(()=>{function s(){const s=document.body.clientWidth+"px";document.documentElement.style.setProperty("--body-width",s)}s(),window.addEventListener("resize",s)})();const l=s=>`\n    <li>\n      <a href="${s.permalink}">\n        <span class="lms-course-classic__title">${s.post_title}</span>\n      </a>\n    </li>\n  `,i=(s,e)=>s.map((s=>`\n        <li>\n          <a href="${e}?terms[]=${s.id}&category[]=${s.id}">\n            <span>${s.name}</span>\n          </a>\n          ${s.children&&s.children.length>0?`\n            <span class="stmlms-chevron-down children-expand"></span>\n            <ul>\n              ${((s,e)=>s.map((s=>`\n        <li>\n          <a href="${e}?terms[]=${s.term_id}&category[]=${s.term_id}">\n            <span>${s.name}</span>\n          </a>\n        </li>\n      `)).join(""))(s.children,e)}\n            </ul>\n          `:""}\n        </li>\n      `)).join("");document.addEventListener("DOMContentLoaded",(async()=>{const s=document.querySelectorAll(".lms-courses-search-container__wrap"),[e,t]=await Promise.all([c(!0),o("/api/courses")]),{categories:n,course_url:r}=e,{courses:a}=t;s.forEach((s=>{const e=s.querySelector(".lms-courses-search-list-data");if(!e)return;((s,e,t,n,r)=>{const a=document.createElement("div");switch(a.className="lms-courses-search-box",e){case"buttonInsideLeft":a.classList.add("lms-courses-search-box__button-left");break;case"buttonOutside":a.classList.add("lms-courses-search-box__button-outside");break;case"buttonCompact":a.classList.add("lms-courses-search-box__button-compact")}const c=s=>s.map(l).join("");if("buttonCompact"===e){a.innerHTML=`\n      <div class="lms-courses-search-box__button">\n        <i class="stmlms-magnifier"></i>\n      </div>\n      <div class="lms-courses-search-box__inner">\n        <div class="lms-courses-search-box__inner-wrap">\n            <div class="lms-courses-search-box__inner-sidebar">\n                <div class="lms-courses-search-box__category">\n                    <div class="lms-courses-search-box__category-label">\n                    <i class="stmlms-hamburger"></i> Category\n                    </div>\n                    <div class="lms-courses-search-box__category-list-wrap">\n                    <ul class="lms-courses-search-box__category-list">\n                        ${i(t,n)}\n                    </ul>\n                    </div>\n                </div>\n                <div class="lms-courses-search-box__bar">\n                    <div class="lms-courses-search-box__bar-field">\n                        <input type="text" placeholder="Search" />\n                        <a href="${n}?search=" class="lms-courses-search-box__bar-button">\n                            <i class="stmlms-magnifier"></i>\n                        </a>\n                    </div>\n                    <ul class="lms-courses-search-box__bar-results" style="display: none;">\n                    ${c(r.slice(0,5))}\n                    </ul>\n                </div>\n            </div>\n        </div>\n      </div>\n    `;const e=a.querySelector(".lms-courses-search-box__button"),o=a.querySelector(".lms-courses-search-box__inner"),l=a.querySelector(".lms-courses-search-box__inner-wrap");e.addEventListener("click",(()=>{o.classList.toggle("expanded"),o.classList.contains("expanded")&&(document.body.style.overflow="hidden",s.closest(".lms-courses-search-container").classList.add("popup-open"))})),document.addEventListener("click",(t=>{o.classList.contains("expanded")&&(l.contains(t.target)||e.contains(t.target)||(o.classList.remove("expanded"),document.body.style.overflow="",s.closest(".lms-courses-search-container").classList.remove("popup-open")))}))}else a.innerHTML=`\n      <div class="lms-courses-search-box__category">\n        <div class="lms-courses-search-box__category-label">\n          <i class="stmlms-hamburger"></i> Category\n        </div>\n        <div class="lms-courses-search-box__category-list-wrap">\n          <ul class="lms-courses-search-box__category-list">\n            ${i(t,n)}\n          </ul>\n        </div>\n      </div>\n      <div class="lms-courses-search-box__bar">\n        <div class="lms-courses-search-box__bar-field">\n          <input type="text" placeholder="Search" />\n          <a href="${n}?search=" class="lms-courses-search-box__bar-button">\n            <i class="stmlms-magnifier"></i>\n          </a>\n        </div>\n        <ul class="lms-courses-search-box__bar-results" style="display: none;">\n          ${c(r.slice(0,5))}\n        </ul>\n      </div>\n    `;a.querySelectorAll(".children-expand").forEach((s=>{s.addEventListener("click",(e=>{e.stopPropagation();const t=s.closest("li");t&&t.classList.toggle("children-expanded")}))})),s.appendChild(a);const o=a.querySelector('input[type="text"]'),d=a.querySelector(".lms-courses-search-box__bar-button"),u=a.querySelector(".lms-courses-search-box__bar-results");let m=r.slice(0,5);o.addEventListener("focus",(()=>{m.length>0&&(u.style.display="block")})),o.addEventListener("input",(()=>{(()=>{const s=o.value.toLowerCase();m=""===s?r.slice(0,5):r.filter((e=>e.post_title.toLowerCase().startsWith(s))).sort(((s,e)=>s.post_title.localeCompare(e.post_title))).slice(0,5),u.innerHTML=c(m),0===m.length?u.style.display="none":u.style.display="block"})(),(()=>{const s=o.value.trim();d.href=`${n}?search=${s}`})()})),o.addEventListener("keydown",(s=>{if("Enter"===s.key&&1===m.length){s.preventDefault();const e=m[0];o.value=e.post_title,window.location.href=e.permalink}})),document.addEventListener("click",(s=>{a.contains(s.target)||(u.style.display="none")})),u.addEventListener("click",(()=>{u.style.display="block"}));const h=s.closest(".lms-courses-search-container");if(h){const s=h.querySelector(".lms-course-preloader");s&&s.remove()}})(s,e.dataset.style,n,r,a)}))}))})();