<?php
/**
 * Template for displaying course bundles section
 */

defined('ABSPATH') || exit;

$bundles = MSCB_Bundle::get_all_bundles();
?>

<div class="bundles-section">
    <div class="bundles-section__header">
        <h2><?php esc_html_e('Course Bundles', 'masterstudy-course-bundles'); ?></h2>
        <p class="bundles-section__description"><?php esc_html_e('Get multiple courses at discounted prices', 'masterstudy-course-bundles'); ?></p>
    </div>

    <div class="bundles-section__content">
        <?php if (!empty($bundles)) : ?>
            <div class="bundles-grid">
                <?php foreach ($bundles as $bundle) : ?>
                    <div class="bundle-card">
                        <div class="bundle-card__image">
                            <?php echo get_the_post_thumbnail($bundle->ID, 'large'); ?>
                        </div>
                        <div class="bundle-card__content">
                            <h3 class="bundle-card__title"><?php echo esc_html($bundle->post_title); ?></h3>
                            <div class="bundle-card__description">
                                <?php echo wp_kses_post($bundle->post_excerpt); ?>
                            </div>
                            <div class="bundle-card__courses">
                                <h4><?php esc_html_e('Included Courses:', 'masterstudy-course-bundles'); ?></h4>
                                <?php 
                                $courses = MSCB_Bundle::get_bundle_courses($bundle->ID);
                                if (!empty($courses)) : ?>
                                    <ul>
                                        <?php foreach ($courses as $course) : ?>
                                            <li><?php echo esc_html($course->post_title); ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                <?php endif; ?>
                            </div>
                            <div class="bundle-card__footer">
                                <div class="bundle-card__price">
                                    <?php 
                                    $regular_price = MSCB_Bundle::get_bundle_regular_price($bundle->ID);
                                    $sale_price = MSCB_Bundle::get_bundle_sale_price($bundle->ID);
                                    if ($sale_price && $sale_price < $regular_price) : ?>
                                        <del><?php echo wc_price($regular_price); ?></del>
                                        <ins><?php echo wc_price($sale_price); ?></ins>
                                    <?php else : ?>
                                        <span><?php echo wc_price($regular_price); ?></span>
                                    <?php endif; ?>
                                </div>
                                <a href="<?php echo esc_url(get_permalink($bundle->ID)); ?>" class="bundle-card__button">
                                    <?php esc_html_e('View Bundle', 'masterstudy-course-bundles'); ?>
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else : ?>
            <p class="bundles-section__empty">
                <?php esc_html_e('No bundles available at the moment.', 'masterstudy-course-bundles'); ?>
            </p>
        <?php endif; ?>
    </div>
</div> 