{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "masterstudy/featured-teacher-button", "version": "0.1.0", "title": "Master<PERSON><PERSON><PERSON> Featured Teacher <PERSON><PERSON>", "category": "masterstudy-lms-blocks", "icon": "button", "description": "Displays button", "parent": ["masterstudy/featured-teacher"], "supports": {"html": false}, "attributes": {"showViewAllButton": {"type": "boolean", "default": true}, "buttonText": {"type": "string", "default": ""}, "buttonUrl": {"type": "string", "default": ""}}, "keywords": [], "usesContext": ["masterstudy/showViewAllButton", "masterstudy/buttonText", "masterstudy/buttonUrl"], "example": {}, "textdomain": "masterstudy-lms-learning-management-system", "editorScript": "file:./index.js", "editorStyle": "file:./index.css", "style": "file:./style-index.css"}