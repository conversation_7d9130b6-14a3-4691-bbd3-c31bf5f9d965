(()=>{const e=e=>{"loading"===document.readyState?document.addEventListener("DOMContentLoaded",e,{once:!0}):e()};e((()=>{document.querySelector(".archive-courses-filter__header").addEventListener("click",(function(){const e=document.querySelector(".archive-courses-filter");document.querySelector(".archive-courses-filter__mobile-switcher").classList.toggle("active"),e.classList.toggle("filter-opened"),t()}))})),e((()=>{document.querySelectorAll(".lms-courses-filter-option-title").forEach((e=>{e.addEventListener("click",(()=>{const t=e.closest(".lms-courses-filter-option-title");if(t){const e=t.closest(".archive-courses-filter-item");e&&e.classList.toggle("hide-filter")}}))}))}));const t=()=>{document.querySelectorAll(".lms-courses-filter-option-collapse").forEach((e=>{const t=e.querySelector(".lms-courses-filter-option-list");e.setAttribute("style",`--collapse-max-height: ${t.offsetHeight}px`)}))};e(t),window.addEventListener("resize",t)})();