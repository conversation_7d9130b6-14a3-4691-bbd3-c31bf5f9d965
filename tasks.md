# ✅ Custom Linking Plugin – Tasks

## 📊 Progress: 25% Complete

---

## 📁 Folder Responsibilities

- `admin/` → Handles all **admin-side UI**, settings, and panel logic  
- `database/` → Handles **plugin activation**, **table creation**, and all **DB queries**  
- `frontend/` → Manages **user flow**, button handling, cart redirection, etc.  
- `includes/` → Contains the **core logic**, acts as the **backbone** of the plugin  
  (business logic, processing, validation, structure, helpers, autoloader)

---

## 🧩 Task 1: Admin Panel (`/admin`)
- [ ] Task 1.1: Initialize admin menu and settings page
- [ ] Task 1.2: Create `adminLink.php` and link it to the main plugin file
- [ ] Task 1.3: Create UI for entering LMS Course ID + WooCommerce Product ID
- [ ] Task 1.4: Add dropdown for selecting link type (`course` or `bundle`)
- [ ] Task 1.5: Pass data to `includes/` for processing and DB insertion
- [ ] Task 1.6: Show existing links with option to edit/delete

---

## 🗃️ Task 2: Database (`/database`) - ✅ COMPLETED
- [x] Task 2.1: Create `databaseLink.php` and link it to the main plugin file
- [x] Task 2.2: Create table on plugin activation (course_id, product_id, type)
- [x] Task 2.3: Create plugin uninstall function to remove table if needed
- [x] Task 2.4: Add DB functions for insert, update, delete, get links

---

## 🌐 Task 3: Frontend Flow (`/frontend`)
- [ ] Task 3.1: Create `frontendLink.php` and link it to main plugin file
- [ ] Task 3.2: Hook into LMS "Get Course" button
- [ ] Task 3.3: On button click, check for linked WooCommerce product
- [ ] Task 3.4: Try to add the linked product to WooCommerce cart
- [ ] Task 3.5: If add-to-cart fails, redirect user to product page
- [ ] Task 3.6: If successful, redirect to WooCommerce cart page

---

## 🧠 Task 4: Includes / Core Logic (`/includes`) - 🟡 IN PROGRESS
- [ ] Task 4.1: Create `includesLink.php` and connect it in the main plugin file
- [x] Task 4.2: Create `class-core.php` (acts as autoloader + plugin backbone)
- [ ] Task 4.3: Handle logic for saving, retrieving, validating link data
- [ ] Task 4.4: Create reusable functions (e.g., `get_linked_product($course_id)`)
- [ ] Task 4.5: Create utility functions (e.g., `log_event()`, `sanitize_input()`)
- [ ] Task 4.6: Keep all logic separated from UI for clean MVC-ish structure

---

## 🛒 Task 5: WooCommerce Purchase Access Logic
- [ ] Task 5.1: Hook into WooCommerce order completion
- [ ] Task 5.2: Detect if purchased product is linked to course/bundle
- [ ] Task 5.3: Grant course access using LMS functions
- [ ] Task 5.4: Support multiple courses if bundle is purchased

---

## 🧰 Task 6: System Utilities - 🟡 IN PROGRESS
- [x] Task 6.1: Create uninstall hook for plugin cleanup (optional)
- [ ] Task 6.2: Create debug logger function (logs to `debug.log`)
- [ ] Task 6.3: Add `WP_DEBUG` constants in config if not already present

---

## 📄 Task 7: Documentation - 🟡 IN PROGRESS
- [x] Task 7.1: Finalize and maintain `instructions.md`
- [x] Task 7.2: Track and check off items in `tasks.md`
- [ ] Task 7.3: Document admin usage and frontend behavior

---

## 🧪 Task 8: Testing
- [ ] Task 8.1: Create temp test files (e.g., `test-db.php`) to test logic in isolation
- [ ] Task 8.2: Test admin panel (add/edit/delete links)
- [ ] Task 8.3: Test frontend flow (course → cart → checkout)
- [ ] Task 8.4: Test course access logic after purchase
- [ ] Task 8.5: Handle edge cases and error logs

---

## ✅ Completed Tasks
- Database setup and table creation
- Plugin activation/deactivation hooks
- Core plugin structure
- Basic documentation

## 🚧 Next Steps
1. Complete the admin interface for managing course-product links
2. Implement frontend integration with LMS and WooCommerce
3. Add WooCommerce order completion hooks
4. Implement testing and debugging utilities
5. Finalize documentation
