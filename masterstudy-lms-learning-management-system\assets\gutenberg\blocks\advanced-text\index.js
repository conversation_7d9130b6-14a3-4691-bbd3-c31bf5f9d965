(()=>{"use strict";var e,t={5177:()=>{const e=window.React,t=window.wp.blocks,n=window.wp.i18n;let l=function(e){return e.TOP_lEFT="top-left",e.TOP_CENTER="top-center",e.TOP_RIGHT="top-right",e.BOTTOM_lEFT="bottom-left",e.BOTTOM_CENTER="bottom-center",e.BOTTOM_RIGHT="bottom-right",e}({});const a=["padding","margin","borderWidth","inset"],r=["top","right","bottom","left"],s=[{name:n.__("Small","masterstudy-lms-learning-management-system"),size:"1.4rem",slug:"small"},{name:n.__("Normal","masterstudy-lms-learning-management-system"),size:"1.6rem",slug:"normal"},{name:n.__("Large","masterstudy-lms-learning-management-system"),size:"2rem",slug:"lg"},{name:n.__("Extra Large","masterstudy-lms-learning-management-system"),size:"2.4rem",slug:"xl"}];l.TOP_lEFT,l.TOP_CENTER,l.TOP_RIGHT,l.BOTTOM_lEFT,l.BOTTOM_CENTER,l.BOTTOM_RIGHT,n.__("Newest","masterstudy-lms-learning-management-system"),n.__("Oldest","masterstudy-lms-learning-management-system"),n.__("Overall rating","masterstudy-lms-learning-management-system"),n.__("Popular","masterstudy-lms-learning-management-system"),n.__("Price low","masterstudy-lms-learning-management-system"),n.__("Price high","masterstudy-lms-learning-management-system");const o=(e,t,n,l)=>{const{type:a,cssProperty:r,value:s}=l,o={[e]:{...t[e]}};if("string"===a)s?o[e]={...o[e],[r]:s}:delete o[e][r],n(o);else{let l={[r]:s};r in t[e]&&(l={[r]:{...t[e][r],...l[r]}});for(const e in l[r])l[r][e]||delete l[r][e];Object.keys(l).length<1&&(l[r]=void 0),o[e]={...o[e],...l},n(o)}},i=(e,t,n)=>{const l={};return n.forEach((n=>{n.cssProperty&&(l[n.cssProperty]=((e,t,n,l)=>{const s=void 0!==l?((e,t)=>{if(!new RegExp(a.join("|"),"i").test(e))return t;if("object"==typeof t&&null!==t&&r.every((e=>e in t)))return t;if("string"==typeof t||"number"==typeof t){const e=t.toString().split(/\s+/),n={};return r.forEach(((t,l)=>{let a=1===e.length?e[0]:"";a=2===e.length?e[l%2?1:0]:a,a=3===e.length?e[l<3?l:1]:a,a=4===e.length?e[l]:a,n[t]=a})),n}return""})(t,l):"";return n[e][t]||s})(e,n.cssProperty,t,n.default))})),l},m=window.wp.components,c=({device:t,setDevice:l})=>(0,e.createElement)(m.__experimentalToggleGroupControl,{label:n.__("Select device","masterstudy-lms-learning-management-system"),onChange:e=>l(e),size:"default",value:t||"desktop"},(0,e.createElement)(m.__experimentalToggleGroupControlOption,{label:n.__("Mobile","masterstudy-lms-learning-management-system"),value:"mobile"}),(0,e.createElement)(m.__experimentalToggleGroupControlOption,{label:n.__("Tablet","masterstudy-lms-learning-management-system"),value:"tablet"}),(0,e.createElement)(m.__experimentalToggleGroupControlOption,{label:n.__("Desktop","masterstudy-lms-learning-management-system"),value:"desktop"})),g=window.wp.blockEditor,d=window.wp.element,u=(window.wp.data,(e,t={},n,l={})=>{const s=n?`-${n}`:"",o=`wp-block-masterstudy-${e}`,i=[],m={};return Object.entries(t).forEach((([e,t])=>{const n=a.includes(e),c=`--${o}${s}--${e}`,g=new RegExp(a.join("|"),"i"),d=n||g.test(e)?(e=>{const t=r.map((t=>e[t]?e[t]:"undefined")),n=[...new Set(t)];return 1===n.length?n[0]:t.join(" ").replace(/undefined/g,"0px")})(t):"string"==typeof t?t.trim():t;"undefined"!==d&&t&&(i.push(`${c}:${d}`),m[c]=d,l[c]=d)})),{blockClassName:o,blockStyleVariables:i.join(";"),blockStyleObject:m,accumulatedStyles:l}});(0,d.createContext)(null);const p=JSON.parse('{"UU":"masterstudy/advanced-text"}');(0,t.registerBlockType)(p.UU,{icon:{src:(0,e.createElement)("svg",{width:"512",height:"513",viewBox:"0 0 512 513",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("g",{clipPath:"url(#clip0_710_13)"},(0,e.createElement)("path",{opacity:"0.3",d:"M-29.453 40.9417H461.453C464.25 40.9417 466.933 42.0528 468.911 44.0308C470.889 46.0087 472 48.6914 472 51.4887V226.806C472 229.603 470.889 232.286 468.911 234.264C466.933 236.241 464.25 237.353 461.453 237.353H356.259C353.462 237.353 350.779 236.241 348.801 234.264C346.823 232.286 345.712 229.603 345.712 226.806V167.23H279.13V426.642H338.713C340.098 426.642 341.47 426.914 342.749 427.444C344.029 427.975 345.191 428.751 346.171 429.731C347.15 430.71 347.927 431.873 348.457 433.152C348.987 434.432 349.26 435.804 349.26 437.189V542.395C349.26 545.192 348.149 547.875 346.171 549.852C344.193 551.83 341.51 552.942 338.713 552.942H93.28C90.4828 552.942 87.8001 551.83 85.8221 549.852C83.8442 547.875 82.733 545.192 82.733 542.395V437.187C82.7335 434.39 83.845 431.708 85.8228 429.73C87.8007 427.753 90.4831 426.642 93.28 426.642H152.849V167.23H86.288V226.806C86.288 229.603 85.1768 232.286 83.1989 234.264C81.2209 236.241 78.5382 237.353 75.741 237.353H-29.453C-32.2502 237.353 -34.9329 236.241 -36.9109 234.264C-38.8888 232.286 -40 229.603 -40 226.806V51.4887C-40 48.6914 -38.8888 46.0087 -36.9109 44.0308C-34.9329 42.0528 -32.2502 40.9417 -29.453 40.9417Z",fill:"#227AFF"}),(0,e.createElement)("path",{d:"M10.547 0.94165H501.453C504.25 0.94165 506.933 2.05285 508.911 4.0308C510.889 6.00874 512 8.69141 512 11.4887V186.806C512 189.603 510.889 192.286 508.911 194.264C506.933 196.241 504.25 197.353 501.453 197.353H396.259C393.462 197.353 390.779 196.241 388.801 194.264C386.823 192.286 385.712 189.603 385.712 186.806V127.23H319.13V386.642H378.713C380.098 386.642 381.47 386.914 382.749 387.444C384.029 387.975 385.191 388.751 386.171 389.731C387.15 390.71 387.927 391.873 388.457 393.152C388.987 394.432 389.26 395.804 389.26 397.189V502.395C389.26 505.192 388.149 507.875 386.171 509.852C384.193 511.83 381.51 512.942 378.713 512.942H133.28C130.483 512.942 127.8 511.83 125.822 509.852C123.844 507.875 122.733 505.192 122.733 502.395V397.187C122.734 394.39 123.845 391.708 125.823 389.73C127.801 387.753 130.483 386.642 133.28 386.642H192.849V127.23H126.288V186.806C126.288 189.603 125.177 192.286 123.199 194.264C121.221 196.241 118.538 197.353 115.741 197.353H10.547C7.74976 197.353 5.06709 196.241 3.08914 194.264C1.1112 192.286 0 189.603 0 186.806V11.4887C0 8.69141 1.1112 6.00874 3.08914 4.0308C5.06709 2.05285 7.74976 0.94165 10.547 0.94165ZM490.907 22.0357H21.094V176.259H105.194V116.683C105.194 113.885 106.305 111.203 108.283 109.225C110.261 107.247 112.944 106.136 115.741 106.136H203.4C206.197 106.136 208.88 107.247 210.858 109.225C212.836 111.203 213.947 113.885 213.947 116.683V397.183C213.948 398.568 213.675 399.94 213.145 401.22C212.616 402.5 211.839 403.663 210.859 404.643C209.88 405.623 208.717 406.4 207.437 406.93C206.157 407.461 204.785 407.734 203.4 407.734H143.827V491.849H368.166V407.734H308.583C305.786 407.734 303.103 406.622 301.125 404.645C299.147 402.667 298.036 399.984 298.036 397.187V116.687C298.036 113.889 299.147 111.207 301.125 109.229C303.103 107.251 305.786 106.14 308.583 106.14H396.259C399.056 106.14 401.739 107.251 403.717 109.229C405.695 111.207 406.806 113.889 406.806 116.687V176.263H490.906L490.907 22.0357Z",fill:"black"})),(0,e.createElement)("defs",null,(0,e.createElement)("clipPath",{id:"clip0_710_13"},(0,e.createElement)("rect",{width:"512",height:"512",fill:"white",transform:"translate(0 0.94165)"}))))},edit:function({attributes:t,setAttributes:l,clientId:a}){const{blockStyles:r,level:p}=t,[y,_]=(0,d.useState)("desktop"),[C,v]=(0,d.useState)(!1),x=i(y,t,[{cssProperty:"textAlign",default:"left"},{cssProperty:"padding",default:0},{cssProperty:"margin",default:0},{cssProperty:"titleFontSize",default:"2rem"},{cssProperty:"textFontSize",default:"1rem"},{cssProperty:"titleMargin",default:"20px 0px"},{cssProperty:"titlePadding",default:0},{cssProperty:"textMargin",default:"15px 0px"},{cssProperty:"textPadding",default:0}]);((e,t,n)=>{(0,d.useEffect)((()=>{(!e||e&&e!==n)&&t({clientId:n})}),[e,t,n])})(t.clientId,l,a);const{blockClassName:E,accumulatedStyles:f}=u("advanced-text",x,y,t.blockStyles),P=(0,g.useBlockProps)({className:`${E} ${E}-${t.clientId}`,style:r});return(0,d.useEffect)((()=>{l({blockStyles:f})}),[f,l]),(0,e.createElement)(e.Fragment,null,(0,e.createElement)(g.BlockControls,{group:"block"},C&&(0,e.createElement)(g.HeadingLevelDropdown,{value:p,onChange:e=>l({level:e})}),(0,e.createElement)(g.AlignmentControl,{value:x.textAlign,onChange:e=>o(y,t,l,{type:"string",cssProperty:"textAlign",value:e})})),(0,e.createElement)(g.InspectorControls,null,(0,e.createElement)(m.Panel,{className:"masterstudy-components-panel"},(0,e.createElement)("div",{style:{borderTop:"1px solid #e0e0e0"}},(0,e.createElement)(m.PanelBody,{title:n.__("General Settings","masterstudy-lms-learning-management-system")},(0,e.createElement)(c,{device:y,setDevice:_}),(0,e.createElement)(m.__experimentalToggleGroupControl,{__nextHasNoMarginBottom:!0,isBlock:!0,value:x.textAlign,label:n.__("Alignment","masterstudy-lms-learning-management-system"),onChange:e=>o(y,t,l,{type:"string",cssProperty:"textAlign",value:e})},(0,e.createElement)(m.__experimentalToggleGroupControlOptionIcon,{icon:"editor-alignleft",value:"left"}),(0,e.createElement)(m.__experimentalToggleGroupControlOptionIcon,{icon:"editor-aligncenter",value:"center"}),(0,e.createElement)(m.__experimentalToggleGroupControlOptionIcon,{icon:"editor-alignright",value:"right"}),(0,e.createElement)(m.__experimentalToggleGroupControlOptionIcon,{icon:"editor-justify",value:"justify"})),(0,e.createElement)(m.PanelRow,null,(0,e.createElement)(m.__experimentalBoxControl,{values:x.padding,label:n.__("Padding","masterstudy-lms-learning-management-system"),onChange:e=>o(y,t,l,{type:"string",cssProperty:"padding",value:e})})),(0,e.createElement)(m.PanelRow,null,(0,e.createElement)(m.__experimentalBoxControl,{values:x.margin,label:n.__("Margin","masterstudy-lms-learning-management-system"),onChange:e=>o(y,t,l,{type:"string",cssProperty:"margin",value:e})})))),(0,e.createElement)("div",{style:{borderTop:"1px solid #e0e0e0"}},(0,e.createElement)(m.PanelBody,{title:n.__("Title Settings","masterstudy-lms-learning-management-system")},(0,e.createElement)(c,{device:y,setDevice:_}),(0,e.createElement)(m.FontSizePicker,{__nextHasNoMarginBottom:!0,fontSizes:s,label:n.__("Font size","masterstudy-lms-learning-management-system"),value:x.titleFontSize,fallbackFontSize:"1.6rem",withSlider:!0,onChange:e=>o(y,t,l,{type:"string",cssProperty:"titleFontSize",value:e})}),(0,e.createElement)(m.PanelRow,null,(0,e.createElement)(m.__experimentalBoxControl,{values:x.titlePadding,label:n.__("Padding","masterstudy-lms-learning-management-system"),onChange:e=>o(y,t,l,{type:"string",cssProperty:"titlePadding",value:e})})),(0,e.createElement)(m.PanelRow,null,(0,e.createElement)(m.__experimentalBoxControl,{values:x.titleMargin,label:n.__("Margin","masterstudy-lms-learning-management-system"),onChange:e=>o(y,t,l,{type:"string",cssProperty:"titleMargin",value:e})})))),(0,e.createElement)("div",{style:{borderTop:"1px solid #e0e0e0"}},(0,e.createElement)(m.PanelBody,{title:n.__("Text Settings","masterstudy-lms-learning-management-system")},(0,e.createElement)(c,{device:y,setDevice:_}),(0,e.createElement)(m.FontSizePicker,{__nextHasNoMarginBottom:!0,fontSizes:s,label:n.__("Font size","masterstudy-lms-learning-management-system"),value:x.textFontSize,fallbackFontSize:"1rem",withSlider:!0,onChange:e=>o(y,t,l,{type:"string",cssProperty:"textFontSize",value:e})}),(0,e.createElement)(m.PanelRow,null,(0,e.createElement)(m.__experimentalBoxControl,{values:x.textPadding,label:n.__("Padding","masterstudy-lms-learning-management-system"),onChange:e=>o(y,t,l,{type:"string",cssProperty:"textPadding",value:e})})),(0,e.createElement)(m.PanelRow,null,(0,e.createElement)(m.__experimentalBoxControl,{values:x.textMargin,label:n.__("Margin","masterstudy-lms-learning-management-system"),onChange:e=>o(y,t,l,{type:"string",cssProperty:"textMargin",value:e})})))))),(0,e.createElement)("div",{...P},(0,e.createElement)(g.RichText,{tagName:`h${p}`,className:`${E}__title`,onClick:()=>{v(!0)},value:t.title,onChange:e=>l({title:e}),placeholder:n.__("Heading","masterstudy-lms-learning-management-system")}),(0,e.createElement)(g.RichText,{tagName:"p",className:`${E}__text`,value:t.text,onChange:e=>l({text:e}),placeholder:n.__("Text","masterstudy-lms-learning-management-system")})))},save:({attributes:t})=>{const{blockStyles:n,level:l}=t;let a="";["mobile","tablet","desktop"].forEach((e=>{const l=i(e,t,[{cssProperty:"textAlign",default:"left"},{cssProperty:"padding",default:0},{cssProperty:"margin",default:0},{cssProperty:"titleFontSize",default:"2rem"},{cssProperty:"textFontSize",default:"1rem"},{cssProperty:"titleMargin",default:"20px 0px"},{cssProperty:"titlePadding",default:0},{cssProperty:"textMargin",default:"15px 0px"},{cssProperty:"textPadding",default:0}]),{blockClassName:r}=u("advanced-text",l,e,n);a=r}));const r=g.useBlockProps.save({className:`${a} ${a}-${t.clientId}`,style:n});return(0,e.createElement)("div",{...r},(0,e.createElement)(g.RichText.Content,{tagName:`h${l}`,className:`${a}__title`,value:t.title}),(0,e.createElement)(g.RichText.Content,{tagName:"p",className:`${a}__text`,value:t.text}))}})}},n={};function l(e){var a=n[e];if(void 0!==a)return a.exports;var r=n[e]={exports:{}};return t[e](r,r.exports,l),r.exports}l.m=t,e=[],l.O=(t,n,a,r)=>{if(!n){var s=1/0;for(c=0;c<e.length;c++){for(var[n,a,r]=e[c],o=!0,i=0;i<n.length;i++)(!1&r||s>=r)&&Object.keys(l.O).every((e=>l.O[e](n[i])))?n.splice(i--,1):(o=!1,r<s&&(s=r));if(o){e.splice(c--,1);var m=a();void 0!==m&&(t=m)}}return t}r=r||0;for(var c=e.length;c>0&&e[c-1][2]>r;c--)e[c]=e[c-1];e[c]=[n,a,r]},l.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={3314:0,5206:0};l.O.j=t=>0===e[t];var t=(t,n)=>{var a,r,[s,o,i]=n,m=0;if(s.some((t=>0!==e[t]))){for(a in o)l.o(o,a)&&(l.m[a]=o[a]);if(i)var c=i(l)}for(t&&t(n);m<s.length;m++)r=s[m],l.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return l.O(c)},n=globalThis.webpackChunkmasterstudy_lms_learning_management_system=globalThis.webpackChunkmasterstudy_lms_learning_management_system||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})();var a=l.O(void 0,[5206],(()=>l(5177)));a=l.O(a)})();