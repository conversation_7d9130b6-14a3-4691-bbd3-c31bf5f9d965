<?php

if (!defined('ABSPATH')) {
    exit;
}

/*
 * MSCB_Admin handles the admin interface for managing course bundles.
 */
class MSCB_Admin {
    private $bundle;

    // Set up hooks for admin menu, meta boxes, saving, and columns
    public function __construct() {
        $this->bundle = new MSCB_Bundle();
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('add_meta_boxes', array($this, 'add_meta_boxes'));
        add_action('save_post_mscb_bundle', array($this, 'save_bundle_meta'));
        add_action('admin_enqueue_scripts', array($this, 'admin_enqueue_scripts'));
        add_filter('manage_mscb_bundle_posts_columns', array($this, 'add_courses_and_price_column'));
        add_action('manage_mscb_bundle_posts_custom_column', array($this, 'render_courses_and_price_column'), 10, 2);
        add_filter('bulk_actions-edit-mscb_bundle', array($this, 'register_bulk_actions'));
        add_filter('handle_bulk_actions-edit-mscb_bundle', array($this, 'handle_bulk_actions'), 10, 3);
        add_action('admin_notices', array($this, 'bulk_action_admin_notice'));
    }

    /* Add the Course Bundles menu to the admin sidebar */
    public function add_admin_menu() {
        add_menu_page(
            __('Course Bundles', 'masterstudy-course-bundler'),
            __('Course Bundles', 'masterstudy-course-bundler'),
            'manage_options',
            'mscb-bundles',
            array($this, 'render_bundles_page'),
            'dashicons-welcome-learn-more',
            30
        );
    }

    /* Add meta boxes for courses and pricing to the bundle edit screen */
    public function add_meta_boxes() {
        add_meta_box(
            'mscb_bundle_courses',
            __('Bundle Courses', 'masterstudy-course-bundler'),
            array($this, 'render_courses_meta_box'),
            'mscb_bundle',
            'normal',
            'high'
        );
        add_meta_box(
            'mscb_bundle_pricing',
            __('Bundle Pricing', 'masterstudy-course-bundler'),
            array($this, 'render_pricing_meta_box'),
            'mscb_bundle',
            'normal',
            'high'
        );
    }

    /* Render the main admin page for bundles (list view) */
    public function render_bundles_page() {
        echo '<div class="wrap"><h1>' . esc_html__('Course Bundles', 'masterstudy-course-bundler') . '</h1>';
        echo '<div class="mscb-admin-table-scroll">';
        // The table will be rendered by WordPress after this function, so we just open the wrapper here.
    }

    /* Render the meta box for selecting courses in a bundle */
    public function render_courses_meta_box($post) {
        wp_nonce_field('mscb_bundle_courses', 'mscb_bundle_courses_nonce');
        $courses = $this->bundle->get_bundle_courses($post->ID);
        $selected_course_ids = array_map(function($c) { return $c['course_id']; }, $courses);
        $available_courses = get_posts(array(
            'post_type' => 'stm-courses',
            'posts_per_page' => -1,
            'orderby' => 'title',
            'order' => 'ASC'
        ));
        ?>
        <div class="mscb-courses-container">
            <select id="mscb-course-select" class="widefat">
                <option value=""><?php esc_html_e('Select a course...', 'masterstudy-course-bundler'); ?></option>
                <?php foreach ($available_courses as $course) : ?>
                    <option value="<?php echo esc_attr($course->ID); ?>">
                        <?php echo esc_html($course->post_title); ?>
                    </option>
                <?php endforeach; ?>
            </select>
            <button type="button" class="button" id="mscb-add-course">
                <?php esc_html_e('Add Course', 'masterstudy-course-bundler'); ?>
            </button>
            <input type="hidden" id="mscb-courses-input" name="mscb_courses" value="<?php echo esc_attr(implode(',', $selected_course_ids)); ?>">
            <div id="mscb-courses-list">
                <?php foreach ($courses as $course) : ?>
                    <div class="mscb-course-item" data-course-id="<?php echo esc_attr($course['course_id']); ?>">
                        <span class="mscb-course-title"><?php echo esc_html($course['course_title']); ?></span>
                        <button type="button" class="button mscb-remove-course">
                            <?php esc_html_e('Remove', 'masterstudy-course-bundler'); ?>
                        </button>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php
    }

    /* Render the meta box for setting bundle pricing */
    public function render_pricing_meta_box($post) {
        wp_nonce_field('mscb_bundle_pricing', 'mscb_bundle_pricing_nonce');
        $price = get_post_meta($post->ID, '_mscb_price', true);
        $sale_price = get_post_meta($post->ID, '_mscb_sale_price', true);
        ?>
        <div class="mscb-pricing-fields">
            <p>
                <label for="mscb_price">
                    <?php esc_html_e('Regular Price', 'masterstudy-course-bundler'); ?>
                </label>
                <input type="number" 
                       id="mscb_price" 
                       name="mscb_price" 
                       value="<?php echo esc_attr($price); ?>" 
                       step="0.01" 
                       min="0" 
                       class="widefat">
            </p>
            <p>
                <label for="mscb_sale_price">
                    <?php esc_html_e('Sale Price', 'masterstudy-course-bundler'); ?>
                </label>
                <input type="number" 
                       id="mscb_sale_price" 
                       name="mscb_sale_price" 
                       value="<?php echo esc_attr($sale_price); ?>" 
                       step="0.01" 
                       min="0" 
                       class="widefat">
            </p>
        </div>
        <?php
    }

    /* Save bundle meta (courses and pricing) when a bundle is saved */
    public function save_bundle_meta($post_id) {
        if (isset($_POST['mscb_bundle_courses_nonce']) && 
            wp_verify_nonce($_POST['mscb_bundle_courses_nonce'], 'mscb_bundle_courses')) {
            if (isset($_POST['mscb_courses'])) {
                $course_ids = array_filter(array_map('intval', explode(',', $_POST['mscb_courses'])));
                global $wpdb;
                $wpdb->delete($this->bundle->get_table_bundle_courses(), array('bundle_id' => $post_id)); //* Remove all old courses for this bundle
                $order = 0;
                foreach ($course_ids as $cid) {
                    $this->bundle->add_course_to_bundle($post_id, $cid, $order++); //* Add each selected course
                }
            }
        }
        if (isset($_POST['mscb_bundle_pricing_nonce']) && 
            wp_verify_nonce($_POST['mscb_bundle_pricing_nonce'], 'mscb_bundle_pricing')) {
            if (isset($_POST['mscb_price'])) {
                update_post_meta($post_id, '_mscb_price', sanitize_text_field($_POST['mscb_price']));
            }
            if (isset($_POST['mscb_sale_price'])) {
                update_post_meta($post_id, '_mscb_sale_price', sanitize_text_field($_POST['mscb_sale_price']));
            }
        }
    }

    /* Enqueue admin JS for dynamic course selection */
    public function admin_enqueue_scripts($hook) {
        if ($hook === 'post-new.php' || $hook === 'post.php' || $hook === 'edit.php') {
            wp_enqueue_script('mscb-admin-js', MSCB_PLUGIN_URL . 'assets/js/mscb-admin.js', array('jquery'), MSCB_VERSION, true);
            wp_enqueue_style('mscb-admin-css', MSCB_PLUGIN_URL . 'assets/css/mscb-admin.css', array(), MSCB_VERSION);
        }
    }

    /* Add custom columns for courses and price in the admin list */
    public function add_courses_and_price_column($columns) {
        $columns['mscb_courses'] = __('Courses in Bundle', 'masterstudy-course-bundler');
        $columns['mscb_price'] = __('Bundle Price', 'masterstudy-course-bundler');
        $columns['thumbnail'] = __('Thumbnail', 'masterstudy-course-bundler');
        return $columns;
    }

    /* Render the custom columns for courses and price */
    public function render_courses_and_price_column($column, $post_id) {
        if ($column === 'mscb_courses') {
            $courses = $this->bundle->get_bundle_courses($post_id);
            if (empty($courses)) {
                echo '<em>' . esc_html__('No courses', 'masterstudy-course-bundler') . '</em>';
            } else {
                $names = array();
                foreach ($courses as $course) {
                    $names[] = esc_html($course['course_title']);
                }
                echo implode(', ', $names);
            }
        }
        if ($column === 'mscb_price') {
            $price = get_post_meta($post_id, '_mscb_price', true);
            $sale_price = get_post_meta($post_id, '_mscb_sale_price', true);
            $currency = '₹'; // Change to your currency symbol
            if ($sale_price && $sale_price !== $price) {
                echo '<span style="color: #d54e21;"><del>' . esc_html($currency . number_format((float)$price, 2)) . '</del> ' . esc_html($currency . number_format((float)$sale_price, 2)) . '</span>';
            } else {
                echo esc_html($currency . number_format((float)$price, 2));
            }
        }
        if ($column === 'thumbnail') {
            if (has_post_thumbnail($post_id)) {
                echo get_the_post_thumbnail($post_id, array(40, 40));
            } else {
                echo '<img src="' . MSCB_PLUGIN_URL . 'assets/images/default-bundle.png" width="40" height="40" alt="' . esc_attr__('Default Bundle Image', 'masterstudy-course-bundler') . '">';
            }
        }
    }

    /**
     * Add bulk actions for bundles
     */
    public function register_bulk_actions($bulk_actions) {
        $bulk_actions['duplicate'] = __('Duplicate', 'masterstudy-course-bundler');
        $bulk_actions['activate'] = __('Activate', 'masterstudy-course-bundler');
        $bulk_actions['deactivate'] = __('Deactivate', 'masterstudy-course-bundler');
        return $bulk_actions;
    }

    /**
     * Handle bulk actions
     */
    public function handle_bulk_actions($redirect_to, $action, $post_ids) {
        if (!in_array($action, array('duplicate', 'activate', 'deactivate'))) {
            return $redirect_to;
        }

        $processed = 0;
        $action_key = $action . 'd'; // For notice message (duplicated, activated, deactivated)

        foreach ($post_ids as $post_id) {
            switch ($action) {
                case 'duplicate':
                    if ($this->duplicate_bundle($post_id)) {
                        $processed++;
                    }
                    break;
                case 'activate':
                    if ($this->update_bundle_status($post_id, 'publish')) {
                        $processed++;
                    }
                    break;
                case 'deactivate':
                    if ($this->update_bundle_status($post_id, 'draft')) {
                        $processed++;
                    }
                    break;
            }
        }

        $redirect_to = add_query_arg($action_key, $processed, $redirect_to);
        return $redirect_to;
    }

    /**
     * Display admin notice after bulk action
     */
    public function bulk_action_admin_notice() {
        $notices = array(
            'duplicated' => array(
                'singular' => '%s bundle duplicated successfully.',
                'plural' => '%s bundles duplicated successfully.',
                'type' => 'success'
            ),
            'activated' => array(
                'singular' => '%s bundle activated successfully.',
                'plural' => '%s bundles activated successfully.',
                'type' => 'success'
            ),
            'deactivated' => array(
                'singular' => '%s bundle deactivated successfully.',
                'plural' => '%s bundles deactivated successfully.',
                'type' => 'success'
            )
        );

        foreach ($notices as $key => $notice) {
            if (!empty($_REQUEST[$key])) {
                $count = intval($_REQUEST[$key]);
                $message = sprintf(
                    _n($notice['singular'], $notice['plural'], $count, 'masterstudy-course-bundler'),
                    number_format_i18n($count)
                );
                printf(
                    '<div class="notice notice-%s is-dismissible mscb-notice"><p>%s</p></div>',
                    esc_attr($notice['type']),
                    esc_html($message)
                );
            }
        }
    }

    /**
     * Duplicate a bundle
     */
    private function duplicate_bundle($post_id) {
        $post = get_post($post_id);
        if (!$post) {
            return false;
        }

        // Create duplicate post
        $new_post_args = array(
            'post_title'    => $post->post_title . ' ' . __('(Copy)', 'masterstudy-course-bundler'),
            'post_content'  => $post->post_content,
            'post_status'   => 'draft',
            'post_type'     => $post->post_type
        );

        $new_post_id = wp_insert_post($new_post_args);

        if (!$new_post_id) {
            return false;
        }

        // Copy post thumbnail
        if (has_post_thumbnail($post_id)) {
            $thumbnail_id = get_post_thumbnail_id($post_id);
            set_post_thumbnail($new_post_id, $thumbnail_id);
        }

        // Copy bundle courses
        $courses = $this->bundle->get_bundle_courses($post_id);
        if ($courses) {
            foreach ($courses as $course) {
                $this->bundle->add_course_to_bundle($new_post_id, $course['course_id'], $course['order_index']);
            }
        }

        // Copy bundle meta
        $price = get_post_meta($post_id, '_mscb_price', true);
        $sale_price = get_post_meta($post_id, '_mscb_sale_price', true);
        
        if ($price) {
            update_post_meta($new_post_id, '_mscb_price', $price);
        }
        if ($sale_price) {
            update_post_meta($new_post_id, '_mscb_sale_price', $sale_price);
        }

        return true;
    }

    /**
     * Update bundle status
     */
    private function update_bundle_status($post_id, $status) {
        $post = array(
            'ID' => $post_id,
            'post_status' => $status
        );
        return (bool) wp_update_post($post);
    }
} 