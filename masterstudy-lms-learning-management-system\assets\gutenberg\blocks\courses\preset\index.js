(()=>{var e,t={1163:(e,t,n)=>{"use strict";const i=window.wp.i18n,a=window.wp.blocks,r=window.React;var o=n.n(r);const l=()=>(0,r.createElement)("svg",{width:"512",height:"513",viewBox:"0 0 512 513",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)("g",{clipPath:"url(#clip0_708_21983)"},(0,r.createElement)("path",{d:"M471.767 1.93774H40.2229C18.5675 1.95767 1.01603 19.5091 0.996109 41.1645V472.709C1.01603 494.364 18.5675 511.916 40.2229 511.946H471.767C493.423 511.926 510.974 494.374 511.004 472.709V41.1645C510.984 19.5091 493.433 1.95767 471.767 1.93774ZM40.2229 21.5511H471.767C482.595 21.5611 491.371 30.3368 491.381 41.1645V80.3913H20.6095V41.1645C20.6195 30.3368 29.3952 21.5611 40.2229 21.5511ZM471.767 492.332H40.2229C29.3952 492.322 20.6195 483.546 20.6095 472.719V100.015H491.391V472.709C491.381 483.546 482.605 492.322 471.767 492.332Z",fill:"black"}),(0,r.createElement)("path",{d:"M471.777 512.942H40.2229C18.0694 512.922 0.0199222 494.872 0 472.719V41.1645C0.0199222 19.0111 18.0694 0.961573 40.2229 0.94165H471.767C493.931 0.961573 511.97 19.0111 511.99 41.1645V472.709C511.98 494.872 493.931 512.922 471.777 512.942ZM40.2229 2.93387C19.1651 2.95379 2.01214 20.1068 1.99222 41.1745V472.719C2.01214 493.777 19.1651 510.93 40.2328 510.949H471.777C492.835 510.93 509.988 493.776 510.008 472.709V41.1645C509.988 20.1068 492.835 2.95379 471.767 2.93387H40.2229ZM471.777 493.328H40.2229C28.8672 493.318 19.6233 484.074 19.6134 472.719V99.0185H492.377V472.709C492.377 484.074 483.133 493.318 471.777 493.328ZM21.6056 101.011V472.709C21.6156 482.969 29.9729 491.316 40.2229 491.326H471.767C482.027 491.316 490.374 482.969 490.384 472.709V101.011H21.6056ZM492.387 81.3974H19.6134V41.1645C19.6233 29.8089 28.8672 20.565 40.2229 20.555H471.767C483.123 20.565 492.367 29.8089 492.377 41.1645V81.3974H492.387ZM21.6056 79.4052H490.384V41.1645C490.374 30.9046 482.017 22.5572 471.767 22.5473H40.2229C29.963 22.5572 21.6156 30.9146 21.6056 41.1645V79.4052Z",fill:"black"}),(0,r.createElement)("path",{d:"M128.817 60.9272C134.247 60.9272 138.648 56.5255 138.648 51.0956C138.648 45.6658 134.247 41.264 128.817 41.264C123.387 41.264 118.985 45.6658 118.985 51.0956C118.985 56.5255 123.387 60.9272 128.817 60.9272Z",fill:"black"}),(0,r.createElement)("path",{d:"M128.817 61.9334C122.85 61.9334 117.989 57.0723 117.989 51.1057C117.989 45.139 122.85 40.278 128.817 40.278C134.784 40.278 139.645 45.139 139.645 51.1057C139.645 57.0723 134.784 61.9334 128.817 61.9334ZM128.817 42.2602C123.946 42.2602 119.981 46.2247 119.981 51.0957C119.981 55.9667 123.946 59.9312 128.817 59.9312C133.688 59.9312 137.652 55.9667 137.652 51.0957C137.652 46.2247 133.688 42.2602 128.817 42.2602Z",fill:"black"}),(0,r.createElement)("path",{d:"M89.4904 60.9272C94.9203 60.9272 99.322 56.5255 99.322 51.0956C99.322 45.6658 94.9203 41.264 89.4904 41.264C84.0606 41.264 79.6588 45.6658 79.6588 51.0956C79.6588 56.5255 84.0606 60.9272 89.4904 60.9272Z",fill:"black"}),(0,r.createElement)("path",{d:"M89.4904 61.9334C83.5237 61.9334 78.6627 57.0723 78.6627 51.1057C78.6627 45.139 83.5237 40.278 89.4904 40.278C95.4571 40.278 100.318 45.139 100.318 51.1057C100.318 57.0723 95.4571 61.9334 89.4904 61.9334ZM89.4904 42.2602C84.6195 42.2602 80.6549 46.2247 80.6549 51.0957C80.6549 55.9667 84.6195 59.9312 89.4904 59.9312C94.3614 59.9312 98.3259 55.9667 98.3259 51.0957C98.3259 46.2247 94.3614 42.2602 89.4904 42.2602Z",fill:"black"}),(0,r.createElement)("path",{d:"M50.1541 60.9272C55.5839 60.9272 59.9857 56.5255 59.9857 51.0956C59.9857 45.6658 55.5839 41.264 50.1541 41.264C44.7242 41.264 40.3225 45.6658 40.3225 51.0956C40.3225 56.5255 44.7242 60.9272 50.1541 60.9272Z",fill:"black"}),(0,r.createElement)("path",{d:"M50.1541 61.9334C44.1874 61.9334 39.3264 57.0723 39.3264 51.1057C39.3264 45.139 44.1874 40.278 50.1541 40.278C56.1208 40.278 60.9818 45.139 60.9818 51.1057C60.9818 57.0723 56.1307 61.9334 50.1541 61.9334ZM50.1541 42.2602C45.2831 42.2602 41.3186 46.2247 41.3186 51.0957C41.3186 55.9667 45.2831 59.9312 50.1541 59.9312C55.0251 59.9312 58.9896 55.9667 58.9896 51.0957C58.9896 46.2247 55.035 42.2602 50.1541 42.2602Z",fill:"black"}),(0,r.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M63 168.942C63 159.553 70.6112 151.942 80 151.942H136C145.389 151.942 153 159.553 153 168.942V216.942C153 226.33 145.389 233.942 136 233.942H80C70.6112 233.942 63 226.33 63 216.942V168.942ZM81 169.942V215.942H135V169.942H81ZM167 168.942C167 159.553 174.611 151.942 184 151.942H232C241.389 151.942 249 159.553 249 168.942V216.942C249 226.33 241.389 233.942 232 233.942H184C174.611 233.942 167 226.33 167 216.942V168.942ZM185 169.942V215.942H231V169.942H185ZM263 168.942C263 159.553 270.611 151.942 280 151.942H328C337.389 151.942 345 159.553 345 168.942V216.942C345 226.33 337.389 233.942 328 233.942H280C270.611 233.942 263 226.33 263 216.942V168.942ZM281 169.942V215.942H327V169.942H281ZM63 272.942C63 263.553 70.6112 255.942 80 255.942H136C145.389 255.942 153 263.553 153 272.942V320.942C153 330.33 145.389 337.942 136 337.942H80C70.6112 337.942 63 330.33 63 320.942V272.942ZM81 273.942V319.942H135V273.942H81ZM167 272.942C167 263.553 174.611 255.942 184 255.942H232C241.389 255.942 249 263.553 249 272.942V320.942C249 330.33 241.389 337.942 232 337.942H184C174.611 337.942 167 330.33 167 320.942V272.942ZM185 273.942V319.942H231V273.942H185ZM263 272.942C263 263.553 270.611 255.942 280 255.942H328C337.389 255.942 345 263.553 345 272.942V320.942C345 330.33 337.389 337.942 328 337.942H280C270.611 337.942 263 330.33 263 320.942V272.942ZM281 273.942V319.942H327V273.942H281ZM63 376.942C63 367.553 70.6112 359.942 80 359.942H136C145.389 359.942 153 367.553 153 376.942V424.942C153 434.331 145.389 441.942 136 441.942H80C70.6112 441.942 63 434.331 63 424.942V376.942ZM81 377.942V423.942H135V377.942H81ZM167 376.942C167 367.553 174.611 359.942 184 359.942H232C241.389 359.942 249 367.553 249 376.942V424.942C249 434.331 241.389 441.942 232 441.942H184C174.611 441.942 167 434.331 167 424.942V376.942ZM185 377.942V423.942H231V377.942H185ZM263 376.942C263 367.553 270.611 359.942 280 359.942H328C337.389 359.942 345 367.553 345 376.942V424.942C345 434.331 337.389 441.942 328 441.942H280C270.611 441.942 263 434.331 263 424.942V376.942ZM281 377.942V423.942H327V377.942H281Z",fill:"black"}),(0,r.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M359 168.942C359 159.553 366.611 151.942 376 151.942H432C441.389 151.942 449 159.553 449 168.942V216.942C449 226.33 441.389 233.942 432 233.942H376C366.611 233.942 359 226.33 359 216.942V168.942ZM377 169.942V215.942H431V169.942H377ZM359 272.942C359 263.553 366.611 255.942 376 255.942H432C441.389 255.942 449 263.553 449 272.942V320.942C449 330.33 441.389 337.942 432 337.942H376C366.611 337.942 359 330.33 359 320.942V272.942ZM377 273.942V319.942H431V273.942H377ZM359 376.942C359 367.553 366.611 359.942 376 359.942H432C441.389 359.942 449 367.553 449 376.942V424.942C449 434.331 441.389 441.942 432 441.942H376C366.611 441.942 359 434.331 359 424.942V376.942ZM377 377.942V423.942H431V377.942H377Z",fill:"black"}),(0,r.createElement)("rect",{opacity:"0.3",x:"166.612",y:"256.942",width:"336.66",height:"247.271",rx:"30",fill:"#227AFF"})),(0,r.createElement)("defs",null,(0,r.createElement)("clipPath",{id:"clip0_708_21983"},(0,r.createElement)("rect",{width:"512",height:"512",fill:"white",transform:"translate(0 0.94165)"})))),s=window.wp.element,p=window.wp.blockEditor;var c=n(6942),d=n.n(c);const u=window.wp.components,m=({condition:e,fallback:t=null,children:n})=>(0,r.createElement)(r.Fragment,null,e?n:t),g=(e,t)=>{if(typeof e!=typeof t)return!1;if(Number.isNaN(e)&&Number.isNaN(t))return!0;if("object"!=typeof e||null===e||null===t)return e===t;if(Object.keys(e).length!==Object.keys(t).length)return!1;if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;const n=e.slice().sort(),i=t.slice().sort();return n.every(((e,t)=>g(e,i[t])))}for(const n of Object.keys(e))if(!g(e[n],t[n]))return!1;return!0};let h=function(e){return e.ALL="all",e.SOME="some",e}({}),b=function(e){return e.NORMAL="Normal",e.HOVER="Hover",e.ACTIVE="Active",e.FOCUS="Focus",e}({}),y=function(e){return e.DESKTOP="Desktop",e.TABLET="Tablet",e.MOBILE="Mobile",e}({}),S=function(e){return e.TOP_lEFT="top-left",e.TOP_CENTER="top-center",e.TOP_RIGHT="top-right",e.BOTTOM_lEFT="bottom-left",e.BOTTOM_CENTER="bottom-center",e.BOTTOM_RIGHT="bottom-right",e}({});const v=["",null,void 0,"null","undefined"],f=[".jpg",".jpeg",".png",".gif"],x=e=>v.includes(e),_=(e,t,n="")=>{const i=e[t];return"object"==typeof i&&null!==i?((e,t)=>{return n=e,Object.values(n).every((e=>v.includes(e)))?null:((e,t="")=>{const n=Object.entries(e).reduce(((e,[n,i])=>(e[n]=(i||"0")+t,e)),{});return`${n.top} ${n.right} ${n.bottom} ${n.left}`})(e,t);var n})(i,n):((e,t)=>x(e)?e:(e=>{if("string"!=typeof e)return!1;const t=e.slice(e.lastIndexOf("."));return f.includes(t)||e.startsWith("blob")})(e)?`url('${e}')`:String(e+t))(i,n)},C=e=>({desktopPropertyName:e,tabletPropertyName:e+"Tablet",mobilePropertyName:e+"Mobile"}),N=(e,t,n)=>{const i={};return n.forEach((({isAdaptive:n,hasHover:a,unit:r},o)=>{if(t.hasOwnProperty(o)){const{unitMeasureDesktop:l,unitMeasureTablet:s,unitMeasureMobile:p}=((e,t)=>{var n;return{unitMeasureDesktop:null!==(n=e[t])&&void 0!==n?n:"",unitMeasureTablet:t?e[t+"Tablet"]:"",unitMeasureMobile:t?e[t+"Mobile"]:""}})(t,r);if(n&&a){const{desktopHoverPropertyName:n,mobileHoverPropertyName:a,tabletHoverPropertyName:r}=(e=>{const t=e+b.HOVER;return{desktopHoverPropertyName:t,tabletHoverPropertyName:t+"Tablet",mobileHoverPropertyName:t+"Mobile"}})(o),c=_(t,n,l);x(c)||(i[`--lms-${e}-${n}`]=c);const d=_(t,r,s);x(d)||(i[`--lms-${e}-${r}`]=d);const u=_(t,a,p);x(u)||(i[`--lms-${e}-${a}`]=u)}if(a){const n=o+b.HOVER,a=_(t,n,l);x(a)||(i[`--lms-${e}-${n}`]=a)}if(n){const{desktopPropertyName:n,mobilePropertyName:a,tabletPropertyName:r}=C(o),c=_(t,n,l);x(c)||(i[`--lms-${e}-${n}`]=c);const d=_(t,r,s);x(d)||(i[`--lms-${e}-${r}`]=d);const u=_(t,a,p);x(u)||(i[`--lms-${e}-${a}`]=u)}const c=_(t,o,l);x(c)||(i[`--lms-${e}-${o}`]=c)}})),i},w=(e,t,n,i,a,r)=>`${!0===e?"inset ":""} ${t}px ${n}px ${""!==i?`${i}px`:""} ${""!==a?`${a}px`:""} ${r}`,E=(e,t,n,i,a,r,o,l,s)=>{const p={};if(t[n]&&null!==t[i]&&null!==t[a]&&(p[`--lms-${e}-boxShadow`]=w(t[l],t[i],t[a],t[r],t[o],t[n])),s){const{tabletPropertyName:s,mobilePropertyName:c}=C(l),{tabletPropertyName:d,mobilePropertyName:u}=C(i),{tabletPropertyName:m,mobilePropertyName:g}=C(a),{tabletPropertyName:h,mobilePropertyName:b}=C(n),{tabletPropertyName:y,mobilePropertyName:S}=C(r),{tabletPropertyName:v,mobilePropertyName:f}=C(o);t[h]&&null!==t[d]&&null!==t[m]&&(p[`--lms-${e}-boxShadowTablet`]=w(t[s],t[d],t[m],t[y],t[v],t[h])),t[b]&&null!==t[u]&&null!==t[g]&&(p[`--lms-${e}-boxShadowMobile`]=w(t[c],t[u],t[g],t[S],t[f],t[b]))}return p},U=(e,t)=>{let n={};for(let i=0;i<t.length;i++)n[`--lms-${e}-${t[i].id}`]=String(i+1),t[i].children&&(n={...n,...U(e,t[i].children)});return n},T=(i.__("Small","masterstudy-lms-learning-management-system"),i.__("Normal","masterstudy-lms-learning-management-system"),i.__("Large","masterstudy-lms-learning-management-system"),i.__("Extra Large","masterstudy-lms-learning-management-system"),"wp-block-masterstudy-settings__"),M={top:"",right:"",bottom:"",left:""};function F(e){return Array.isArray(e)?e.map((e=>T+e)):T+e}S.TOP_lEFT,S.TOP_CENTER,S.TOP_RIGHT,S.BOTTOM_lEFT,S.BOTTOM_CENTER,S.BOTTOM_RIGHT,i.__("Newest","masterstudy-lms-learning-management-system"),i.__("Oldest","masterstudy-lms-learning-management-system"),i.__("Overall rating","masterstudy-lms-learning-management-system"),i.__("Popular","masterstudy-lms-learning-management-system"),i.__("Price low","masterstudy-lms-learning-management-system"),i.__("Price high","masterstudy-lms-learning-management-system");const P=window.wp.data,B=()=>(0,P.useSelect)((e=>e("core/editor").getDeviceType()||e("core/edit-site")?.__experimentalGetPreviewDeviceType()||e("core/edit-post")?.__experimentalGetPreviewDeviceType()||e("masterstudy/store")?.getDeviceType()),[])||"Desktop",L=(e=!1)=>{const[t,n]=(0,s.useState)(e),i=(0,s.useCallback)((()=>{n(!0)}),[]);return{isOpen:t,onClose:(0,s.useCallback)((()=>{n(!1)}),[]),onOpen:i,onToggle:(0,s.useCallback)((()=>{n((e=>!e))}),[])}},H=(0,s.createContext)(null),W=({children:e,...t})=>(0,r.createElement)(H.Provider,{value:{...t}},e),D=()=>{const e=(0,s.useContext)(H);if(!e)throw new Error("No settings context provided");return e},A=(e="")=>{const{attributes:t,setAttributes:n,onResetByFieldName:i,changedFieldsByName:a}=D();return{value:t[e],onChange:t=>n({[e]:t}),onReset:i.get(e),isChanged:a.get(e)}},I=(e,t=!1,n=!1)=>{const{hoverName:i,onChangeHoverName:a}=(()=>{const[e,t]=(0,s.useState)(b.NORMAL);return{hoverName:e,onChangeHoverName:(0,s.useCallback)((e=>{t(e)}),[])}})(),r=B();return{fieldName:(0,s.useMemo)((()=>{const a=i===b.HOVER?i:"",o=r===y.DESKTOP?"":r;return n&&t?e+a+o:n&&!t?e+a:t&&!n?e+o:e}),[e,n,t,i,r]),hoverName:i,onChangeHoverName:a}},z=(e,t=!1,n="Normal")=>{const i=B(),a=(0,s.useMemo)((()=>{const a=n===b.NORMAL?"":n,r=i===y.DESKTOP?"":i;return a&&t?e+a+r:a&&!t?e+a:t&&!a?e+r:e}),[e,t,n,i]),{value:r,isChanged:o,onReset:l}=A(a);return{fieldName:a,value:r,isChanged:o,onReset:l}},R=(e=[],t=h.ALL)=>{const{attributes:n}=D();return!e.length||(t===h.ALL?e.every((({name:e,value:t})=>{const i=n[e];return Array.isArray(t)?Array.isArray(i)?g(t,i):t.includes(i):t===i})):t!==h.SOME||e.some((({name:e,value:t})=>{const i=n[e];return Array.isArray(t)?Array.isArray(i)?g(t,i):t.includes(i):t===i})))},k=e=>{const t=(0,s.useRef)(null);return(0,s.useEffect)((()=>{const n=n=>{t.current&&!t.current.contains(n.target)&&e()};return document.addEventListener("click",n),()=>{document.removeEventListener("click",n)}}),[t,e]),t},O=e=>(0,r.createElement)(u.SVG,{width:"16",height:"16",viewBox:"0 0 12 13",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,r.createElement)(u.Path,{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})),V=e=>(0,r.createElement)(u.SVG,{width:"16",height:"16",viewBox:"0 0 14 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,r.createElement)(u.G,{"clip-path":"url(#clip0_1068_38993)"},(0,r.createElement)(u.Path,{d:"M11.1973 8.60005L8.74967 8.11005V5.67171C8.74967 5.517 8.68822 5.36863 8.57882 5.25923C8.46942 5.14984 8.32105 5.08838 8.16634 5.08838H6.99967C6.84496 5.08838 6.69659 5.14984 6.5872 5.25923C6.4778 5.36863 6.41634 5.517 6.41634 5.67171V8.58838H5.24967C5.15021 8.58844 5.05241 8.61391 4.96555 8.66238C4.87869 8.71084 4.80565 8.78068 4.75336 8.86529C4.70106 8.9499 4.67125 9.04646 4.66674 9.14582C4.66223 9.24518 4.68317 9.34405 4.72759 9.43305L6.47759 12.933C6.57676 13.1302 6.77859 13.255 6.99967 13.255H11.083C11.2377 13.255 11.3861 13.1936 11.4955 13.0842C11.6049 12.9748 11.6663 12.8264 11.6663 12.6717V9.17171C11.6665 9.03685 11.6198 8.90612 11.5343 8.80186C11.4487 8.69759 11.3296 8.62626 11.1973 8.60005Z"}),(0,r.createElement)(u.Path,{d:"M10.4997 1.58838H3.49967C2.85626 1.58838 2.33301 2.11221 2.33301 2.75505V6.83838C2.33301 7.4818 2.85626 8.00505 3.49967 8.00505H5.24967V6.83838H3.49967V2.75505H10.4997V6.83838H11.6663V2.75505C11.6663 2.11221 11.1431 1.58838 10.4997 1.58838Z"})),(0,r.createElement)("defs",null,(0,r.createElement)("clipPath",{id:"clip0_1068_38993"},(0,r.createElement)(u.Rect,{width:"14",height:"14",transform:"translate(0 0.421875)"})))),j=[{value:b.NORMAL,label:i.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,r.createElement)(O,{onClick:e})},{value:b.HOVER,label:i.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,r.createElement)(V,{onClick:e})},{value:b.ACTIVE,label:i.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,r.createElement)(V,{onClick:e})},{value:b.FOCUS,label:i.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,r.createElement)(V,{onClick:e})}],$={[b.NORMAL]:{icon:(0,r.createElement)(O,null),label:i.__("Normal State","masterstudy-lms-learning-management-system")},[b.HOVER]:{icon:(0,r.createElement)(V,null),label:i.__("Hovered State","masterstudy-lms-learning-management-system")},[b.ACTIVE]:{icon:(0,r.createElement)(V,null),label:i.__("Active State","masterstudy-lms-learning-management-system")},[b.FOCUS]:{icon:(0,r.createElement)(V,null),label:i.__("Focus State","masterstudy-lms-learning-management-system")}},Z=(e,t)=>{let n=[];return n=e.length?j.filter((t=>e.includes(t.value))):j,n=n.filter((e=>e.value!==t)),{ICONS_MAP:$,options:n}},[G,K,X,Y,q]=F(["hover-state","hover-state__selected","hover-state__selected__opened-menu","hover-state__menu","hover-state__menu__item"]),J=({stateOptions:e,currentState:t,onSelect:n})=>{const{isOpen:i,onOpen:a,onClose:o}=L(),l=k(o),{ICONS_MAP:s,options:p}=Z(e,t);return(0,r.createElement)("div",{className:G,ref:l},(0,r.createElement)("div",{className:d()([K],{[X]:i}),onClick:a,title:s[t]?.label},s[t]?.icon),(0,r.createElement)(m,{condition:i},(0,r.createElement)("div",{className:Y},p.map((({value:e,icon:t,label:i})=>(0,r.createElement)("div",{key:e,className:q,title:i},t((()=>n(e)))))))))},Q=F("color-indicator"),ee=(0,s.memo)((({color:e,onChange:t})=>(0,r.createElement)("div",{className:Q},(0,r.createElement)(p.PanelColorSettings,{enableAlpha:!0,disableCustomColors:!1,__experimentalHasMultipleOrigins:!0,__experimentalIsRenderedInSidebar:!0,colorSettings:[{label:"",value:e,onChange:t}]}))));var te;function ne(){return ne=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)({}).hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},ne.apply(null,arguments)}var ie,ae,re=function(e){return r.createElement("svg",ne({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 12 13"},e),te||(te=r.createElement("path",{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})))};function oe(){return oe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)({}).hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},oe.apply(null,arguments)}var le=function(e){return r.createElement("svg",oe({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 14 15"},e),ie||(ie=r.createElement("g",{clipPath:"url(#state-hover_svg__a)"},r.createElement("path",{d:"M11.197 8.6 8.75 8.11V5.672a.583.583 0 0 0-.584-.584H7a.583.583 0 0 0-.584.584v2.916H5.25a.584.584 0 0 0-.522.845l1.75 3.5c.099.197.3.322.522.322h4.083a.583.583 0 0 0 .583-.583v-3.5a.58.58 0 0 0-.469-.572"}),r.createElement("path",{d:"M10.5 1.588h-7c-.644 0-1.167.524-1.167 1.167v4.083c0 .644.523 1.167 1.167 1.167h1.75V6.838H3.5V2.755h7v4.083h1.166V2.755c0-.643-.523-1.167-1.166-1.167"}))),ae||(ae=r.createElement("defs",null,r.createElement("clipPath",{id:"state-hover_svg__a"},r.createElement("path",{d:"M0 .422h14v14H0z"})))))};const se=[{value:b.NORMAL,label:i.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,r.createElement)(re,{onClick:e})},{value:b.HOVER,label:i.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,r.createElement)(le,{onClick:e})}],pe={[b.NORMAL]:{icon:(0,r.createElement)(re,null),label:i.__("Normal State","masterstudy-lms-learning-management-system")},[b.HOVER]:{icon:(0,r.createElement)(le,null),label:i.__("Hovered State","masterstudy-lms-learning-management-system")}},ce=F("hover-state"),de=F("hover-state__selected"),ue=F("hover-state__selected__opened-menu"),me=F("has-changes"),ge=F("hover-state__menu"),he=F("hover-state__menu__item"),be=(0,s.memo)((e=>{const{hoverName:t,onChangeHoverName:n,fieldName:i}=e,{changedFieldsByName:a}=D(),o=a.get(i),{isOpen:l,onOpen:p,onClose:c}=L(),u=k(c),{ICONS_MAP:g,options:h}=(e=>{const t=(0,s.useMemo)((()=>se.filter((t=>t.value!==e))),[e]);return{ICONS_MAP:pe,options:t}})(t),b=(0,s.useCallback)((e=>{n(e),c()}),[n,c]);return(0,r.createElement)("div",{className:ce,ref:u},(0,r.createElement)("div",{className:d()([de],{[ue]:l,[me]:o}),onClick:p,title:g[t]?.label},g[t]?.icon),(0,r.createElement)(m,{condition:l},(0,r.createElement)("div",{className:ge},h.map((({value:e,icon:t,label:n})=>(0,r.createElement)("div",{key:e,className:he,title:n},t((()=>b(e)))))))))})),ye={Desktop:{icon:"desktop",label:i.__("Desktop","masterstudy-lms-learning-management-system")},Tablet:{icon:"tablet",label:i.__("Tablet","masterstudy-lms-learning-management-system")},Mobile:{icon:"smartphone",label:i.__("Mobile","masterstudy-lms-learning-management-system")}},Se=[{value:y.DESKTOP,icon:"desktop",label:i.__("Desktop","masterstudy-lms-learning-management-system")},{value:y.TABLET,icon:"tablet",label:i.__("Tablet","masterstudy-lms-learning-management-system")},{value:y.MOBILE,icon:"smartphone",label:i.__("Mobile","masterstudy-lms-learning-management-system")}],ve=F("device-picker"),fe=F("device-picker__selected"),xe=F("device-picker__selected__opened-menu"),_e=F("device-picker__menu"),Ce=F("device-picker__menu__item"),Ne=()=>{const{isOpen:e,onOpen:t,onClose:n}=L(),{value:i,onChange:a}=(e=>{const t=B(),n=(0,P.useDispatch)();return{value:(0,s.useMemo)((()=>ye[t]),[t]),onChange:t=>{n("core/edit-site")&&n("core/edit-site").__experimentalSetPreviewDeviceType?n("core/edit-site").__experimentalSetPreviewDeviceType(t):n("core/edit-post")&&n("core/edit-post").__experimentalSetPreviewDeviceType?n("core/edit-post").__experimentalSetPreviewDeviceType(t):n("masterstudy/store").setDeviceType(t),e()}}})(n),o=(e=>(0,s.useMemo)((()=>Se.filter((t=>t.icon!==e))),[e]))(i.icon),l=k(n);return(0,r.createElement)("div",{className:ve,ref:l},(0,r.createElement)(u.Dashicon,{className:d()([fe],{[xe]:e}),icon:i.icon,size:16,onClick:t,title:i.label}),(0,r.createElement)(m,{condition:e},(0,r.createElement)("div",{className:_e},o.map((e=>(0,r.createElement)(u.Dashicon,{key:e.value,icon:e.icon,size:16,onClick:()=>a(e.value),className:Ce,title:e.label}))))))},we=F("reset-button"),Ee=({onReset:e})=>(0,r.createElement)(u.Dashicon,{icon:"undo",onClick:e,className:we,size:16}),Ue=[{label:"PX",value:"px"},{label:"%",value:"%"},{label:"EM",value:"em"},{label:"REM",value:"rem"},{label:"VW",value:"vw"},{label:"VH",value:"vh"}],Te=F("unit"),Me=F("unit__single"),Fe=F("unit__list"),Pe=({name:e,isAdaptive:t})=>{const{isOpen:n,onOpen:i,onClose:a}=L(),{fieldName:o}=I(e,t),{value:l,onChange:s}=A(o),p=k(a);return(0,r.createElement)("div",{className:Te,ref:p},(0,r.createElement)("div",{className:Me,onClick:i},l),(0,r.createElement)(m,{condition:n},(0,r.createElement)("div",{className:Fe},Ue.map((({value:e,label:t})=>(0,r.createElement)("div",{key:e,onClick:()=>(s(e),void a())},t))))))},Be=F("popover-modal"),Le=F("popover-modal__close dashicon dashicons dashicons-no-alt"),He=e=>{const{isOpen:t,onClose:n,popoverContent:i}=e;return(0,r.createElement)(m,{condition:t},(0,r.createElement)(u.Popover,{position:"middle left",onClose:n,className:Be},i,(0,r.createElement)("span",{onClick:n,className:Le})))},We=F("setting-label"),De=F("setting-label__content"),Ae=e=>{const{label:t,isChanged:n=!1,onReset:i,showDevicePicker:a=!0,HoverStateControl:o=null,unitName:l,popoverContent:s=null,dependencies:p}=e,{isOpen:c,onClose:d,onToggle:u}=L();return R(p)?(0,r.createElement)("div",{className:We},(0,r.createElement)("div",{className:De},(0,r.createElement)("div",{onClick:u},t),(0,r.createElement)(m,{condition:Boolean(s)},(0,r.createElement)(He,{isOpen:c,onClose:d,popoverContent:s})),(0,r.createElement)(m,{condition:a},(0,r.createElement)(Ne,null)),(0,r.createElement)(m,{condition:Boolean(o)},o)),(0,r.createElement)(m,{condition:Boolean(l)},(0,r.createElement)(Pe,{name:l,isAdaptive:a})),(0,r.createElement)(m,{condition:n},(0,r.createElement)(Ee,{onReset:i}))):null},Ie=F("suffix"),ze=()=>(0,r.createElement)("div",{className:Ie},(0,r.createElement)(u.Dashicon,{icon:"color-picker",size:16})),Re=F("color-picker"),ke=e=>{const{name:t,label:n,placeholder:i,dependencyMode:a,dependencies:o,isAdaptive:l=!1,hasHover:s=!1}=e,{fieldName:p,hoverName:c,onChangeHoverName:d}=I(t,l,s),{value:g,isChanged:h,onChange:b,onReset:y}=A(p);return R(o,a)?(0,r.createElement)("div",{className:Re},(0,r.createElement)(m,{condition:Boolean(n)},(0,r.createElement)(Ae,{label:n,isChanged:h,onReset:y,showDevicePicker:l,HoverStateControl:(0,r.createElement)(m,{condition:s},(0,r.createElement)(be,{hoverName:c,onChangeHoverName:d,fieldName:p}))})),(0,r.createElement)(u.__experimentalInputControl,{prefix:(0,r.createElement)(ee,{color:g,onChange:b}),suffix:(0,r.createElement)(ze,null),onChange:b,value:g,placeholder:i})):null},Oe=F("number-steppers"),Ve=F("indent-steppers"),je=F("indent-stepper-plus"),$e=F("indent-stepper-minus"),Ze=({onIncrement:e,onDecrement:t,withArrows:n=!1})=>n?(0,r.createElement)("span",{className:Ve},(0,r.createElement)("button",{onClick:e,className:je}),(0,r.createElement)("button",{onClick:t,className:$e})):(0,r.createElement)("span",{className:Oe},(0,r.createElement)("button",{onClick:e},"+"),(0,r.createElement)("button",{onClick:t},"-")),[Ge,Ke]=F(["indents","indents-control"]),Xe=({name:e,label:t,unitName:n,popoverContent:a,dependencyMode:o,dependencies:l,isAdaptive:p=!1})=>{const{fieldName:c}=I(e,p),{value:d,onResetSegmentedBox:g,hasChanges:h,handleInputIncrement:b,handleInputDecrement:y,updateDirectionsValues:S,lastFieldValue:v}=((e,t)=>{const{value:n,isChanged:i,onChange:a,onReset:r}=A(e),{onResetByFieldName:o,changedFieldsByName:l}=D(),p=i||l.get(t),c=e=>{a({...n,...e})},[d,u]=(0,s.useState)(!1);return{value:n,onResetSegmentedBox:()=>{r(),o.get(t)()},hasChanges:p,handleInputIncrement:e=>Number(n[e])+1,handleInputDecrement:e=>Number(n[e])-1,updateDirectionsValues:(e,t,n)=>{e?(u(!1),c({top:n,right:n,bottom:n,left:n})):(u(n),c({[t]:n}))},lastFieldValue:d}})(c,n),[f,x]=(0,s.useState)((()=>{const{left:e,right:t,top:n,bottom:i}=d;return""!==e&&e===t&&t===n&&n===i})),_=e=>{const[t,n]=Object.entries(e)[0];S(f,t,n)},C=e=>()=>{const t=b(e);S(f,e,String(t))},N=e=>()=>{const t=y(e);S(f,e,String(t))};return R(l,o)?(0,r.createElement)("div",{className:Ge},(0,r.createElement)(m,{condition:Boolean(t)},(0,r.createElement)(Ae,{label:null!=t?t:"",isChanged:h,onReset:g,unitName:n,popoverContent:a,showDevicePicker:p})),(0,r.createElement)("div",{className:`${Ke} ${f?"active":""}`},(0,r.createElement)("div",null,(0,r.createElement)(u.__experimentalNumberControl,{value:d.top,onChange:e=>{_({top:e})},spinControls:"none",suffix:(0,r.createElement)(Ze,{onIncrement:C("top"),onDecrement:N("top"),withArrows:!0})}),(0,r.createElement)("div",null,i.__("Top","masterstudy-lms-learning-management-system"))),(0,r.createElement)("div",null,(0,r.createElement)(u.__experimentalNumberControl,{value:d.right,onChange:e=>{_({right:e})},spinControls:"none",suffix:(0,r.createElement)(Ze,{onIncrement:C("right"),onDecrement:N("right"),withArrows:!0})}),(0,r.createElement)("div",null,i.__("Right","masterstudy-lms-learning-management-system"))),(0,r.createElement)("div",null,(0,r.createElement)(u.__experimentalNumberControl,{value:d.bottom,onChange:e=>{_({bottom:e})},spinControls:"none",suffix:(0,r.createElement)(Ze,{onIncrement:C("bottom"),onDecrement:N("bottom"),withArrows:!0})}),(0,r.createElement)("div",null,i.__("Bottom","masterstudy-lms-learning-management-system"))),(0,r.createElement)("div",null,(0,r.createElement)(u.__experimentalNumberControl,{value:d.left,onChange:e=>{_({left:e})},spinControls:"none",suffix:(0,r.createElement)(Ze,{onIncrement:C("left"),onDecrement:N("left"),withArrows:!0})}),(0,r.createElement)("div",null,i.__("Left","masterstudy-lms-learning-management-system"))),(0,r.createElement)(u.Dashicon,{icon:"dashicons "+(f?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{f||!1===v||S(!0,"left",v),x((e=>!e))}}))):null},[Ye,qe,Je,Qe]=F(["toggle-group-wrapper","toggle-group","toggle-group__toggle","toggle-group__active-toggle"]),et=e=>{const{name:t,options:n,label:i,isAdaptive:a=!1,dependencyMode:o,dependencies:l}=e,{fieldName:s}=I(t,a),{value:p,isChanged:c,onChange:u,onReset:g}=A(s);return R(l,o)?(0,r.createElement)("div",{className:Ye},(0,r.createElement)(m,{condition:Boolean(i)},(0,r.createElement)(Ae,{label:i,isChanged:c,onReset:g,showDevicePicker:a})),(0,r.createElement)("div",{className:qe},n.map((e=>(0,r.createElement)("div",{key:e.value,className:d()([Je],{[Qe]:e.value===p}),onClick:()=>u(e.value)},e.label))))):null},[tt,nt,it,at]=F(["border-control","border-control-solid","border-control-dashed","border-control-dotted"]),rt=e=>{const{label:t,borderStyleName:n,borderColorName:a,borderWidthName:o,dependencyMode:l,dependencies:p,isAdaptive:c=!1,hasHover:u=!1}=e,[g,h]=(0,s.useState)("Normal"),{fieldName:b,value:y,isChanged:S,onReset:v}=z(n,c,g),{fieldName:f,isChanged:x,onReset:_}=z(a,c,g),{fieldName:C,isChanged:N,onReset:w}=z(o,c,g);if(!R(p,l))return null;const E=S||x||N;return(0,r.createElement)("div",{className:d()([tt],{"has-reset-button":E})},(0,r.createElement)(Ae,{label:t,isChanged:E,onReset:()=>{v(),_(),w()},showDevicePicker:c,HoverStateControl:(0,r.createElement)(m,{condition:u},(0,r.createElement)(J,{stateOptions:["Normal","Hover"],currentState:g,onSelect:h}))}),(0,r.createElement)(et,{options:[{label:(0,r.createElement)("span",null,i.__("None","masterstudy-lms-learning-management-system")),value:"none"},{label:(0,r.createElement)("span",{className:nt}),value:"solid"},{label:(0,r.createElement)("span",{className:it},(0,r.createElement)("span",null)),value:"dashed"},{label:(0,r.createElement)("span",{className:at},(0,r.createElement)("span",null,(0,r.createElement)("span",null))),value:"dotted"}],name:b}),(0,r.createElement)(m,{condition:"none"!==y},(0,r.createElement)(ke,{name:f,placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(Xe,{name:C})))},ot=F("border-radius"),lt=F("border-radius-control"),st=({name:e,label:t,unitName:n,popoverContent:i,dependencyMode:a,dependencies:o,isAdaptive:l=!1,hasHover:p=!1})=>{const{fieldName:c}=I(e,l,p),{value:m,onResetBorderRadius:g,hasChanges:h,handleInputIncrement:b,handleInputDecrement:y,updateDirectionsValues:S,lastFieldValue:v}=((e,t)=>{const[n,i]=(0,s.useState)(!1),{value:a,isChanged:r,onChange:o,onReset:l}=A(e),{onResetByFieldName:p,changedFieldsByName:c}=D(),d=r||c.get(t),u=e=>{o({...a,...e})};return{value:a,onResetBorderRadius:()=>{l(),p.get(t)()},hasChanges:d,handleInputIncrement:e=>Number(a[e])+1,handleInputDecrement:e=>Number(a[e])-1,updateDirectionsValues:(e,t,n)=>{e?(u({top:n,right:n,bottom:n,left:n}),i(!1)):(u({[t]:n}),i(n))},lastFieldValue:n}})(c,n),[f,x]=(0,s.useState)((()=>{const{left:e,right:t,top:n,bottom:i}=m;return""!==e&&e===t&&t===n&&n===i})),_=e=>{const[t,n]=Object.entries(e)[0];S(f,t,n)},C=e=>()=>{const t=b(e);S(f,e,String(t))},N=e=>()=>{const t=y(e);S(f,e,String(t))};return R(o,a)?(0,r.createElement)("div",{className:ot},(0,r.createElement)(Ae,{label:t,isChanged:h,onReset:g,unitName:n,popoverContent:i,showDevicePicker:l}),(0,r.createElement)("div",{className:d()([lt],{"has-reset-button":h,active:f})},(0,r.createElement)("div",{className:"number-control-top"},(0,r.createElement)(u.__experimentalNumberControl,{value:m.top,onChange:e=>{_({top:e})},spinControls:"none",suffix:(0,r.createElement)(Ze,{onIncrement:C("top"),onDecrement:N("top"),withArrows:!0})})),(0,r.createElement)("div",{className:"number-control-right"},(0,r.createElement)(u.__experimentalNumberControl,{value:m.right,onChange:e=>{_({right:e})},spinControls:"none",suffix:(0,r.createElement)(Ze,{onIncrement:C("right"),onDecrement:N("right"),withArrows:!0})})),(0,r.createElement)("div",{className:"number-control-left"},(0,r.createElement)(u.__experimentalNumberControl,{value:m.left,onChange:e=>{_({left:e})},spinControls:"none",suffix:(0,r.createElement)(Ze,{onIncrement:C("left"),onDecrement:N("left"),withArrows:!0})})),(0,r.createElement)("div",{className:"number-control-bottom"},(0,r.createElement)(u.__experimentalNumberControl,{value:m.bottom,onChange:e=>{_({bottom:e})},spinControls:"none",suffix:(0,r.createElement)(Ze,{onIncrement:C("bottom"),onDecrement:N("bottom"),withArrows:!0})})),(0,r.createElement)(u.Dashicon,{icon:"dashicons "+(f?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{f||!1===v||S(!0,"left",v),x((e=>!e))}}))):null},pt=F("box-shadow-preset"),ct=({preset:e})=>(0,r.createElement)("div",{className:pt},(0,r.createElement)("div",{style:{boxShadow:`${e.horizontal}px ${e.vertical}px ${e.blur}px ${e.spread}px rgba(0, 0, 0, 0.25) ${e.inset?"inset":""}`}})),dt=F("presets"),ut=F("presets__item-wrapper"),mt=F("presets__item-wrapper__preset"),gt=F("presets__item-wrapper__name"),ht=(0,s.memo)((e=>{const{presets:t,activePreset:n,onSelectPreset:i,PresetItem:a,detectIsActive:o,detectByIndex:l=!1}=e;return(0,r.createElement)("div",{className:dt},t.map((({name:e,...t},s)=>(0,r.createElement)("div",{key:s,className:d()([ut],{active:o(n,l?s:t)}),onClick:()=>i(t)},(0,r.createElement)("div",{className:mt},(0,r.createElement)(a,{preset:t})),(0,r.createElement)("span",{className:gt},e)))))})),bt=F("range-control"),yt=e=>{const{name:t,label:n,min:i,max:a,unitName:o,dependencyMode:l,dependencies:s,isAdaptive:p=!1}=e,{fieldName:c}=I(t,p),{value:d,onChange:g,onResetNumberField:h,hasChanges:b}=((e,t)=>{const{value:n,isChanged:i,onChange:a,onReset:r}=A(e),{onResetByFieldName:o,changedFieldsByName:l}=D();return{value:n,onChange:a,onResetNumberField:()=>{r(),o.get(t)()},hasChanges:i||l.get(t)}})(c,o);return R(s,l)?(0,r.createElement)("div",{className:bt},(0,r.createElement)(m,{condition:Boolean(n)},(0,r.createElement)(Ae,{label:n,isChanged:b,onReset:h,unitName:o,showDevicePicker:p})),(0,r.createElement)(u.RangeControl,{value:d,onChange:g,min:i,max:a})):null},St=F("switch"),vt=e=>{const{name:t,label:n,dependencyMode:i,dependencies:a,isAdaptive:o=!1}=e,{fieldName:l}=I(t,o),{value:s,onChange:p}=A(l);return R(a,i)?(0,r.createElement)("div",{className:St,"data-has-label":Boolean(n).toString()},(0,r.createElement)(u.ToggleControl,{label:n,checked:s,onChange:p}),(0,r.createElement)(m,{condition:o},(0,r.createElement)(Ne,null))):null},ft=F("box-shadow-settings"),xt=F("box-shadow-presets-title"),_t=[{name:"Drop",horizontal:0,vertical:2,blur:2,spread:0,inset:!1},{name:"Glow",horizontal:0,vertical:4,blur:20,spread:0,inset:!1},{name:"Outline",horizontal:0,vertical:2,blur:10,spread:0,inset:!1},{name:"Sparse",horizontal:0,vertical:10,blur:50,spread:0,inset:!1}],Ct=e=>{const{label:t,min:n,max:a,shadowColorName:o,shadowHorizontalName:l,shadowVerticalName:p,shadowBlurName:c,shadowSpreadName:d,shadowInsetName:u,popoverContent:h,dependencyMode:b,dependencies:y,isAdaptive:S=!1,hasHover:v=!1,presets:f=_t}=e,[x,_]=(0,s.useState)("Normal"),{fieldName:C}=z(o,S,x),{fieldName:N}=z(l,S,x),{fieldName:w}=z(p,S,x),{fieldName:E}=z(c,S,x),{fieldName:U}=z(d,S,x),{fieldName:T}=z(u,S,x),{isChanged:M,onReset:F,onSelectPreset:P,activePreset:B}=((e,t,n,i,a,r)=>{const{setAttributes:o}=D(),{value:l,isChanged:p,onReset:c}=A(e),{value:d,isChanged:u,onReset:m}=A(t),{value:g,isChanged:h,onReset:b}=A(n),{value:y,isChanged:S,onReset:v}=A(i),{value:f,isChanged:x,onReset:_}=A(a),{value:C,isChanged:N,onReset:w}=A(r),E=p||u||h||S||x||N,U=(0,s.useCallback)((e=>{const{horizontal:l,vertical:s,blur:p,spread:c,inset:d}=e;o({[t]:l,[n]:s,[i]:p,[a]:c,[r]:d})}),[t,n,i,a,o,r]);return{activePreset:(0,s.useMemo)((()=>({horizontal:d,vertical:g,blur:y,spread:f,inset:null!=C&&C})),[d,g,y,f,C]),onReset:()=>{[c,m,b,v,_,w].forEach((e=>e()))},isChanged:E,onSelectPreset:U}})(C,N,w,E,U,T);return R(y,b)?(0,r.createElement)("div",{className:ft},(0,r.createElement)(Ae,{label:t,isChanged:M,onReset:F,popoverContent:h,showDevicePicker:S,HoverStateControl:(0,r.createElement)(m,{condition:v},(0,r.createElement)(J,{stateOptions:["Normal","Hover"],currentState:x,onSelect:_}))}),(0,r.createElement)(ht,{presets:f,onSelectPreset:P,activePreset:B,PresetItem:ct,detectIsActive:g,detectByIndex:!1}),(0,r.createElement)(ke,{name:C,label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:"Select color"}),(0,r.createElement)(yt,{name:N,label:i.__("Horizontal Offset","masterstudy-lms-learning-management-system"),min:n,max:a}),(0,r.createElement)(yt,{name:w,label:i.__("Vertical Offset","masterstudy-lms-learning-management-system"),min:n,max:a}),(0,r.createElement)(yt,{name:E,label:i.__("Blur","masterstudy-lms-learning-management-system"),min:n,max:a}),(0,r.createElement)(yt,{name:U,label:i.__("Spread","masterstudy-lms-learning-management-system"),min:n,max:a}),(0,r.createElement)("div",{className:xt},i.__("Position","masterstudy-lms-learning-management-system")),(0,r.createElement)(vt,{name:T,label:i.__("Inset","masterstudy-lms-learning-management-system")})):null},Nt=(F("input-field"),F("input-field-control"),F("number-field")),wt=F("number-field-control"),Et=e=>{const{name:t,label:n,unitName:i,help:a,popoverContent:o,dependencyMode:l,dependencies:s,isAdaptive:p=!1}=e,{fieldName:c}=I(t,p),{value:d,onResetNumberField:m,hasChanges:g,handleIncrement:h,handleDecrement:b,handleInputChange:y}=((e,t)=>{const{value:n,isChanged:i,onChange:a,onReset:r}=A(e),{onResetByFieldName:o,changedFieldsByName:l}=D(),s=i||l.get(t);return{value:n,onResetNumberField:()=>{r(),o.get(t)()},hasChanges:s,handleIncrement:()=>{a(n+1)},handleDecrement:()=>{a(n-1)},handleInputChange:e=>{const t=Number(""===e?0:e);a(t)}}})(c,i);return R(s,l)?(0,r.createElement)("div",{className:Nt},(0,r.createElement)(Ae,{label:n,isChanged:g,onReset:m,unitName:i,showDevicePicker:p,popoverContent:o}),(0,r.createElement)("div",{className:wt},(0,r.createElement)(u.__experimentalNumberControl,{value:d,onChange:y,spinControls:"none",suffix:(0,r.createElement)(Ze,{onIncrement:h,onDecrement:b})})),a&&(0,r.createElement)("small",null,a)):null},Ut=({className:e})=>(0,r.createElement)("div",{className:e},i.__("No options","masterstudy-lms-learning-management-system")),Tt=F("select__single-item"),Mt=F("select__container"),Ft=F("select__container__multi-item"),Pt=({multiple:e,value:t,options:n,onChange:i})=>{const{singleValue:a,multipleValue:o}=((e,t,n)=>({singleValue:(0,s.useMemo)((()=>t?null:n.find((t=>t.value===e))?.label),[t,e,n]),multipleValue:(0,s.useMemo)((()=>t?e:null),[t,e])}))(t,e,n);return(0,r.createElement)(m,{condition:e,fallback:(0,r.createElement)("div",{className:Tt},a)},(0,r.createElement)("div",{className:Mt},o?.map((e=>{const t=n.find((t=>t.value===e));return t?(0,r.createElement)("div",{key:t.value,className:Ft},(0,r.createElement)("div",null,t.label),(0,r.createElement)(u.Dashicon,{icon:"no-alt",onClick:()=>i(t.value),size:16})):null}))))},Bt=F("select"),Lt=F("select__select-box"),Ht=F("select__placeholder"),Wt=F("select__select-box-multiple"),Dt=F("select__menu"),At=F("select__menu__options-container"),It=F("select__menu__item"),zt=e=>{const{options:t,multiple:n=!1,placeholder:i="Select",value:a,onSelect:o}=e,{isOpen:l,onToggle:p,onClose:c}=L(),g=k(c),h=((e,t,n)=>(0,s.useMemo)((()=>n&&Array.isArray(e)?t.filter((t=>!e.includes(t.value))):t.filter((t=>t.value!==e))),[e,t,n]))(a,t,n),b=((e,t,n,i)=>(0,s.useCallback)((a=>{if(t&&Array.isArray(e)){const t=e.includes(a)?e.filter((e=>e!==a)):[...e,a];n(t)}else n(a),i()}),[t,e,n,i]))(a,n,o,c),y=((e,t)=>(0,s.useMemo)((()=>t&&Array.isArray(e)?Boolean(e.length):Boolean(e)),[t,e]))(a,n),S=n&&Array.isArray(a)&&a?.length>0;return(0,r.createElement)("div",{className:Bt,ref:g},(0,r.createElement)("div",{className:d()([Lt],{[Wt]:S}),onClick:p},(0,r.createElement)(m,{condition:y,fallback:(0,r.createElement)("div",{className:Ht},i)},(0,r.createElement)(Pt,{onChange:b,options:t,multiple:n,value:a})),(0,r.createElement)(u.Dashicon,{icon:l?"arrow-up-alt2":"arrow-down-alt2",size:16,style:{color:"#4D5E6F"}})),(0,r.createElement)(m,{condition:l},(0,r.createElement)("div",{className:Dt},(0,r.createElement)(m,{condition:Boolean(h.length),fallback:(0,r.createElement)(Ut,{className:It})},(0,r.createElement)("div",{className:At},h.map((e=>(0,r.createElement)("div",{key:e.value,onClick:()=>b(e.value),className:It},e.label))))))))},Rt=F("setting-select"),kt=e=>{const{name:t,options:n,label:i,multiple:a=!1,placeholder:o,isAdaptive:l=!1,dependencyMode:s,dependencies:p}=e,{fieldName:c}=I(t,l),{value:d,isChanged:u,onChange:g,onReset:h}=A(c);return R(p,s)?(0,r.createElement)("div",{className:Rt},(0,r.createElement)(m,{condition:Boolean(i)},(0,r.createElement)(Ae,{label:i,isChanged:u,onReset:h,showDevicePicker:l})),(0,r.createElement)(zt,{options:n,value:d,onSelect:g,multiple:a,placeholder:o})):null},Ot=(F("row-select"),F("row-select__label"),F("row-select__control"),F("typography-select")),Vt=F("typography-select-label"),jt=e=>{const{name:t,label:n,options:i,isAdaptive:a=!1}=e,{fieldName:o}=I(t,a),{isChanged:l,onReset:s}=A(o);return(0,r.createElement)("div",{className:Ot},(0,r.createElement)("div",{className:Vt},(0,r.createElement)("div",null,n),(0,r.createElement)(m,{condition:a},(0,r.createElement)(Ne,null))),(0,r.createElement)(kt,{name:t,options:i,isAdaptive:a}),(0,r.createElement)(m,{condition:l},(0,r.createElement)(Ee,{onReset:s})))},$t=F("typography"),Zt=e=>{const{fontSizeName:t,fontWeightName:n,textTransformName:a,fontStyleName:o,textDecorationName:l,lineHeightName:s,letterSpacingName:p,wordSpacingName:c,fontSizeUnitName:d,lineHeightUnitName:u,letterSpacingUnitName:m,wordSpacingUnitName:g,dependencyMode:h,dependencies:b,isAdaptive:y=!1}=e,{fontWeightOptions:S,textTransformOptions:v,fontStyleOptions:f,textDecorationOptions:x}={fontWeightOptions:[{label:i.__("100 (Thin)","masterstudy-lms-learning-management-system"),value:"100"},{label:i.__("200 (Extra Light)","masterstudy-lms-learning-management-system"),value:"200"},{label:i.__("300 (Light)","masterstudy-lms-learning-management-system"),value:"300"},{label:i.__("400 (Normal)","masterstudy-lms-learning-management-system"),value:"400"},{label:i.__("500 (Medium)","masterstudy-lms-learning-management-system"),value:"500"},{label:i.__("600 (Semi Bold)","masterstudy-lms-learning-management-system"),value:"600"},{label:i.__("700 (Bold)","masterstudy-lms-learning-management-system"),value:"700"},{label:i.__("800 (Extra Bold)","masterstudy-lms-learning-management-system"),value:"800"},{label:i.__("900 (Extra)","masterstudy-lms-learning-management-system"),value:"900"}],textTransformOptions:[{label:i.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:i.__("Uppercase","masterstudy-lms-learning-management-system"),value:"uppercase"},{label:i.__("Lowercase","masterstudy-lms-learning-management-system"),value:"lowercase"},{label:i.__("Capitalize","masterstudy-lms-learning-management-system"),value:"capitalize"},{label:i.__("Normal","masterstudy-lms-learning-management-system"),value:"none"}],fontStyleOptions:[{label:i.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:i.__("Normal","masterstudy-lms-learning-management-system"),value:"none"},{label:i.__("Italic","masterstudy-lms-learning-management-system"),value:"italic"},{label:i.__("Oblique","masterstudy-lms-learning-management-system"),value:"oblique"}],textDecorationOptions:[{label:i.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:i.__("Underline","masterstudy-lms-learning-management-system"),value:"underline"},{label:i.__("Line Through","masterstudy-lms-learning-management-system"),value:"line-through"},{label:i.__("None","masterstudy-lms-learning-management-system"),value:"none"}]};return R(b,h)?(0,r.createElement)("div",{className:$t},(0,r.createElement)(yt,{name:t,label:i.__("Size","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:d,isAdaptive:y}),(0,r.createElement)(jt,{name:n,label:i.__("Weight","masterstudy-lms-learning-management-system"),options:S}),(0,r.createElement)(jt,{name:a,label:i.__("Transform","masterstudy-lms-learning-management-system"),options:v}),(0,r.createElement)(jt,{name:o,label:i.__("Style","masterstudy-lms-learning-management-system"),options:f}),(0,r.createElement)(jt,{name:l,label:i.__("Decoration","masterstudy-lms-learning-management-system"),options:x}),(0,r.createElement)(yt,{name:s,label:i.__("Line Height","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:u,isAdaptive:y}),(0,r.createElement)(yt,{name:p,label:i.__("Letter Spacing","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:m,isAdaptive:y}),c&&(0,r.createElement)(yt,{name:c,label:i.__("Word Spacing","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:g,isAdaptive:y})):null},Gt=(F("file-upload"),F("file-upload__wrap"),F("file-upload__image"),F("file-upload__remove"),F("file-upload__replace"),(0,s.createContext)({activeTab:0,setActiveTab:()=>{}})),Kt=()=>{const e=(0,s.useContext)(Gt);if(!e)throw new Error("useTabs should be used inside Tabs");return e},Xt=({children:e})=>{const[t,n]=(0,s.useState)(0);return(0,r.createElement)(Gt.Provider,{value:{activeTab:t,setActiveTab:n}},(0,r.createElement)("div",{className:`active-tab-${t}`},e))},Yt=F("tab-list"),qt=({children:e})=>(0,r.createElement)("div",{className:Yt},s.Children.map(e,((e,t)=>(0,s.cloneElement)(e,{index:t})))),Jt=F("tab"),Qt=F("tab-active"),en=F("content"),tn=({index:e,title:t,icon:n})=>{const{activeTab:i,setActiveTab:a}=Kt();return(0,r.createElement)("div",{className:d()([Jt],{[Qt]:i===e}),onClick:()=>a(e)},(0,r.createElement)("div",{className:en},(0,r.createElement)("div",null,n),(0,r.createElement)("div",null,t)))},nn=({children:e})=>(0,r.createElement)("div",null,s.Children.map(e,((e,t)=>(0,s.cloneElement)(e,{index:t})))),an=F("tab-panel"),rn=({index:e,children:t})=>{const{activeTab:n}=Kt();return n===e?(0,r.createElement)("div",{className:an},t):null},on=({generalTab:e,styleTab:t,advancedTab:n})=>(0,r.createElement)(Xt,null,(0,r.createElement)(qt,null,(0,r.createElement)(tn,{title:i.__("General","masterstudy-lms-learning-management-system"),icon:(0,r.createElement)(u.Dashicon,{icon:"layout"})}),(0,r.createElement)(tn,{title:i.__("Style","masterstudy-lms-learning-management-system"),icon:(0,r.createElement)(u.Dashicon,{icon:"admin-appearance"})}),(0,r.createElement)(tn,{title:i.__("Advanced","masterstudy-lms-learning-management-system"),icon:(0,r.createElement)(u.Dashicon,{icon:"admin-settings"})})),(0,r.createElement)(nn,null,(0,r.createElement)(rn,null,e),(0,r.createElement)(rn,null,t),(0,r.createElement)(rn,null,n))),ln=window.ReactDOM,sn="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function pn(e){const t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function cn(e){return"nodeType"in e}function dn(e){var t,n;return e?pn(e)?e:cn(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function un(e){const{Document:t}=dn(e);return e instanceof t}function mn(e){return!pn(e)&&e instanceof dn(e).HTMLElement}function gn(e){return e instanceof dn(e).SVGElement}function hn(e){return e?pn(e)?e.document:cn(e)?un(e)?e:mn(e)||gn(e)?e.ownerDocument:document:document:document}const bn=sn?r.useLayoutEffect:r.useEffect;function yn(e){const t=(0,r.useRef)(e);return bn((()=>{t.current=e})),(0,r.useCallback)((function(){for(var e=arguments.length,n=new Array(e),i=0;i<e;i++)n[i]=arguments[i];return null==t.current?void 0:t.current(...n)}),[])}function Sn(e,t){void 0===t&&(t=[e]);const n=(0,r.useRef)(e);return bn((()=>{n.current!==e&&(n.current=e)}),t),n}function vn(e,t){const n=(0,r.useRef)();return(0,r.useMemo)((()=>{const t=e(n.current);return n.current=t,t}),[...t])}function fn(e){const t=yn(e),n=(0,r.useRef)(null),i=(0,r.useCallback)((e=>{e!==n.current&&(null==t||t(e,n.current)),n.current=e}),[]);return[n,i]}function xn(e){const t=(0,r.useRef)();return(0,r.useEffect)((()=>{t.current=e}),[e]),t.current}let Cn={};function Nn(e,t){return(0,r.useMemo)((()=>{if(t)return t;const n=null==Cn[e]?0:Cn[e]+1;return Cn[e]=n,e+"-"+n}),[e,t])}function wn(e){return function(t){for(var n=arguments.length,i=new Array(n>1?n-1:0),a=1;a<n;a++)i[a-1]=arguments[a];return i.reduce(((t,n)=>{const i=Object.entries(n);for(const[n,a]of i){const i=t[n];null!=i&&(t[n]=i+e*a)}return t}),{...t})}}const En=wn(1),Un=wn(-1);function Tn(e){if(!e)return!1;const{KeyboardEvent:t}=dn(e.target);return t&&e instanceof t}function Mn(e){if(function(e){if(!e)return!1;const{TouchEvent:t}=dn(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return function(e){return"clientX"in e&&"clientY"in e}(e)?{x:e.clientX,y:e.clientY}:null}const Fn=Object.freeze({Translate:{toString(e){if(!e)return;const{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;const{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[Fn.Translate.toString(e),Fn.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:i}=e;return t+" "+n+"ms "+i}}}),Pn="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function Bn(e){return e.matches(Pn)?e:e.querySelector(Pn)}const Ln={display:"none"};function Hn(e){let{id:t,value:n}=e;return o().createElement("div",{id:t,style:Ln},n)}function Wn(e){let{id:t,announcement:n,ariaLiveType:i="assertive"}=e;return o().createElement("div",{id:t,style:{position:"fixed",width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":i,"aria-atomic":!0},n)}const Dn=(0,r.createContext)(null),An={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},In={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function zn(e){let{announcements:t=In,container:n,hiddenTextDescribedById:i,screenReaderInstructions:a=An}=e;const{announce:l,announcement:s}=function(){const[e,t]=(0,r.useState)("");return{announce:(0,r.useCallback)((e=>{null!=e&&t(e)}),[]),announcement:e}}(),p=Nn("DndLiveRegion"),[c,d]=(0,r.useState)(!1);if((0,r.useEffect)((()=>{d(!0)}),[]),function(e){const t=(0,r.useContext)(Dn);(0,r.useEffect)((()=>{if(!t)throw new Error("useDndMonitor must be used within a children of <DndContext>");return t(e)}),[e,t])}((0,r.useMemo)((()=>({onDragStart(e){let{active:n}=e;l(t.onDragStart({active:n}))},onDragMove(e){let{active:n,over:i}=e;t.onDragMove&&l(t.onDragMove({active:n,over:i}))},onDragOver(e){let{active:n,over:i}=e;l(t.onDragOver({active:n,over:i}))},onDragEnd(e){let{active:n,over:i}=e;l(t.onDragEnd({active:n,over:i}))},onDragCancel(e){let{active:n,over:i}=e;l(t.onDragCancel({active:n,over:i}))}})),[l,t])),!c)return null;const u=o().createElement(o().Fragment,null,o().createElement(Hn,{id:i,value:a.draggable}),o().createElement(Wn,{id:p,announcement:s}));return n?(0,ln.createPortal)(u,n):u}var Rn;function kn(){}function On(e,t){return(0,r.useMemo)((()=>({sensor:e,options:null!=t?t:{}})),[e,t])}!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(Rn||(Rn={}));const Vn=Object.freeze({x:0,y:0});function jn(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function $n(e,t){let{data:{value:n}}=e,{data:{value:i}}=t;return n-i}function Zn(e,t){let{data:{value:n}}=e,{data:{value:i}}=t;return i-n}function Gn(e){let{left:t,top:n,height:i,width:a}=e;return[{x:t,y:n},{x:t+a,y:n},{x:t,y:n+i},{x:t+a,y:n+i}]}function Kn(e,t){if(!e||0===e.length)return null;const[n]=e;return t?n[t]:n}function Xn(e,t,n){return void 0===t&&(t=e.left),void 0===n&&(n=e.top),{x:t+.5*e.width,y:n+.5*e.height}}const Yn=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:i}=e;const a=Xn(t,t.left,t.top),r=[];for(const e of i){const{id:t}=e,i=n.get(t);if(i){const n=jn(Xn(i),a);r.push({id:t,data:{droppableContainer:e,value:n}})}}return r.sort($n)};function qn(e,t){const n=Math.max(t.top,e.top),i=Math.max(t.left,e.left),a=Math.min(t.left+t.width,e.left+e.width),r=Math.min(t.top+t.height,e.top+e.height),o=a-i,l=r-n;if(i<a&&n<r){const n=t.width*t.height,i=e.width*e.height,a=o*l;return Number((a/(n+i-a)).toFixed(4))}return 0}const Jn=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:i}=e;const a=[];for(const e of i){const{id:i}=e,r=n.get(i);if(r){const n=qn(r,t);n>0&&a.push({id:i,data:{droppableContainer:e,value:n}})}}return a.sort(Zn)};function Qn(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:Vn}function ei(e){return function(t){for(var n=arguments.length,i=new Array(n>1?n-1:0),a=1;a<n;a++)i[a-1]=arguments[a];return i.reduce(((t,n)=>({...t,top:t.top+e*n.y,bottom:t.bottom+e*n.y,left:t.left+e*n.x,right:t.right+e*n.x})),{...t})}}const ti=ei(1);const ni={ignoreTransform:!1};function ii(e,t){void 0===t&&(t=ni);let n=e.getBoundingClientRect();if(t.ignoreTransform){const{transform:t,transformOrigin:i}=dn(e).getComputedStyle(e);t&&(n=function(e,t,n){const i=function(e){if(e.startsWith("matrix3d(")){const t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){const t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}(t);if(!i)return e;const{scaleX:a,scaleY:r,x:o,y:l}=i,s=e.left-o-(1-a)*parseFloat(n),p=e.top-l-(1-r)*parseFloat(n.slice(n.indexOf(" ")+1)),c=a?e.width/a:e.width,d=r?e.height/r:e.height;return{width:c,height:d,top:p,right:s+c,bottom:p+d,left:s}}(n,t,i))}const{top:i,left:a,width:r,height:o,bottom:l,right:s}=n;return{top:i,left:a,width:r,height:o,bottom:l,right:s}}function ai(e){return ii(e,{ignoreTransform:!0})}function ri(e,t){const n=[];return e?function i(a){if(null!=t&&n.length>=t)return n;if(!a)return n;if(un(a)&&null!=a.scrollingElement&&!n.includes(a.scrollingElement))return n.push(a.scrollingElement),n;if(!mn(a)||gn(a))return n;if(n.includes(a))return n;const r=dn(e).getComputedStyle(a);return a!==e&&function(e,t){void 0===t&&(t=dn(e).getComputedStyle(e));const n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some((e=>{const i=t[e];return"string"==typeof i&&n.test(i)}))}(a,r)&&n.push(a),function(e,t){return void 0===t&&(t=dn(e).getComputedStyle(e)),"fixed"===t.position}(a,r)?n:i(a.parentNode)}(e):n}function oi(e){const[t]=ri(e,1);return null!=t?t:null}function li(e){return sn&&e?pn(e)?e:cn(e)?un(e)||e===hn(e).scrollingElement?window:mn(e)?e:null:null:null}function si(e){return pn(e)?e.scrollX:e.scrollLeft}function pi(e){return pn(e)?e.scrollY:e.scrollTop}function ci(e){return{x:si(e),y:pi(e)}}var di;function ui(e){return!(!sn||!e)&&e===document.scrollingElement}function mi(e){const t={x:0,y:0},n=ui(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},i={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height};return{isTop:e.scrollTop<=t.y,isLeft:e.scrollLeft<=t.x,isBottom:e.scrollTop>=i.y,isRight:e.scrollLeft>=i.x,maxScroll:i,minScroll:t}}!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(di||(di={}));const gi={x:.2,y:.2};function hi(e,t,n,i,a){let{top:r,left:o,right:l,bottom:s}=n;void 0===i&&(i=10),void 0===a&&(a=gi);const{isTop:p,isBottom:c,isLeft:d,isRight:u}=mi(e),m={x:0,y:0},g={x:0,y:0},h=t.height*a.y,b=t.width*a.x;return!p&&r<=t.top+h?(m.y=di.Backward,g.y=i*Math.abs((t.top+h-r)/h)):!c&&s>=t.bottom-h&&(m.y=di.Forward,g.y=i*Math.abs((t.bottom-h-s)/h)),!u&&l>=t.right-b?(m.x=di.Forward,g.x=i*Math.abs((t.right-b-l)/b)):!d&&o<=t.left+b&&(m.x=di.Backward,g.x=i*Math.abs((t.left+b-o)/b)),{direction:m,speed:g}}function bi(e){if(e===document.scrollingElement){const{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}const{top:t,left:n,right:i,bottom:a}=e.getBoundingClientRect();return{top:t,left:n,right:i,bottom:a,width:e.clientWidth,height:e.clientHeight}}function yi(e){return e.reduce(((e,t)=>En(e,ci(t))),Vn)}const Si=[["x",["left","right"],function(e){return e.reduce(((e,t)=>e+si(t)),0)}],["y",["top","bottom"],function(e){return e.reduce(((e,t)=>e+pi(t)),0)}]];class vi{constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;const n=ri(t),i=yi(n);this.rect={...e},this.width=e.width,this.height=e.height;for(const[e,t,a]of Si)for(const r of t)Object.defineProperty(this,r,{get:()=>{const t=a(n),o=i[e]-t;return this.rect[r]+o},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class fi{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach((e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)}))},this.target=e}add(e,t,n){var i;null==(i=this.target)||i.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function xi(e,t){const n=Math.abs(e.x),i=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+i**2)>t:"x"in t&&"y"in t?n>t.x&&i>t.y:"x"in t?n>t.x:"y"in t&&i>t.y}var _i,Ci;function Ni(e){e.preventDefault()}function wi(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(_i||(_i={})),function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter"}(Ci||(Ci={}));const Ei={start:[Ci.Space,Ci.Enter],cancel:[Ci.Esc],end:[Ci.Space,Ci.Enter]},Ui=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case Ci.Right:return{...n,x:n.x+25};case Ci.Left:return{...n,x:n.x-25};case Ci.Down:return{...n,y:n.y+25};case Ci.Up:return{...n,y:n.y-25}}};class Ti{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;const{event:{target:t}}=e;this.props=e,this.listeners=new fi(hn(t)),this.windowListeners=new fi(dn(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(_i.Resize,this.handleCancel),this.windowListeners.add(_i.VisibilityChange,this.handleCancel),setTimeout((()=>this.listeners.add(_i.Keydown,this.handleKeyDown)))}handleStart(){const{activeNode:e,onStart:t}=this.props,n=e.node.current;n&&function(e,t){if(void 0===t&&(t=ii),!e)return;const{top:n,left:i,bottom:a,right:r}=t(e);oi(e)&&(a<=0||r<=0||n>=window.innerHeight||i>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}(n),t(Vn)}handleKeyDown(e){if(Tn(e)){const{active:t,context:n,options:i}=this.props,{keyboardCodes:a=Ei,coordinateGetter:r=Ui,scrollBehavior:o="smooth"}=i,{code:l}=e;if(a.end.includes(l))return void this.handleEnd(e);if(a.cancel.includes(l))return void this.handleCancel(e);const{collisionRect:s}=n.current,p=s?{x:s.left,y:s.top}:Vn;this.referenceCoordinates||(this.referenceCoordinates=p);const c=r(e,{active:t,context:n.current,currentCoordinates:p});if(c){const t=Un(c,p),i={x:0,y:0},{scrollableAncestors:a}=n.current;for(const n of a){const a=e.code,{isTop:r,isRight:l,isLeft:s,isBottom:p,maxScroll:d,minScroll:u}=mi(n),m=bi(n),g={x:Math.min(a===Ci.Right?m.right-m.width/2:m.right,Math.max(a===Ci.Right?m.left:m.left+m.width/2,c.x)),y:Math.min(a===Ci.Down?m.bottom-m.height/2:m.bottom,Math.max(a===Ci.Down?m.top:m.top+m.height/2,c.y))},h=a===Ci.Right&&!l||a===Ci.Left&&!s,b=a===Ci.Down&&!p||a===Ci.Up&&!r;if(h&&g.x!==c.x){const e=n.scrollLeft+t.x,r=a===Ci.Right&&e<=d.x||a===Ci.Left&&e>=u.x;if(r&&!t.y)return void n.scrollTo({left:e,behavior:o});i.x=r?n.scrollLeft-e:a===Ci.Right?n.scrollLeft-d.x:n.scrollLeft-u.x,i.x&&n.scrollBy({left:-i.x,behavior:o});break}if(b&&g.y!==c.y){const e=n.scrollTop+t.y,r=a===Ci.Down&&e<=d.y||a===Ci.Up&&e>=u.y;if(r&&!t.x)return void n.scrollTo({top:e,behavior:o});i.y=r?n.scrollTop-e:a===Ci.Down?n.scrollTop-d.y:n.scrollTop-u.y,i.y&&n.scrollBy({top:-i.y,behavior:o});break}}this.handleMove(e,En(Un(c,this.referenceCoordinates),i))}}}handleMove(e,t){const{onMove:n}=this.props;e.preventDefault(),n(t)}handleEnd(e){const{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){const{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function Mi(e){return Boolean(e&&"distance"in e)}function Fi(e){return Boolean(e&&"delay"in e)}Ti.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:i=Ei,onActivation:a}=t,{active:r}=n;const{code:o}=e.nativeEvent;if(i.start.includes(o)){const t=r.activatorNode.current;return!(t&&e.target!==t||(e.preventDefault(),null==a||a({event:e.nativeEvent}),0))}return!1}}];class Pi{constructor(e,t,n){var i;void 0===n&&(n=function(e){const{EventTarget:t}=dn(e);return e instanceof t?e:hn(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;const{event:a}=e,{target:r}=a;this.props=e,this.events=t,this.document=hn(r),this.documentListeners=new fi(this.document),this.listeners=new fi(n),this.windowListeners=new fi(dn(r)),this.initialCoordinates=null!=(i=Mn(a))?i:Vn,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),this.windowListeners.add(_i.Resize,this.handleCancel),this.windowListeners.add(_i.DragStart,Ni),this.windowListeners.add(_i.VisibilityChange,this.handleCancel),this.windowListeners.add(_i.ContextMenu,Ni),this.documentListeners.add(_i.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(Fi(t))return void(this.timeoutId=setTimeout(this.handleStart,t.delay));if(Mi(t))return}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handleStart(){const{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(_i.Click,wi,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(_i.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;const{activated:n,initialCoordinates:i,props:a}=this,{onMove:r,options:{activationConstraint:o}}=a;if(!i)return;const l=null!=(t=Mn(e))?t:Vn,s=Un(i,l);if(!n&&o){if(Mi(o)){if(null!=o.tolerance&&xi(s,o.tolerance))return this.handleCancel();if(xi(s,o.distance))return this.handleStart()}return Fi(o)&&xi(s,o.tolerance)?this.handleCancel():void 0}e.cancelable&&e.preventDefault(),r(l)}handleEnd(){const{onEnd:e}=this.props;this.detach(),e()}handleCancel(){const{onCancel:e}=this.props;this.detach(),e()}handleKeydown(e){e.code===Ci.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}const Bi={move:{name:"pointermove"},end:{name:"pointerup"}};class Li extends Pi{constructor(e){const{event:t}=e,n=hn(t.target);super(e,Bi,n)}}Li.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:i}=t;return!(!n.isPrimary||0!==n.button||(null==i||i({event:n}),0))}}];const Hi={move:{name:"mousemove"},end:{name:"mouseup"}};var Wi;!function(e){e[e.RightClick=2]="RightClick"}(Wi||(Wi={})),class extends Pi{constructor(e){super(e,Hi,hn(e.event.target))}}.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:i}=t;return n.button!==Wi.RightClick&&(null==i||i({event:n}),!0)}}];const Di={move:{name:"touchmove"},end:{name:"touchend"}};var Ai,Ii;(class extends Pi{constructor(e){super(e,Di)}static setup(){return window.addEventListener(Di.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(Di.move.name,e)};function e(){}}}).activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:i}=t;const{touches:a}=n;return!(a.length>1||(null==i||i({event:n}),0))}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(Ai||(Ai={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(Ii||(Ii={}));const zi={x:{[di.Backward]:!1,[di.Forward]:!1},y:{[di.Backward]:!1,[di.Forward]:!1}};var Ri,ki;!function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(Ri||(Ri={})),function(e){e.Optimized="optimized"}(ki||(ki={}));const Oi=new Map;function Vi(e,t){return vn((n=>e?n||("function"==typeof t?t(e):e):null),[t,e])}function ji(e){let{callback:t,disabled:n}=e;const i=yn(t),a=(0,r.useMemo)((()=>{if(n||"undefined"==typeof window||void 0===window.ResizeObserver)return;const{ResizeObserver:e}=window;return new e(i)}),[n]);return(0,r.useEffect)((()=>()=>null==a?void 0:a.disconnect()),[a]),a}function $i(e){return new vi(ii(e),e)}function Zi(e,t,n){void 0===t&&(t=$i);const[i,a]=(0,r.useReducer)((function(i){if(!e)return null;var a;if(!1===e.isConnected)return null!=(a=null!=i?i:n)?a:null;const r=t(e);return JSON.stringify(i)===JSON.stringify(r)?i:r}),null),o=function(e){let{callback:t,disabled:n}=e;const i=yn(t),a=(0,r.useMemo)((()=>{if(n||"undefined"==typeof window||void 0===window.MutationObserver)return;const{MutationObserver:e}=window;return new e(i)}),[i,n]);return(0,r.useEffect)((()=>()=>null==a?void 0:a.disconnect()),[a]),a}({callback(t){if(e)for(const n of t){const{type:t,target:i}=n;if("childList"===t&&i instanceof HTMLElement&&i.contains(e)){a();break}}}}),l=ji({callback:a});return bn((()=>{a(),e?(null==l||l.observe(e),null==o||o.observe(document.body,{childList:!0,subtree:!0})):(null==l||l.disconnect(),null==o||o.disconnect())}),[e]),i}const Gi=[];function Ki(e,t){void 0===t&&(t=[]);const n=(0,r.useRef)(null);return(0,r.useEffect)((()=>{n.current=null}),t),(0,r.useEffect)((()=>{const t=e!==Vn;t&&!n.current&&(n.current=e),!t&&n.current&&(n.current=null)}),[e]),n.current?Un(e,n.current):Vn}function Xi(e){return(0,r.useMemo)((()=>e?function(e){const t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}(e):null),[e])}const Yi=[];const qi=[{sensor:Li,options:{}},{sensor:Ti,options:{}}],Ji={current:{}},Qi={draggable:{measure:ai},droppable:{measure:ai,strategy:Ri.WhileDragging,frequency:ki.Optimized},dragOverlay:{measure:ii}};class ea extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter((e=>{let{disabled:t}=e;return!t}))}getNodeFor(e){var t,n;return null!=(t=null==(n=this.get(e))?void 0:n.node.current)?t:void 0}}const ta={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new ea,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:kn},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:Qi,measureDroppableContainers:kn,windowRect:null,measuringScheduled:!1},na={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:kn,draggableNodes:new Map,over:null,measureDroppableContainers:kn},ia=(0,r.createContext)(na),aa=(0,r.createContext)(ta);function ra(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new ea}}}function oa(e,t){switch(t.type){case Rn.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case Rn.DragMove:return e.draggable.active?{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}}:e;case Rn.DragEnd:case Rn.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case Rn.RegisterDroppable:{const{element:n}=t,{id:i}=n,a=new ea(e.droppable.containers);return a.set(i,n),{...e,droppable:{...e.droppable,containers:a}}}case Rn.SetDroppableDisabled:{const{id:n,key:i,disabled:a}=t,r=e.droppable.containers.get(n);if(!r||i!==r.key)return e;const o=new ea(e.droppable.containers);return o.set(n,{...r,disabled:a}),{...e,droppable:{...e.droppable,containers:o}}}case Rn.UnregisterDroppable:{const{id:n,key:i}=t,a=e.droppable.containers.get(n);if(!a||i!==a.key)return e;const r=new ea(e.droppable.containers);return r.delete(n),{...e,droppable:{...e.droppable,containers:r}}}default:return e}}function la(e){let{disabled:t}=e;const{active:n,activatorEvent:i,draggableNodes:a}=(0,r.useContext)(ia),o=xn(i),l=xn(null==n?void 0:n.id);return(0,r.useEffect)((()=>{if(!t&&!i&&o&&null!=l){if(!Tn(o))return;if(document.activeElement===o.target)return;const e=a.get(l);if(!e)return;const{activatorNode:t,node:n}=e;if(!t.current&&!n.current)return;requestAnimationFrame((()=>{for(const e of[t.current,n.current]){if(!e)continue;const t=Bn(e);if(t){t.focus();break}}}))}}),[i,t,a,l,o]),null}const sa=(0,r.createContext)({...Vn,scaleX:1,scaleY:1});var pa;!function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(pa||(pa={}));const ca=(0,r.memo)((function(e){var t,n,i,a;let{id:l,accessibility:s,autoScroll:p=!0,children:c,sensors:d=qi,collisionDetection:u=Jn,measuring:m,modifiers:g,...h}=e;const b=(0,r.useReducer)(oa,void 0,ra),[y,S]=b,[v,f]=function(){const[e]=(0,r.useState)((()=>new Set)),t=(0,r.useCallback)((t=>(e.add(t),()=>e.delete(t))),[e]);return[(0,r.useCallback)((t=>{let{type:n,event:i}=t;e.forEach((e=>{var t;return null==(t=e[n])?void 0:t.call(e,i)}))}),[e]),t]}(),[x,_]=(0,r.useState)(pa.Uninitialized),C=x===pa.Initialized,{draggable:{active:N,nodes:w,translate:E},droppable:{containers:U}}=y,T=N?w.get(N):null,M=(0,r.useRef)({initial:null,translated:null}),F=(0,r.useMemo)((()=>{var e;return null!=N?{id:N,data:null!=(e=null==T?void 0:T.data)?e:Ji,rect:M}:null}),[N,T]),P=(0,r.useRef)(null),[B,L]=(0,r.useState)(null),[H,W]=(0,r.useState)(null),D=Sn(h,Object.values(h)),A=Nn("DndDescribedBy",l),I=(0,r.useMemo)((()=>U.getEnabled()),[U]),z=(R=m,(0,r.useMemo)((()=>({draggable:{...Qi.draggable,...null==R?void 0:R.draggable},droppable:{...Qi.droppable,...null==R?void 0:R.droppable},dragOverlay:{...Qi.dragOverlay,...null==R?void 0:R.dragOverlay}})),[null==R?void 0:R.draggable,null==R?void 0:R.droppable,null==R?void 0:R.dragOverlay]));var R;const{droppableRects:k,measureDroppableContainers:O,measuringScheduled:V}=function(e,t){let{dragging:n,dependencies:i,config:a}=t;const[o,l]=(0,r.useState)(null),{frequency:s,measure:p,strategy:c}=a,d=(0,r.useRef)(e),u=function(){switch(c){case Ri.Always:return!1;case Ri.BeforeDragging:return n;default:return!n}}(),m=Sn(u),g=(0,r.useCallback)((function(e){void 0===e&&(e=[]),m.current||l((t=>null===t?e:t.concat(e.filter((e=>!t.includes(e))))))}),[m]),h=(0,r.useRef)(null),b=vn((t=>{if(u&&!n)return Oi;if(!t||t===Oi||d.current!==e||null!=o){const t=new Map;for(let n of e){if(!n)continue;if(o&&o.length>0&&!o.includes(n.id)&&n.rect.current){t.set(n.id,n.rect.current);continue}const e=n.node.current,i=e?new vi(p(e),e):null;n.rect.current=i,i&&t.set(n.id,i)}return t}return t}),[e,o,n,u,p]);return(0,r.useEffect)((()=>{d.current=e}),[e]),(0,r.useEffect)((()=>{u||g()}),[n,u]),(0,r.useEffect)((()=>{o&&o.length>0&&l(null)}),[JSON.stringify(o)]),(0,r.useEffect)((()=>{u||"number"!=typeof s||null!==h.current||(h.current=setTimeout((()=>{g(),h.current=null}),s))}),[s,u,g,...i]),{droppableRects:b,measureDroppableContainers:g,measuringScheduled:null!=o}}(I,{dragging:C,dependencies:[E.x,E.y],config:z.droppable}),j=function(e,t){const n=null!==t?e.get(t):void 0,i=n?n.node.current:null;return vn((e=>{var n;return null===t?null:null!=(n=null!=i?i:e)?n:null}),[i,t])}(w,N),$=(0,r.useMemo)((()=>H?Mn(H):null),[H]),Z=function(){const e=!1===(null==B?void 0:B.autoScrollEnabled),t="object"==typeof p?!1===p.enabled:!1===p,n=C&&!e&&!t;return"object"==typeof p?{...p,enabled:n}:{enabled:n}}(),G=function(e,t){return Vi(e,t)}(j,z.draggable.measure);!function(e){let{activeNode:t,measure:n,initialRect:i,config:a=!0}=e;const o=(0,r.useRef)(!1),{x:l,y:s}="boolean"==typeof a?{x:a,y:a}:a;bn((()=>{if(!l&&!s||!t)return void(o.current=!1);if(o.current||!i)return;const e=null==t?void 0:t.node.current;if(!e||!1===e.isConnected)return;const a=Qn(n(e),i);if(l||(a.x=0),s||(a.y=0),o.current=!0,Math.abs(a.x)>0||Math.abs(a.y)>0){const t=oi(e);t&&t.scrollBy({top:a.y,left:a.x})}}),[t,l,s,i,n])}({activeNode:N?w.get(N):null,config:Z.layoutShiftCompensation,initialRect:G,measure:z.draggable.measure});const K=Zi(j,z.draggable.measure,G),X=Zi(j?j.parentElement:null),Y=(0,r.useRef)({activatorEvent:null,active:null,activeNode:j,collisionRect:null,collisions:null,droppableRects:k,draggableNodes:w,draggingNode:null,draggingNodeRect:null,droppableContainers:U,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),q=U.getNodeFor(null==(t=Y.current.over)?void 0:t.id),J=function(e){let{measure:t}=e;const[n,i]=(0,r.useState)(null),a=ji({callback:(0,r.useCallback)((e=>{for(const{target:n}of e)if(mn(n)){i((e=>{const i=t(n);return e?{...e,width:i.width,height:i.height}:i}));break}}),[t])}),o=(0,r.useCallback)((e=>{const n=function(e){if(!e)return null;if(e.children.length>1)return e;const t=e.children[0];return mn(t)?t:e}(e);null==a||a.disconnect(),n&&(null==a||a.observe(n)),i(n?t(n):null)}),[t,a]),[l,s]=fn(o);return(0,r.useMemo)((()=>({nodeRef:l,rect:n,setRef:s})),[n,l,s])}({measure:z.dragOverlay.measure}),Q=null!=(n=J.nodeRef.current)?n:j,ee=C?null!=(i=J.rect)?i:K:null,te=Boolean(J.nodeRef.current&&J.rect),ne=Qn(ie=te?null:K,Vi(ie));var ie;const ae=Xi(Q?dn(Q):null),re=function(e){const t=(0,r.useRef)(e),n=vn((n=>e?n&&n!==Gi&&e&&t.current&&e.parentNode===t.current.parentNode?n:ri(e):Gi),[e]);return(0,r.useEffect)((()=>{t.current=e}),[e]),n}(C?null!=q?q:j:null),oe=function(e,t){void 0===t&&(t=ii);const[n]=e,i=Xi(n?dn(n):null),[a,o]=(0,r.useReducer)((function(){return e.length?e.map((e=>ui(e)?i:new vi(t(e),e))):Yi}),Yi),l=ji({callback:o});return e.length>0&&a===Yi&&o(),bn((()=>{e.length?e.forEach((e=>null==l?void 0:l.observe(e))):(null==l||l.disconnect(),o())}),[e]),a}(re),le=function(e,t){let{transform:n,...i}=t;return null!=e&&e.length?e.reduce(((e,t)=>t({transform:e,...i})),n):n}(g,{transform:{x:E.x-ne.x,y:E.y-ne.y,scaleX:1,scaleY:1},activatorEvent:H,active:F,activeNodeRect:K,containerNodeRect:X,draggingNodeRect:ee,over:Y.current.over,overlayNodeRect:J.rect,scrollableAncestors:re,scrollableAncestorRects:oe,windowRect:ae}),se=$?En($,E):null,pe=function(e){const[t,n]=(0,r.useState)(null),i=(0,r.useRef)(e),a=(0,r.useCallback)((e=>{const t=li(e.target);t&&n((e=>e?(e.set(t,ci(t)),new Map(e)):null))}),[]);return(0,r.useEffect)((()=>{const t=i.current;if(e!==t){r(t);const o=e.map((e=>{const t=li(e);return t?(t.addEventListener("scroll",a,{passive:!0}),[t,ci(t)]):null})).filter((e=>null!=e));n(o.length?new Map(o):null),i.current=e}return()=>{r(e),r(t)};function r(e){e.forEach((e=>{const t=li(e);null==t||t.removeEventListener("scroll",a)}))}}),[a,e]),(0,r.useMemo)((()=>e.length?t?Array.from(t.values()).reduce(((e,t)=>En(e,t)),Vn):yi(e):Vn),[e,t])}(re),ce=Ki(pe),de=Ki(pe,[K]),ue=En(le,ce),me=ee?ti(ee,le):null,ge=F&&me?u({active:F,collisionRect:me,droppableRects:k,droppableContainers:I,pointerCoordinates:se}):null,he=Kn(ge,"id"),[be,ye]=(0,r.useState)(null),Se=function(e,t,n){return{...e,scaleX:t&&n?t.width/n.width:1,scaleY:t&&n?t.height/n.height:1}}(te?le:En(le,de),null!=(a=null==be?void 0:be.rect)?a:null,K),ve=(0,r.useCallback)(((e,t)=>{let{sensor:n,options:i}=t;if(null==P.current)return;const a=w.get(P.current);if(!a)return;const r=e.nativeEvent,o=new n({active:P.current,activeNode:a,event:r,options:i,context:Y,onStart(e){const t=P.current;if(null==t)return;const n=w.get(t);if(!n)return;const{onDragStart:i}=D.current,a={active:{id:t,data:n.data,rect:M}};(0,ln.unstable_batchedUpdates)((()=>{null==i||i(a),_(pa.Initializing),S({type:Rn.DragStart,initialCoordinates:e,active:t}),v({type:"onDragStart",event:a})}))},onMove(e){S({type:Rn.DragMove,coordinates:e})},onEnd:l(Rn.DragEnd),onCancel:l(Rn.DragCancel)});function l(e){return async function(){const{active:t,collisions:n,over:i,scrollAdjustedTranslate:a}=Y.current;let o=null;if(t&&a){const{cancelDrop:l}=D.current;o={activatorEvent:r,active:t,collisions:n,delta:a,over:i},e===Rn.DragEnd&&"function"==typeof l&&await Promise.resolve(l(o))&&(e=Rn.DragCancel)}P.current=null,(0,ln.unstable_batchedUpdates)((()=>{S({type:e}),_(pa.Uninitialized),ye(null),L(null),W(null);const t=e===Rn.DragEnd?"onDragEnd":"onDragCancel";if(o){const e=D.current[t];null==e||e(o),v({type:t,event:o})}}))}}(0,ln.unstable_batchedUpdates)((()=>{L(o),W(e.nativeEvent)}))}),[w]),fe=(0,r.useCallback)(((e,t)=>(n,i)=>{const a=n.nativeEvent,r=w.get(i);if(null!==P.current||!r||a.dndKit||a.defaultPrevented)return;const o={active:r};!0===e(n,t.options,o)&&(a.dndKit={capturedBy:t.sensor},P.current=i,ve(n,t))}),[w,ve]),xe=function(e,t){return(0,r.useMemo)((()=>e.reduce(((e,n)=>{const{sensor:i}=n;return[...e,...i.activators.map((e=>({eventName:e.eventName,handler:t(e.handler,n)})))]}),[])),[e,t])}(d,fe);!function(e){(0,r.useEffect)((()=>{if(!sn)return;const t=e.map((e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()}));return()=>{for(const e of t)null==e||e()}}),e.map((e=>{let{sensor:t}=e;return t})))}(d),bn((()=>{K&&x===pa.Initializing&&_(pa.Initialized)}),[K,x]),(0,r.useEffect)((()=>{const{onDragMove:e}=D.current,{active:t,activatorEvent:n,collisions:i,over:a}=Y.current;if(!t||!n)return;const r={active:t,activatorEvent:n,collisions:i,delta:{x:ue.x,y:ue.y},over:a};(0,ln.unstable_batchedUpdates)((()=>{null==e||e(r),v({type:"onDragMove",event:r})}))}),[ue.x,ue.y]),(0,r.useEffect)((()=>{const{active:e,activatorEvent:t,collisions:n,droppableContainers:i,scrollAdjustedTranslate:a}=Y.current;if(!e||null==P.current||!t||!a)return;const{onDragOver:r}=D.current,o=i.get(he),l=o&&o.rect.current?{id:o.id,rect:o.rect.current,data:o.data,disabled:o.disabled}:null,s={active:e,activatorEvent:t,collisions:n,delta:{x:a.x,y:a.y},over:l};(0,ln.unstable_batchedUpdates)((()=>{ye(l),null==r||r(s),v({type:"onDragOver",event:s})}))}),[he]),bn((()=>{Y.current={activatorEvent:H,active:F,activeNode:j,collisionRect:me,collisions:ge,droppableRects:k,draggableNodes:w,draggingNode:Q,draggingNodeRect:ee,droppableContainers:U,over:be,scrollableAncestors:re,scrollAdjustedTranslate:ue},M.current={initial:ee,translated:me}}),[F,j,ge,me,w,Q,ee,k,U,be,re,ue]),function(e){let{acceleration:t,activator:n=Ai.Pointer,canScroll:i,draggingRect:a,enabled:o,interval:l=5,order:s=Ii.TreeOrder,pointerCoordinates:p,scrollableAncestors:c,scrollableAncestorRects:d,delta:u,threshold:m}=e;const g=function(e){let{delta:t,disabled:n}=e;const i=xn(t);return vn((e=>{if(n||!i||!e)return zi;const a=Math.sign(t.x-i.x),r=Math.sign(t.y-i.y);return{x:{[di.Backward]:e.x[di.Backward]||-1===a,[di.Forward]:e.x[di.Forward]||1===a},y:{[di.Backward]:e.y[di.Backward]||-1===r,[di.Forward]:e.y[di.Forward]||1===r}}}),[n,t,i])}({delta:u,disabled:!o}),[h,b]=function(){const e=(0,r.useRef)(null);return[(0,r.useCallback)(((t,n)=>{e.current=setInterval(t,n)}),[]),(0,r.useCallback)((()=>{null!==e.current&&(clearInterval(e.current),e.current=null)}),[])]}(),y=(0,r.useRef)({x:0,y:0}),S=(0,r.useRef)({x:0,y:0}),v=(0,r.useMemo)((()=>{switch(n){case Ai.Pointer:return p?{top:p.y,bottom:p.y,left:p.x,right:p.x}:null;case Ai.DraggableRect:return a}}),[n,a,p]),f=(0,r.useRef)(null),x=(0,r.useCallback)((()=>{const e=f.current;if(!e)return;const t=y.current.x*S.current.x,n=y.current.y*S.current.y;e.scrollBy(t,n)}),[]),_=(0,r.useMemo)((()=>s===Ii.TreeOrder?[...c].reverse():c),[s,c]);(0,r.useEffect)((()=>{if(o&&c.length&&v){for(const e of _){if(!1===(null==i?void 0:i(e)))continue;const n=c.indexOf(e),a=d[n];if(!a)continue;const{direction:r,speed:o}=hi(e,a,v,t,m);for(const e of["x","y"])g[e][r[e]]||(o[e]=0,r[e]=0);if(o.x>0||o.y>0)return b(),f.current=e,h(x,l),y.current=o,void(S.current=r)}y.current={x:0,y:0},S.current={x:0,y:0},b()}else b()}),[t,x,i,b,o,l,JSON.stringify(v),JSON.stringify(g),h,c,_,d,JSON.stringify(m)])}({...Z,delta:E,draggingRect:me,pointerCoordinates:se,scrollableAncestors:re,scrollableAncestorRects:oe});const _e=(0,r.useMemo)((()=>({active:F,activeNode:j,activeNodeRect:K,activatorEvent:H,collisions:ge,containerNodeRect:X,dragOverlay:J,draggableNodes:w,droppableContainers:U,droppableRects:k,over:be,measureDroppableContainers:O,scrollableAncestors:re,scrollableAncestorRects:oe,measuringConfiguration:z,measuringScheduled:V,windowRect:ae})),[F,j,K,H,ge,X,J,w,U,k,be,O,re,oe,z,V,ae]),Ce=(0,r.useMemo)((()=>({activatorEvent:H,activators:xe,active:F,activeNodeRect:K,ariaDescribedById:{draggable:A},dispatch:S,draggableNodes:w,over:be,measureDroppableContainers:O})),[H,xe,F,K,S,A,w,be,O]);return o().createElement(Dn.Provider,{value:f},o().createElement(ia.Provider,{value:Ce},o().createElement(aa.Provider,{value:_e},o().createElement(sa.Provider,{value:Se},c)),o().createElement(la,{disabled:!1===(null==s?void 0:s.restoreFocus)})),o().createElement(zn,{...s,hiddenTextDescribedById:A}))})),da=(0,r.createContext)(null),ua="button",ma="Droppable";const ga={timeout:25};function ha(e,t,n){const i=e.slice();return i.splice(n<0?i.length+n:n,0,i.splice(t,1)[0]),i}function ba(e,t){return e.reduce(((e,n,i)=>{const a=t.get(n);return a&&(e[i]=a),e}),Array(e.length))}function ya(e){return null!==e&&e>=0}const Sa=e=>{let{rects:t,activeIndex:n,overIndex:i,index:a}=e;const r=ha(t,i,n),o=t[a],l=r[a];return l&&o?{x:l.left-o.left,y:l.top-o.top,scaleX:l.width/o.width,scaleY:l.height/o.height}:null},va={scaleX:1,scaleY:1},fa=e=>{var t;let{activeIndex:n,activeNodeRect:i,index:a,rects:r,overIndex:o}=e;const l=null!=(t=r[n])?t:i;if(!l)return null;if(a===n){const e=r[o];return e?{x:0,y:n<o?e.top+e.height-(l.top+l.height):e.top-l.top,...va}:null}const s=function(e,t,n){const i=e[t],a=e[t-1],r=e[t+1];return i?n<t?a?i.top-(a.top+a.height):r?r.top-(i.top+i.height):0:r?r.top-(i.top+i.height):a?i.top-(a.top+a.height):0:0}(r,a,n);return a>n&&a<=o?{x:0,y:-l.height-s,...va}:a<n&&a>=o?{x:0,y:l.height+s,...va}:{x:0,y:0,...va}},xa="Sortable",_a=o().createContext({activeIndex:-1,containerId:xa,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:Sa,disabled:{draggable:!1,droppable:!1}});function Ca(e){let{children:t,id:n,items:i,strategy:a=Sa,disabled:l=!1}=e;const{active:s,dragOverlay:p,droppableRects:c,over:d,measureDroppableContainers:u}=(0,r.useContext)(aa),m=Nn(xa,n),g=Boolean(null!==p.rect),h=(0,r.useMemo)((()=>i.map((e=>"object"==typeof e&&"id"in e?e.id:e))),[i]),b=null!=s,y=s?h.indexOf(s.id):-1,S=d?h.indexOf(d.id):-1,v=(0,r.useRef)(h),f=!function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(h,v.current),x=-1!==S&&-1===y||f,_=function(e){return"boolean"==typeof e?{draggable:e,droppable:e}:e}(l);bn((()=>{f&&b&&u(h)}),[f,h,b,u]),(0,r.useEffect)((()=>{v.current=h}),[h]);const C=(0,r.useMemo)((()=>({activeIndex:y,containerId:m,disabled:_,disableTransforms:x,items:h,overIndex:S,useDragOverlay:g,sortedRects:ba(h,c),strategy:a})),[y,m,_.draggable,_.droppable,x,h,S,c,g,a]);return o().createElement(_a.Provider,{value:C},t)}const Na=e=>{let{id:t,items:n,activeIndex:i,overIndex:a}=e;return ha(n,i,a).indexOf(t)},wa=e=>{let{containerId:t,isSorting:n,wasDragging:i,index:a,items:r,newIndex:o,previousItems:l,previousContainerId:s,transition:p}=e;return!(!p||!i||l!==r&&a===o||!n&&(o===a||t!==s))},Ea={duration:200,easing:"ease"},Ua="transform",Ta=Fn.Transition.toString({property:Ua,duration:0,easing:"linear"}),Ma={roleDescription:"sortable"};function Fa(e){let{animateLayoutChanges:t=wa,attributes:n,disabled:i,data:a,getNewIndex:o=Na,id:l,strategy:s,resizeObserverConfig:p,transition:c=Ea}=e;const{items:d,containerId:u,activeIndex:m,disabled:g,disableTransforms:h,sortedRects:b,overIndex:y,useDragOverlay:S,strategy:v}=(0,r.useContext)(_a),f=function(e,t){var n,i;return"boolean"==typeof e?{draggable:e,droppable:!1}:{draggable:null!=(n=null==e?void 0:e.draggable)?n:t.draggable,droppable:null!=(i=null==e?void 0:e.droppable)?i:t.droppable}}(i,g),x=d.indexOf(l),_=(0,r.useMemo)((()=>({sortable:{containerId:u,index:x,items:d},...a})),[u,a,x,d]),C=(0,r.useMemo)((()=>d.slice(d.indexOf(l))),[d,l]),{rect:N,node:w,isOver:E,setNodeRef:U}=function(e){let{data:t,disabled:n=!1,id:i,resizeObserverConfig:a}=e;const o=Nn("Droppable"),{active:l,dispatch:s,over:p,measureDroppableContainers:c}=(0,r.useContext)(ia),d=(0,r.useRef)({disabled:n}),u=(0,r.useRef)(!1),m=(0,r.useRef)(null),g=(0,r.useRef)(null),{disabled:h,updateMeasurementsFor:b,timeout:y}={...ga,...a},S=Sn(null!=b?b:i),v=ji({callback:(0,r.useCallback)((()=>{u.current?(null!=g.current&&clearTimeout(g.current),g.current=setTimeout((()=>{c(Array.isArray(S.current)?S.current:[S.current]),g.current=null}),y)):u.current=!0}),[y]),disabled:h||!l}),f=(0,r.useCallback)(((e,t)=>{v&&(t&&(v.unobserve(t),u.current=!1),e&&v.observe(e))}),[v]),[x,_]=fn(f),C=Sn(t);return(0,r.useEffect)((()=>{v&&x.current&&(v.disconnect(),u.current=!1,v.observe(x.current))}),[x,v]),bn((()=>(s({type:Rn.RegisterDroppable,element:{id:i,key:o,disabled:n,node:x,rect:m,data:C}}),()=>s({type:Rn.UnregisterDroppable,key:o,id:i}))),[i]),(0,r.useEffect)((()=>{n!==d.current.disabled&&(s({type:Rn.SetDroppableDisabled,id:i,key:o,disabled:n}),d.current.disabled=n)}),[i,o,n,s]),{active:l,rect:m,isOver:(null==p?void 0:p.id)===i,node:x,over:p,setNodeRef:_}}({id:l,data:_,disabled:f.droppable,resizeObserverConfig:{updateMeasurementsFor:C,...p}}),{active:T,activatorEvent:M,activeNodeRect:F,attributes:P,setNodeRef:B,listeners:L,isDragging:H,over:W,setActivatorNodeRef:D,transform:A}=function(e){let{id:t,data:n,disabled:i=!1,attributes:a}=e;const o=Nn(ma),{activators:l,activatorEvent:s,active:p,activeNodeRect:c,ariaDescribedById:d,draggableNodes:u,over:m}=(0,r.useContext)(ia),{role:g=ua,roleDescription:h="draggable",tabIndex:b=0}=null!=a?a:{},y=(null==p?void 0:p.id)===t,S=(0,r.useContext)(y?sa:da),[v,f]=fn(),[x,_]=fn(),C=function(e,t){return(0,r.useMemo)((()=>e.reduce(((e,n)=>{let{eventName:i,handler:a}=n;return e[i]=e=>{a(e,t)},e}),{})),[e,t])}(l,t),N=Sn(n);return bn((()=>(u.set(t,{id:t,key:o,node:v,activatorNode:x,data:N}),()=>{const e=u.get(t);e&&e.key===o&&u.delete(t)})),[u,t]),{active:p,activatorEvent:s,activeNodeRect:c,attributes:(0,r.useMemo)((()=>({role:g,tabIndex:b,"aria-disabled":i,"aria-pressed":!(!y||g!==ua)||void 0,"aria-roledescription":h,"aria-describedby":d.draggable})),[i,g,b,y,h,d.draggable]),isDragging:y,listeners:i?void 0:C,node:v,over:m,setNodeRef:f,setActivatorNodeRef:_,transform:S}}({id:l,data:_,attributes:{...Ma,...n},disabled:f.draggable}),I=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.useMemo)((()=>e=>{t.forEach((t=>t(e)))}),t)}(U,B),z=Boolean(T),R=z&&!h&&ya(m)&&ya(y),k=!S&&H,O=k&&R?A:null,V=R?null!=O?O:(null!=s?s:v)({rects:b,activeNodeRect:F,activeIndex:m,overIndex:y,index:x}):null,j=ya(m)&&ya(y)?o({id:l,items:d,activeIndex:m,overIndex:y}):x,$=null==T?void 0:T.id,Z=(0,r.useRef)({activeId:$,items:d,newIndex:j,containerId:u}),G=d!==Z.current.items,K=t({active:T,containerId:u,isDragging:H,isSorting:z,id:l,index:x,items:d,newIndex:Z.current.newIndex,previousItems:Z.current.items,previousContainerId:Z.current.containerId,transition:c,wasDragging:null!=Z.current.activeId}),X=function(e){let{disabled:t,index:n,node:i,rect:a}=e;const[o,l]=(0,r.useState)(null),s=(0,r.useRef)(n);return bn((()=>{if(!t&&n!==s.current&&i.current){const e=a.current;if(e){const t=ii(i.current,{ignoreTransform:!0}),n={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};(n.x||n.y)&&l(n)}}n!==s.current&&(s.current=n)}),[t,n,i,a]),(0,r.useEffect)((()=>{o&&l(null)}),[o]),o}({disabled:!K,index:x,node:w,rect:N});return(0,r.useEffect)((()=>{z&&Z.current.newIndex!==j&&(Z.current.newIndex=j),u!==Z.current.containerId&&(Z.current.containerId=u),d!==Z.current.items&&(Z.current.items=d)}),[z,j,u,d]),(0,r.useEffect)((()=>{if($===Z.current.activeId)return;if($&&!Z.current.activeId)return void(Z.current.activeId=$);const e=setTimeout((()=>{Z.current.activeId=$}),50);return()=>clearTimeout(e)}),[$]),{active:T,activeIndex:m,attributes:P,data:_,rect:N,index:x,newIndex:j,items:d,isOver:E,isSorting:z,isDragging:H,listeners:L,node:w,overIndex:y,over:W,setNodeRef:I,setActivatorNodeRef:D,setDroppableNodeRef:U,setDraggableNodeRef:B,transform:null!=X?X:V,transition:X||G&&Z.current.newIndex===x?Ta:k&&!Tn(M)||!c?void 0:z||K?Fn.Transition.toString({...c,property:Ua}):void 0}}function Pa(e){if(!e)return!1;const t=e.data.current;return!!(t&&"sortable"in t&&"object"==typeof t.sortable&&"containerId"in t.sortable&&"items"in t.sortable&&"index"in t.sortable)}const Ba=[Ci.Down,Ci.Right,Ci.Up,Ci.Left],La=(e,t)=>{let{context:{active:n,collisionRect:i,droppableRects:a,droppableContainers:r,over:o,scrollableAncestors:l}}=t;if(Ba.includes(e.code)){if(e.preventDefault(),!n||!i)return;const t=[];r.getEnabled().forEach((n=>{if(!n||null!=n&&n.disabled)return;const r=a.get(n.id);if(r)switch(e.code){case Ci.Down:i.top<r.top&&t.push(n);break;case Ci.Up:i.top>r.top&&t.push(n);break;case Ci.Left:i.left>r.left&&t.push(n);break;case Ci.Right:i.left<r.left&&t.push(n)}}));const c=(e=>{let{collisionRect:t,droppableRects:n,droppableContainers:i}=e;const a=Gn(t),r=[];for(const e of i){const{id:t}=e,i=n.get(t);if(i){const n=Gn(i),o=a.reduce(((e,t,i)=>e+jn(n[i],t)),0),l=Number((o/4).toFixed(4));r.push({id:t,data:{droppableContainer:e,value:l}})}}return r.sort($n)})({active:n,collisionRect:i,droppableRects:a,droppableContainers:t,pointerCoordinates:null});let d=Kn(c,"id");if(d===(null==o?void 0:o.id)&&c.length>1&&(d=c[1].id),null!=d){const e=r.get(n.id),t=r.get(d),o=t?a.get(t.id):null,c=null==t?void 0:t.node.current;if(c&&o&&e&&t){const n=ri(c).some(((e,t)=>l[t]!==e)),a=Ha(e,t),r=(p=t,!(!Pa(s=e)||!Pa(p))&&!!Ha(s,p)&&s.data.current.sortable.index<p.data.current.sortable.index),d=n||!a?{x:0,y:0}:{x:r?i.width-o.width:0,y:r?i.height-o.height:0},u={x:o.left,y:o.top};return d.x&&d.y?u:Un(u,d)}}}var s,p};function Ha(e,t){return!(!Pa(e)||!Pa(t))&&e.data.current.sortable.containerId===t.data.current.sortable.containerId}const Wa=[{label:i.__("Lectures","masterstudy-lms-learning-management-system"),value:"lectures"},{label:i.__("Duration","masterstudy-lms-learning-management-system"),value:"duration"},{label:i.__("Views","masterstudy-lms-learning-management-system"),value:"views"},{label:i.__("Level","masterstudy-lms-learning-management-system"),value:"level"},{label:i.__("Members","masterstudy-lms-learning-management-system"),value:"members"},{label:i.__("Empty","masterstudy-lms-learning-management-system"),value:"empty"}],Da={selectMeta:Wa,selectDataslot1:Wa,selectDataslot2:Wa,selectPopupDataslot1:Wa,selectPopupDataslot2:Wa,selectPopupDataslot3:Wa},Aa=F("sortable__item"),Ia=F("sortable__item__disabled"),za=F("sortable__item__content"),Ra=F("sortable__item__content__drag-item"),ka=F("sortable__item__content__drag-item__disabled"),Oa=F("sortable__item__content__title"),Va=F("sortable__item__control"),ja=F("sortable__item__icon"),$a=({item:e,parentIndex:t,isParent:n=!1,onToggle:i,isOpen:a})=>{const{id:o,label:l,switchFieldName:s,selectFieldName:p}=e,{value:c}=A(e.switchFieldName),g=e.switchFieldName&&!c,{attributes:h,listeners:b,setNodeRef:y,transform:S,transition:v}=Fa({id:o,disabled:g,data:{parentIndex:t}}),f={transform:Fn.Transform.toString(S),transition:v};return(0,r.createElement)("div",{ref:y,style:f,...h,className:d()([Aa],{[Ia]:g})},(0,r.createElement)("div",{className:za},(0,r.createElement)(u.Dashicon,{...b,icon:"menu-alt2",size:16,className:d()([Ra],{[ka]:g})}),(0,r.createElement)("span",{className:Oa},l)),(0,r.createElement)("div",{className:Va},(0,r.createElement)(m,{condition:Boolean(s)},(0,r.createElement)(vt,{name:s})),(0,r.createElement)(m,{condition:Boolean(p)},(0,r.createElement)(kt,{name:p,options:Da[p]||[]}))),(0,r.createElement)(m,{condition:n},(0,r.createElement)(u.Dashicon,{icon:a?"arrow-up-alt2":"arrow-down-alt2",className:ja,size:16,onClick:i})))},Za=F("nested-sortable"),Ga=({items:e,parentIndex:t})=>(0,r.createElement)(Ca,{items:e,strategy:fa},(0,r.createElement)("div",{className:Za},e.map((e=>(0,r.createElement)($a,{key:e.id,item:e,parentIndex:t}))))),Ka=F("nested-sortable__item"),Xa=({item:e,parentIndex:t,onToggle:n,isOpen:i})=>(0,r.createElement)("div",{className:Ka},(0,r.createElement)($a,{item:e,isParent:!0,onToggle:n,isOpen:i}),(0,r.createElement)(m,{condition:i},(0,r.createElement)(Ga,{items:e.children,parentIndex:t}))),Ya=F("sortable"),qa=({name:e,dependencyMode:t,dependencies:n})=>{const{value:i,onChange:a}=A(e),o=i,l=R(n,t),p=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.useMemo)((()=>[...t].filter((e=>null!=e))),[...t])}(On(Li),On(Ti,{coordinateGetter:La})),{onClose:c,onToggle:d,getIsOpen:u}=(()=>{const[e,t]=(0,s.useState)([]),n=(0,s.useCallback)((e=>{t((t=>[...t,e]))}),[]),i=(0,s.useCallback)((e=>{t((t=>t.filter((t=>t!==e))))}),[]),a=(0,s.useCallback)((t=>e.includes(t)),[e]);return{onToggle:(0,s.useCallback)((e=>{a(e)?i(e):n(e)}),[a,i,n]),onClose:i,getIsOpen:a}})();return l?(0,r.createElement)(ca,{sensors:p,collisionDetection:Yn,onDragEnd:e=>{const{active:t,over:n}=e;if(t.id!==n.id){const e=t.data.current.parentIndex;let i=[];if(e){const a=o[e].children.findIndex((e=>e.id===t.id)),r=o[e].children.findIndex((e=>e.id===n.id)),l=ha(o[e].children,a,r);i=[...o],i[e].children=l}else{const e=o.findIndex((e=>e.id===t.id)),a=o.findIndex((e=>e.id===n.id));i=ha(o,e,a)}a(i)}},onDragStart:e=>{c(String(e.active.id))}},(0,r.createElement)(Ca,{items:o,strategy:fa},(0,r.createElement)("div",{className:Ya},o.map(((e,t)=>e.children?(0,r.createElement)(Xa,{key:e.id,item:e,parentIndex:t,onToggle:()=>d(e.id),isOpen:u(e.id)}):(0,r.createElement)($a,{key:e.id,item:e})))))):null},Ja=F("accordion"),Qa=F("accordion__header"),er=F("accordion__header-flex"),tr=F("accordion__content"),nr=F("accordion__icon"),ir=F("accordion__title"),ar=F("accordion__title-disabled"),rr=F("accordion__indicator"),or=F("accordion__controls"),lr=F("accordion__controls-disabled"),sr=({title:e,children:t,accordionFields:n,switchName:i,visible:a=!0,isDefaultOpen:o=!1})=>{const{isOpen:l,onToggle:p,disabled:c,onReset:h,hasChanges:b,onClose:y}=((e,t,n)=>{var i;const{isOpen:a,onToggle:r,onClose:o}=L(t),{defaultValues:l,attributes:s,setAttributes:p}=D(),c=((e,t,n)=>{for(const i of n)if(!g(e[i],t[i]))return!0;return!1})(l,s,e);return{isOpen:a,onToggle:r,disabled:!(null===(i=s[n])||void 0===i||i),hasChanges:c,onReset:t=>{t.stopPropagation(),p(e.reduce(((e,t)=>(e[t]=l[t],e)),{}))},onClose:o}})(n,o,i);return((e,t)=>{const{attributes:n}=D(),i=!n[t];(0,s.useEffect)((()=>{i&&e()}),[i,e])})(y,i),a?(0,r.createElement)("div",{className:Ja},(0,r.createElement)("div",{className:Qa},(0,r.createElement)("div",{className:er,onClick:c?null:p},(0,r.createElement)("div",{className:d()(ir,{[ar]:c,"with-switch":Boolean(i)})},(0,r.createElement)("div",null,e),(0,r.createElement)(m,{condition:b&&!c},(0,r.createElement)("div",{className:rr}))),(0,r.createElement)("div",{className:d()(or,{[lr]:c})},(0,r.createElement)(u.Dashicon,{icon:l?"arrow-up-alt2":"arrow-down-alt2",className:nr,size:16}))),(0,r.createElement)(m,{condition:Boolean(i)},(0,r.createElement)(vt,{name:i})),(0,r.createElement)(m,{condition:b&&!c},(0,r.createElement)(Ee,{onReset:h}))),l&&(0,r.createElement)("div",{className:tr},t)):null},pr=(e,t)=>{const{hasInnerBlocks:n}=(0,P.useSelect)((n=>{const i=n(p.store),a=i.getBlockCount(e),r=i.getBlocks(e);return t(a?r[0].name:""),{hasInnerBlocks:a>0}}),[e,t]),{replaceInnerBlocks:i}=(0,P.useDispatch)(p.store);return{hasInnerBlocks:n,replaceInnerBlocks:i,onRemovePreset:()=>{i(e,(0,a.createBlocksFromInnerBlocksTemplate)([]),!1)}}},cr=F("preset-picker"),dr=F("preset-picker__label"),ur=F("preset-picker__remove"),mr=F("preset-picker__presets-list"),gr=F("preset-picker__presets-list__item"),hr=F("preset-picker__presets-list__item__preset"),br=F("preset-picker__presets-list__item__preset-active"),yr=e=>{const{label:t,presets:n,setAttributes:i,innerPresetName:o,onChangePresetName:l,clientId:s}=e,{replaceInnerBlocks:p,onRemovePreset:c}=pr(s,l);return(0,r.createElement)("div",{className:cr},(0,r.createElement)("div",{className:dr},(0,r.createElement)("div",null,t),(0,r.createElement)(m,{condition:""!==o},(0,r.createElement)(u.Dashicon,{icon:"undo",onClick:c,className:ur,size:16}))),(0,r.createElement)("div",{className:mr},n.map((e=>(0,r.createElement)("div",{key:e.name,className:d()(gr,{[br]:o.includes(e.name)}),onClick:()=>(e=>{e.attributes&&i(e.attributes),e.innerBlocks&&p(s,(0,a.createBlocksFromInnerBlocksTemplate)(e.innerBlocks),!1)})(e)},(0,r.createElement)("div",{className:hr},e.icon),(0,r.createElement)("span",null,e.title))))))},Sr={cardGap:30,cardGapTablet:null,cardGapMobile:null,cardGapUnit:"px",cardGapUnitTablet:"px",cardGapUnitMobile:"px",cardItems:[{id:"card-category",label:i.__("Category","masterstudy-lms-learning-management-system"),switchFieldName:"showCategory",selectFieldName:""},{id:"card-title",label:i.__("Title","masterstudy-lms-learning-management-system"),switchFieldName:"",selectFieldName:""},{id:"card-meta",label:i.__("Dataslots","masterstudy-lms-learning-management-system"),switchFieldName:"",selectFieldName:"",children:[{id:"card-dataslot-1",label:i.__("Dataslot 1","masterstudy-lms-learning-management-system"),switchFieldName:"",selectFieldName:"selectDataslot1"},{id:"card-dataslot-2",label:i.__("Dataslot 2","masterstudy-lms-learning-management-system"),switchFieldName:"",selectFieldName:"selectDataslot2"}]},{id:"card-divider",label:i.__("Divider","masterstudy-lms-learning-management-system"),switchFieldName:"showDivider",selectFieldName:""},{id:"card-info",label:i.__("Price and Rating","masterstudy-lms-learning-management-system"),switchFieldName:"",selectFieldName:"",children:[{id:"card-rating",label:i.__("Rating","masterstudy-lms-learning-management-system"),switchFieldName:"showRating",selectFieldName:""},{id:"card-price",label:i.__("Price","masterstudy-lms-learning-management-system"),switchFieldName:"showPrice",selectFieldName:""}]}],showCategory:!0,showPrice:!0,showRating:!0,showDivider:!1,selectDataslot1:"lectures",selectDataslot2:"duration",showPopup:!0,popupItems:[{id:"popup-instructor",label:i.__("Instructor","masterstudy-lms-learning-management-system"),switchFieldName:"showPopupInstructor",selectFieldName:""},{id:"popup-title",label:i.__("Title","masterstudy-lms-learning-management-system"),switchFieldName:"",selectFieldName:""},{id:"popup-description",label:i.__("Description","masterstudy-lms-learning-management-system"),switchFieldName:"",selectFieldName:""},{id:"popup-meta",label:i.__("Dataslots","masterstudy-lms-learning-management-system"),switchFieldName:"",selectFieldName:"",children:[{id:"popup-dataslot-1",label:i.__("Dataslot 1","masterstudy-lms-learning-management-system"),switchFieldName:"",selectFieldName:"selectPopupDataslot1"},{id:"popup-dataslot-2",label:i.__("Dataslot 2","masterstudy-lms-learning-management-system"),switchFieldName:"",selectFieldName:"selectPopupDataslot2"},{id:"popup-dataslot-3",label:i.__("Dataslot 3","masterstudy-lms-learning-management-system"),switchFieldName:"",selectFieldName:"selectPopupDataslot3"}]},{id:"popup-preview",label:i.__("Preview Button","masterstudy-lms-learning-management-system"),switchFieldName:"",selectFieldName:""},{id:"popup-info",label:i.__("Wishlist and Price","masterstudy-lms-learning-management-system"),switchFieldName:"",selectFieldName:"",children:[{id:"popup-wishlist",label:i.__("Wishlist","masterstudy-lms-learning-management-system"),switchFieldName:"showPopupWishlist",selectFieldName:""},{id:"popup-price",label:i.__("Price","masterstudy-lms-learning-management-system"),switchFieldName:"showPopupPrice",selectFieldName:""}]}],showPopupInstructor:!0,showPopupPrice:!0,showPopupWishlist:!0,selectPopupDataslot1:"level",selectPopupDataslot2:"lectures",selectPopupDataslot3:"duration",featuredPosition:"start",statusStyle:"rectangle",statusPosition:"start"},vr=(Object.keys(Sr),[{name:i.__("Rectangle","masterstudy-lms-learning-management-system"),value:"rectangle",icon:()=>(0,r.createElement)(u.SVG,{xmlns:"http://www.w3.org/2000/svg",width:"62",height:"22",viewBox:"0 0 62 22",fill:"none"},(0,r.createElement)(u.Rect,{width:"62",height:"22",rx:"4",fill:"currentColor"}))},{name:i.__("Flag","masterstudy-lms-learning-management-system"),value:"flag",icon:()=>(0,r.createElement)(u.SVG,{xmlns:"http://www.w3.org/2000/svg",width:"68",height:"22",viewBox:"0 0 68 22",fill:"none"},(0,r.createElement)(u.Rect,{width:"58",height:"22",fill:"currentColor"}),(0,r.createElement)(u.Path,{d:"M58 0H63.4789C65.2152 0 66.1267 2.0606 64.9588 3.34535L58 11L64.9588 18.6547C66.1267 19.9394 65.2152 22 63.4789 22H58V0Z",fill:"currentColor"}))},{name:i.__("Arrow","masterstudy-lms-learning-management-system"),value:"arrow",icon:()=>(0,r.createElement)(u.SVG,{xmlns:"http://www.w3.org/2000/svg",width:"68",height:"22",viewBox:"0 0 68 22",fill:"none"},(0,r.createElement)(u.Rect,{width:"58",height:"22",fill:"currentColor"}),(0,r.createElement)(u.Path,{d:"M66.777 9.65465L58 0V22L66.777 12.3453C67.4705 11.5825 67.4705 10.4175 66.777 9.65465Z",fill:"currentColor"}))}]),fr={cntrMargin:M,cntrMarginTablet:M,cntrMarginMobile:M,cntrMarginUnit:"px",cntrMarginUnitTablet:"px",cntrMarginUnitMobile:"px",cntrPadding:M,cntrPaddingTablet:M,cntrPaddingMobile:M,cntrPaddingUnit:"px",cntrPaddingUnitTablet:"px",cntrPaddingUnitMobile:"px"},xr=Object.keys(fr),_r={cardPadding:M,cardPaddingTablet:M,cardPaddingMobile:M,cardPaddingUnit:"px",cardPaddingUnitTablet:"px",cardPaddingUnitMobile:"px",cardBgColor:"#ffffff",cardBorderStyle:"none",cardBorderStyleTablet:"",cardBorderStyleMobile:"",cardBorderColor:"",cardBorderColorTablet:"",cardBorderColorMobile:"",cardBorderWidth:M,cardBorderWidthTablet:M,cardBorderWidthMobile:M,cardBorderWidthUnit:"px",cardBorderWidthUnitTablet:"px",cardBorderWidthUnitMobile:"px",cardBorderRadius:{top:"4",right:"4",bottom:"4",left:"4"},cardBorderRadiusTablet:M,cardBorderRadiusMobile:M,cardBorderRadiusUnit:"px",cardBorderRadiusUnitTablet:"px",cardBorderRadiusUnitMobile:"px",cardShadowColor:"",cardShadowColorTablet:"",cardShadowColorMobile:"",cardShadowHorizontal:null,cardShadowHorizontalTablet:null,cardShadowHorizontalMobile:null,cardShadowVertical:null,cardShadowVerticalTablet:null,cardShadowVerticalMobile:null,cardShadowBlur:null,cardShadowBlurTablet:null,cardShadowBlurMobile:null,cardShadowSpread:null,cardShadowSpreadTablet:null,cardShadowSpreadMobile:null,cardShadowInset:!1,cardShadowInsetTablet:!1,cardShadowInsetMobile:!1},Cr=Object.keys(_r),Nr={ibPadding:{top:"15",right:"20",bottom:"15",left:"20"},ibPaddingTablet:M,ibPaddingMobile:M,ibPaddingUnit:"px",ibPaddingUnitTablet:"px",ibPaddingUnitMobile:"px",ibBgColor:"",ibBorderStyle:"none",ibBorderStyleTablet:"",ibBorderStyleMobile:"",ibBorderColor:"",ibBorderColorTablet:"",ibBorderColorMobile:"",ibBorderWidth:M,ibBorderWidthTablet:M,ibBorderWidthMobile:M,ibBorderWidthUnit:"px",ibBorderWidthUnitTablet:"px",ibBorderWidthUnitMobile:"px",ibBorderRadius:M,ibBorderRadiusTablet:M,ibBorderRadiusMobile:M,ibBorderRadiusUnit:"px",ibBorderRadiusUnitTablet:"px",ibBorderRadiusUnitMobile:"px"},wr=Object.keys(Nr),Er={imageHeight:150,imageHeightTablet:null,imageHeightMobile:null,imageHeightUnit:"px",imageHeightUnitTablet:"px",imageHeightUnitMobile:"px",imageBorderStyle:"none",imageBorderStyleTablet:"",imageBorderStyleMobile:"",imageBorderColor:"",imageBorderColorTablet:"",imageBorderColorMobile:"",imageBorderWidth:M,imageBorderWidthTablet:M,imageBorderWidthMobile:M,imageBorderWidthUnit:"px",imageBorderWidthUnitTablet:"px",imageBorderWidthUnitMobile:"px",imageBorderRadius:{top:"4",right:"4",bottom:"0",left:"0"},imageBorderRadiusTablet:M,imageBorderRadiusMobile:M,imageBorderRadiusUnit:"px",imageBorderRadiusUnitTablet:"px",imageBorderRadiusUnitMobile:"px"},Ur=Object.keys(Er),Tr={categoryFontSize:13,categoryFontSizeTablet:null,categoryFontSizeMobile:null,categoryFontSizeUnit:"px",categoryFontSizeUnitTablet:"px",categoryFontSizeUnitMobile:"px",categoryFontWeight:"500",categoryTextTransform:"inherit",categoryFontStyle:"inherit",categoryTextDecoration:"inherit",categoryLineHeight:15,categoryLineHeightTablet:null,categoryLineHeightMobile:null,categoryLineHeightUnit:"px",categoryLineHeightUnitTablet:"px",categoryLineHeightUnitMobile:"px",categoryLetterSpacing:0,categoryLetterSpacingTablet:null,categoryLetterSpacingMobile:null,categoryLetterSpacingUnit:"px",categoryLetterSpacingUnitTablet:"px",categoryLetterSpacingUnitMobile:"px",categoryWordSpacing:0,categoryWordSpacingTablet:null,categoryWordSpacingMobile:null,categoryWordSpacingUnit:"px",categoryWordSpacingUnitTablet:"px",categoryWordSpacingUnitMobile:"px",categoryColor:"#4d5e6f",categoryPadding:M,categoryPaddingTablet:M,categoryPaddingMobile:M,categoryPaddingUnit:"px",categoryPaddingUnitTablet:"px",categoryPaddingUnitMobile:"px",categoryMargin:M,categoryMarginTablet:M,categoryMarginMobile:M,categoryMarginUnit:"px",categoryMarginUnitTablet:"px",categoryMarginUnitMobile:"px"},Mr=Object.keys(Tr),Fr={titleFontSize:15,titleFontSizeTablet:null,titleFontSizeMobile:null,titleFontSizeUnit:"px",titleFontSizeUnitTablet:"px",titleFontSizeUnitMobile:"px",titleFontWeight:"500",titleTextTransform:"inherit",titleFontStyle:"inherit",titleTextDecoration:"inherit",titleLineHeight:18,titleLineHeightTablet:null,titleLineHeightMobile:null,titleLineHeightUnit:"px",titleLineHeightUnitTablet:"px",titleLineHeightUnitMobile:"px",titleLetterSpacing:0,titleLetterSpacingTablet:null,titleLetterSpacingMobile:null,titleLetterSpacingUnit:"px",titleLetterSpacingUnitTablet:"px",titleLetterSpacingUnitMobile:"px",titleWordSpacing:0,titleWordSpacingTablet:null,titleWordSpacingMobile:null,titleWordSpacingUnit:"px",titleWordSpacingUnitTablet:"px",titleWordSpacingUnitMobile:"px",titleColor:"#001931",titlePadding:M,titlePaddingTablet:M,titlePaddingMobile:M,titlePaddingUnit:"px",titlePaddingUnitTablet:"px",titlePaddingUnitMobile:"px",titleMargin:M,titleMarginTablet:M,titleMarginMobile:M,titleMarginUnit:"px",titleMarginUnitTablet:"px",titleMarginUnitMobile:"px"},Pr=Object.keys(Fr),Br={progressFontSize:14,progressFontSizeTablet:null,progressFontSizeMobile:null,progressFontSizeUnit:"px",progressFontSizeUnitTablet:"px",progressFontSizeUnitMobile:"px",progressFontWeight:"500",progressTextTransform:"inherit",progressFontStyle:"inherit",progressTextDecoration:"inherit",progressLineHeight:14,progressLineHeightTablet:null,progressLineHeightMobile:null,progressLineHeightUnit:"px",progressLineHeightUnitTablet:"px",progressLineHeightUnitMobile:"px",progressLetterSpacing:0,progressLetterSpacingTablet:null,progressLetterSpacingMobile:null,progressLetterSpacingUnit:"px",progressLetterSpacingUnitTablet:"px",progressLetterSpacingUnitMobile:"px",progressWordSpacing:0,progressWordSpacingTablet:null,progressWordSpacingMobile:null,progressWordSpacingUnit:"px",progressWordSpacingUnitTablet:"px",progressWordSpacingUnitMobile:"px",progressColor:"",progressEmptyColor:"#b3bac2",progressFilledColor:"#ffa800",progressMargin:M,progressMarginTablet:M,progressMarginMobile:M,progressMarginUnit:"px",progressMarginUnitTablet:"px",progressMarginUnitMobile:"px"},Lr=Object.keys(Br),Hr={dividerHeight:1,dividerHeightUnit:"px",dividerColor:"#DBE0E9"},Wr=Object.keys(Hr),Dr={metaFontSize:14,metaFontSizeTablet:null,metaFontSizeMobile:null,metaFontSizeUnit:"px",metaFontSizeUnitTablet:"px",metaFontSizeUnitMobile:"px",metaFontWeight:"400",metaTextTransform:"inherit",metaFontStyle:"inherit",metaTextDecoration:"inherit",metaLineHeight:14,metaLineHeightTablet:null,metaLineHeightMobile:null,metaLineHeightUnit:"px",metaLineHeightUnitTablet:"px",metaLineHeightUnitMobile:"px",metaLetterSpacing:0,metaLetterSpacingTablet:null,metaLetterSpacingMobile:null,metaLetterSpacingUnit:"px",metaLetterSpacingUnitTablet:"px",metaLetterSpacingUnitMobile:"px",metaWordSpacing:0,metaWordSpacingTablet:null,metaWordSpacingMobile:null,metaWordSpacingUnit:"px",metaWordSpacingUnitTablet:"px",metaWordSpacingUnitMobile:"px",metaColor:"#4D5E6F",metaBgColor:"#eef1f7",metaBorderStyle:"none",metaBorderStyleTablet:"",metaBorderStyleMobile:"",metaBorderColor:"",metaBorderColorTablet:"",metaBorderColorMobile:"",metaBorderWidth:M,metaBorderWidthTablet:M,metaBorderWidthMobile:M,metaBorderWidthUnit:"px",metaBorderWidthUnitTablet:"px",metaBorderWidthUnitMobile:"px",metaBorderRadius:{top:"4",left:"4",bottom:"4",right:"4"},metaBorderRadiusTablet:M,metaBorderRadiusMobile:M,metaBorderRadiusUnit:"px",metaBorderRadiusUnitTablet:"px",metaBorderRadiusUnitMobile:"px",metaMargin:M,metaMarginTablet:M,metaMarginMobile:M,metaMarginUnit:"px",metaMarginUnitTablet:"px",metaMarginUnitMobile:"px",metaPadding:{top:"5",right:"10",bottom:"5",left:"10"},metaPaddingTablet:M,metaPaddingMobile:M,metaPaddingUnit:"px",metaPaddingUnitTablet:"px",metaPaddingUnitMobile:"px"},Ar=Object.keys(Dr),Ir={priceFontSize:15,priceFontSizeTablet:null,priceFontSizeMobile:null,priceFontSizeUnit:"px",priceFontSizeUnitTablet:"px",priceFontSizeUnitMobile:"px",priceFontWeight:"700",priceTextTransform:"inherit",priceFontStyle:"inherit",priceTextDecoration:"inherit",priceLineHeight:15,priceLineHeightTablet:null,priceLineHeightMobile:null,priceLineHeightUnit:"px",priceLineHeightUnitTablet:"px",priceLineHeightUnitMobile:"px",priceLetterSpacing:0,priceLetterSpacingTablet:null,priceLetterSpacingMobile:null,priceLetterSpacingUnit:"px",priceLetterSpacingUnitTablet:"px",priceLetterSpacingUnitMobile:"px",priceWordSpacing:0,priceWordSpacingTablet:null,priceWordSpacingMobile:null,priceWordSpacingUnit:"px",priceWordSpacingUnitTablet:"px",priceWordSpacingUnitMobile:"px",priceColor:"#001931",priceBgColor:"#227aff",specialPriceFontSize:14,specialPriceFontSizeTablet:null,specialPriceFontSizeMobile:null,specialPriceFontSizeUnit:"px",specialPriceFontSizeUnitTablet:"px",specialPriceFontSizeUnitMobile:"px",specialPriceFontWeight:"700",specialPriceTextTransform:"inherit",specialPriceFontStyle:"inherit",specialPriceTextDecoration:"inherit",specialPriceLineHeight:14,specialPriceLineHeightTablet:null,specialPriceLineHeightMobile:null,specialPriceLineHeightUnit:"px",specialPriceLineHeightUnitTablet:"px",specialPriceLineHeightUnitMobile:"px",specialPriceLetterSpacing:0,specialPriceLetterSpacingTablet:null,specialPriceLetterSpacingMobile:null,specialPriceLetterSpacingUnit:"px",specialPriceLetterSpacingUnitTablet:"px",specialPriceLetterSpacingUnitMobile:"px",specialPriceWordSpacing:0,specialPriceWordSpacingTablet:null,specialPriceWordSpacingMobile:null,specialPriceWordSpacingUnit:"px",specialPriceWordSpacingUnitTablet:"px",specialPriceWordSpacingUnitMobile:"px",specialPriceColor:"#001931",oldPriceFontSize:12,oldPriceFontSizeTablet:null,oldPriceFontSizeMobile:null,oldPriceFontSizeUnit:"px",oldPriceFontSizeUnitTablet:"px",oldPriceFontSizeUnitMobile:"px",oldPriceFontWeight:"400",oldPriceTextTransform:"inherit",oldPriceFontStyle:"inherit",oldPriceTextDecoration:"line-through",oldPriceLineHeight:12,oldPriceLineHeightTablet:null,oldPriceLineHeightMobile:null,oldPriceLineHeightUnit:"px",oldPriceLineHeightUnitTablet:"px",oldPriceLineHeightUnitMobile:"px",oldPriceLetterSpacing:0,oldPriceLetterSpacingTablet:null,oldPriceLetterSpacingMobile:null,oldPriceLetterSpacingUnit:"px",oldPriceLetterSpacingUnitTablet:"px",oldPriceLetterSpacingUnitMobile:"px",oldPriceWordSpacing:0,oldPriceWordSpacingTablet:null,oldPriceWordSpacingMobile:null,oldPriceWordSpacingUnit:"px",oldPriceWordSpacingUnitTablet:"px",oldPriceWordSpacingUnitMobile:"px",oldPriceColor:"#4D5E6F",subscripFontSize:14,subscripFontSizeTablet:null,subscripFontSizeMobile:null,subscripFontSizeUnit:"px",subscripFontSizeUnitTablet:"px",subscripFontSizeUnitMobile:"px",subscripFontWeight:"500",subscripTextTransform:"inherit",subscripFontStyle:"inherit",subscripTextDecoration:"inherit",subscripLineHeight:14,subscripLineHeightTablet:null,subscripLineHeightMobile:null,subscripLineHeightUnit:"px",subscripLineHeightUnitTablet:"px",subscripLineHeightUnitMobile:"px",subscripLetterSpacing:0,subscripLetterSpacingTablet:null,subscripLetterSpacingMobile:null,subscripLetterSpacingUnit:"px",subscripLetterSpacingUnitTablet:"px",subscripLetterSpacingUnitMobile:"px",subscripWordSpacing:0,subscripWordSpacingTablet:null,subscripWordSpacingMobile:null,subscripWordSpacingUnit:"px",subscripWordSpacingUnitTablet:"px",subscripWordSpacingUnitMobile:"px",subscripColor:"#4D5E6F",subscripIconColor:"#4D5E6F",subscripBgColor:"#227aff"},zr=Object.keys(Ir),Rr={buttonFontSize:14,buttonFontSizeTablet:null,buttonFontSizeMobile:null,buttonFontSizeUnit:"px",buttonFontSizeUnitTablet:"px",buttonFontSizeUnitMobile:"px",buttonFontWeight:"500",buttonTextTransform:"inherit",buttonFontStyle:"inherit",buttonTextDecoration:"inherit",buttonLineHeight:14,buttonLineHeightTablet:null,buttonLineHeightMobile:null,buttonLineHeightUnit:"px",buttonLineHeightUnitTablet:"px",buttonLineHeightUnitMobile:"px",buttonLetterSpacing:0,buttonLetterSpacingTablet:null,buttonLetterSpacingMobile:null,buttonLetterSpacingUnit:"px",buttonLetterSpacingUnitTablet:"px",buttonLetterSpacingUnitMobile:"px",buttonWordSpacing:0,buttonWordSpacingTablet:null,buttonWordSpacingMobile:null,buttonWordSpacingUnit:"px",buttonWordSpacingUnitTablet:"px",buttonWordSpacingUnitMobile:"px",buttonColor:"#FFFFFF",buttonColorHover:"#FFFFFF",buttonBgColor:"#227AFF",buttonBgColorHover:"#227AFF",buttonBorderStyle:"none",buttonBorderStyleTablet:"",buttonBorderStyleMobile:"",buttonBorderColor:"",buttonBorderColorTablet:"",buttonBorderColorMobile:"",buttonBorderWidth:M,buttonBorderWidthTablet:M,buttonBorderWidthMobile:M,buttonBorderWidthUnit:"px",buttonBorderWidthUnitTablet:"px",buttonBorderWidthUnitMobile:"px",buttonBorderRadius:{top:"5",right:"5",bottom:"5",left:"5"},buttonBorderRadiusTablet:M,buttonBorderRadiusMobile:M,buttonBorderRadiusUnit:"px",buttonBorderRadiusUnitTablet:"px",buttonBorderRadiusUnitMobile:"px"},kr=Object.keys(Rr),Or={statusFeaturedFontSize:13,statusFeaturedFontSizeUnit:"px",statusFeaturedFontWeight:"700",statusFeaturedTextTransform:"uppercase",statusFeaturedFontStyle:"inherit",statusFeaturedTextDecoration:"inherit",statusFeaturedLineHeight:13,statusFeaturedLineHeightUnit:"px",statusFeaturedLetterSpacing:0,statusFeaturedLetterSpacingUnit:"px",statusFeaturedWordSpacing:0,statusFeaturedWordSpacingUnit:"px",statusFeaturedColor:"#ffffff",statusFeaturedBgColor:"#61CC2F",statusHotFontSize:13,statusHotFontSizeUnit:"px",statusHotFontWeight:"700",statusHotTextTransform:"inherit",statusHotFontStyle:"inherit",statusHotTextDecoration:"inherit",statusHotLineHeight:13,statusHotLineHeightUnit:"px",statusHotLetterSpacing:0,statusHotLetterSpacingUnit:"px",statusHotWordSpacing:0,statusHotWordSpacingUnit:"px",statusHotColor:"#ffffff",statusHotBgColor:"#ff3945",statusNewFontSize:13,statusNewFontSizeUnit:"px",statusNewFontWeight:"700",statusNewTextTransform:"inherit",statusNewFontStyle:"inherit",statusNewTextDecoration:"inherit",statusNewLineHeight:13,statusNewLineHeightUnit:"px",statusNewLetterSpacing:0,statusNewLetterSpacingUnit:"px",statusNewWordSpacing:0,statusNewWordSpacingUnit:"px",statusNewColor:"#ffffff",statusNewBgColor:"#61cc2f",statusSpecialFontSize:13,statusSpecialFontSizeUnit:"px",statusSpecialFontWeight:"700",statusSpecialTextTransform:"inherit",statusSpecialFontStyle:"inherit",statusSpecialTextDecoration:"inherit",statusSpecialLineHeight:13,statusSpecialLineHeightUnit:"px",statusSpecialLetterSpacing:0,statusSpecialLetterSpacingUnit:"px",statusSpecialWordSpacing:0,statusSpecialWordSpacingUnit:"px",statusSpecialColor:"#ffffff",statusSpecialBgColor:"#4ed7a8"},Vr=Object.keys(Or),jr={countdownLabelFontSize:null,countdownLabelFontSizeUnit:"px",countdownLabelFontWeight:"",countdownLabelTextTransform:"",countdownLabelFontStyle:"",countdownLabelTextDecoration:"",countdownLabelLineHeight:null,countdownLabelLineHeightUnit:"px",countdownLabelLetterSpacing:null,countdownLabelLetterSpacingUnit:"px",countdownLabelWordSpacing:null,countdownLabelWordSpacingUnit:"px",countdownLabelColor:"",countdownLabelBgColor:"",countdownCounterFontSize:null,countdownCounterFontSizeUnit:"px",countdownCounterFontWeight:"",countdownCounterTextTransform:"",countdownCounterFontStyle:"",countdownCounterTextDecoration:"",countdownCounterLineHeight:null,countdownCounterLineHeightUnit:"px",countdownCounterLetterSpacing:null,countdownCounterLetterSpacingUnit:"px",countdownCounterWordSpacing:null,countdownCounterWordSpacingUnit:"px",countdownCounterColor:"",countdownCounterBackgroundColor:"",countdownCounterBorderRadius:M,countdownCounterBorderRadiusTablet:M,countdownCounterBorderRadiusMobile:M,countdownCounterBorderRadiusUnit:"px",countdownCounterBorderRadiusUnitTablet:"px",countdownCounterBorderRadiusUnitMobile:"px",countdownCounterHeight:null,countdownCounterHeightTablet:null,countdownCounterHeightMobile:null,countdownCounterHeightUnit:"px",countdownCounterHeightUnitTablet:"px",countdownCounterHeightUnitMobile:"px",countdownCounterWidth:null,countdownCounterWidthTablet:null,countdownCounterWidthMobile:null,countdownCounterWidthUnit:"px",countdownCounterWidthUnitTablet:"px",countdownCounterWidthUnitMobile:"px",countdownCounterGap:null,countdownCounterGapTablet:null,countdownCounterGapMobile:null,countdownCounterGapUnit:"px",countdownCounterGapUnitTablet:"px",countdownCounterGapUnitMobile:"px",countdownCounterPadding:M,countdownCounterPaddingTablet:M,countdownCounterPaddingMobile:M,countdownCounterPaddingUnit:"px",countdownCounterPaddingUnitTablet:"px",countdownCounterPaddingUnitMobile:"px",countdownCounterMargin:M,countdownCounterMarginTablet:M,countdownCounterMarginMobile:M,countdownCounterMarginUnit:"px",countdownCounterMarginUnitTablet:"px",countdownCounterMarginUnitMobile:"px"},$r=Object.keys(jr),Zr={popupPadding:{top:"30",right:"30",bottom:"30",left:"30"},popupPaddingTablet:M,popupPaddingMobile:M,popupPaddingUnit:"px",popupPaddingUnitTablet:"px",popupPaddingUnitMobile:"px",popupBg:"#ffffff",popupBorderStyle:"none",popupBorderStyleTablet:"",popupBorderStyleMobile:"",popupBorderColor:"",popupBorderColorTablet:"",popupBorderColorMobile:"",popupBorderWidth:M,popupBorderWidthTablet:M,popupBorderWidthMobile:M,popupBorderWidthUnit:"px",popupBorderWidthUnitTablet:"px",popupBorderWidthUnitMobile:"px",popupBorderRadius:M,popupBorderRadiusTablet:M,popupBorderRadiusMobile:M,popupBorderRadiusUnit:"px",popupBorderRadiusUnitTablet:"px",popupBorderRadiusUnitMobile:"px",popupShadowColor:"#00000033",popupShadowColorTablet:"",popupShadowColorMobile:"",popupShadowHorizontal:0,popupShadowHorizontalTablet:null,popupShadowHorizontalMobile:null,popupShadowVertical:0,popupShadowVerticalTablet:null,popupShadowVerticalMobile:null,popupShadowBlur:30,popupShadowBlurTablet:null,popupShadowBlurMobile:null,popupShadowSpread:0,popupShadowSpreadTablet:null,popupShadowSpreadMobile:null,popupShadowInset:!1,popupShadowInsetTablet:!1,popupShadowInsetMobile:!1},Gr=Object.keys(Zr),Kr={popupIImgMargin:{top:"",right:"5",bottom:"",left:""},popupIImgMarginTablet:M,popupIImgMarginMobile:M,popupIImgMarginUnit:"px",popupIImgMarginUnitTablet:"px",popupIImgMarginUnitMobile:"px",popupIImgSize:24,popupIImgSizeTablet:null,popupIImgSizeMobile:null,popupIImgSizeUnit:"px",popupIImgSizeUnitTablet:"px",popupIImgSizeUnitMobile:"px",popupIImgBorderStyle:"none",popupIImgBorderStyleTablet:"",popupIImgBorderStyleMobile:"",popupIImgBorderColor:"",popupIImgBorderColorTablet:"",popupIImgBorderColorMobile:"",popupIImgBorderWidth:M,popupIImgBorderWidthTablet:M,popupIImgBorderWidthMobile:M,popupIImgBorderWidthUnit:"px",popupIImgBorderWidthUnitTablet:"px",popupIImgBorderWidthUnitMobile:"px",popupIImgBorderRadius:{top:"12",right:"12",bottom:"12",left:"12"},popupIImgBorderRadiusTablet:M,popupIImgBorderRadiusMobile:M,popupIImgBorderRadiusUnit:"px",popupIImgBorderRadiusUnitTablet:"px",popupIImgBorderRadiusUnitMobile:"px"},Xr=Object.keys(Kr),Yr={popupINameFontSize:14,popupINameFontSizeTablet:null,popupINameFontSizeMobile:null,popupINameFontSizeUnit:"px",popupINameFontSizeUnitTablet:"px",popupINameFontSizeUnitMobile:"px",popupINameFontWeight:"500",popupINameTextTransform:"inherit",popupINameFontStyle:"inherit",popupINameTextDecoration:"inherit",popupINameLineHeight:21,popupINameLineHeightTablet:null,popupINameLineHeightMobile:null,popupINameLineHeightUnit:"px",popupINameLineHeightUnitTablet:"px",popupINameLineHeightUnitMobile:"px",popupINameLetterSpacing:0,popupINameLetterSpacingTablet:null,popupINameLetterSpacingMobile:null,popupINameLetterSpacingUnit:"px",popupINameLetterSpacingUnitTablet:"px",popupINameLetterSpacingUnitMobile:"px",popupINameWordSpacing:0,popupINameWordSpacingTablet:null,popupINameWordSpacingMobile:null,popupINameWordSpacingUnit:"px",popupINameWordSpacingUnitTablet:"px",popupINameWordSpacingUnitMobile:"px",popupINameColor:"#4d5e6f"},qr=Object.keys(Yr),Jr={popupTitleFontSize:18,popupTitleFontSizeTablet:null,popupTitleFontSizeMobile:null,popupTitleFontSizeUnit:"px",popupTitleFontSizeUnitTablet:"px",popupTitleFontSizeUnitMobile:"px",popupTitleFontWeight:"500",popupTitleTextTransform:"inherit",popupTitleFontStyle:"inherit",popupTitleTextDecoration:"inherit",popupTitleLineHeight:21,popupTitleLineHeightTablet:null,popupTitleLineHeightMobile:null,popupTitleLineHeightUnit:"px",popupTitleLineHeightUnitTablet:"px",popupTitleLineHeightUnitMobile:"px",popupTitleLetterSpacing:0,popupTitleLetterSpacingTablet:null,popupTitleLetterSpacingMobile:null,popupTitleLetterSpacingUnit:"px",popupTitleLetterSpacingUnitTablet:"px",popupTitleLetterSpacingUnitMobile:"px",popupTitleWordSpacing:0,popupTitleWordSpacingTablet:null,popupTitleWordSpacingMobile:null,popupTitleWordSpacingUnit:"px",popupTitleWordSpacingUnitTablet:"px",popupTitleWordSpacingUnitMobile:"px",popupTitleColor:"#001931",popupTitleMargin:M,popupTitleMarginTablet:M,popupTitleMarginMobile:M,popupTitleMarginUnit:"px",popupTitleMarginUnitTablet:"px",popupTitleMarginUnitMobile:"px",popupTitlePadding:M,popupTitlePaddingTablet:M,popupTitlePaddingMobile:M,popupTitlePaddingUnit:"px",popupTitlePaddingUnitTablet:"px",popupTitlePaddingUnitMobile:"px"},Qr=Object.keys(Jr),eo={popupExcerptFontSize:15,popupExcerptFontSizeTablet:null,popupExcerptFontSizeMobile:null,popupExcerptFontSizeUnit:"px",popupExcerptFontSizeUnitTablet:"px",popupExcerptFontSizeUnitMobile:"px",popupExcerptFontWeight:"400",popupExcerptTextTransform:"inherit",popupExcerptFontStyle:"inherit",popupExcerptTextDecoration:"inherit",popupExcerptLineHeight:21,popupExcerptLineHeightTablet:null,popupExcerptLineHeightMobile:null,popupExcerptLineHeightUnit:"px",popupExcerptLineHeightUnitTablet:"px",popupExcerptLineHeightUnitMobile:"px",popupExcerptLetterSpacing:0,popupExcerptLetterSpacingTablet:null,popupExcerptLetterSpacingMobile:null,popupExcerptLetterSpacingUnit:"px",popupExcerptLetterSpacingUnitTablet:"px",popupExcerptLetterSpacingUnitMobile:"px",popupExcerptWordSpacing:0,popupExcerptWordSpacingTablet:null,popupExcerptWordSpacingMobile:null,popupExcerptWordSpacingUnit:"px",popupExcerptWordSpacingUnitTablet:"px",popupExcerptWordSpacingUnitMobile:"px",popupExcerptColor:"#4d5e6f",popupExcerptMargin:{top:"10",right:"",bottom:"",left:""},popupExcerptMarginTablet:M,popupExcerptMarginMobile:M,popupExcerptMarginUnit:"px",popupExcerptMarginUnitTablet:"px",popupExcerptMarginUnitMobile:"px",popupExcerptPadding:M,popupExcerptPaddingTablet:M,popupExcerptPaddingMobile:M,popupExcerptPaddingUnit:"px",popupExcerptPaddingUnitTablet:"px",popupExcerptPaddingUnitMobile:"px"},to=Object.keys(eo),no={popupMetaFontSize:14,popupMetaFontSizeTablet:null,popupMetaFontSizeMobile:null,popupMetaFontSizeUnit:"px",popupMetaFontSizeUnitTablet:"px",popupMetaFontSizeUnitMobile:"px",popupMetaFontWeight:"400",popupMetaTextTransform:"inherit",popupMetaFontStyle:"inherit",popupMetaTextDecoration:"inherit",popupMetaLineHeight:14,popupMetaLineHeightTablet:null,popupMetaLineHeightMobile:null,popupMetaLineHeightUnit:"px",popupMetaLineHeightUnitTablet:"px",popupMetaLineHeightUnitMobile:"px",popupMetaLetterSpacing:0,popupMetaLetterSpacingTablet:null,popupMetaLetterSpacingMobile:null,popupMetaLetterSpacingUnit:"px",popupMetaLetterSpacingUnitTablet:"px",popupMetaLetterSpacingUnitMobile:"px",popupMetaWordSpacing:0,popupMetaWordSpacingTablet:null,popupMetaWordSpacingMobile:null,popupMetaWordSpacingUnit:"px",popupMetaWordSpacingUnitTablet:"px",popupMetaWordSpacingUnitMobile:"px",popupMetaColor:"#4D5E6F",popupMetaBgColor:"",popupMetaBorderStyle:"none",popupMetaBorderStyleTablet:"",popupMetaBorderStyleMobile:"",popupMetaBorderColor:"",popupMetaBorderColorTablet:"",popupMetaBorderColorMobile:"",popupMetaBorderWidth:M,popupMetaBorderWidthTablet:M,popupMetaBorderWidthMobile:M,popupMetaBorderWidthUnit:"px",popupMetaBorderWidthUnitTablet:"px",popupMetaBorderWidthUnitMobile:"px",popupMetaBorderRadius:M,popupMetaBorderRadiusTablet:M,popupMetaBorderRadiusMobile:M,popupMetaBorderRadiusUnit:"px",popupMetaBorderRadiusUnitTablet:"px",popupMetaBorderRadiusUnitMobile:"px",popupMetaMargin:{top:"10",right:"",bottom:"",left:""},popupMetaMarginTablet:M,popupMetaMarginMobile:M,popupMetaMarginUnit:"px",popupMetaMarginUnitTablet:"px",popupMetaMarginUnitMobile:"px",popupMetaPadding:M,popupMetaPaddingTablet:M,popupMetaPaddingMobile:M,popupMetaPaddingUnit:"px",popupMetaPaddingUnitTablet:"px",popupMetaPaddingUnitMobile:"px"},io=Object.keys(no),ao={popupPriceFontSize:15,popupPriceFontSizeTablet:null,popupPriceFontSizeMobile:null,popupPriceFontSizeUnit:"px",popupPriceFontSizeUnitTablet:"px",popupPriceFontSizeUnitMobile:"px",popupPriceFontWeight:"600",popupPriceTextTransform:"inherit",popupPriceFontStyle:"inherit",popupPriceTextDecoration:"inherit",popupPriceLineHeight:15,popupPriceLineHeightTablet:null,popupPriceLineHeightMobile:null,popupPriceLineHeightUnit:"px",popupPriceLineHeightUnitTablet:"px",popupPriceLineHeightUnitMobile:"px",popupPriceLetterSpacing:0,popupPriceLetterSpacingTablet:null,popupPriceLetterSpacingMobile:null,popupPriceLetterSpacingUnit:"px",popupPriceLetterSpacingUnitTablet:"px",popupPriceLetterSpacingUnitMobile:"px",popupPriceWordSpacing:0,popupPriceWordSpacingTablet:null,popupPriceWordSpacingMobile:null,popupPriceWordSpacingUnit:"px",popupPriceWordSpacingUnitTablet:"px",popupPriceWordSpacingUnitMobile:"px",popupPriceColor:"#001931",popupSpecialPriceFontSize:14,popupSpecialPriceFontSizeTablet:null,popupSpecialPriceFontSizeMobile:null,popupSpecialPriceFontSizeUnit:"px",popupSpecialPriceFontSizeUnitTablet:"px",popupSpecialPriceFontSizeUnitMobile:"px",popupSpecialPriceFontWeight:"700",popupSpecialPriceTextTransform:"inherit",popupSpecialPriceFontStyle:"inherit",popupSpecialPriceTextDecoration:"inherit",popupSpecialPriceLineHeight:14,popupSpecialPriceLineHeightTablet:null,popupSpecialPriceLineHeightMobile:null,popupSpecialPriceLineHeightUnit:"px",popupSpecialPriceLineHeightUnitTablet:"px",popupSpecialPriceLineHeightUnitMobile:"px",popupSpecialPriceLetterSpacing:0,popupSpecialPriceLetterSpacingTablet:null,popupSpecialPriceLetterSpacingMobile:null,popupSpecialPriceLetterSpacingUnit:"px",popupSpecialPriceLetterSpacingUnitTablet:"px",popupSpecialPriceLetterSpacingUnitMobile:"px",popupSpecialPriceWordSpacing:0,popupSpecialPriceWordSpacingTablet:null,popupSpecialPriceWordSpacingMobile:null,popupSpecialPriceWordSpacingUnit:"px",popupSpecialPriceWordSpacingUnitTablet:"px",popupSpecialPriceWordSpacingUnitMobile:"px",popupSpecialPriceColor:"#001931",popupOldPriceFontSize:14,popupOldPriceFontSizeTablet:null,popupOldPriceFontSizeMobile:null,popupOldPriceFontSizeUnit:"px",popupOldPriceFontSizeUnitTablet:"px",popupOldPriceFontSizeUnitMobile:"px",popupOldPriceFontWeight:"500",popupOldPriceTextTransform:"inherit",popupOldPriceFontStyle:"inherit",popupOldPriceTextDecoration:"line-through",popupOldPriceLineHeight:14,popupOldPriceLineHeightTablet:null,popupOldPriceLineHeightMobile:null,popupOldPriceLineHeightUnit:"px",popupOldPriceLineHeightUnitTablet:"px",popupOldPriceLineHeightUnitMobile:"px",popupOldPriceLetterSpacing:0,popupOldPriceLetterSpacingTablet:null,popupOldPriceLetterSpacingMobile:null,popupOldPriceLetterSpacingUnit:"px",popupOldPriceLetterSpacingUnitTablet:"px",popupOldPriceLetterSpacingUnitMobile:"px",popupOldPriceWordSpacing:0,popupOldPriceWordSpacingTablet:null,popupOldPriceWordSpacingMobile:null,popupOldPriceWordSpacingUnit:"px",popupOldPriceWordSpacingUnitTablet:"px",popupOldPriceWordSpacingUnitMobile:"px",popupOldPriceColor:"#4D5E6F",subscriptionFontSize:14,subscriptionFontSizeTablet:null,subscriptionFontSizeMobile:null,subscriptionFontSizeUnit:"px",subscriptionFontSizeUnitTablet:"px",subscriptionFontSizeUnitMobile:"px",subscriptionFontWeight:"500",subscriptionTextTransform:"inherit",subscriptionFontStyle:"inherit",subscriptionTextDecoration:"inherit",subscriptionLineHeight:14,subscriptionLineHeightTablet:null,subscriptionLineHeightMobile:null,subscriptionLineHeightUnit:"px",subscriptionLineHeightUnitTablet:"px",subscriptionLineHeightUnitMobile:"px",subscriptionLetterSpacing:0,subscriptionLetterSpacingTablet:null,subscriptionLetterSpacingMobile:null,subscriptionLetterSpacingUnit:"px",subscriptionLetterSpacingUnitTablet:"px",subscriptionLetterSpacingUnitMobile:"px",subscriptionWordSpacing:0,subscriptionWordSpacingTablet:null,subscriptionWordSpacingMobile:null,subscriptionWordSpacingUnit:"px",subscriptionWordSpacingUnitTablet:"px",subscriptionWordSpacingUnitMobile:"px",subscriptionColor:"#4D5E6F",subscriptionIconColor:"#4D5E6F"},ro=Object.keys(ao),oo={wishlistFontSize:14,wishlistFontSizeTablet:null,wishlistFontSizeMobile:null,wishlistFontSizeUnit:"px",wishlistFontSizeUnitTablet:"px",wishlistFontSizeUnitMobile:"px",wishlistFontWeight:"500",wishlistTextTransform:"inherit",wishlistFontStyle:"inherit",wishlistTextDecoration:"inherit",wishlistLineHeight:14,wishlistLineHeightTablet:null,wishlistLineHeightMobile:null,wishlistLineHeightUnit:"px",wishlistLineHeightUnitTablet:"px",wishlistLineHeightUnitMobile:"px",wishlistLetterSpacing:0,wishlistLetterSpacingTablet:null,wishlistLetterSpacingMobile:null,wishlistLetterSpacingUnit:"px",wishlistLetterSpacingUnitTablet:"px",wishlistLetterSpacingUnitMobile:"px",wishlistWordSpacing:0,wishlistWordSpacingTablet:null,wishlistWordSpacingMobile:null,wishlistWordSpacingUnit:"px",wishlistWordSpacingUnitTablet:"px",wishlistWordSpacingUnitMobile:"px",wishlistColor:"#4D5E6F",wishlistIconEmptyColor:"#4D5E6F",wishlistIconFilledColor:"#ff1f59",wishlistLoadedColorColor:""},lo=Object.keys(oo),so={popupButtonFontSize:14,popupButtonFontSizeTablet:null,popupButtonFontSizeMobile:null,popupButtonFontSizeUnit:"px",popupButtonFontSizeUnitTablet:"px",popupButtonFontSizeUnitMobile:"px",popupButtonFontWeight:"500",popupButtonTextTransform:"inherit",popupButtonFontStyle:"inherit",popupButtonTextDecoration:"inherit",popupButtonLineHeight:14,popupButtonLineHeightTablet:null,popupButtonLineHeightMobile:null,popupButtonLineHeightUnit:"px",popupButtonLineHeightUnitTablet:"px",popupButtonLineHeightUnitMobile:"px",popupButtonLetterSpacing:0,popupButtonLetterSpacingTablet:null,popupButtonLetterSpacingMobile:null,popupButtonLetterSpacingUnit:"px",popupButtonLetterSpacingUnitTablet:"px",popupButtonLetterSpacingUnitMobile:"px",popupButtonWordSpacing:0,popupButtonWordSpacingTablet:null,popupButtonWordSpacingMobile:null,popupButtonWordSpacingUnit:"px",popupButtonWordSpacingUnitTablet:"px",popupButtonWordSpacingUnitMobile:"px",popupButtonColor:"#FFFFFF",popupButtonColorHover:"#FFFFFF",popupButtonBgColor:"#227AFF",popupButtonBgColorHover:"#227AFF",popupButtonBorderStyle:"none",popupButtonBorderStyleHover:"",popupButtonBorderStyleTablet:"",popupButtonBorderStyleHoverTablet:"",popupButtonBorderStyleMobile:"",popupButtonBorderStyleHoverMobile:"",popupButtonBorderColor:"",popupButtonBorderColorHover:"",popupButtonBorderColorTablet:"",popupButtonBorderColorHoverTablet:"",popupButtonBorderColorMobile:"",popupButtonBorderColorHoverMobile:"",popupButtonBorderWidth:M,popupButtonBorderWidthHover:M,popupButtonBorderWidthTablet:M,popupButtonBorderWidthHoverTablet:M,popupButtonBorderWidthMobile:M,popupButtonBorderWidthHoverMobile:M,popupButtonBorderWidthUnit:"px",popupButtonBorderWidthUnitTablet:"px",popupButtonBorderWidthUnitMobile:"px",popupButtonBorderRadius:{top:"5",right:"5",bottom:"5",left:"5"},popupButtonBorderRadiusTablet:M,popupButtonBorderRadiusMobile:M,popupButtonBorderRadiusUnit:"px",popupButtonBorderRadiusUnitTablet:"px",popupButtonBorderRadiusUnitMobile:"px",popupButtonMargin:{top:"10",right:"",bottom:"",left:""},popupButtonMarginTablet:M,popupButtonMarginMobile:M,popupButtonMarginUnit:"px",popupButtonMarginUnitTablet:"px",popupButtonMarginUnitMobile:"px",popupButtonPadding:{top:"11",right:"20",bottom:"11",left:"20"},popupButtonPaddingTablet:M,popupButtonPaddingMobile:M,popupButtonPaddingUnit:"px",popupButtonPaddingUnitTablet:"px",popupButtonPaddingUnitMobile:"px"},po=Object.keys(so),co={...{...Sr},...{...fr,..._r,...Nr,...Er,...Tr,...Fr,...Br,...Hr,...Dr,...Ir,...Rr,...Or,...jr,...Zr,...Kr,...Yr,...Jr,...eo,...no,...so,...oo,...ao}},uo=new Map([["cardGap",{unit:"cardGapUnit",isAdaptive:!0}],["cntrMargin",{isAdaptive:!0,unit:"cntrMarginUnit"}],["cntrPadding",{isAdaptive:!0,unit:"cntrPaddingUnit"}],["cardPadding",{isAdaptive:!0,unit:"cardPaddingUnit"}],["cardBgColor",{}],["cardBorderStyle",{isAdaptive:!0}],["cardBorderColor",{isAdaptive:!0}],["cardBorderWidth",{isAdaptive:!0,unit:"cardBorderWidthUnit"}],["cardBorderRadius",{isAdaptive:!0,unit:"cardBorderRadiusUnit"}],["ibPadding",{isAdaptive:!0,unit:"ibPaddingUnit"}],["ibBgColor",{}],["ibBorderStyle",{isAdaptive:!0}],["ibBorderColor",{isAdaptive:!0}],["ibBorderWidth",{isAdaptive:!0,unit:"ibBorderWidthUnit"}],["ibBorderRadius",{isAdaptive:!0,unit:"ibBorderRadiusUnit"}],["imageHeight",{isAdaptive:!0,unit:"imageHeightUnit"}],["imageBorderStyle",{isAdaptive:!0}],["imageBorderColor",{isAdaptive:!0}],["imageBorderWidth",{isAdaptive:!0,unit:"imageBorderWidthUnit"}],["imageBorderRadius",{isAdaptive:!0,unit:"imageBorderRadiusUnit"}],["categoryFontSize",{unit:"categoryFontSizeUnit",isAdaptive:!0}],["categoryFontWeight",{}],["categoryTextTransform",{}],["categoryFontStyle",{}],["categoryTextDecoration",{}],["categoryLineHeight",{unit:"categoryLineHeightUnit",isAdaptive:!0}],["categoryLetterSpacing",{unit:"categoryLetterSpacingUnit",isAdaptive:!0}],["categoryWordSpacing",{unit:"categoryWordSpacingUnit",isAdaptive:!0}],["categoryColor",{}],["categoryPadding",{unit:"categoryPaddingUnit",isAdaptive:!0}],["categoryMargin",{unit:"categoryMarginUnit",isAdaptive:!0}],["titleFontSize",{unit:"titleFontSizeUnit",isAdaptive:!0}],["titleFontWeight",{}],["titleTextTransform",{}],["titleFontStyle",{}],["titleTextDecoration",{}],["titleLineHeight",{unit:"titleLineHeightUnit",isAdaptive:!0}],["titleLetterSpacing",{unit:"titleLetterSpacingUnit",isAdaptive:!0}],["titleWordSpacing",{unit:"titleWordSpacingUnit",isAdaptive:!0}],["titleColor",{}],["titlePadding",{unit:"titlePaddingUnit",isAdaptive:!0}],["titleMargin",{unit:"titleMarginUnit",isAdaptive:!0}],["progressFontSize",{unit:"progressFontSizeUnit",isAdaptive:!0}],["progressFontWeight",{}],["progressTextTransform",{}],["progressFontStyle",{}],["progressTextDecoration",{}],["progressLineHeight",{unit:"progressLineHeightUnit",isAdaptive:!0}],["progressLetterSpacing",{unit:"progressLetterSpacingUnit",isAdaptive:!0}],["progressWordSpacing",{unit:"progressWordSpacingUnit",isAdaptive:!0}],["progressColor",{}],["progressEmptyColor",{}],["progressFilledColor",{}],["progressMargin",{unit:"progressMarginUnit",isAdaptive:!0}],["metaFontSize",{unit:"metaFontSizeUnit",isAdaptive:!0}],["metaFontWeight",{}],["metaTextTransform",{}],["metaFontStyle",{}],["metaTextDecoration",{}],["metaLineHeight",{unit:"metaLineHeightUnit",isAdaptive:!0}],["metaLetterSpacing",{unit:"metaLetterSpacingUnit",isAdaptive:!0}],["metaWordSpacing",{unit:"metaWordSpacingUnit",isAdaptive:!0}],["metaColor",{}],["metaBgColor",{}],["metaBorderStyle",{isAdaptive:!0}],["metaBorderColor",{isAdaptive:!0}],["metaBorderWidth",{isAdaptive:!0,unit:"metaBorderWidthUnit"}],["metaBorderRadius",{isAdaptive:!0,unit:"metaBorderRadiusUnit"}],["metaMargin",{unit:"metaMarginUnit",isAdaptive:!0}],["metaPadding",{unit:"metaPaddingUnit",isAdaptive:!0}],["dividerHeight",{unit:"dividerHeightUnit"}],["dividerColor",{}],["priceFontSize",{unit:"priceFontSizeUnit",isAdaptive:!0}],["priceFontWeight",{}],["priceTextTransform",{}],["priceFontStyle",{}],["priceTextDecoration",{}],["priceLineHeight",{unit:"priceLineHeightUnit",isAdaptive:!0}],["priceLetterSpacing",{unit:"priceLetterSpacingUnit",isAdaptive:!0}],["priceWordSpacing",{unit:"priceWordSpacingUnit",isAdaptive:!0}],["priceColor",{}],["priceBgColor",{}],["specialPriceFontSize",{unit:"specialPriceFontSizeUnit",isAdaptive:!0}],["specialPriceFontWeight",{}],["specialPriceTextTransform",{}],["specialPriceFontStyle",{}],["specialPriceTextDecoration",{}],["specialPriceLineHeight",{unit:"specialPriceLineHeightUnit",isAdaptive:!0}],["specialPriceLetterSpacing",{unit:"specialPriceLetterSpacingUnit",isAdaptive:!0}],["specialPriceWordSpacing",{unit:"specialPriceWordSpacingUnit",isAdaptive:!0}],["specialPriceColor",{}],["oldPriceFontSize",{unit:"oldPriceFontSizeUnit",isAdaptive:!0}],["oldPriceFontWeight",{}],["oldPriceTextTransform",{}],["oldPriceFontStyle",{}],["oldPriceTextDecoration",{}],["oldPriceLineHeight",{unit:"oldPriceLineHeightUnit",isAdaptive:!0}],["oldPriceLetterSpacing",{unit:"oldPriceLetterSpacingUnit",isAdaptive:!0}],["oldPriceWordSpacing",{unit:"oldPriceWordSpacingUnit",isAdaptive:!0}],["oldPriceColor",{}],["subscripFontSize",{unit:"subscripFontSizeUnit",isAdaptive:!0}],["subscripFontWeight",{}],["subscripTextTransform",{}],["subscripFontStyle",{}],["subscripTextDecoration",{}],["subscripLineHeight",{unit:"subscripLineHeightUnit",isAdaptive:!0}],["subscripLetterSpacing",{unit:"subscripLetterSpacingUnit",isAdaptive:!0}],["subscripWordSpacing",{unit:"subscripWordSpacingUnit",isAdaptive:!0}],["subscripColor",{}],["subscripIconColor",{}],["subscripBgColor",{}],["buttonFontSize",{unit:"buttonFontSizeUnit",isAdaptive:!0}],["buttonFontWeight",{}],["buttonTextTransform",{}],["buttonFontStyle",{}],["buttonTextDecoration",{}],["buttonLineHeight",{unit:"buttonLineHeightUnit",isAdaptive:!0}],["buttonLetterSpacing",{unit:"buttonLetterSpacingUnit",isAdaptive:!0}],["buttonWordSpacing",{unit:"buttonWordSpacingUnit",isAdaptive:!0}],["buttonColor",{hasHover:!0}],["buttonBgColor",{hasHover:!0}],["buttonBorderStyle",{isAdaptive:!0}],["buttonBorderColor",{isAdaptive:!0}],["buttonBorderWidth",{isAdaptive:!0,unit:"buttonBorderWidthUnit"}],["buttonBorderRadius",{isAdaptive:!0,unit:"buttonBorderRadiusUnit"}],["statusFeaturedFontSize",{unit:"statusFeaturedFontSizeUnit"}],["statusFeaturedFontWeight",{}],["statusFeaturedTextTransform",{}],["statusFeaturedFontStyle",{}],["statusFeaturedTextDecoration",{}],["statusFeaturedLineHeight",{unit:"statusFeaturedLineHeightUnit"}],["statusFeaturedLetterSpacing",{unit:"statusFeaturedLetterSpacingUnit"}],["statusFeaturedWordSpacing",{unit:"statusFeaturedWordSpacingUnit"}],["statusFeaturedColor",{}],["statusFeaturedBgColor",{}],["statusHotFontSize",{unit:"statusHotFontSizeUnit"}],["statusHotFontWeight",{}],["statusHotTextTransform",{}],["statusHotFontStyle",{}],["statusHotTextDecoration",{}],["statusHotLineHeight",{unit:"statusHotLineHeightUnit"}],["statusHotLetterSpacing",{unit:"statusHotLetterSpacingUnit"}],["statusHotWordSpacing",{unit:"statusHotWordSpacingUnit"}],["statusHotColor",{}],["statusHotBgColor",{}],["statusNewFontSize",{unit:"statusNewFontSizeUnit"}],["statusNewFontWeight",{}],["statusNewTextTransform",{}],["statusNewFontStyle",{}],["statusNewTextDecoration",{}],["statusNewLineHeight",{unit:"statusNewLineHeightUnit"}],["statusNewLetterSpacing",{unit:"statusNewLetterSpacingUnit"}],["statusNewWordSpacing",{unit:"statusNewWordSpacingUnit"}],["statusNewColor",{}],["statusNewBgColor",{}],["statusSpecialFontSize",{unit:"statusSpecialFontSizeUnit"}],["statusSpecialFontWeight",{}],["statusSpecialTextTransform",{}],["statusSpecialFontStyle",{}],["statusSpecialTextDecoration",{}],["statusSpecialLineHeight",{unit:"statusSpecialLineHeightUnit"}],["statusSpecialLetterSpacing",{unit:"statusSpecialLetterSpacingUnit"}],["statusSpecialWordSpacing",{unit:"statusSpecialWordSpacingUnit"}],["statusSpecialColor",{}],["statusSpecialBgColor",{}],["countdownLabelFontSize",{unit:"countdownLabelFontSizeUnit"}],["countdownLabelFontWeight",{}],["countdownLabelTextTransform",{}],["countdownLabelFontStyle",{}],["countdownLabelTextDecoration",{}],["countdownLabelLineHeight",{unit:"countdownLabelLineHeightUnit"}],["countdownLabelLetterSpacing",{unit:"countdownLabelLetterSpacingUnit"}],["countdownLabelWordSpacing",{unit:"countdownLabelWordSpacingUnit"}],["countdownLabelColor",{}],["countdownLabelBgColor",{}],["countdownCounterFontSize",{unit:"countdownCounterFontSizeUnit"}],["countdownCounterFontWeight",{}],["countdownCounterTextTransform",{}],["countdownCounterFontStyle",{}],["countdownCounterTextDecoration",{}],["countdownCounterLineHeight",{unit:"countdownCounterLineHeightUnit"}],["countdownCounterLetterSpacing",{unit:"countdownCounterLetterSpacingUnit"}],["countdownCounterWordSpacing",{unit:"countdownCounterWordSpacingUnit"}],["countdownCounterColor",{}],["countdownCounterBackgroundColor",{}],["countdownCounterHeight",{unit:"countdownCounterHeightUnit",isAdaptive:!0}],["countdownCounterWidth",{unit:"countdownCounterWidthUnit",isAdaptive:!0}],["countdownCounterBorderRadius",{isAdaptive:!0,unit:"countdownCounterBorderRadiusUnit"}],["countdownCounterGap",{unit:"countdownCounterGapUnit",isAdaptive:!0}],["countdownCounterPadding",{unit:"countdownCounterPaddingUnit",isAdaptive:!0}],["countdownCounterMargin",{unit:"countdownCounterMarginUnit",isAdaptive:!0}],["popupPadding",{unit:"popupPaddingUnit",isAdaptive:!0}],["popupBg",{}],["popupBorderStyle",{isAdaptive:!0}],["popupBorderColor",{isAdaptive:!0}],["popupBorderWidth",{isAdaptive:!0,unit:"popupBorderWidthUnit"}],["popupBorderRadius",{isAdaptive:!0,unit:"popupBorderRadiusUnit"}],["popupIImgMargin",{unit:"popupIImgMarginUnit",isAdaptive:!0}],["popupIImgSize",{unit:"popupIImgSizeUnit",isAdaptive:!0}],["popupIImgBorderStyle",{isAdaptive:!0}],["popupIImgBorderColor",{isAdaptive:!0}],["popupIImgBorderWidth",{isAdaptive:!0,unit:"popupIImgBorderWidthUnit"}],["popupIImgBorderRadius",{isAdaptive:!0,unit:"popupIImgBorderRadiusUnit"}],["popupINameFontSize",{unit:"popupINameFontSizeUnit",isAdaptive:!0}],["popupINameFontWeight",{}],["popupINameTextTransform",{}],["popupINameFontStyle",{}],["popupINameTextDecoration",{}],["popupINameLineHeight",{unit:"popupINameLineHeightUnit",isAdaptive:!0}],["popupINameLetterSpacing",{unit:"popupINameLetterSpacingUnit",isAdaptive:!0}],["popupINameWordSpacing",{unit:"popupINameWordSpacingUnit",isAdaptive:!0}],["popupINameColor",{}],["popupTitleFontSize",{unit:"popupTitleFontSizeUnit",isAdaptive:!0}],["popupTitleFontWeight",{}],["popupTitleTextTransform",{}],["popupTitleFontStyle",{}],["popupTitleTextDecoration",{}],["popupTitleLineHeight",{unit:"popupTitleLineHeightUnit",isAdaptive:!0}],["popupTitleLetterSpacing",{unit:"popupTitleLetterSpacingUnit",isAdaptive:!0}],["popupTitleWordSpacing",{unit:"popupTitleWordSpacingUnit",isAdaptive:!0}],["popupTitleColor",{}],["popupTitleMargin",{unit:"popupTitleMarginUnit",isAdaptive:!0}],["popupTitlePadding",{unit:"popupTitlePaddingUnit",isAdaptive:!0}],["popupExcerptFontSize",{unit:"popupExcerptFontSizeUnit",isAdaptive:!0}],["popupExcerptFontWeight",{}],["popupExcerptTextTransform",{}],["popupExcerptFontStyle",{}],["popupExcerptTextDecoration",{}],["popupExcerptLineHeight",{unit:"popupExcerptLineHeightUnit",isAdaptive:!0}],["popupExcerptLetterSpacing",{unit:"popupExcerptLetterSpacingUnit",isAdaptive:!0}],["popupExcerptWordSpacing",{unit:"popupExcerptWordSpacingUnit",isAdaptive:!0}],["popupExcerptColor",{}],["popupExcerptMargin",{unit:"popupExcerptMarginUnit",isAdaptive:!0}],["popupExcerptPadding",{unit:"popupExcerptPaddingUnit",isAdaptive:!0}],["popupMetaFontSize",{unit:"popupMetaFontSizeUnit",isAdaptive:!0}],["popupMetaFontWeight",{}],["popupMetaTextTransform",{}],["popupMetaFontStyle",{}],["popupMetaTextDecoration",{}],["popupMetaLineHeight",{unit:"popupMetaLineHeightUnit",isAdaptive:!0}],["popupMetaLetterSpacing",{unit:"popupMetaLetterSpacingUnit",isAdaptive:!0}],["popupMetaWordSpacing",{unit:"popupMetaWordSpacingUnit",isAdaptive:!0}],["popupMetaColor",{}],["popupMetaBgColor",{}],["popupMetaBorderStyle",{isAdaptive:!0}],["popupMetaBorderColor",{isAdaptive:!0}],["popupMetaBorderWidth",{isAdaptive:!0,unit:"popupMetaBorderWidthUnit"}],["popupMetaBorderRadius",{isAdaptive:!0,unit:"popupMetaBorderRadiusUnit"}],["popupMetaMargin",{unit:"popupMetaMarginUnit",isAdaptive:!0}],["popupMetaPadding",{unit:"popupMetaPaddingUnit",isAdaptive:!0}],["popupButtonFontSize",{unit:"popupButtonFontSizeUnit",isAdaptive:!0}],["popupButtonFontWeight",{}],["popupButtonTextTransform",{}],["popupButtonFontStyle",{}],["popupButtonTextDecoration",{}],["popupButtonLineHeight",{unit:"popupButtonLineHeightUnit",isAdaptive:!0}],["popupButtonLetterSpacing",{unit:"popupButtonLetterSpacingUnit",isAdaptive:!0}],["popupButtonWordSpacing",{unit:"popupButtonWordSpacingUnit",isAdaptive:!0}],["popupButtonColor",{hasHover:!0}],["popupButtonBgColor",{hasHover:!0}],["popupButtonBorderStyle",{isAdaptive:!0,hasHover:!0}],["popupButtonBorderColor",{isAdaptive:!0,hasHover:!0}],["popupButtonBorderWidth",{isAdaptive:!0,hasHover:!0,unit:"popupButtonBorderWidthUnit"}],["popupButtonBorderRadius",{isAdaptive:!0,unit:"popupButtonBorderRadiusUnit"}],["popupButtonMargin",{unit:"popupButtonMarginUnit",isAdaptive:!0}],["popupButtonPadding",{unit:"popupButtonPaddingUnit",isAdaptive:!0}],["wishlistFontSize",{unit:"wishlistFontSizeUnit",isAdaptive:!0}],["wishlistFontWeight",{}],["wishlistTextTransform",{}],["wishlistFontStyle",{}],["wishlistTextDecoration",{}],["wishlistLineHeight",{unit:"wishlistLineHeightUnit",isAdaptive:!0}],["wishlistLetterSpacing",{unit:"wishlistLetterSpacingUnit",isAdaptive:!0}],["wishlistWordSpacing",{unit:"wishlistWordSpacingUnit",isAdaptive:!0}],["wishlistColor",{}],["wishlistIconEmptyColor",{}],["wishlistIconFilledColor",{}],["wishlistLoadedColorColor",{}],["popupPriceFontSize",{unit:"popupPriceFontSizeUnit",isAdaptive:!0}],["popupPriceFontWeight",{}],["popupPriceTextTransform",{}],["popupPriceFontStyle",{}],["popupPriceTextDecoration",{}],["popupPriceLineHeight",{unit:"popupPriceLineHeightUnit",isAdaptive:!0}],["popupPriceLetterSpacing",{unit:"popupPriceLetterSpacingUnit",isAdaptive:!0}],["popupPriceWordSpacing",{unit:"popupPriceWordSpacingUnit",isAdaptive:!0}],["popupPriceColor",{}],["popupSpecialPriceFontSize",{unit:"popupSpecialPriceFontSizeUnit",isAdaptive:!0}],["popupSpecialPriceFontWeight",{}],["popupSpecialPriceTextTransform",{}],["popupSpecialPriceFontStyle",{}],["popupSpecialPriceTextDecoration",{}],["popupSpecialPriceLineHeight",{unit:"popupSpecialPriceLineHeightUnit",isAdaptive:!0}],["popupSpecialPriceLetterSpacing",{unit:"popupSpecialPriceLetterSpacingUnit",isAdaptive:!0}],["popupSpecialPriceWordSpacing",{unit:"popupSpecialPriceWordSpacingUnit",isAdaptive:!0}],["popupSpecialPriceColor",{}],["popupOldPriceFontSize",{unit:"popupOldPriceFontSizeUnit",isAdaptive:!0}],["popupOldPriceFontWeight",{}],["popupOldPriceTextTransform",{}],["popupOldPriceFontStyle",{}],["popupOldPriceTextDecoration",{}],["popupOldPriceLineHeight",{unit:"popupOldPriceLineHeightUnit",isAdaptive:!0}],["popupOldPriceLetterSpacing",{unit:"popupOldPriceLetterSpacingUnit",isAdaptive:!0}],["popupOldPriceWordSpacing",{unit:"popupOldPriceWordSpacingUnit",isAdaptive:!0}],["popupOldPriceColor",{}],["subscriptionFontSize",{unit:"subscriptionFontSizeUnit",isAdaptive:!0}],["subscriptionFontWeight",{}],["subscriptionTextTransform",{}],["subscriptionFontStyle",{}],["subscriptionTextDecoration",{}],["subscriptionLineHeight",{unit:"subscriptionLineHeightUnit",isAdaptive:!0}],["subscriptionLetterSpacing",{unit:"subscriptionLetterSpacingUnit",isAdaptive:!0}],["subscriptionWordSpacing",{unit:"subscriptionWordSpacingUnit",isAdaptive:!0}],["subscriptionColor",{}],["subscriptionIconColor",{}]]),mo=["masterstudy/courses-preset-classic","masterstudy/courses-preset-price-accent"],go=n.p+"images/preview-classic.7d6cd04d.png",ho=n.p+"images/preview-price-accent.113ec30f.png",bo=n.p+"images/preview-price-button.1b3e5c55.png",yo=n.p+"images/preview-full-size-image.6e6b6758.png",So=[{name:"classic",title:i.__("Classic","masterstudy-lms-learning-management-system"),description:i.__("Displays Courses Grid Classic","masterstudy-lms-learning-management-system"),icon:(0,r.createElement)("img",{src:go,alt:"Classic"}),innerBlocks:[["masterstudy/courses-preset-classic"]],attributes:{selectDataslot1:"lectures",selectDataslot2:"duration",showPopup:!0,showDivider:!1,showRating:!0,cardItems:[{id:"card-category",label:i.__("Category","masterstudy-lms-learning-management-system"),switchFieldName:"showCategory",selectFieldName:""},{id:"card-title",label:i.__("Title","masterstudy-lms-learning-management-system"),switchFieldName:"",selectFieldName:""},{id:"card-meta",label:i.__("Dataslots","masterstudy-lms-learning-management-system"),switchFieldName:"",selectFieldName:"",children:[{id:"card-dataslot-1",label:i.__("Dataslot 1","masterstudy-lms-learning-management-system"),switchFieldName:"",selectFieldName:"selectDataslot1"},{id:"card-dataslot-2",label:i.__("Dataslot 2","masterstudy-lms-learning-management-system"),switchFieldName:"",selectFieldName:"selectDataslot2"}]},{id:"card-divider",label:i.__("Divider","masterstudy-lms-learning-management-system"),switchFieldName:"showDivider",selectFieldName:""},{id:"card-info",label:i.__("Price and Rating","masterstudy-lms-learning-management-system"),switchFieldName:"",selectFieldName:"",children:[{id:"card-rating",label:i.__("Rating","masterstudy-lms-learning-management-system"),switchFieldName:"showRating",selectFieldName:""},{id:"card-price",label:i.__("Price","masterstudy-lms-learning-management-system"),switchFieldName:"showPrice",selectFieldName:""}]}],priceColor:"#001931",specialPriceColor:"#001931",oldPriceColor:"#4D5E6F",subscripColor:"#4D5E6F",subscripIconColor:"#4D5E6F",countdownLabelColor:"",countdownCounterColor:"",countdownCounterBackgroundColor:"",categoryColor:"#4d5e6f",titleColor:"#001931",cardBorderRadius:{top:"4",right:"4",bottom:"4",left:"4"},imageHeight:150,imageBorderRadius:{top:"4",right:"4",bottom:"0",left:"0"},ibPadding:{top:"15",right:"20",bottom:"15",left:"20"},oldPriceFontSize:12,popupExcerptColor:co.popupExcerptColor,progressColor:co.progressColor,progressMargin:co.progressMargin,showPopupWishlist:co.showPopupWishlist,wishlistIconEmptyColor:co.wishlistIconEmptyColor,metaMargin:co.metaMargin,metaPadding:co.metaPadding,metaBgColor:co.metaBgColor,metaColor:co.metaColor},scope:["block"]},{name:"price-accent",title:i.__("Price Accent","masterstudy-lms-learning-management-system"),description:i.__("Displays Courses Price Accent","masterstudy-lms-learning-management-system"),icon:(0,r.createElement)("img",{src:ho,alt:"Price Accent"}),innerBlocks:[["masterstudy/courses-preset-price-accent"]],attributes:{selectDataslot1:"lectures",selectDataslot2:"duration",showPopup:!1,showDivider:!1,showRating:!0,cardItems:[{id:"card-rating",label:i.__("Rating","masterstudy-lms-learning-management-system"),switchFieldName:"showRating",selectFieldName:""},{id:"card-category",label:i.__("Category","masterstudy-lms-learning-management-system"),switchFieldName:"showCategory",selectFieldName:""},{id:"card-title",label:i.__("Title","masterstudy-lms-learning-management-system"),switchFieldName:"",selectFieldName:""},{id:"card-meta",label:i.__("Dataslots","masterstudy-lms-learning-management-system"),switchFieldName:"",selectFieldName:"",children:[{id:"card-dataslot-1",label:i.__("Dataslot 1","masterstudy-lms-learning-management-system"),switchFieldName:"",selectFieldName:"selectDataslot1"},{id:"card-dataslot-2",label:i.__("Dataslot 2","masterstudy-lms-learning-management-system"),switchFieldName:"",selectFieldName:"selectDataslot2"}]},{id:"card-divider",label:i.__("Divider","masterstudy-lms-learning-management-system"),switchFieldName:"showDivider",selectFieldName:""}],priceColor:"#ffffff",specialPriceColor:"#ffffff",oldPriceColor:"#ffffff",subscripColor:"#ffffff",subscripIconColor:"#ffffff",countdownLabelColor:"#ffffff",countdownCounterColor:"#ffffff",countdownCounterBackgroundColor:"#4d5e6f",categoryColor:"#4d5e6f",titleColor:"#001931",cardBorderRadius:{top:"",right:"",bottom:"",left:""},imageHeight:150,imageBorderRadius:{top:"",right:"",bottom:"",left:""},ibPadding:{top:"20",right:"20",bottom:"20",left:"20"},oldPriceFontSize:12,popupExcerptColor:co.popupExcerptColor,progressColor:co.progressColor,progressMargin:co.progressMargin,showPopupWishlist:co.showPopupWishlist,wishlistIconEmptyColor:co.wishlistIconEmptyColor,metaMargin:co.metaMargin,metaPadding:co.metaPadding,metaBgColor:co.metaBgColor,metaColor:co.metaColor},scope:["block"]},{name:"price-button",title:i.__("Price button","masterstudy-lms-learning-management-system"),description:i.__("Displays Courses Price Button","masterstudy-lms-learning-management-system"),icon:(0,r.createElement)("img",{src:bo,alt:"Price button"}),innerBlocks:[["masterstudy/courses-preset-price-button"]],attributes:{selectDataslot1:"empty",selectDataslot2:"empty",showPopup:!1,showDivider:!0,showRating:!0,cardItems:[{id:"card-category",label:i.__("Category","masterstudy-lms-learning-management-system"),switchFieldName:"showCategory",selectFieldName:""},{id:"card-title",label:i.__("Title","masterstudy-lms-learning-management-system"),switchFieldName:"",selectFieldName:""},{id:"card-meta",label:i.__("Dataslots","masterstudy-lms-learning-management-system"),switchFieldName:"",selectFieldName:"",children:[{id:"card-dataslot-1",label:i.__("Dataslot 1","masterstudy-lms-learning-management-system"),switchFieldName:"",selectFieldName:"selectDataslot1"},{id:"card-dataslot-2",label:i.__("Dataslot 2","masterstudy-lms-learning-management-system"),switchFieldName:"",selectFieldName:"selectDataslot2"}]},{id:"card-rating",label:i.__("Rating","masterstudy-lms-learning-management-system"),switchFieldName:"showRating",selectFieldName:""},{id:"card-divider",label:i.__("Divider","masterstudy-lms-learning-management-system"),switchFieldName:"showDivider",selectFieldName:""}],priceColor:"#227AFF",specialPriceColor:"#227AFF",oldPriceColor:"#4D5E6F",subscripColor:"#4D5E6F",subscripIconColor:"#4D5E6F",countdownLabelColor:"",countdownCounterColor:"",countdownCounterBackgroundColor:"",categoryColor:"#4d5e6f",titleColor:"#001931",cardBorderRadius:{top:"8",right:"8",bottom:"8",left:"8"},imageHeight:150,imageBorderRadius:{top:"8",right:"8",bottom:"8",left:"8"},ibPadding:{top:"20",right:"20",bottom:"20",left:"20"},oldPriceFontSize:12,popupExcerptColor:co.popupExcerptColor,progressColor:co.progressColor,progressMargin:co.progressMargin,showPopupWishlist:co.showPopupWishlist,wishlistIconEmptyColor:co.wishlistIconEmptyColor,metaMargin:co.metaMargin,metaPadding:co.metaPadding,metaBgColor:co.metaBgColor,metaColor:co.metaColor},scope:["block"]},{name:"full-size-image",title:i.__("Full Size Image","masterstudy-lms-learning-management-system"),description:i.__("Displays Courses Full Size Image","masterstudy-lms-learning-management-system"),icon:(0,r.createElement)("img",{src:yo,alt:"Full Size"}),innerBlocks:[["masterstudy/courses-preset-full-size-image"]],attributes:{selectDataslot1:"members",selectDataslot2:"views",showPopup:!1,showDivider:!1,showRating:!0,priceColor:"#ffffff",specialPriceColor:"#ffffff",oldPriceColor:"#B3BAC2",subscripColor:"#ffffff",subscripIconColor:"#ffffff",countdownLabelColor:"#ffffff",countdownCounterColor:"#ffffff",countdownCounterBackgroundColor:"#4d5e6f",categoryColor:"#ffffff",titleColor:"#ffffff",cardBorderRadius:{top:"8",right:"8",bottom:"8",left:"8"},imageHeight:348,imageBorderRadius:{top:"8",right:"8",bottom:"8",left:"8"},ibPadding:{top:"20",right:"20",bottom:"20",left:"20"},oldPriceFontSize:14,popupExcerptColor:"#ffffff",progressColor:"#ffffff",progressMargin:{top:8,right:0,bottom:0,left:0},showPopupWishlist:!0,wishlistIconEmptyColor:"#ffffff",metaMargin:{top:10,right:0,bottom:0,left:0},metaPadding:{top:8,right:0,bottom:0,left:0},metaBgColor:"",metaColor:"#ffffff"},scope:["block"]}],vo=F("status-style-preset"),fo=({preset:e})=>(0,r.createElement)("div",{className:vo},(0,r.createElement)("div",{className:`${vo}--${e.value}`},(0,r.createElement)(m,{condition:Boolean(e.icon)},(0,r.createElement)(e.icon,null)))),xo=F("status-style-settings"),_o=(e,t)=>e.value===t.value,Co=({label:e,attributeName:t,presets:n,popoverContent:i=null,isAdaptive:a=!1,dependencyMode:o,dependencies:l})=>{const{fieldName:p}=I(t,a),{isChanged:c,onReset:d,onSelectPreset:u,activePreset:m}=(e=>{const{setAttributes:t}=D(),{value:n,isChanged:i,onReset:a}=A(e),r=(0,s.useCallback)((n=>{const{value:i}=n;t({[e]:i})}),[e,t]);return{activePreset:(0,s.useMemo)((()=>({value:n})),[n]),onReset:a,isChanged:i,onSelectPreset:r}})(p);return R(l,o)?(0,r.createElement)("div",{className:xo},(0,r.createElement)(Ae,{label:e,isChanged:c,onReset:d,popoverContent:i,showDevicePicker:a}),(0,r.createElement)(ht,{presets:n,onSelectPreset:u,activePreset:m,PresetItem:fo,detectIsActive:_o,detectByIndex:!1})):null},No=({setInnerPresetName:e,innerPresetName:t})=>{const{setAttributes:n,clientId:a}=D();return(0,r.createElement)(r.Fragment,null,(0,r.createElement)(sr,{title:i.__("Course Card","masterstudy-lms-learning-management-system"),accordionFields:[]},(0,r.createElement)(yr,{label:i.__("Card preset","masterstudy-lms-learning-management-system"),setAttributes:n,clientId:a,innerPresetName:t,onChangePresetName:e,presets:So}),(0,r.createElement)(Et,{name:"cardGap",label:i.__("Space between cards","masterstudy-lms-learning-management-system"),unitName:"cardGapUnit",isAdaptive:!0}),(0,r.createElement)(Ae,{label:i.__("Card items","masterstudy-lms-learning-management-system"),isChanged:!1,onReset:()=>{},showDevicePicker:!1}),(0,r.createElement)(m,{condition:"masterstudy/courses-preset-full-size-image"===t,fallback:(0,r.createElement)(qa,{name:"cardItems"})},(0,r.createElement)(vt,{name:"showCategory",label:i.__("Show category","masterstudy-lms-learning-management-system")}),(0,r.createElement)(vt,{name:"showDivider",label:i.__("Show divider","masterstudy-lms-learning-management-system")}),(0,r.createElement)(vt,{name:"showPrice",label:i.__("Show price","masterstudy-lms-learning-management-system")}),(0,r.createElement)(vt,{name:"showRating",label:i.__("Show rating","masterstudy-lms-learning-management-system")}),(0,r.createElement)(vt,{name:"showPopupWishlist",label:i.__("Show wishlist","masterstudy-lms-learning-management-system")}),(0,r.createElement)(kt,{name:"selectDataslot1",label:i.__("Dataslot 1","masterstudy-lms-learning-management-system"),options:Da.selectDataslot1}),(0,r.createElement)(kt,{name:"selectDataslot2",label:i.__("Dataslot 2","masterstudy-lms-learning-management-system"),options:Da.selectDataslot2})),(0,r.createElement)(m,{condition:mo.includes(t)},(0,r.createElement)(vt,{name:"showPopup",label:i.__("Popup items","masterstudy-lms-learning-management-system")})),(0,r.createElement)(qa,{name:"popupItems",dependencies:[{name:"showPopup",value:!0}]}),(0,r.createElement)(et,{name:"featuredPosition",label:i.__("Featured label position","masterstudy-lms-learning-management-system"),options:[{label:(0,r.createElement)(u.Dashicon,{icon:"align-pull-left"}),value:"start"},{label:(0,r.createElement)(u.Dashicon,{icon:"align-pull-right"}),value:"end"}]}),(0,r.createElement)(Co,{label:i.__("Status styles","masterstudy-lms-learning-management-system"),presets:vr,attributeName:"statusStyle"}),(0,r.createElement)(et,{name:"statusPosition",label:i.__("Status position","masterstudy-lms-learning-management-system"),options:[{label:(0,r.createElement)(u.Dashicon,{icon:"align-pull-left"}),value:"start"},{label:(0,r.createElement)(u.Dashicon,{icon:"align-center"}),value:"center"},{label:(0,r.createElement)(u.Dashicon,{icon:"align-pull-right"}),value:"end"}]})))},wo=window.wp.apiFetch;var Eo=n.n(wo);const Uo=({innerPresetName:e})=>{const{settings:t}=(()=>{const[e,t]=(0,s.useState)({coming_soon:!1}),{setIsFetching:n,setError:i,isFetching:a,error:r}=(()=>{const[e,t]=(0,s.useState)(!0),[n,i]=(0,s.useState)("");return{isFetching:e,setIsFetching:t,error:n,setError:i}})();return(0,s.useEffect)((()=>{n(!0),(async()=>{try{return await Eo()({path:"masterstudy-lms/v2/blocks/settings"})}catch(e){throw new Error(e)}})().then((e=>{t((e=>({coming_soon:e.addons.coming_soon||!1}))(e))})).catch((e=>{i(e.message)})).finally((()=>{n(!1)}))}),[i,n]),{settings:e,isFetching:a,error:r}})(),{attributes:n}=D();return(0,r.createElement)(r.Fragment,null,(0,r.createElement)(sr,{title:i.__("Container","masterstudy-lms-learning-management-system"),accordionFields:xr},(0,r.createElement)(Xe,{name:"cntrPadding",label:i.__("Padding","masterstudy-lms-learning-management-system"),unitName:"cntrPaddingUnit",isAdaptive:!0}),(0,r.createElement)(Xe,{name:"cntrMargin",label:i.__("Margin","masterstudy-lms-learning-management-system"),unitName:"cntrMarginUnit",isAdaptive:!0})),(0,r.createElement)(sr,{title:i.__("Card","masterstudy-lms-learning-management-system"),accordionFields:Cr},(0,r.createElement)(Xe,{name:"cardPadding",label:i.__("Padding","masterstudy-lms-learning-management-system"),unitName:"cardPaddingUnit",isAdaptive:!0}),(0,r.createElement)(ke,{name:"cardBgColor",label:i.__("Background","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(rt,{label:i.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"cardBorderStyle",borderColorName:"cardBorderColor",borderWidthName:"cardBorderWidth",isAdaptive:!0}),(0,r.createElement)(st,{name:"cardBorderRadius",label:i.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,r.createElement)(Ct,{label:i.__("Select Preset","masterstudy-lms-learning-management-system"),min:0,max:100,shadowColorName:"cardShadowColor",shadowHorizontalName:"cardShadowHorizontal",shadowVerticalName:"cardShadowVertical",shadowBlurName:"cardShadowBlur",shadowSpreadName:"cardShadowSpread",shadowInsetName:"cardShadowInset",popoverContent:null,isAdaptive:!0})),(0,r.createElement)(sr,{title:i.__("Card InfoBlock","masterstudy-lms-learning-management-system"),accordionFields:wr},(0,r.createElement)(Xe,{name:"ibPadding",label:i.__("Padding","masterstudy-lms-learning-management-system"),unitName:"ibPaddingUnit",isAdaptive:!0}),(0,r.createElement)(ke,{name:"ibBgColor",label:i.__("Background Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(rt,{label:i.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"ibBorderStyle",borderColorName:"ibBorderColor",borderWidthName:"ibBorderWidth",isAdaptive:!0}),(0,r.createElement)(st,{name:"ibBorderRadius",label:i.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0})),(0,r.createElement)(sr,{title:i.__("Image","masterstudy-lms-learning-management-system"),accordionFields:Ur},(0,r.createElement)(Et,{name:"imageHeight",label:i.__("Height","masterstudy-lms-learning-management-system"),unitName:"imageHeightUnit",isAdaptive:!0}),(0,r.createElement)(rt,{label:i.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"imageBorderStyle",borderColorName:"imageBorderColor",borderWidthName:"imageBorderWidth",isAdaptive:!0}),(0,r.createElement)(st,{name:"imageBorderRadius",label:i.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0})),(0,r.createElement)(sr,{title:i.__("Category","masterstudy-lms-learning-management-system"),accordionFields:Mr},(0,r.createElement)(Zt,{fontSizeName:"categoryFontSize",fontSizeUnitName:"categoryFontSizeUnit",fontWeightName:"categoryFontWeight",textTransformName:"categoryTextTransform",fontStyleName:"categoryFontStyle",textDecorationName:"categoryTextDecoration",lineHeightName:"categoryLineHeight",lineHeightUnitName:"categoryLineHeightUnit",letterSpacingName:"categoryLetterSpacing",letterSpacingUnitName:"categoryLetterSpacingUnit",wordSpacingName:"categoryWordSpacing",wordSpacingUnitName:"categoryWordSpacingUnit",isAdaptive:!0}),(0,r.createElement)(ke,{name:"categoryColor",label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(Xe,{name:"categoryPadding",label:i.__("Padding","masterstudy-lms-learning-management-system"),unitName:"categoryPaddingUnit",isAdaptive:!0}),(0,r.createElement)(Xe,{name:"categoryMargin",label:i.__("Margin","masterstudy-lms-learning-management-system"),unitName:"categoryMarginUnit",isAdaptive:!0})),(0,r.createElement)(sr,{title:i.__("Title","masterstudy-lms-learning-management-system"),accordionFields:Pr},(0,r.createElement)(Zt,{fontSizeName:"titleFontSize",fontSizeUnitName:"titleFontSizeUnit",fontWeightName:"titleFontWeight",textTransformName:"titleTextTransform",fontStyleName:"titleFontStyle",textDecorationName:"titleTextDecoration",lineHeightName:"titleLineHeight",lineHeightUnitName:"titleLineHeightUnit",letterSpacingName:"titleLetterSpacing",letterSpacingUnitName:"titleLetterSpacingUnit",wordSpacingName:"titleWordSpacing",wordSpacingUnitName:"titleWordSpacingUnit",isAdaptive:!0}),(0,r.createElement)(ke,{name:"titleColor",label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(Xe,{name:"titlePadding",label:i.__("Padding","masterstudy-lms-learning-management-system"),unitName:"titlePaddingUnit",isAdaptive:!0}),(0,r.createElement)(Xe,{name:"titleMargin",label:i.__("Margin","masterstudy-lms-learning-management-system"),unitName:"titleMarginUnit",isAdaptive:!0})),(0,r.createElement)(sr,{title:i.__("Divider","masterstudy-lms-learning-management-system"),accordionFields:Wr},(0,r.createElement)(Et,{name:"dividerHeight",label:i.__("Height","masterstudy-lms-learning-management-system"),unitName:"dividerHeightUnit"}),(0,r.createElement)(ke,{name:"dividerColor",label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")})),(0,r.createElement)(sr,{title:i.__("Data Slots","masterstudy-lms-learning-management-system"),accordionFields:Ar},(0,r.createElement)(Zt,{fontSizeName:"metaFontSize",fontSizeUnitName:"metaFontSizeUnit",fontWeightName:"metaFontWeight",textTransformName:"metaTextTransform",fontStyleName:"metaFontStyle",textDecorationName:"metaTextDecoration",lineHeightName:"metaLineHeight",lineHeightUnitName:"metaLineHeightUnit",letterSpacingName:"metaLetterSpacing",letterSpacingUnitName:"metaLetterSpacingUnit",wordSpacingName:"metaWordSpacing",wordSpacingUnitName:"metaWordSpacingUnit",isAdaptive:!0}),(0,r.createElement)(ke,{name:"metaColor",label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(ke,{name:"metaBgColor",label:i.__("Background Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(rt,{label:i.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"metaBorderStyle",borderColorName:"metaBorderColor",borderWidthName:"metaBorderWidth",isAdaptive:!0}),(0,r.createElement)(st,{name:"metaBorderRadius",label:i.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,r.createElement)(Xe,{name:"metaMargin",label:i.__("Margin","masterstudy-lms-learning-management-system"),unitName:"metaMarginUnit",isAdaptive:!0}),(0,r.createElement)(Xe,{name:"metaPadding",label:i.__("Padding","masterstudy-lms-learning-management-system"),unitName:"metaPaddingUnit",isAdaptive:!0})),(0,r.createElement)(sr,{title:i.__("Price","masterstudy-lms-learning-management-system"),accordionFields:zr},(0,r.createElement)(m,{condition:"masterstudy/courses-preset-price-accent"===e},(0,r.createElement)(ke,{name:"priceBgColor",label:i.__("Price Background Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")})),(0,r.createElement)(u.__experimentalHeading,{level:"3",upperCase:!1,style:{marginBlock:"0px",color:"#4D5E6F"}},i.__("Typography","masterstudy-lms-learning-management-system")),(0,r.createElement)(Zt,{fontSizeName:"priceFontSize",fontSizeUnitName:"priceFontSizeUnit",fontWeightName:"priceFontWeight",textTransformName:"priceTextTransform",fontStyleName:"priceFontStyle",textDecorationName:"priceTextDecoration",lineHeightName:"priceLineHeight",lineHeightUnitName:"priceLineHeightUnit",letterSpacingName:"priceLetterSpacing",letterSpacingUnitName:"priceLetterSpacingUnit",wordSpacingName:"priceWordSpacing",wordSpacingUnitName:"priceWordSpacingUnit",isAdaptive:!0}),(0,r.createElement)(ke,{name:"priceColor",label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(u.__experimentalHeading,{level:"3",upperCase:!1,style:{marginBlock:"16px 0px",color:"#4D5E6F"}},i.__("Sale Price Typography","masterstudy-lms-learning-management-system")),(0,r.createElement)(Zt,{fontSizeName:"specialPriceFontSize",fontSizeUnitName:"specialPriceFontSizeUnit",fontWeightName:"specialPriceFontWeight",textTransformName:"specialPriceTextTransform",fontStyleName:"specialPriceFontStyle",textDecorationName:"specialPriceTextDecoration",lineHeightName:"specialPriceLineHeight",lineHeightUnitName:"specialPriceLineHeightUnit",letterSpacingName:"specialPriceLetterSpacing",letterSpacingUnitName:"specialPriceLetterSpacingUnit",wordSpacingName:"specialPriceWordSpacing",wordSpacingUnitName:"specialPriceWordSpacingUnit",isAdaptive:!0}),(0,r.createElement)(ke,{name:"specialPriceColor",label:i.__("Sale Price Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(u.__experimentalHeading,{level:"3",upperCase:!1,style:{marginBlock:"16px 0px",color:"#4D5E6F"}},i.__("Old Price Typography","masterstudy-lms-learning-management-system")),(0,r.createElement)(Zt,{fontSizeName:"oldPriceFontSize",fontSizeUnitName:"oldPriceFontSizeUnit",fontWeightName:"oldPriceFontWeight",textTransformName:"oldPriceTextTransform",fontStyleName:"oldPriceFontStyle",textDecorationName:"oldPriceTextDecoration",lineHeightName:"oldPriceLineHeight",lineHeightUnitName:"oldPriceLineHeightUnit",letterSpacingName:"oldPriceLetterSpacing",letterSpacingUnitName:"oldPriceLetterSpacingUnit",wordSpacingName:"oldPriceWordSpacing",wordSpacingUnitName:"oldPriceWordSpacingUnit",isAdaptive:!0}),(0,r.createElement)(ke,{name:"oldPriceColor",label:i.__("Old Price Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(u.__experimentalHeading,{level:"3",upperCase:!1,style:{marginBlock:"16px 0px",color:"#4D5E6F"}},i.__("Subscription typography","masterstudy-lms-learning-management-system")),(0,r.createElement)(Zt,{fontSizeName:"subscripFontSize",fontSizeUnitName:"subscripFontSizeUnit",fontWeightName:"subscripFontWeight",textTransformName:"subscripTextTransform",fontStyleName:"subscripFontStyle",textDecorationName:"subscripTextDecoration",lineHeightName:"subscripLineHeight",lineHeightUnitName:"subscripLineHeightUnit",letterSpacingName:"subscripLetterSpacing",letterSpacingUnitName:"subscripLetterSpacingUnit",wordSpacingName:"subscripWordSpacing",wordSpacingUnitName:"subscripWordSpacingUnit",isAdaptive:!0}),(0,r.createElement)(ke,{name:"subscripColor",label:i.__("Subscription Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(ke,{name:"subscripIconColor",label:i.__("Subscription Icon Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(m,{condition:"masterstudy/courses-preset-price-accent"===e},(0,r.createElement)(ke,{name:"subscripBgColor",label:i.__("Subscription Background Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}))),(0,r.createElement)(sr,{title:i.__("Preview Button","masterstudy-lms-learning-management-system"),accordionFields:kr,visible:"masterstudy/courses-preset-price-button"===e},(0,r.createElement)(Zt,{fontSizeName:"buttonFontSize",fontSizeUnitName:"buttonFontSizeUnit",fontWeightName:"buttonFontWeight",textTransformName:"buttonTextTransform",fontStyleName:"buttonFontStyle",textDecorationName:"buttonTextDecoration",lineHeightName:"buttonLineHeight",lineHeightUnitName:"buttonLineHeightUnit",letterSpacingName:"buttonLetterSpacing",letterSpacingUnitName:"buttonLetterSpacingUnit",wordSpacingName:"buttonWordSpacing",wordSpacingUnitName:"buttonWordSpacingUnit",isAdaptive:!0}),(0,r.createElement)(ke,{name:"buttonColor",label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,r.createElement)(ke,{name:"buttonBgColor",label:i.__("Background Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,r.createElement)(rt,{label:i.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"buttonBorderStyle",borderColorName:"buttonBorderColor",borderWidthName:"buttonBorderWidth",isAdaptive:!0}),(0,r.createElement)(st,{name:"buttonBorderRadius",label:i.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0})),(0,r.createElement)(sr,{title:i.__("Status","masterstudy-lms-learning-management-system"),accordionFields:Vr},(0,r.createElement)(u.__experimentalHeading,{level:"3",upperCase:!1,style:{marginBlock:"16px 0px",color:"#4D5E6F"}},i.__("Featured typography","masterstudy-lms-learning-management-system")),(0,r.createElement)(Zt,{fontSizeName:"statusFeaturedFontSize",fontSizeUnitName:"statusFeaturedFontSizeUnit",fontWeightName:"statusFeaturedFontWeight",textTransformName:"statusFeaturedTextTransform",fontStyleName:"statusFeaturedFontStyle",textDecorationName:"statusFeaturedTextDecoration",lineHeightName:"statusFeaturedLineHeight",lineHeightUnitName:"statusFeaturedLineHeightUnit",letterSpacingName:"statusFeaturedLetterSpacing",letterSpacingUnitName:"statusFeaturedLetterSpacingUnit",wordSpacingName:"statusFeaturedWordSpacing",wordSpacingUnitName:"statusFeaturedWordSpacingUnit"}),(0,r.createElement)(ke,{name:"statusFeaturedColor",label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(ke,{name:"statusFeaturedBgColor",label:i.__("Background Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(u.__experimentalHeading,{level:"3",upperCase:!1,style:{marginBlock:"16px 0px",color:"#4D5E6F"}},i.__("Hot typography","masterstudy-lms-learning-management-system")),(0,r.createElement)(Zt,{fontSizeName:"statusHotFontSize",fontSizeUnitName:"statusHotFontSizeUnit",fontWeightName:"statusHotFontWeight",textTransformName:"statusHotTextTransform",fontStyleName:"statusHotFontStyle",textDecorationName:"statusHotTextDecoration",lineHeightName:"statusHotLineHeight",lineHeightUnitName:"statusHotLineHeightUnit",letterSpacingName:"statusHotLetterSpacing",letterSpacingUnitName:"statusHotLetterSpacingUnit",wordSpacingName:"statusHotWordSpacing",wordSpacingUnitName:"statusHotWordSpacingUnit"}),(0,r.createElement)(ke,{name:"statusHotColor",label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(ke,{name:"statusHotBgColor",label:i.__("Background Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(u.__experimentalHeading,{level:"3",upperCase:!1,style:{marginBlock:"16px 0px",color:"#4D5E6F"}},i.__("New typography","masterstudy-lms-learning-management-system")),(0,r.createElement)(Zt,{fontSizeName:"statusNewFontSize",fontSizeUnitName:"statusNewFontSizeUnit",fontWeightName:"statusNewFontWeight",textTransformName:"statusNewTextTransform",fontStyleName:"statusNewFontStyle",textDecorationName:"statusNewTextDecoration",lineHeightName:"statusNewLineHeight",lineHeightUnitName:"statusNewLineHeightUnit",letterSpacingName:"statusNewLetterSpacing",letterSpacingUnitName:"statusNewLetterSpacingUnit",wordSpacingName:"statusNewWordSpacing",wordSpacingUnitName:"statusNewWordSpacingUnit"}),(0,r.createElement)(ke,{name:"statusNewColor",label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(ke,{name:"statusNewBgColor",label:i.__("Background Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(u.__experimentalHeading,{level:"3",upperCase:!1,style:{marginBlock:"16px 0px",color:"#4D5E6F"}},i.__("Special typography","masterstudy-lms-learning-management-system")),(0,r.createElement)(Zt,{fontSizeName:"statusSpecialFontSize",fontSizeUnitName:"statusSpecialFontSizeUnit",fontWeightName:"statusSpecialFontWeight",textTransformName:"statusSpecialTextTransform",fontStyleName:"statusSpecialFontStyle",textDecorationName:"statusSpecialTextDecoration",lineHeightName:"statusSpecialLineHeight",lineHeightUnitName:"statusSpecialLineHeightUnit",letterSpacingName:"statusSpecialLetterSpacing",letterSpacingUnitName:"statusSpecialLetterSpacingUnit",wordSpacingName:"statusSpecialWordSpacing",wordSpacingUnitName:"statusSpecialWordSpacingUnit"}),(0,r.createElement)(ke,{name:"statusSpecialColor",label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(ke,{name:"statusSpecialBgColor",label:i.__("Background Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")})),(0,r.createElement)(sr,{title:i.__("Countdown","masterstudy-lms-learning-management-system"),accordionFields:$r,visible:t.coming_soon},(0,r.createElement)(u.__experimentalHeading,{level:"3",upperCase:!1,style:{marginBlock:"16px 0px",color:"#4D5E6F"}},i.__("Label typography","masterstudy-lms-learning-management-system")),(0,r.createElement)(Zt,{fontSizeName:"countdownLabelFontSize",fontSizeUnitName:"countdownLabelFontSizeUnit",fontWeightName:"countdownLabelFontWeight",textTransformName:"countdownLabelTextTransform",fontStyleName:"countdownLabelFontStyle",textDecorationName:"countdownLabelTextDecoration",lineHeightName:"countdownLabelLineHeight",lineHeightUnitName:"countdownLabelLineHeightUnit",letterSpacingName:"countdownLabelLetterSpacing",letterSpacingUnitName:"countdownLabelLetterSpacingUnit",wordSpacingName:"countdownLabelWordSpacing",wordSpacingUnitName:"countdownLabelWordSpacingUnit"}),(0,r.createElement)(m,{condition:"masterstudy/courses-preset-price-accent"===e},(0,r.createElement)(ke,{name:"countdownLabelBgColor",label:i.__("Background Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")})),(0,r.createElement)(ke,{name:"countdownLabelColor",label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(u.__experimentalHeading,{level:"3",upperCase:!1,style:{marginBlock:"16px 0px",color:"#4D5E6F"}},i.__("Counter typography","masterstudy-lms-learning-management-system")),(0,r.createElement)(Zt,{fontSizeName:"countdownCounterFontSize",fontSizeUnitName:"countdownCounterFontSizeUnit",fontWeightName:"countdownCounterFontWeight",textTransformName:"countdownCounterTextTransform",fontStyleName:"countdownCounterFontStyle",textDecorationName:"countdownCounterTextDecoration",lineHeightName:"countdownCounterLineHeight",lineHeightUnitName:"countdownCounterLineHeightUnit",letterSpacingName:"countdownCounterLetterSpacing",letterSpacingUnitName:"countdownCounterLetterSpacingUnit",wordSpacingName:"countdownCounterWordSpacing",wordSpacingUnitName:"countdownCounterWordSpacingUnit"}),(0,r.createElement)(ke,{name:"countdownCounterColor",label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(ke,{name:"countdownCounterBackgroundColor",label:i.__("BackgroundColor","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(st,{name:"countdownCounterBorderRadius",label:i.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,r.createElement)(yt,{name:"countdownCounterHeight",label:i.__("Height","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:"countdownCounterHeightUnit",isAdaptive:!0}),(0,r.createElement)(yt,{name:"countdownCounterWidth",label:i.__("Width","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:"countdownCounterWidthUnit",isAdaptive:!0}),(0,r.createElement)(Et,{name:"countdownCounterGap",label:i.__("Space between counts","masterstudy-lms-learning-management-system"),unitName:"countdownCounterGapUnit",isAdaptive:!0}),(0,r.createElement)(Xe,{name:"countdownCounterPadding",label:i.__("Padding","masterstudy-lms-learning-management-system"),unitName:"countdownCounterPaddingUnit",isAdaptive:!0}),(0,r.createElement)(Xe,{name:"countdownCounterMargin",label:i.__("Margin","masterstudy-lms-learning-management-system"),unitName:"countdownCounterMarginUnit",isAdaptive:!0})),(0,r.createElement)(m,{condition:!0===n.showPopup},(0,r.createElement)(sr,{title:i.__("Popup","masterstudy-lms-learning-management-system"),accordionFields:Gr},(0,r.createElement)(Xe,{name:"popupPadding",label:i.__("Padding","masterstudy-lms-learning-management-system"),unitName:"popupPaddingUnit",isAdaptive:!0}),(0,r.createElement)(ke,{name:"popupBg",label:i.__("Background Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(rt,{label:i.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"popupBorderStyle",borderColorName:"popupBorderColor",borderWidthName:"popupBorderWidth",isAdaptive:!0}),(0,r.createElement)(st,{name:"popupBorderRadius",label:i.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,r.createElement)(Ct,{label:i.__("Select Preset","masterstudy-lms-learning-management-system"),min:0,max:100,shadowColorName:"popupShadowColor",shadowHorizontalName:"popupShadowHorizontal",shadowVerticalName:"popupShadowVertical",shadowBlurName:"popupShadowBlur",shadowSpreadName:"popupShadowSpread",shadowInsetName:"popupShadowInset",popoverContent:null,isAdaptive:!0})),(0,r.createElement)(sr,{title:i.__("Popup Instructor’s Image","masterstudy-lms-learning-management-system"),accordionFields:Xr},(0,r.createElement)(Xe,{name:"popupIImgMargin",label:i.__("Margin","masterstudy-lms-learning-management-system"),unitName:"popupIImgMarginUnit",isAdaptive:!0}),(0,r.createElement)(yt,{name:"popupIImgSize",label:i.__("Size","masterstudy-lms-learning-management-system"),min:8,max:400,unitName:"popupIImgSizeUnit",isAdaptive:!0}),(0,r.createElement)(rt,{label:i.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"popupIImgBorderStyle",borderColorName:"popupIImgBorderColor",borderWidthName:"popupIImgBorderWidth",isAdaptive:!0}),(0,r.createElement)(st,{name:"popupIImgBorderRadius",label:i.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0})),(0,r.createElement)(sr,{title:i.__("Popup Instructor’s Name","masterstudy-lms-learning-management-system"),accordionFields:qr},(0,r.createElement)(Zt,{fontSizeName:"popupINameFontSize",fontSizeUnitName:"popupINameFontSizeUnit",fontWeightName:"popupINameFontWeight",textTransformName:"popupINameTextTransform",fontStyleName:"popupINameFontStyle",textDecorationName:"popupINameTextDecoration",lineHeightName:"popupINameLineHeight",lineHeightUnitName:"popupINameLineHeightUnit",letterSpacingName:"popupINameLetterSpacing",letterSpacingUnitName:"popupINameLetterSpacingUnit",wordSpacingName:"popupINameWordSpacing",wordSpacingUnitName:"popupINameWordSpacingUnit",isAdaptive:!0}),(0,r.createElement)(ke,{name:"popupINameColor",label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")})),(0,r.createElement)(sr,{title:i.__("Popup Title","masterstudy-lms-learning-management-system"),accordionFields:Qr},(0,r.createElement)(Xe,{name:"popupTitleMargin",label:i.__("Margin","masterstudy-lms-learning-management-system"),unitName:"popupTitleMarginUnit",isAdaptive:!0}),(0,r.createElement)(Xe,{name:"popupTitlePadding",label:i.__("Padding","masterstudy-lms-learning-management-system"),unitName:"popupTitlePaddingUnit",isAdaptive:!0}),(0,r.createElement)(Zt,{fontSizeName:"popupTitleFontSize",fontSizeUnitName:"popupTitleFontSizeUnit",fontWeightName:"popupTitleFontWeight",textTransformName:"popupTitleTextTransform",fontStyleName:"popupTitleFontStyle",textDecorationName:"popupTitleTextDecoration",lineHeightName:"popupTitleLineHeight",lineHeightUnitName:"popupTitleLineHeightUnit",letterSpacingName:"popupTitleLetterSpacing",letterSpacingUnitName:"popupTitleLetterSpacingUnit",wordSpacingName:"popupTitleWordSpacing",wordSpacingUnitName:"popupTitleWordSpacingUnit",isAdaptive:!0}),(0,r.createElement)(ke,{name:"popupTitleColor",label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")})),(0,r.createElement)(sr,{title:i.__("Popup Excerpt","masterstudy-lms-learning-management-system"),accordionFields:to},(0,r.createElement)(Xe,{name:"popupExcerptMargin",label:i.__("Margin","masterstudy-lms-learning-management-system"),unitName:"popupExcerptMarginUnit",isAdaptive:!0}),(0,r.createElement)(Xe,{name:"popupExcerptPadding",label:i.__("Padding","masterstudy-lms-learning-management-system"),unitName:"popupExcerptPaddingUnit",isAdaptive:!0}),(0,r.createElement)(Zt,{fontSizeName:"popupExcerptFontSize",fontSizeUnitName:"popupExcerptFontSizeUnit",fontWeightName:"popupExcerptFontWeight",textTransformName:"popupExcerptTextTransform",fontStyleName:"popupExcerptFontStyle",textDecorationName:"popupExcerptTextDecoration",lineHeightName:"popupExcerptLineHeight",lineHeightUnitName:"popupExcerptLineHeightUnit",letterSpacingName:"popupExcerptLetterSpacing",letterSpacingUnitName:"popupExcerptLetterSpacingUnit",wordSpacingName:"popupExcerptWordSpacing",wordSpacingUnitName:"popupExcerptWordSpacingUnit",isAdaptive:!0}),(0,r.createElement)(ke,{name:"popupExcerptColor",label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")})),(0,r.createElement)(sr,{title:i.__("Popup Data Slots","masterstudy-lms-learning-management-system"),accordionFields:io},(0,r.createElement)(Zt,{fontSizeName:"popupMetaFontSize",fontSizeUnitName:"popupMetaFontSizeUnit",fontWeightName:"popupMetaFontWeight",textTransformName:"popupMetaTextTransform",fontStyleName:"popupMetaFontStyle",textDecorationName:"popupMetaTextDecoration",lineHeightName:"popupMetaLineHeight",lineHeightUnitName:"popupMetaLineHeightUnit",letterSpacingName:"popupMetaLetterSpacing",letterSpacingUnitName:"popupMetaLetterSpacingUnit",wordSpacingName:"popupMetaWordSpacing",wordSpacingUnitName:"popupMetaWordSpacingUnit",isAdaptive:!0}),(0,r.createElement)(ke,{name:"popupMetaColor",label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(ke,{name:"popupMetaBgColor",label:i.__("Background Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(rt,{label:i.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"popupMetaBorderStyle",borderColorName:"popupMetaBorderColor",borderWidthName:"popupMetaBorderWidth",isAdaptive:!0}),(0,r.createElement)(st,{name:"popupMetaBorderRadius",label:i.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,r.createElement)(Xe,{name:"popupMetaMargin",label:i.__("Margin","masterstudy-lms-learning-management-system"),unitName:"popupMetaMarginUnit",isAdaptive:!0}),(0,r.createElement)(Xe,{name:"popupMetaPadding",label:i.__("Padding","masterstudy-lms-learning-management-system"),unitName:"popupMetaPaddingUnit",isAdaptive:!0})),(0,r.createElement)(sr,{title:i.__("Popup Button","masterstudy-lms-learning-management-system"),accordionFields:po},(0,r.createElement)(Zt,{fontSizeName:"popupButtonFontSize",fontSizeUnitName:"popupButtonFontSizeUnit",fontWeightName:"popupButtonFontWeight",textTransformName:"popupButtonTextTransform",fontStyleName:"popupButtonFontStyle",textDecorationName:"popupButtonTextDecoration",lineHeightName:"popupButtonLineHeight",lineHeightUnitName:"popupButtonLineHeightUnit",letterSpacingName:"popupButtonLetterSpacing",letterSpacingUnitName:"popupButtonLetterSpacingUnit",wordSpacingName:"popupButtonWordSpacing",wordSpacingUnitName:"popupButtonWordSpacingUnit",isAdaptive:!0}),(0,r.createElement)(ke,{name:"popupButtonColor",label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,r.createElement)(ke,{name:"popupButtonBgColor",label:i.__("Background Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,r.createElement)(rt,{label:i.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"popupButtonBorderStyle",borderColorName:"popupButtonBorderColor",borderWidthName:"popupButtonBorderWidth",hasHover:!0,isAdaptive:!0}),(0,r.createElement)(st,{name:"popupButtonBorderRadius",label:i.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,r.createElement)(Xe,{name:"popupButtonMargin",label:i.__("Margin","masterstudy-lms-learning-management-system"),unitName:"popupButtonMarginUnit",isAdaptive:!0}),(0,r.createElement)(Xe,{name:"popupButtonPadding",label:i.__("Padding","masterstudy-lms-learning-management-system"),unitName:"popupButtonPaddingUnit",isAdaptive:!0})),(0,r.createElement)(sr,{title:i.__("Popup Add to Wishlist","masterstudy-lms-learning-management-system"),accordionFields:lo},(0,r.createElement)(Zt,{fontSizeName:"wishlistFontSize",fontSizeUnitName:"wishlistFontSizeUnit",fontWeightName:"wishlistFontWeight",textTransformName:"wishlistTextTransform",fontStyleName:"wishlistFontStyle",textDecorationName:"wishlistTextDecoration",lineHeightName:"wishlistLineHeight",lineHeightUnitName:"wishlistLineHeightUnit",letterSpacingName:"wishlistLetterSpacing",letterSpacingUnitName:"wishlistLetterSpacingUnit",wordSpacingName:"wishlistWordSpacing",wordSpacingUnitName:"wishlistWordSpacingUnit",isAdaptive:!0}),(0,r.createElement)(ke,{name:"wishlistColor",label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(ke,{name:"wishlistIconEmptyColor",label:i.__("Icon Empty Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(ke,{name:"wishlistIconFilledColor",label:i.__("Icon Filled Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(ke,{name:"wishlistLoadedColorColor",label:i.__("Loaded Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")})),(0,r.createElement)(sr,{title:i.__("Popup price","masterstudy-lms-learning-management-system"),accordionFields:ro},(0,r.createElement)(u.__experimentalHeading,{level:"3",upperCase:!1,style:{marginBlock:"0px",color:"#4D5E6F"}},i.__("Typography","masterstudy-lms-learning-management-system")),(0,r.createElement)(Zt,{fontSizeName:"popupPriceFontSize",fontSizeUnitName:"popupPriceFontSizeUnit",fontWeightName:"popupPriceFontWeight",textTransformName:"popupPriceTextTransform",fontStyleName:"popupPriceFontStyle",textDecorationName:"popupPriceTextDecoration",lineHeightName:"popupPriceLineHeight",lineHeightUnitName:"popupPriceLineHeightUnit",letterSpacingName:"popupPriceLetterSpacing",letterSpacingUnitName:"popupPriceLetterSpacingUnit",wordSpacingName:"popupPriceWordSpacing",wordSpacingUnitName:"popupPriceWordSpacingUnit",isAdaptive:!0}),(0,r.createElement)(ke,{name:"popupPriceColor",label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(u.__experimentalHeading,{level:"3",upperCase:!1,style:{marginBlock:"16px 0px",color:"#4D5E6F"}},i.__("Sale Price Typography","masterstudy-lms-learning-management-system")),(0,r.createElement)(Zt,{fontSizeName:"popupSpecialPriceFontSize",fontSizeUnitName:"popupSpecialPriceFontSizeUnit",fontWeightName:"popupSpecialPriceFontWeight",textTransformName:"popupSpecialPriceTextTransform",fontStyleName:"popupSpecialPriceFontStyle",textDecorationName:"popupSpecialPriceTextDecoration",lineHeightName:"popupSpecialPriceLineHeight",lineHeightUnitName:"popupSpecialPriceLineHeightUnit",letterSpacingName:"popupSpecialPriceLetterSpacing",letterSpacingUnitName:"popupSpecialPriceLetterSpacingUnit",wordSpacingName:"popupSpecialPriceWordSpacing",wordSpacingUnitName:"popupSpecialPriceWordSpacingUnit",isAdaptive:!0}),(0,r.createElement)(ke,{name:"popupSpecialPriceColor",label:i.__("Sale Price Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(u.__experimentalHeading,{level:"3",upperCase:!1,style:{marginBlock:"16px 0px",color:"#4D5E6F"}},i.__("Old Price Typography","masterstudy-lms-learning-management-system")),(0,r.createElement)(Zt,{fontSizeName:"popupOldPriceFontSize",fontSizeUnitName:"popupOldPriceFontSizeUnit",fontWeightName:"popupOldPriceFontWeight",textTransformName:"popupOldPriceTextTransform",fontStyleName:"popupOldPriceFontStyle",textDecorationName:"popupOldPriceTextDecoration",lineHeightName:"popupOldPriceLineHeight",lineHeightUnitName:"popupOldPriceLineHeightUnit",letterSpacingName:"popupOldPriceLetterSpacing",letterSpacingUnitName:"popupOldPriceLetterSpacingUnit",wordSpacingName:"popupOldPriceWordSpacing",wordSpacingUnitName:"popupOldPriceWordSpacingUnit",isAdaptive:!0}),(0,r.createElement)(ke,{name:"popupOldPriceColor",label:i.__("Old Price Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(u.__experimentalHeading,{level:"3",upperCase:!1,style:{marginBlock:"16px 0px",color:"#4D5E6F"}},i.__("Subscription typography","masterstudy-lms-learning-management-system")),(0,r.createElement)(Zt,{fontSizeName:"subscriptionFontSize",fontSizeUnitName:"subscriptionFontSizeUnit",fontWeightName:"subscriptionFontWeight",textTransformName:"subscriptionTextTransform",fontStyleName:"subscriptionFontStyle",textDecorationName:"subscriptionTextDecoration",lineHeightName:"subscriptionLineHeight",lineHeightUnitName:"subscriptionLineHeightUnit",letterSpacingName:"subscriptionLetterSpacing",letterSpacingUnitName:"subscriptionLetterSpacingUnit",wordSpacingName:"subscriptionWordSpacing",wordSpacingUnitName:"subscriptionWordSpacingUnit",isAdaptive:!0}),(0,r.createElement)(ke,{name:"subscriptionColor",label:i.__("Subscription Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(ke,{name:"subscriptionIconColor",label:i.__("Subscription Icon Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}))),(0,r.createElement)(m,{condition:"masterstudy/courses-preset-full-size-image"===e},(0,r.createElement)(sr,{title:i.__("Excerpt","masterstudy-lms-learning-management-system"),accordionFields:to},(0,r.createElement)(Xe,{name:"popupExcerptMargin",label:i.__("Margin","masterstudy-lms-learning-management-system"),unitName:"popupExcerptMarginUnit",isAdaptive:!0}),(0,r.createElement)(Xe,{name:"popupExcerptPadding",label:i.__("Padding","masterstudy-lms-learning-management-system"),unitName:"popupExcerptPaddingUnit",isAdaptive:!0}),(0,r.createElement)(Zt,{fontSizeName:"popupExcerptFontSize",fontSizeUnitName:"popupExcerptFontSizeUnit",fontWeightName:"popupExcerptFontWeight",textTransformName:"popupExcerptTextTransform",fontStyleName:"popupExcerptFontStyle",textDecorationName:"popupExcerptTextDecoration",lineHeightName:"popupExcerptLineHeight",lineHeightUnitName:"popupExcerptLineHeightUnit",letterSpacingName:"popupExcerptLetterSpacing",letterSpacingUnitName:"popupExcerptLetterSpacingUnit",wordSpacingName:"popupExcerptWordSpacing",wordSpacingUnitName:"popupExcerptWordSpacingUnit",isAdaptive:!0}),(0,r.createElement)(ke,{name:"popupExcerptColor",label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}))),(0,r.createElement)(sr,{title:i.__("Progress","masterstudy-lms-learning-management-system"),accordionFields:Lr},(0,r.createElement)(Zt,{fontSizeName:"progressFontSize",fontSizeUnitName:"progressFontSizeUnit",fontWeightName:"progressFontWeight",textTransformName:"progressTextTransform",fontStyleName:"progressFontStyle",textDecorationName:"progressTextDecoration",lineHeightName:"progressLineHeight",lineHeightUnitName:"progressLineHeightUnit",letterSpacingName:"progressLetterSpacing",letterSpacingUnitName:"progressLetterSpacingUnit",wordSpacingName:"progressWordSpacing",wordSpacingUnitName:"progressWordSpacingUnit",isAdaptive:!0}),(0,r.createElement)(ke,{name:"progressColor",label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(ke,{name:"progressEmptyColor",label:i.__("Empty BAr Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(ke,{name:"progressFilledColor",label:i.__("Filled Bar Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(Xe,{name:"progressMargin",label:i.__("Margin","masterstudy-lms-learning-management-system"),unitName:"progressMarginUnit",isAdaptive:!0})),(0,r.createElement)(m,{condition:"masterstudy/courses-preset-full-size-image"===e},(0,r.createElement)(sr,{title:i.__("Add to Wishlist","masterstudy-lms-learning-management-system"),accordionFields:lo},(0,r.createElement)(ke,{name:"wishlistIconEmptyColor",label:i.__("Icon Empty Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(ke,{name:"wishlistIconFilledColor",label:i.__("Icon Filled Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(ke,{name:"wishlistLoadedColorColor",label:i.__("Loaded Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}))),(0,r.createElement)(m,{condition:"masterstudy/courses-preset-full-size-image"===e},(0,r.createElement)(sr,{title:i.__("Data Slots","masterstudy-lms-learning-management-system"),accordionFields:Ar},(0,r.createElement)(Zt,{fontSizeName:"metaFontSize",fontSizeUnitName:"metaFontSizeUnit",fontWeightName:"metaFontWeight",textTransformName:"metaTextTransform",fontStyleName:"metaFontStyle",textDecorationName:"metaTextDecoration",lineHeightName:"metaLineHeight",lineHeightUnitName:"metaLineHeightUnit",letterSpacingName:"metaLetterSpacing",letterSpacingUnitName:"metaLetterSpacingUnit",wordSpacingName:"metaWordSpacing",wordSpacingUnitName:"metaWordSpacingUnit",isAdaptive:!0}),(0,r.createElement)(ke,{name:"metaColor",label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(ke,{name:"metaBgColor",label:i.__("Background Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(rt,{label:i.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"metaBorderStyle",borderColorName:"metaBorderColor",borderWidthName:"metaBorderWidth",isAdaptive:!0}),(0,r.createElement)(st,{name:"metaBorderRadius",label:i.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,r.createElement)(Xe,{name:"metaMargin",label:i.__("Margin","masterstudy-lms-learning-management-system"),unitName:"metaMarginUnit",isAdaptive:!0}),(0,r.createElement)(Xe,{name:"metaPadding",label:i.__("Padding","masterstudy-lms-learning-management-system"),unitName:"metaPaddingUnit",isAdaptive:!0}))))},To=({attributes:e,setAttributes:t,innerPresetName:n,setInnerPresetName:i,clientId:a})=>{const{onResetByFieldName:o,changedFieldsByName:l}=((e,t,n,i=[])=>{const a=(e=>{const t={};return Object.entries(e).forEach((([e,n])=>{e.includes("UAG")||(t[e]=n)})),t})(t),r=!g(e,a),o=((e,t,n)=>{const i=new Map;return n.forEach((n=>{i.has(n)||i.set(n,(()=>t({[n]:e[n]})))})),i})(e,n,i),l=((e,t,n)=>{const i=new Map;return n.forEach((n=>{i.has(n)?i.set(n,!1):i.set(n,!g(e[n],t[n]))})),i})(e,a,i);return{hasChanges:r,onResetByFieldName:o,changedFieldsByName:l}})(co,e,t,Object.keys(co));return(0,r.createElement)(p.InspectorControls,null,(0,r.createElement)(W,{attributes:e,setAttributes:t,defaultValues:co,onResetByFieldName:o,changedFieldsByName:l,clientId:a},(0,r.createElement)(on,{generalTab:(0,r.createElement)(No,{setInnerPresetName:i,innerPresetName:n}),styleTab:(0,r.createElement)(Uo,{innerPresetName:n}),advancedTab:(0,r.createElement)(r.Fragment,null)})))},Mo=e=>({...N("preset",e,uo),...E("p-popup",e,"popupShadowColor","popupShadowHorizontal","popupShadowVertical","popupShadowBlur","popupShadowSpread","popupShadowInset",!0),...E("p-card",e,"cardShadowColor","cardShadowHorizontal","cardShadowVertical","cardShadowBlur","cardShadowSpread","cardShadowInset",!0),...U("preset",e.cardItems),...U("preset",e.popupItems)}),Fo=JSON.parse('{"UU":"masterstudy/courses-preset"}');(0,a.registerBlockType)(Fo.UU,{title:i._x("MasterStudy Courses Preset","block title","masterstudy-lms-learning-management-system"),description:i._x("Choose courses preset","block description","masterstudy-lms-learning-management-system"),category:"masterstudy-lms-blocks",icon:l,edit:({attributes:e,setAttributes:t,context:n,clientId:o})=>{const[c,u]=(0,s.useState)("");((e,t)=>{(0,s.useEffect)((()=>{t({coursesPerRow:e["masterstudy/coursesPerRow"],coursesPerRowTablet:e["masterstudy/coursesPerRowTablet"],coursesPerRowMobile:e["masterstudy/coursesPerRowMobile"]})}),[t,e["masterstudy/coursesPerRow"],e["masterstudy/coursesPerRowTablet"],e["masterstudy/coursesPerRowMobile"]])})(n,t);const m=(0,p.useBlockProps)({className:d()("lms-courses-preset",`lms-courses-preset__item-${e.coursesPerRow}`,`lms-courses-preset__item-tablet-${e.coursesPerRowTablet}`,`lms-courses-preset__item-mobile-${e.coursesPerRowMobile}`,{[`featured-label--position-${e.featuredPosition}`]:"start"!==e.featuredPosition},{[`status-label--type-${e.statusStyle}`]:"rectangle"!==e.statusStyle},{[`status-label--position-${e.statusPosition}`]:"start"!==e.statusPosition}),style:Mo(e)}),{hasInnerBlocks:g,replaceInnerBlocks:h}=pr(o,u),b=(0,p.useInnerBlocksProps)({...m},{allowedBlocks:["masterstudy/courses-preset-classic","masterstudy/courses-preset-price-button"],templateLock:!1,renderAppender:!1});return(0,r.createElement)(r.Fragment,null,(0,r.createElement)(To,{attributes:e,setAttributes:t,innerPresetName:c,setInnerPresetName:u,clientId:o}),g?(0,r.createElement)("div",{...b}):(0,r.createElement)("div",{...m},(0,r.createElement)("div",{className:"lms-block-variation-picker"},(0,r.createElement)(p.__experimentalBlockVariationPicker,{icon:l,label:i.__("Choose preset","masterstudy-lms-learning-management-system"),instructions:i.__("Select a variation to start with.","masterstudy-lms-learning-management-system"),variations:So,onSelect:e=>{e.attributes&&t(e.attributes),e.innerBlocks&&h(o,(0,a.createBlocksFromInnerBlocksTemplate)(e.innerBlocks),!1)}}))))},save:({attributes:e})=>{const t=p.useBlockProps.save({className:d()("lms-courses-preset",`lms-courses-preset__item-${e.coursesPerRow}`,`lms-courses-preset__item-tablet-${e.coursesPerRowTablet}`,`lms-courses-preset__item-mobile-${e.coursesPerRowMobile}`,{[`featured-label--position-${e.featuredPosition}`]:"start"!==e.featuredPosition},{[`status-label--type-${e.statusStyle}`]:"rectangle"!==e.statusStyle},{[`status-label--position-${e.statusPosition}`]:"start"!==e.statusPosition}),style:Mo(e)}),n=p.useInnerBlocksProps.save(t);return(0,r.createElement)("div",{...n})}})},6942:(e,t)=>{var n;!function(){"use strict";var i={}.hasOwnProperty;function a(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=o(e,r(n)))}return e}function r(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return a.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)i.call(e,n)&&e[n]&&(t=o(t,n));return t}function o(e,t){return t?e?e+" "+t:e+t:e}e.exports?(a.default=a,e.exports=a):void 0===(n=function(){return a}.apply(t,[]))||(e.exports=n)}()}},n={};function i(e){var a=n[e];if(void 0!==a)return a.exports;var r=n[e]={exports:{}};return t[e](r,r.exports,i),r.exports}i.m=t,e=[],i.O=(t,n,a,r)=>{if(!n){var o=1/0;for(c=0;c<e.length;c++){for(var[n,a,r]=e[c],l=!0,s=0;s<n.length;s++)(!1&r||o>=r)&&Object.keys(i.O).every((e=>i.O[e](n[s])))?n.splice(s--,1):(l=!1,r<o&&(o=r));if(l){e.splice(c--,1);var p=a();void 0!==p&&(t=p)}}return t}r=r||0;for(var c=e.length;c>0&&e[c-1][2]>r;c--)e[c]=e[c-1];e[c]=[n,a,r]},i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},i.d=(e,t)=>{for(var n in t)i.o(t,n)&&!i.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},i.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e;i.g.importScripts&&(e=i.g.location+"");var t=i.g.document;if(!e&&t&&(t.currentScript&&(e=t.currentScript.src),!e)){var n=t.getElementsByTagName("script");if(n.length)for(var a=n.length-1;a>-1&&(!e||!/^http(s?):/.test(e));)e=n[a--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),i.p=e+"../../"})(),(()=>{var e={7434:0,9550:0};i.O.j=t=>0===e[t];var t=(t,n)=>{var a,r,[o,l,s]=n,p=0;if(o.some((t=>0!==e[t]))){for(a in l)i.o(l,a)&&(i.m[a]=l[a]);if(s)var c=s(i)}for(t&&t(n);p<o.length;p++)r=o[p],i.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return i.O(c)},n=globalThis.webpackChunkmasterstudy_lms_learning_management_system=globalThis.webpackChunkmasterstudy_lms_learning_management_system||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})();var a=i.O(void 0,[9550],(()=>i(1163)));a=i.O(a)})();