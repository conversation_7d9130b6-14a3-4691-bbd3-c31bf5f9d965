<?php

if (!defined('ABSPATH')) {
    exit;
}

/**
 * MSCB_Frontend handles the frontend display of course bundles.
 */
class MSCB_Frontend {
    private $bundle;

    /**
     * Initialize hooks for frontend display
     */
    public function __construct() {
        $this->bundle = new MSCB_Bundle();
        
        // Add shortcodes
        add_shortcode('mscb_bundles', array($this, 'bundles_shortcode'));
        add_shortcode('mscb_bundle', array($this, 'single_bundle_shortcode'));
        
        // Add bundles to courses archive
        add_action('stm_lms_after_courses_list', array($this, 'display_bundles_after_courses'));
        
        // Add bundle template to single course page
        add_filter('single_template', array($this, 'bundle_single_template'));
        
        // Enqueue frontend scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        
        // Register REST API endpoints
        add_action('rest_api_init', array($this, 'register_rest_routes'));
        
        // Register AJAX handlers for bundle purchase
        add_action('wp_ajax_mscb_add_bundle_to_cart', array($this, 'add_bundle_to_cart'));
        add_action('wp_ajax_nopriv_mscb_add_bundle_to_cart', array($this, 'add_bundle_to_cart'));
        
        // Make bundles purchasable in MasterStudy LMS
        add_filter('stm_lms_before_single_item_cart_title', array($this, 'maybe_modify_cart_title'), 10, 2);
        add_filter('stm_lms_get_item_url', array($this, 'maybe_modify_item_url'), 10, 2);
        
        // Handle bundle purchase completion and enrollment
        add_action('stm_lms_order_accepted', array($this, 'process_bundle_purchase'), 10, 2);
        
        // Modify order item details to show bundle name
        add_filter('stm_lms_order_items', array($this, 'modify_order_items'), 10, 1);

        add_action('wp_enqueue_scripts', array($this, 'enqueue_styles'));
    }

    /**
     * Enqueue frontend scripts and styles
     */
    public function enqueue_scripts() {
        wp_enqueue_style('mscb-frontend-css', MSCB_PLUGIN_URL . 'assets/css/mscb-frontend.css', array(), MSCB_VERSION);
        wp_enqueue_script('mscb-frontend-js', MSCB_PLUGIN_URL . 'assets/js/mscb-frontend.js', array('jquery'), MSCB_VERSION, true);
        
        // Localize script with bundle data
        wp_localize_script('mscb-frontend-js', 'mscb_data', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('mscb-frontend-nonce'),
        ));
    }

    /**
     * Enqueue frontend styles
     */
    public function enqueue_styles() {
        wp_enqueue_style(
            'font-awesome',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css'
        );

        // Add styles
        ?>
        <style>
            /* Bundle grid layout */
            .mscb-bundles-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                gap: 30px;
                margin: 30px 0;
            }
            .mscb-bundle-item {
                background: #fff;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                overflow: hidden;
                transition: transform 0.2s;
            }
            .mscb-bundle-item:hover {
                transform: translateY(-5px);
            }
            .mscb-bundle-thumb img {
                width: 100%;
                height: auto;
            }
            .mscb-bundle-content {
                padding: 20px;
            }
            .mscb-bundle-title {
                font-size: 18px;
                margin: 0 0 10px;
                color: #273044;
                font-weight: 600;
            }
            .mscb-bundle-meta {
                color: #666;
                font-size: 14px;
                margin-bottom: 15px;
            }
            .mscb-bundle-meta i {
                color: #2441e7;
                margin-right: 5px;
            }
            .mscb-bundle-courses {
                margin: 15px 0;
            }
            .mscb-course-item {
                font-size: 14px;
                color: #273044;
                padding: 8px 0;
                border-bottom: 1px solid #eee;
            }
            .mscb-course-item:last-child {
                border-bottom: none;
            }
            .mscb-course-more {
                font-size: 13px;
                color: #777;
                font-style: italic;
                margin-top: 5px;
            }
            /* Price display */
            .mscb-bundle-pricing {
                display: flex;
                align-items: center;
                gap: 10px;
                margin: 15px 0;
                flex-wrap: wrap;
            }
            .mscb-bundle-discount {
                background: #ff0000;
                color: white;
                padding: 3px 8px;
                border-radius: 3px;
                font-size: 12px;
                font-weight: bold;
                margin-left: auto;
            }
            .mscb-regular-price {
                text-decoration: line-through;
                color: #aaa;
                font-size: 14px;
                margin-right: 5px;
            }
            .mscb-bundle-price {
                color: #ff0000;
                font-size: 20px;
                font-weight: bold;
            }
            /* Button style */
            .mscb-bundle-actions {
                margin-top: 20px;
            }
            .mscb-view-bundle {
                display: inline-block;
                width: 100%;
                background: #ff9800;
                color: #fff;
                padding: 12px 20px;
                border-radius: 5px;
                text-decoration: none;
                transition: background 0.2s;
                text-align: center;
                font-weight: 600;
                font-size: 14px;
            }
            .mscb-view-bundle:hover {
                background: #f57c00;
                color: #fff;
                text-decoration: none;
            }
        </style>
        <?php
    }

    /**
     * Bundles shortcode callback
     */
    public function bundles_shortcode($atts) {
        $atts = shortcode_atts(array(
            'limit' => 10,
            'columns' => 3,
            'featured' => false,
        ), $atts);
        
        ob_start();
        $this->render_bundles_grid($atts);
        return ob_get_clean();
    }

    /**
     * Render bundles grid
     */
    public function render_bundles_grid($args = array()) {
        $defaults = array(
            'limit' => 10,
            'columns' => 3,
            'featured' => false,
            'title' => '',
            'subtitle' => '',
        );
        
        $args = wp_parse_args($args, $defaults);
        
        // Get bundles
        $query_args = array(
            'post_type' => 'mscb_bundle',
            'posts_per_page' => $args['limit'],
            'post_status' => 'publish',
        );
        
        if ($args['featured']) {
            $query_args['meta_query'] = array(
                array(
                    'key' => '_mscb_featured',
                    'value' => '1',
                    'compare' => '=',
                )
            );
        }
        
        $bundles = get_posts($query_args);
        
        if (empty($bundles)) {
            return;
        }
        
        echo '<div class="mscb-bundles-grid">';
        
        foreach ($bundles as $bundle) {
            $bundle_id = $bundle->ID;
            
            // Get bundle courses
            $courses = $this->bundle->get_bundle_courses($bundle_id);
            $course_count = count($courses);
            
            // Get bundle data
            $price = get_post_meta($bundle_id, '_mscb_price', true);
            $regular_price = get_post_meta($bundle_id, '_mscb_regular_price', true);
            $discount = '';
            if ($regular_price && $price) {
                $discount = round((($regular_price - $price) / $regular_price) * 100);
            }
            
            echo '<div class="mscb-bundle-item">';
            
            // Bundle thumbnail
            if (has_post_thumbnail($bundle_id)) {
                echo '<div class="mscb-bundle-thumb">';
                echo get_the_post_thumbnail($bundle_id, 'large');
                echo '</div>';
            }
            
            // Bundle content
            echo '<div class="mscb-bundle-content">';
            
            // Title
            echo '<h3 class="mscb-bundle-title">' . esc_html(get_the_title($bundle_id)) . '</h3>';
            
            // Course count badge
            echo '<div class="mscb-bundle-meta">';
            echo '<i class="fa fa-book"></i> ' . sprintf(_n('%d Course', '%d Courses', $course_count, 'masterstudy-course-bundler'), $course_count);
            echo '</div>';
            
            // Course list
            echo '<div class="mscb-bundle-courses">';
            $displayed_courses = 0;
            $more_courses = 0;
            
            foreach ($courses as $course) {
                if ($displayed_courses < 3) {
                    echo '<div class="mscb-course-item">' . esc_html(get_the_title($course['course_id'])) . '</div>';
                    $displayed_courses++;
                } else {
                    $more_courses++;
                }
            }
            
            if ($more_courses > 0) {
                echo '<div class="mscb-course-more">+' . $more_courses . ' more</div>';
            }
            echo '</div>';
            
            // View bundle button
            echo '<div class="mscb-bundle-actions">';
            echo '<a href="' . esc_url(get_permalink($bundle_id)) . '" class="mscb-view-bundle">';
            echo esc_html__('View Bundle', 'masterstudy-course-bundler');
            echo '</a>';
            echo '</div>';
            
            echo '</div>'; // .mscb-bundle-content
            echo '</div>'; // .mscb-bundle-item
        }
        
        echo '</div>'; // .mscb-bundles-grid
    }

    /**
     * Display bundles after courses in the course archive
     */
    public function display_bundles_after_courses() {
        echo '<div class="stm_lms_bundles_section">';
        $this->render_bundles_grid(array(
            'limit' => 6,
            'columns' => 3,
            'featured' => true,
            'title' => __('Course Bundles', 'masterstudy-course-bundler'),
            'subtitle' => __('Get multiple courses at discounted prices', 'masterstudy-course-bundler')
        ));
        echo '</div>';
    }

    /**
     * Custom template for single bundle
     */
    public function bundle_single_template($template) {
        global $post;
        
        if ($post->post_type === 'mscb_bundle') {
            $custom_template = MSCB_PLUGIN_DIR . 'templates/single-bundle.php';
            
            if (file_exists($custom_template)) {
                return $custom_template;
            }
        }
        
        return $template;
    }

    /**
     * Register REST API endpoints
     */
    public function register_rest_routes() {
        register_rest_route('mscb/v1', '/bundles', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_bundles_api'),
            'permission_callback' => '__return_true',
        ));
        
        register_rest_route('mscb/v1', '/bundle/(?P<id>\d+)', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_bundle_api'),
            'permission_callback' => '__return_true',
        ));
    }

    /**
     * API endpoint to get bundles
     */
    public function get_bundles_api($request) {
        $limit = $request->get_param('limit') ? intval($request->get_param('limit')) : 10;
        
        $query_args = array(
            'post_type' => 'mscb_bundle',
            'posts_per_page' => $limit,
            'post_status' => 'publish',
        );
        
        $bundles = get_posts($query_args);
        $data = array();
        
        foreach ($bundles as $bundle) {
            $bundle_data = $this->get_bundle_data($bundle->ID);
            if ($bundle_data) {
                $data[] = $bundle_data;
            }
        }
        
        return rest_ensure_response($data);
    }

    /**
     * API endpoint to get a single bundle
     */
    public function get_bundle_api($request) {
        $bundle_id = $request->get_param('id');
        $bundle_data = $this->get_bundle_data($bundle_id);
        
        if (!$bundle_data) {
            return new WP_Error('not_found', __('Bundle not found', 'masterstudy-course-bundler'), array('status' => 404));
        }
        
        return rest_ensure_response($bundle_data);
    }

    /**
     * Get bundle data for API
     */
    private function get_bundle_data($bundle_id) {
        $bundle = get_post($bundle_id);
        
        if (!$bundle || $bundle->post_type !== 'mscb_bundle' || $bundle->post_status !== 'publish') {
            return false;
        }
        
        $price = get_post_meta($bundle_id, '_mscb_price', true);
        $sale_price = get_post_meta($bundle_id, '_mscb_sale_price', true);
        $courses = $this->bundle->get_bundle_courses($bundle_id);
        
        $courses_data = array();
        foreach ($courses as $course) {
            $course_post = get_post($course['course_id']);
            if ($course_post) {
                $courses_data[] = array(
                    'id' => $course['course_id'],
                    'title' => $course_post->post_title,
                    'url' => get_permalink($course['course_id']),
                );
            }
        }
        
        return array(
            'id' => $bundle_id,
            'title' => $bundle->post_title,
            'content' => $bundle->post_content,
            'excerpt' => $bundle->post_excerpt,
            'url' => get_permalink($bundle_id),
            'price' => $price,
            'sale_price' => $sale_price,
            'thumbnail' => get_the_post_thumbnail_url($bundle_id, 'large'),
            'courses' => $courses_data,
        );
    }
    
    /**
     * AJAX handler for adding a bundle to cart
     */
    public function add_bundle_to_cart() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'mscb-frontend-nonce')) {
            wp_send_json_error(array('message' => 'Invalid security token'));
            return;
        }
        
        // Check if bundle ID is provided
        if (!isset($_POST['bundle_id']) || empty($_POST['bundle_id'])) {
            wp_send_json_error(array('message' => 'Bundle ID is required'));
            return;
        }
        
        $bundle_id = intval($_POST['bundle_id']);
        
        // Check if bundle exists
        $bundle_post = get_post($bundle_id);
        if (!$bundle_post || $bundle_post->post_type !== 'mscb_bundle') {
            wp_send_json_error(array('message' => 'Bundle not found'));
            return;
        }
        
        // Set up bundle as a purchasable item in MasterStudy LMS
        update_post_meta($bundle_id, 'single_sale', '1'); // Mark as purchasable
        
        // Get bundle price
        $price = get_post_meta($bundle_id, '_mscb_price', true);
        $sale_price = get_post_meta($bundle_id, '_mscb_sale_price', true);
        
        // Use sale price if available, otherwise use regular price
        $final_price = !empty($sale_price) ? $sale_price : $price;
        
        // Set price meta for MasterStudy LMS
        update_post_meta($bundle_id, 'price', $price);
        update_post_meta($bundle_id, 'sale_price', $sale_price);
        
        // Get current user
        $user_id = get_current_user_id();
        
        try {
            // Redirect to checkout directly
            $checkout_url = site_url('/checkout-2/');
            
            // Add to MasterStudy LMS cart using their API
            if (class_exists('STM_LMS_Cart')) {
                // Use MasterStudy's cart function
                $cart_result = STM_LMS_Cart::masterstudy_add_to_cart($bundle_id, $user_id);
                
                wp_send_json_success(array(
                    'message' => 'Bundle added to cart',
                    'redirect_url' => $checkout_url,
                    'cart_result' => $cart_result
                ));
            } else {
                wp_send_json_error(array('message' => 'MasterStudy LMS Cart is not available'));
            }
        } catch (Exception $e) {
            wp_send_json_error(array('message' => $e->getMessage()));
        }
    }
    
    /**
     * Modify cart item title for bundles
     */
    public function maybe_modify_cart_title($title, $item_id) {
        // Check if this is a bundle
        if (get_post_type($item_id) === 'mscb_bundle') {
            return get_the_title($item_id) . ' <span class="bundle-tag">' . __('Bundle', 'masterstudy-course-bundler') . '</span>';
        }
        
        return $title;
    }
    
    /**
     * Modify item URL for bundles
     */
    public function maybe_modify_item_url($url, $item_id) {
        // Check if this is a bundle
        if (get_post_type($item_id) === 'mscb_bundle') {
            return get_permalink($item_id);
        }
        
        return $url;
    }
    
    /**
     * Process bundle purchase and enroll user in all courses
     * 
     * @param int $user_id User ID
     * @param array $order Order data
     */
    public function process_bundle_purchase($user_id, $order) {
        if (empty($order['items'])) {
            return;
        }
        
        foreach ($order['items'] as $item) {
            // Check if the purchased item is a bundle
            if (get_post_type($item['item_id']) === 'mscb_bundle') {
                $bundle_id = $item['item_id'];
                
                // Get all courses in the bundle
                $bundle_courses = $this->bundle->get_bundle_courses($bundle_id);
                
                if (!empty($bundle_courses)) {
                    // Enroll user in each course
                    foreach ($bundle_courses as $course) {
                        $course_id = $course['course_id'];
                        
                        // Use MasterStudy LMS course enrollment class for proper enrollment
                        if (class_exists('STM_LMS_Course')) {
                            STM_LMS_Course::add_user_course(
                                $course_id,
                                $user_id,
                                0, // current_lesson_id (start from beginning)
                                0, // progress (start at 0%)
                                false, // is_translate
                                '', // enterprise
                                $bundle_id, // bundle_id - important to track the source
                                '', // for_points
                                '' // instructor_id
                            );
                        } elseif (function_exists('stm_lms_add_user_course')) {
                            // Fallback to basic enrollment if class doesn't exist
                            stm_lms_add_user_course(compact('user_id', 'course_id'));
                        }
                        
                        // Record enrollment in our custom table
                        $this->record_bundle_enrollment($user_id, $bundle_id, $course_id, $order['id']);
                        
                        // Log enrollment for debugging
                        error_log("Enrolled user {$user_id} in course {$course_id} from bundle {$bundle_id}");
                    }
                }
            }
        }
    }
    
    /**
     * Record bundle enrollment in custom table
     */
    private function record_bundle_enrollment($user_id, $bundle_id, $course_id, $order_id) {
        global $wpdb;
        
        $table = $wpdb->prefix . 'mscb_bundle_enrollments';
        
        // Check if enrollment already exists
        $exists = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM {$table} WHERE user_id = %d AND bundle_id = %d AND course_id = %d",
            $user_id, $bundle_id, $course_id
        ));
        
        if (!$exists) {
            // Insert new enrollment record
            $wpdb->insert(
                $table,
                array(
                    'user_id' => $user_id,
                    'bundle_id' => $bundle_id,
                    'course_id' => $course_id,
                    'order_id' => $order_id,
                    'enrolled_date' => current_time('mysql')
                ),
                array('%d', '%d', '%d', '%d', '%s')
            );
        }
    }
    
    /**
     * Modify order items to show bundle name
     * 
     * @param array $items Order items
     * @return array Modified order items
     */
    public function modify_order_items($items) {
        if (empty($items)) {
            return $items;
        }
        
        foreach ($items as $key => $item) {
            // Check if the item is a bundle
            if (isset($item['item_id']) && get_post_type($item['item_id']) === 'mscb_bundle') {
                $bundle_id = $item['item_id'];
                $bundle_title = get_the_title($bundle_id);
                
                // Update item title to show bundle name
                $items[$key]['title'] = $bundle_title . ' ' . __('Bundle', 'masterstudy-course-bundler');
                
                // Get bundle image if available
                if (has_post_thumbnail($bundle_id)) {
                    $items[$key]['image'] = get_the_post_thumbnail_url($bundle_id, 'thumbnail');
                }
            }
        }
        
        return $items;
    }
}
