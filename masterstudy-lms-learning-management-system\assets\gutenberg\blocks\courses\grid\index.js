(()=>{var e,t={2886:(e,t,n)=>{"use strict";const a=window.React,l=window.wp.blocks,r=window.wp.blockEditor,s=window.wp.i18n;let o=function(e){return e.ALL="all",e.SOME="some",e}({}),i=function(e){return e.NORMAL="Normal",e.HOVER="Hover",e.ACTIVE="Active",e.FOCUS="Focus",e}({}),c=function(e){return e.DESKTOP="Desktop",e.TABLET="Tablet",e.MOBILE="Mobile",e}({}),m=function(e){return e.TOP_lEFT="top-left",e.TOP_CENTER="top-center",e.TOP_RIGHT="top-right",e.BOTTOM_lEFT="bottom-left",e.BOTTOM_CENTER="bottom-center",e.BOTTOM_RIGHT="bottom-right",e}({});s.__("Small","masterstudy-lms-learning-management-system"),s.__("Normal","masterstudy-lms-learning-management-system"),s.__("Large","masterstudy-lms-learning-management-system"),s.__("Extra Large","masterstudy-lms-learning-management-system");const d="wp-block-masterstudy-settings__",u={top:"",right:"",bottom:"",left:""},p=(m.TOP_lEFT,m.TOP_CENTER,m.TOP_RIGHT,m.BOTTOM_lEFT,m.BOTTOM_CENTER,m.BOTTOM_RIGHT,[{label:s.__("Newest","masterstudy-lms-learning-management-system"),value:"date_high"},{label:s.__("Oldest","masterstudy-lms-learning-management-system"),value:"date_low"},{label:s.__("Overall rating","masterstudy-lms-learning-management-system"),value:"rating"},{label:s.__("Popular","masterstudy-lms-learning-management-system"),value:"popular"},{label:s.__("Price low","masterstudy-lms-learning-management-system"),value:"price_low"},{label:s.__("Price high","masterstudy-lms-learning-management-system"),value:"price_high"}]),g={coursesPerPage:4,coursesPerRow:4,coursesPerRowTablet:2,coursesPerRowMobile:1,coursesOrderBy:"date_high",coursesCategory:[]},h=Object.keys(g),v={...g},y={layoutMargin:u,layoutMarginTablet:u,layoutMarginMobile:u,layoutMarginUnit:"px",layoutMarginUnitTablet:"px",layoutMarginUnitMobile:"px",layoutPadding:{top:"100",right:"20",bottom:"100",left:"20"},layoutPaddingTablet:u,layoutPaddingMobile:u,layoutPaddingUnit:"px",layoutPaddingUnitTablet:"px",layoutPaddingUnitMobile:"px",layoutBackground:"#EEF1F7",layoutBorderStyle:"none",layoutBorderStyleTablet:"",layoutBorderStyleMobile:"",layoutBorderColor:"",layoutBorderColorTablet:"",layoutBorderColorMobile:"",layoutBorderWidth:u,layoutBorderWidthTablet:u,layoutBorderWidthMobile:u,layoutBorderWidthUnit:"px",layoutBorderWidthUnitTablet:"px",layoutBorderWidthUnitMobile:"px",layoutBorderRadius:u,layoutBorderRadiusTablet:u,layoutBorderRadiusMobile:u,layoutBorderRadiusUnit:"px",layoutBorderRadiusUnitTablet:"px",layoutBorderRadiusUnitMobile:"px",layoutWidth:"alignfull",layoutZIndex:null,layoutZIndexTablet:null,layoutZIndexMobile:null},_=Object.keys(y),b={...v,...{...y}},E=new Map([["layoutMargin",{unit:"layoutMarginUnit",isAdaptive:!0}],["layoutPadding",{unit:"layoutPaddingUnit",isAdaptive:!0}],["layoutBackground",{}],["layoutBorderStyle",{isAdaptive:!0}],["layoutBorderColor",{isAdaptive:!0}],["layoutBorderWidth",{isAdaptive:!0,unit:"layoutBorderWidthUnit"}],["layoutBorderRadius",{isAdaptive:!0,unit:"layoutBorderRadiusUnit"}],["layoutZIndex",{isAdaptive:!0}]]),C=(e,t)=>{if(typeof e!=typeof t)return!1;if(Number.isNaN(e)&&Number.isNaN(t))return!0;if("object"!=typeof e||null===e||null===t)return e===t;if(Object.keys(e).length!==Object.keys(t).length)return!1;if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;const n=e.slice().sort(),a=t.slice().sort();return n.every(((e,t)=>C(e,a[t])))}for(const n of Object.keys(e))if(!C(e[n],t[n]))return!1;return!0},f=(e=[])=>e.map((e=>({label:e.name,value:e.term_id}))),w=["",null,void 0,"null","undefined"],N=[".jpg",".jpeg",".png",".gif"],M=e=>w.includes(e),k=(e,t,n="")=>{const a=e[t];return"object"==typeof a&&null!==a?((e,t)=>{return n=e,Object.values(n).every((e=>w.includes(e)))?null:((e,t="")=>{const n=Object.entries(e).reduce(((e,[n,a])=>(e[n]=(a||"0")+t,e)),{});return`${n.top} ${n.right} ${n.bottom} ${n.left}`})(e,t);var n})(a,n):((e,t)=>M(e)?e:(e=>{if("string"!=typeof e)return!1;const t=e.slice(e.lastIndexOf("."));return N.includes(t)||e.startsWith("blob")})(e)?`url('${e}')`:String(e+t))(a,n)},x=(e,t,n)=>{const a={};return n.forEach((({isAdaptive:n,hasHover:l,unit:r},s)=>{if(t.hasOwnProperty(s)){const{unitMeasureDesktop:c,unitMeasureTablet:m,unitMeasureMobile:d}=((e,t)=>{var n;return{unitMeasureDesktop:null!==(n=e[t])&&void 0!==n?n:"",unitMeasureTablet:t?e[t+"Tablet"]:"",unitMeasureMobile:t?e[t+"Mobile"]:""}})(t,r);if(n&&l){const{desktopHoverPropertyName:n,mobileHoverPropertyName:l,tabletHoverPropertyName:r}=(e=>{const t=e+i.HOVER;return{desktopHoverPropertyName:t,tabletHoverPropertyName:t+"Tablet",mobileHoverPropertyName:t+"Mobile"}})(s),o=k(t,n,c);M(o)||(a[`--lms-${e}-${n}`]=o);const u=k(t,r,m);M(u)||(a[`--lms-${e}-${r}`]=u);const p=k(t,l,d);M(p)||(a[`--lms-${e}-${l}`]=p)}if(l){const n=s+i.HOVER,l=k(t,n,c);M(l)||(a[`--lms-${e}-${n}`]=l)}if(n){const{desktopPropertyName:n,mobilePropertyName:l,tabletPropertyName:r}={desktopPropertyName:o=s,tabletPropertyName:o+"Tablet",mobilePropertyName:o+"Mobile"},i=k(t,n,c);M(i)||(a[`--lms-${e}-${n}`]=i);const u=k(t,r,m);M(u)||(a[`--lms-${e}-${r}`]=u);const p=k(t,l,d);M(p)||(a[`--lms-${e}-${l}`]=p)}const u=k(t,s,c);M(u)||(a[`--lms-${e}-${s}`]=u)}var o})),a};function O(e){return Array.isArray(e)?e.map((e=>d+e)):d+e}var S=n(6942),T=n.n(S);const H=window.wp.components,A=({condition:e,fallback:t=null,children:n})=>(0,a.createElement)(a.Fragment,null,e?n:t),V=window.wp.element,B=window.wp.data,R=()=>(0,B.useSelect)((e=>e("core/editor").getDeviceType()||e("core/edit-site")?.__experimentalGetPreviewDeviceType()||e("core/edit-post")?.__experimentalGetPreviewDeviceType()||e("masterstudy/store")?.getDeviceType()),[])||"Desktop",P=(e=!1)=>{const[t,n]=(0,V.useState)(e),a=(0,V.useCallback)((()=>{n(!0)}),[]);return{isOpen:t,onClose:(0,V.useCallback)((()=>{n(!1)}),[]),onOpen:a,onToggle:(0,V.useCallback)((()=>{n((e=>!e))}),[])}},D=(0,V.createContext)(null),I=({children:e,...t})=>(0,a.createElement)(D.Provider,{value:{...t}},e),L=()=>{const e=(0,V.useContext)(D);if(!e)throw new Error("No settings context provided");return e},F=(e="")=>{const{attributes:t,setAttributes:n,onResetByFieldName:a,changedFieldsByName:l}=L();return{value:t[e],onChange:t=>n({[e]:t}),onReset:a.get(e),isChanged:l.get(e)}},Z=(e,t=!1,n=!1)=>{const{hoverName:a,onChangeHoverName:l}=(()=>{const[e,t]=(0,V.useState)(i.NORMAL);return{hoverName:e,onChangeHoverName:(0,V.useCallback)((e=>{t(e)}),[])}})(),r=R();return{fieldName:(0,V.useMemo)((()=>{const l=a===i.HOVER?a:"",s=r===c.DESKTOP?"":r;return n&&t?e+l+s:n&&!t?e+l:t&&!n?e+s:e}),[e,n,t,a,r]),hoverName:a,onChangeHoverName:l}},U=(e,t=!1,n="Normal")=>{const a=R(),l=(0,V.useMemo)((()=>{const l=n===i.NORMAL?"":n,r=a===c.DESKTOP?"":a;return l&&t?e+l+r:l&&!t?e+l:t&&!l?e+r:e}),[e,t,n,a]),{value:r,isChanged:s,onReset:o}=F(l);return{fieldName:l,value:r,isChanged:s,onReset:o}},j=(e=[],t=o.ALL)=>{const{attributes:n}=L();return!e.length||(t===o.ALL?e.every((({name:e,value:t})=>{const a=n[e];return Array.isArray(t)?Array.isArray(a)?C(t,a):t.includes(a):t===a})):t!==o.SOME||e.some((({name:e,value:t})=>{const a=n[e];return Array.isArray(t)?Array.isArray(a)?C(t,a):t.includes(a):t===a})))},z=e=>{const t=(0,V.useRef)(null);return(0,V.useEffect)((()=>{const n=n=>{t.current&&!t.current.contains(n.target)&&e()};return document.addEventListener("click",n),()=>{document.removeEventListener("click",n)}}),[t,e]),t},$=e=>(0,a.createElement)(H.SVG,{width:"16",height:"16",viewBox:"0 0 12 13",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(H.Path,{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})),W=e=>(0,a.createElement)(H.SVG,{width:"16",height:"16",viewBox:"0 0 14 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(H.G,{"clip-path":"url(#clip0_1068_38993)"},(0,a.createElement)(H.Path,{d:"M11.1973 8.60005L8.74967 8.11005V5.67171C8.74967 5.517 8.68822 5.36863 8.57882 5.25923C8.46942 5.14984 8.32105 5.08838 8.16634 5.08838H6.99967C6.84496 5.08838 6.69659 5.14984 6.5872 5.25923C6.4778 5.36863 6.41634 5.517 6.41634 5.67171V8.58838H5.24967C5.15021 8.58844 5.05241 8.61391 4.96555 8.66238C4.87869 8.71084 4.80565 8.78068 4.75336 8.86529C4.70106 8.9499 4.67125 9.04646 4.66674 9.14582C4.66223 9.24518 4.68317 9.34405 4.72759 9.43305L6.47759 12.933C6.57676 13.1302 6.77859 13.255 6.99967 13.255H11.083C11.2377 13.255 11.3861 13.1936 11.4955 13.0842C11.6049 12.9748 11.6663 12.8264 11.6663 12.6717V9.17171C11.6665 9.03685 11.6198 8.90612 11.5343 8.80186C11.4487 8.69759 11.3296 8.62626 11.1973 8.60005Z"}),(0,a.createElement)(H.Path,{d:"M10.4997 1.58838H3.49967C2.85626 1.58838 2.33301 2.11221 2.33301 2.75505V6.83838C2.33301 7.4818 2.85626 8.00505 3.49967 8.00505H5.24967V6.83838H3.49967V2.75505H10.4997V6.83838H11.6663V2.75505C11.6663 2.11221 11.1431 1.58838 10.4997 1.58838Z"})),(0,a.createElement)("defs",null,(0,a.createElement)("clipPath",{id:"clip0_1068_38993"},(0,a.createElement)(H.Rect,{width:"14",height:"14",transform:"translate(0 0.421875)"})))),G=[{value:i.NORMAL,label:s.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)($,{onClick:e})},{value:i.HOVER,label:s.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(W,{onClick:e})},{value:i.ACTIVE,label:s.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(W,{onClick:e})},{value:i.FOCUS,label:s.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(W,{onClick:e})}],K={[i.NORMAL]:{icon:(0,a.createElement)($,null),label:s.__("Normal State","masterstudy-lms-learning-management-system")},[i.HOVER]:{icon:(0,a.createElement)(W,null),label:s.__("Hovered State","masterstudy-lms-learning-management-system")},[i.ACTIVE]:{icon:(0,a.createElement)(W,null),label:s.__("Active State","masterstudy-lms-learning-management-system")},[i.FOCUS]:{icon:(0,a.createElement)(W,null),label:s.__("Focus State","masterstudy-lms-learning-management-system")}},X=(e,t)=>{let n=[];return n=e.length?G.filter((t=>e.includes(t.value))):G,n=n.filter((e=>e.value!==t)),{ICONS_MAP:K,options:n}},[Y,q,J,Q,ee]=O(["hover-state","hover-state__selected","hover-state__selected__opened-menu","hover-state__menu","hover-state__menu__item"]),te=({stateOptions:e,currentState:t,onSelect:n})=>{const{isOpen:l,onOpen:r,onClose:s}=P(),o=z(s),{ICONS_MAP:i,options:c}=X(e,t);return(0,a.createElement)("div",{className:Y,ref:o},(0,a.createElement)("div",{className:T()([q],{[J]:l}),onClick:r,title:i[t]?.label},i[t]?.icon),(0,a.createElement)(A,{condition:l},(0,a.createElement)("div",{className:Q},c.map((({value:e,icon:t,label:l})=>(0,a.createElement)("div",{key:e,className:ee,title:l},t((()=>n(e)))))))))},ne=O("color-indicator"),ae=(0,V.memo)((({color:e,onChange:t})=>(0,a.createElement)("div",{className:ne},(0,a.createElement)(r.PanelColorSettings,{enableAlpha:!0,disableCustomColors:!1,__experimentalHasMultipleOrigins:!0,__experimentalIsRenderedInSidebar:!0,colorSettings:[{label:"",value:e,onChange:t}]}))));var le;function re(){return re=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},re.apply(null,arguments)}var se,oe,ie=function(e){return a.createElement("svg",re({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 12 13"},e),le||(le=a.createElement("path",{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})))};function ce(){return ce=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},ce.apply(null,arguments)}var me=function(e){return a.createElement("svg",ce({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 14 15"},e),se||(se=a.createElement("g",{clipPath:"url(#state-hover_svg__a)"},a.createElement("path",{d:"M11.197 8.6 8.75 8.11V5.672a.583.583 0 0 0-.584-.584H7a.583.583 0 0 0-.584.584v2.916H5.25a.584.584 0 0 0-.522.845l1.75 3.5c.099.197.3.322.522.322h4.083a.583.583 0 0 0 .583-.583v-3.5a.58.58 0 0 0-.469-.572"}),a.createElement("path",{d:"M10.5 1.588h-7c-.644 0-1.167.524-1.167 1.167v4.083c0 .644.523 1.167 1.167 1.167h1.75V6.838H3.5V2.755h7v4.083h1.166V2.755c0-.643-.523-1.167-1.166-1.167"}))),oe||(oe=a.createElement("defs",null,a.createElement("clipPath",{id:"state-hover_svg__a"},a.createElement("path",{d:"M0 .422h14v14H0z"})))))};const de=[{value:i.NORMAL,label:s.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(ie,{onClick:e})},{value:i.HOVER,label:s.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(me,{onClick:e})}],ue={[i.NORMAL]:{icon:(0,a.createElement)(ie,null),label:s.__("Normal State","masterstudy-lms-learning-management-system")},[i.HOVER]:{icon:(0,a.createElement)(me,null),label:s.__("Hovered State","masterstudy-lms-learning-management-system")}},pe=O("hover-state"),ge=O("hover-state__selected"),he=O("hover-state__selected__opened-menu"),ve=O("has-changes"),ye=O("hover-state__menu"),_e=O("hover-state__menu__item"),be=(0,V.memo)((e=>{const{hoverName:t,onChangeHoverName:n,fieldName:l}=e,{changedFieldsByName:r}=L(),s=r.get(l),{isOpen:o,onOpen:i,onClose:c}=P(),m=z(c),{ICONS_MAP:d,options:u}=(e=>{const t=(0,V.useMemo)((()=>de.filter((t=>t.value!==e))),[e]);return{ICONS_MAP:ue,options:t}})(t),p=(0,V.useCallback)((e=>{n(e),c()}),[n,c]);return(0,a.createElement)("div",{className:pe,ref:m},(0,a.createElement)("div",{className:T()([ge],{[he]:o,[ve]:s}),onClick:i,title:d[t]?.label},d[t]?.icon),(0,a.createElement)(A,{condition:o},(0,a.createElement)("div",{className:ye},u.map((({value:e,icon:t,label:n})=>(0,a.createElement)("div",{key:e,className:_e,title:n},t((()=>p(e)))))))))})),Ee={Desktop:{icon:"desktop",label:s.__("Desktop","masterstudy-lms-learning-management-system")},Tablet:{icon:"tablet",label:s.__("Tablet","masterstudy-lms-learning-management-system")},Mobile:{icon:"smartphone",label:s.__("Mobile","masterstudy-lms-learning-management-system")}},Ce=[{value:c.DESKTOP,icon:"desktop",label:s.__("Desktop","masterstudy-lms-learning-management-system")},{value:c.TABLET,icon:"tablet",label:s.__("Tablet","masterstudy-lms-learning-management-system")},{value:c.MOBILE,icon:"smartphone",label:s.__("Mobile","masterstudy-lms-learning-management-system")}],fe=O("device-picker"),we=O("device-picker__selected"),Ne=O("device-picker__selected__opened-menu"),Me=O("device-picker__menu"),ke=O("device-picker__menu__item"),xe=()=>{const{isOpen:e,onOpen:t,onClose:n}=P(),{value:l,onChange:r}=(e=>{const t=R(),n=(0,B.useDispatch)();return{value:(0,V.useMemo)((()=>Ee[t]),[t]),onChange:t=>{n("core/edit-site")&&n("core/edit-site").__experimentalSetPreviewDeviceType?n("core/edit-site").__experimentalSetPreviewDeviceType(t):n("core/edit-post")&&n("core/edit-post").__experimentalSetPreviewDeviceType?n("core/edit-post").__experimentalSetPreviewDeviceType(t):n("masterstudy/store").setDeviceType(t),e()}}})(n),s=(e=>(0,V.useMemo)((()=>Ce.filter((t=>t.icon!==e))),[e]))(l.icon),o=z(n);return(0,a.createElement)("div",{className:fe,ref:o},(0,a.createElement)(H.Dashicon,{className:T()([we],{[Ne]:e}),icon:l.icon,size:16,onClick:t,title:l.label}),(0,a.createElement)(A,{condition:e},(0,a.createElement)("div",{className:Me},s.map((e=>(0,a.createElement)(H.Dashicon,{key:e.value,icon:e.icon,size:16,onClick:()=>r(e.value),className:ke,title:e.label}))))))},Oe=O("reset-button"),Se=({onReset:e})=>(0,a.createElement)(H.Dashicon,{icon:"undo",onClick:e,className:Oe,size:16}),Te=[{label:"PX",value:"px"},{label:"%",value:"%"},{label:"EM",value:"em"},{label:"REM",value:"rem"},{label:"VW",value:"vw"},{label:"VH",value:"vh"}],He=O("unit"),Ae=O("unit__single"),Ve=O("unit__list"),Be=({name:e,isAdaptive:t})=>{const{isOpen:n,onOpen:l,onClose:r}=P(),{fieldName:s}=Z(e,t),{value:o,onChange:i}=F(s),c=z(r);return(0,a.createElement)("div",{className:He,ref:c},(0,a.createElement)("div",{className:Ae,onClick:l},o),(0,a.createElement)(A,{condition:n},(0,a.createElement)("div",{className:Ve},Te.map((({value:e,label:t})=>(0,a.createElement)("div",{key:e,onClick:()=>(i(e),void r())},t))))))},Re=O("popover-modal"),Pe=O("popover-modal__close dashicon dashicons dashicons-no-alt"),De=e=>{const{isOpen:t,onClose:n,popoverContent:l}=e;return(0,a.createElement)(A,{condition:t},(0,a.createElement)(H.Popover,{position:"middle left",onClose:n,className:Re},l,(0,a.createElement)("span",{onClick:n,className:Pe})))},Ie=O("setting-label"),Le=O("setting-label__content"),Fe=e=>{const{label:t,isChanged:n=!1,onReset:l,showDevicePicker:r=!0,HoverStateControl:s=null,unitName:o,popoverContent:i=null,dependencies:c}=e,{isOpen:m,onClose:d,onToggle:u}=P();return j(c)?(0,a.createElement)("div",{className:Ie},(0,a.createElement)("div",{className:Le},(0,a.createElement)("div",{onClick:u},t),(0,a.createElement)(A,{condition:Boolean(i)},(0,a.createElement)(De,{isOpen:m,onClose:d,popoverContent:i})),(0,a.createElement)(A,{condition:r},(0,a.createElement)(xe,null)),(0,a.createElement)(A,{condition:Boolean(s)},s)),(0,a.createElement)(A,{condition:Boolean(o)},(0,a.createElement)(Be,{name:o,isAdaptive:r})),(0,a.createElement)(A,{condition:n},(0,a.createElement)(Se,{onReset:l}))):null},Ze=O("suffix"),Ue=()=>(0,a.createElement)("div",{className:Ze},(0,a.createElement)(H.Dashicon,{icon:"color-picker",size:16})),je=O("color-picker"),ze=e=>{const{name:t,label:n,placeholder:l,dependencyMode:r,dependencies:s,isAdaptive:o=!1,hasHover:i=!1}=e,{fieldName:c,hoverName:m,onChangeHoverName:d}=Z(t,o,i),{value:u,isChanged:p,onChange:g,onReset:h}=F(c);return j(s,r)?(0,a.createElement)("div",{className:je},(0,a.createElement)(A,{condition:Boolean(n)},(0,a.createElement)(Fe,{label:n,isChanged:p,onReset:h,showDevicePicker:o,HoverStateControl:(0,a.createElement)(A,{condition:i},(0,a.createElement)(be,{hoverName:m,onChangeHoverName:d,fieldName:c}))})),(0,a.createElement)(H.__experimentalInputControl,{prefix:(0,a.createElement)(ae,{color:u,onChange:g}),suffix:(0,a.createElement)(Ue,null),onChange:g,value:u,placeholder:l})):null},$e=O("number-steppers"),We=O("indent-steppers"),Ge=O("indent-stepper-plus"),Ke=O("indent-stepper-minus"),Xe=({onIncrement:e,onDecrement:t,withArrows:n=!1})=>n?(0,a.createElement)("span",{className:We},(0,a.createElement)("button",{onClick:e,className:Ge}),(0,a.createElement)("button",{onClick:t,className:Ke})):(0,a.createElement)("span",{className:$e},(0,a.createElement)("button",{onClick:e},"+"),(0,a.createElement)("button",{onClick:t},"-")),[Ye,qe]=O(["indents","indents-control"]),Je=({name:e,label:t,unitName:n,popoverContent:l,dependencyMode:r,dependencies:o,isAdaptive:i=!1})=>{const{fieldName:c}=Z(e,i),{value:m,onResetSegmentedBox:d,hasChanges:u,handleInputIncrement:p,handleInputDecrement:g,updateDirectionsValues:h,lastFieldValue:v}=((e,t)=>{const{value:n,isChanged:a,onChange:l,onReset:r}=F(e),{onResetByFieldName:s,changedFieldsByName:o}=L(),i=a||o.get(t),c=e=>{l({...n,...e})},[m,d]=(0,V.useState)(!1);return{value:n,onResetSegmentedBox:()=>{r(),s.get(t)()},hasChanges:i,handleInputIncrement:e=>Number(n[e])+1,handleInputDecrement:e=>Number(n[e])-1,updateDirectionsValues:(e,t,n)=>{e?(d(!1),c({top:n,right:n,bottom:n,left:n})):(d(n),c({[t]:n}))},lastFieldValue:m}})(c,n),[y,_]=(0,V.useState)((()=>{const{left:e,right:t,top:n,bottom:a}=m;return""!==e&&e===t&&t===n&&n===a})),b=e=>{const[t,n]=Object.entries(e)[0];h(y,t,n)},E=e=>()=>{const t=p(e);h(y,e,String(t))},C=e=>()=>{const t=g(e);h(y,e,String(t))};return j(o,r)?(0,a.createElement)("div",{className:Ye},(0,a.createElement)(A,{condition:Boolean(t)},(0,a.createElement)(Fe,{label:null!=t?t:"",isChanged:u,onReset:d,unitName:n,popoverContent:l,showDevicePicker:i})),(0,a.createElement)("div",{className:`${qe} ${y?"active":""}`},(0,a.createElement)("div",null,(0,a.createElement)(H.__experimentalNumberControl,{value:m.top,onChange:e=>{b({top:e})},spinControls:"none",suffix:(0,a.createElement)(Xe,{onIncrement:E("top"),onDecrement:C("top"),withArrows:!0})}),(0,a.createElement)("div",null,s.__("Top","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(H.__experimentalNumberControl,{value:m.right,onChange:e=>{b({right:e})},spinControls:"none",suffix:(0,a.createElement)(Xe,{onIncrement:E("right"),onDecrement:C("right"),withArrows:!0})}),(0,a.createElement)("div",null,s.__("Right","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(H.__experimentalNumberControl,{value:m.bottom,onChange:e=>{b({bottom:e})},spinControls:"none",suffix:(0,a.createElement)(Xe,{onIncrement:E("bottom"),onDecrement:C("bottom"),withArrows:!0})}),(0,a.createElement)("div",null,s.__("Bottom","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(H.__experimentalNumberControl,{value:m.left,onChange:e=>{b({left:e})},spinControls:"none",suffix:(0,a.createElement)(Xe,{onIncrement:E("left"),onDecrement:C("left"),withArrows:!0})}),(0,a.createElement)("div",null,s.__("Left","masterstudy-lms-learning-management-system"))),(0,a.createElement)(H.Dashicon,{icon:"dashicons "+(y?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{y||!1===v||h(!0,"left",v),_((e=>!e))}}))):null},[Qe,et,tt,nt]=O(["toggle-group-wrapper","toggle-group","toggle-group__toggle","toggle-group__active-toggle"]),at=e=>{const{name:t,options:n,label:l,isAdaptive:r=!1,dependencyMode:s,dependencies:o}=e,{fieldName:i}=Z(t,r),{value:c,isChanged:m,onChange:d,onReset:u}=F(i);return j(o,s)?(0,a.createElement)("div",{className:Qe},(0,a.createElement)(A,{condition:Boolean(l)},(0,a.createElement)(Fe,{label:l,isChanged:m,onReset:u,showDevicePicker:r})),(0,a.createElement)("div",{className:et},n.map((e=>(0,a.createElement)("div",{key:e.value,className:T()([tt],{[nt]:e.value===c}),onClick:()=>d(e.value)},e.label))))):null},[lt,rt,st,ot]=O(["border-control","border-control-solid","border-control-dashed","border-control-dotted"]),it=e=>{const{label:t,borderStyleName:n,borderColorName:l,borderWidthName:r,dependencyMode:o,dependencies:i,isAdaptive:c=!1,hasHover:m=!1}=e,[d,u]=(0,V.useState)("Normal"),{fieldName:p,value:g,isChanged:h,onReset:v}=U(n,c,d),{fieldName:y,isChanged:_,onReset:b}=U(l,c,d),{fieldName:E,isChanged:C,onReset:f}=U(r,c,d);if(!j(i,o))return null;const w=h||_||C;return(0,a.createElement)("div",{className:T()([lt],{"has-reset-button":w})},(0,a.createElement)(Fe,{label:t,isChanged:w,onReset:()=>{v(),b(),f()},showDevicePicker:c,HoverStateControl:(0,a.createElement)(A,{condition:m},(0,a.createElement)(te,{stateOptions:["Normal","Hover"],currentState:d,onSelect:u}))}),(0,a.createElement)(at,{options:[{label:(0,a.createElement)("span",null,s.__("None","masterstudy-lms-learning-management-system")),value:"none"},{label:(0,a.createElement)("span",{className:rt}),value:"solid"},{label:(0,a.createElement)("span",{className:st},(0,a.createElement)("span",null)),value:"dashed"},{label:(0,a.createElement)("span",{className:ot},(0,a.createElement)("span",null,(0,a.createElement)("span",null))),value:"dotted"}],name:p}),(0,a.createElement)(A,{condition:"none"!==g},(0,a.createElement)(ze,{name:y,placeholder:s.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Je,{name:E})))},ct=O("border-radius"),mt=O("border-radius-control"),dt=({name:e,label:t,unitName:n,popoverContent:l,dependencyMode:r,dependencies:s,isAdaptive:o=!1,hasHover:i=!1})=>{const{fieldName:c}=Z(e,o,i),{value:m,onResetBorderRadius:d,hasChanges:u,handleInputIncrement:p,handleInputDecrement:g,updateDirectionsValues:h,lastFieldValue:v}=((e,t)=>{const[n,a]=(0,V.useState)(!1),{value:l,isChanged:r,onChange:s,onReset:o}=F(e),{onResetByFieldName:i,changedFieldsByName:c}=L(),m=r||c.get(t),d=e=>{s({...l,...e})};return{value:l,onResetBorderRadius:()=>{o(),i.get(t)()},hasChanges:m,handleInputIncrement:e=>Number(l[e])+1,handleInputDecrement:e=>Number(l[e])-1,updateDirectionsValues:(e,t,n)=>{e?(d({top:n,right:n,bottom:n,left:n}),a(!1)):(d({[t]:n}),a(n))},lastFieldValue:n}})(c,n),[y,_]=(0,V.useState)((()=>{const{left:e,right:t,top:n,bottom:a}=m;return""!==e&&e===t&&t===n&&n===a})),b=e=>{const[t,n]=Object.entries(e)[0];h(y,t,n)},E=e=>()=>{const t=p(e);h(y,e,String(t))},C=e=>()=>{const t=g(e);h(y,e,String(t))};return j(s,r)?(0,a.createElement)("div",{className:ct},(0,a.createElement)(Fe,{label:t,isChanged:u,onReset:d,unitName:n,popoverContent:l,showDevicePicker:o}),(0,a.createElement)("div",{className:T()([mt],{"has-reset-button":u,active:y})},(0,a.createElement)("div",{className:"number-control-top"},(0,a.createElement)(H.__experimentalNumberControl,{value:m.top,onChange:e=>{b({top:e})},spinControls:"none",suffix:(0,a.createElement)(Xe,{onIncrement:E("top"),onDecrement:C("top"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-right"},(0,a.createElement)(H.__experimentalNumberControl,{value:m.right,onChange:e=>{b({right:e})},spinControls:"none",suffix:(0,a.createElement)(Xe,{onIncrement:E("right"),onDecrement:C("right"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-left"},(0,a.createElement)(H.__experimentalNumberControl,{value:m.left,onChange:e=>{b({left:e})},spinControls:"none",suffix:(0,a.createElement)(Xe,{onIncrement:E("left"),onDecrement:C("left"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-bottom"},(0,a.createElement)(H.__experimentalNumberControl,{value:m.bottom,onChange:e=>{b({bottom:e})},spinControls:"none",suffix:(0,a.createElement)(Xe,{onIncrement:E("bottom"),onDecrement:C("bottom"),withArrows:!0})})),(0,a.createElement)(H.Dashicon,{icon:"dashicons "+(y?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{y||!1===v||h(!0,"left",v),_((e=>!e))}}))):null},ut=(O("box-shadow-preset"),O("presets")),pt=O("presets__item-wrapper"),gt=O("presets__item-wrapper__preset"),ht=O("presets__item-wrapper__name"),vt=((0,V.memo)((e=>{const{presets:t,activePreset:n,onSelectPreset:l,PresetItem:r,detectIsActive:s,detectByIndex:o=!1}=e;return(0,a.createElement)("div",{className:ut},t.map((({name:e,...t},i)=>(0,a.createElement)("div",{key:i,className:T()([pt],{active:s(n,o?i:t)}),onClick:()=>l(t)},(0,a.createElement)("div",{className:gt},(0,a.createElement)(r,{preset:t})),(0,a.createElement)("span",{className:ht},e)))))})),O("range-control")),yt=e=>{const{name:t,label:n,min:l,max:r,unitName:s,dependencyMode:o,dependencies:i,isAdaptive:c=!1}=e,{fieldName:m}=Z(t,c),{value:d,onChange:u,onResetNumberField:p,hasChanges:g}=((e,t)=>{const{value:n,isChanged:a,onChange:l,onReset:r}=F(e),{onResetByFieldName:s,changedFieldsByName:o}=L();return{value:n,onChange:l,onResetNumberField:()=>{r(),s.get(t)()},hasChanges:a||o.get(t)}})(m,s);return j(i,o)?(0,a.createElement)("div",{className:vt},(0,a.createElement)(A,{condition:Boolean(n)},(0,a.createElement)(Fe,{label:n,isChanged:g,onReset:p,unitName:s,showDevicePicker:c})),(0,a.createElement)(H.RangeControl,{value:d,onChange:u,min:l,max:r})):null},_t=O("switch"),bt=e=>{const{name:t,label:n,dependencyMode:l,dependencies:r,isAdaptive:s=!1}=e,{fieldName:o}=Z(t,s),{value:i,onChange:c}=F(o);return j(r,l)?(0,a.createElement)("div",{className:_t,"data-has-label":Boolean(n).toString()},(0,a.createElement)(H.ToggleControl,{label:n,checked:i,onChange:c}),(0,a.createElement)(A,{condition:s},(0,a.createElement)(xe,null))):null},Et=(O("box-shadow-settings"),O("box-shadow-presets-title"),O("input-field"),O("input-field-control"),O("number-field"),O("number-field-control"),({className:e})=>(0,a.createElement)("div",{className:e},s.__("No options","masterstudy-lms-learning-management-system"))),Ct=O("select__single-item"),ft=O("select__container"),wt=O("select__container__multi-item"),Nt=({multiple:e,value:t,options:n,onChange:l})=>{const{singleValue:r,multipleValue:s}=((e,t,n)=>({singleValue:(0,V.useMemo)((()=>t?null:n.find((t=>t.value===e))?.label),[t,e,n]),multipleValue:(0,V.useMemo)((()=>t?e:null),[t,e])}))(t,e,n);return(0,a.createElement)(A,{condition:e,fallback:(0,a.createElement)("div",{className:Ct},r)},(0,a.createElement)("div",{className:ft},s?.map((e=>{const t=n.find((t=>t.value===e));return t?(0,a.createElement)("div",{key:t.value,className:wt},(0,a.createElement)("div",null,t.label),(0,a.createElement)(H.Dashicon,{icon:"no-alt",onClick:()=>l(t.value),size:16})):null}))))},Mt=O("select"),kt=O("select__select-box"),xt=O("select__placeholder"),Ot=O("select__select-box-multiple"),St=O("select__menu"),Tt=O("select__menu__options-container"),Ht=O("select__menu__item"),At=e=>{const{options:t,multiple:n=!1,placeholder:l="Select",value:r,onSelect:s}=e,{isOpen:o,onToggle:i,onClose:c}=P(),m=z(c),d=((e,t,n)=>(0,V.useMemo)((()=>n&&Array.isArray(e)?t.filter((t=>!e.includes(t.value))):t.filter((t=>t.value!==e))),[e,t,n]))(r,t,n),u=((e,t,n,a)=>(0,V.useCallback)((l=>{if(t&&Array.isArray(e)){const t=e.includes(l)?e.filter((e=>e!==l)):[...e,l];n(t)}else n(l),a()}),[t,e,n,a]))(r,n,s,c),p=((e,t)=>(0,V.useMemo)((()=>t&&Array.isArray(e)?Boolean(e.length):Boolean(e)),[t,e]))(r,n),g=n&&Array.isArray(r)&&r?.length>0;return(0,a.createElement)("div",{className:Mt,ref:m},(0,a.createElement)("div",{className:T()([kt],{[Ot]:g}),onClick:i},(0,a.createElement)(A,{condition:p,fallback:(0,a.createElement)("div",{className:xt},l)},(0,a.createElement)(Nt,{onChange:u,options:t,multiple:n,value:r})),(0,a.createElement)(H.Dashicon,{icon:o?"arrow-up-alt2":"arrow-down-alt2",size:16,style:{color:"#4D5E6F"}})),(0,a.createElement)(A,{condition:o},(0,a.createElement)("div",{className:St},(0,a.createElement)(A,{condition:Boolean(d.length),fallback:(0,a.createElement)(Et,{className:Ht})},(0,a.createElement)("div",{className:Tt},d.map((e=>(0,a.createElement)("div",{key:e.value,onClick:()=>u(e.value),className:Ht},e.label))))))))},Vt=O("setting-select"),Bt=e=>{const{name:t,options:n,label:l,multiple:r=!1,placeholder:s,isAdaptive:o=!1,dependencyMode:i,dependencies:c}=e,{fieldName:m}=Z(t,o),{value:d,isChanged:u,onChange:p,onReset:g}=F(m);return j(c,i)?(0,a.createElement)("div",{className:Vt},(0,a.createElement)(A,{condition:Boolean(l)},(0,a.createElement)(Fe,{label:l,isChanged:u,onReset:g,showDevicePicker:o})),(0,a.createElement)(At,{options:n,value:d,onSelect:p,multiple:r,placeholder:s})):null},Rt=O("row-select"),Pt=O("row-select__label"),Dt=O("row-select__control"),It=e=>{const{name:t,label:n,options:l,isAdaptive:r=!1}=e,{fieldName:s}=Z(t,r),{isChanged:o,onReset:i}=F(s);return(0,a.createElement)("div",{className:Rt},(0,a.createElement)("div",{className:Pt},(0,a.createElement)("div",null,n),(0,a.createElement)(A,{condition:r},(0,a.createElement)(xe,null))),(0,a.createElement)("div",{className:Dt},(0,a.createElement)(Bt,{name:t,options:l,isAdaptive:r}),(0,a.createElement)(A,{condition:o},(0,a.createElement)(Se,{onReset:i}))))},Lt=(O("typography-select"),O("typography-select-label"),O("typography"),O("file-upload"),O("file-upload__wrap"),O("file-upload__image"),O("file-upload__remove"),O("file-upload__replace"),(0,V.createContext)({activeTab:0,setActiveTab:()=>{}})),Ft=()=>{const e=(0,V.useContext)(Lt);if(!e)throw new Error("useTabs should be used inside Tabs");return e},Zt=({children:e})=>{const[t,n]=(0,V.useState)(0);return(0,a.createElement)(Lt.Provider,{value:{activeTab:t,setActiveTab:n}},(0,a.createElement)("div",{className:`active-tab-${t}`},e))},Ut=O("tab-list"),jt=({children:e})=>(0,a.createElement)("div",{className:Ut},V.Children.map(e,((e,t)=>(0,V.cloneElement)(e,{index:t})))),zt=O("tab"),$t=O("tab-active"),Wt=O("content"),Gt=({index:e,title:t,icon:n})=>{const{activeTab:l,setActiveTab:r}=Ft();return(0,a.createElement)("div",{className:T()([zt],{[$t]:l===e}),onClick:()=>r(e)},(0,a.createElement)("div",{className:Wt},(0,a.createElement)("div",null,n),(0,a.createElement)("div",null,t)))},Kt=({children:e})=>(0,a.createElement)("div",null,V.Children.map(e,((e,t)=>(0,V.cloneElement)(e,{index:t})))),Xt=O("tab-panel"),Yt=({index:e,children:t})=>{const{activeTab:n}=Ft();return n===e?(0,a.createElement)("div",{className:Xt},t):null},qt=({generalTab:e,styleTab:t,advancedTab:n})=>(0,a.createElement)(Zt,null,(0,a.createElement)(jt,null,(0,a.createElement)(Gt,{title:s.__("General","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(H.Dashicon,{icon:"layout"})}),(0,a.createElement)(Gt,{title:s.__("Style","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(H.Dashicon,{icon:"admin-appearance"})}),(0,a.createElement)(Gt,{title:s.__("Advanced","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(H.Dashicon,{icon:"admin-settings"})})),(0,a.createElement)(Kt,null,(0,a.createElement)(Yt,null,e),(0,a.createElement)(Yt,null,t),(0,a.createElement)(Yt,null,n)));window.ReactDOM;"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement;function Jt(e){const t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function Qt(e){return"nodeType"in e}function en(e){var t,n;return e?Jt(e)?e:Qt(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function tn(e){const{Document:t}=en(e);return e instanceof t}function nn(e){return!Jt(e)&&e instanceof en(e).HTMLElement}function an(e){return e instanceof en(e).SVGElement}function ln(e){return e?Jt(e)?e.document:Qt(e)?tn(e)?e:nn(e)||an(e)?e.ownerDocument:document:document:document}function rn(e){return function(t){for(var n=arguments.length,a=new Array(n>1?n-1:0),l=1;l<n;l++)a[l-1]=arguments[l];return a.reduce(((t,n)=>{const a=Object.entries(n);for(const[n,l]of a){const a=t[n];null!=a&&(t[n]=a+e*l)}return t}),{...t})}}const sn=rn(-1);function on(e){if(function(e){if(!e)return!1;const{TouchEvent:t}=en(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return function(e){return"clientX"in e&&"clientY"in e}(e)?{x:e.clientX,y:e.clientY}:null}var cn;!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(cn||(cn={}));const mn=Object.freeze({x:0,y:0});var dn,un,pn,gn;!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(dn||(dn={}));class hn{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach((e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)}))},this.target=e}add(e,t,n){var a;null==(a=this.target)||a.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function vn(e,t){const n=Math.abs(e.x),a=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+a**2)>t:"x"in t&&"y"in t?n>t.x&&a>t.y:"x"in t?n>t.x:"y"in t&&a>t.y}function yn(e){e.preventDefault()}function bn(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(un||(un={})),(gn=pn||(pn={})).Space="Space",gn.Down="ArrowDown",gn.Right="ArrowRight",gn.Left="ArrowLeft",gn.Up="ArrowUp",gn.Esc="Escape",gn.Enter="Enter";pn.Space,pn.Enter,pn.Esc,pn.Space,pn.Enter;function En(e){return Boolean(e&&"distance"in e)}function Cn(e){return Boolean(e&&"delay"in e)}class fn{constructor(e,t,n){var a;void 0===n&&(n=function(e){const{EventTarget:t}=en(e);return e instanceof t?e:ln(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;const{event:l}=e,{target:r}=l;this.props=e,this.events=t,this.document=ln(r),this.documentListeners=new hn(this.document),this.listeners=new hn(n),this.windowListeners=new hn(en(r)),this.initialCoordinates=null!=(a=on(l))?a:mn,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),this.windowListeners.add(un.Resize,this.handleCancel),this.windowListeners.add(un.DragStart,yn),this.windowListeners.add(un.VisibilityChange,this.handleCancel),this.windowListeners.add(un.ContextMenu,yn),this.documentListeners.add(un.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(Cn(t))return void(this.timeoutId=setTimeout(this.handleStart,t.delay));if(En(t))return}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handleStart(){const{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(un.Click,bn,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(un.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;const{activated:n,initialCoordinates:a,props:l}=this,{onMove:r,options:{activationConstraint:s}}=l;if(!a)return;const o=null!=(t=on(e))?t:mn,i=sn(a,o);if(!n&&s){if(En(s)){if(null!=s.tolerance&&vn(i,s.tolerance))return this.handleCancel();if(vn(i,s.distance))return this.handleStart()}return Cn(s)&&vn(i,s.tolerance)?this.handleCancel():void 0}e.cancelable&&e.preventDefault(),r(o)}handleEnd(){const{onEnd:e}=this.props;this.detach(),e()}handleCancel(){const{onCancel:e}=this.props;this.detach(),e()}handleKeydown(e){e.code===pn.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}const wn={move:{name:"pointermove"},end:{name:"pointerup"}};(class extends fn{constructor(e){const{event:t}=e,n=ln(t.target);super(e,wn,n)}}).activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;return!(!n.isPrimary||0!==n.button||(null==a||a({event:n}),0))}}];const Nn={move:{name:"mousemove"},end:{name:"mouseup"}};var Mn;!function(e){e[e.RightClick=2]="RightClick"}(Mn||(Mn={})),class extends fn{constructor(e){super(e,Nn,ln(e.event.target))}}.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;return n.button!==Mn.RightClick&&(null==a||a({event:n}),!0)}}];const kn={move:{name:"touchmove"},end:{name:"touchend"}};var xn,On,Sn,Tn,Hn;(class extends fn{constructor(e){super(e,kn)}static setup(){return window.addEventListener(kn.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(kn.move.name,e)};function e(){}}}).activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;const{touches:l}=n;return!(l.length>1||(null==a||a({event:n}),0))}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(xn||(xn={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(On||(On={})),dn.Backward,dn.Forward,dn.Backward,dn.Forward,function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(Sn||(Sn={})),function(e){e.Optimized="optimized"}(Tn||(Tn={})),Sn.WhileDragging,Tn.Optimized,Map,function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(Hn||(Hn={})),pn.Down,pn.Right,pn.Up,pn.Left,s.__("Lectures","masterstudy-lms-learning-management-system"),s.__("Duration","masterstudy-lms-learning-management-system"),s.__("Views","masterstudy-lms-learning-management-system"),s.__("Level","masterstudy-lms-learning-management-system"),s.__("Members","masterstudy-lms-learning-management-system"),s.__("Empty","masterstudy-lms-learning-management-system"),O("sortable__item"),O("sortable__item__disabled"),O("sortable__item__content"),O("sortable__item__content__drag-item"),O("sortable__item__content__drag-item__disabled"),O("sortable__item__content__title"),O("sortable__item__control"),O("sortable__item__icon"),O("nested-sortable"),O("nested-sortable__item"),O("sortable");const An=O("accordion"),Vn=O("accordion__header"),Bn=O("accordion__header-flex"),Rn=O("accordion__content"),Pn=O("accordion__icon"),Dn=O("accordion__title"),In=O("accordion__title-disabled"),Ln=O("accordion__indicator"),Fn=O("accordion__controls"),Zn=O("accordion__controls-disabled"),Un=({title:e,children:t,accordionFields:n,switchName:l,visible:r=!0,isDefaultOpen:s=!1})=>{const{isOpen:o,onToggle:i,disabled:c,onReset:m,hasChanges:d,onClose:u}=((e,t,n)=>{var a;const{isOpen:l,onToggle:r,onClose:s}=P(t),{defaultValues:o,attributes:i,setAttributes:c}=L(),m=((e,t,n)=>{for(const a of n)if(!C(e[a],t[a]))return!0;return!1})(o,i,e);return{isOpen:l,onToggle:r,disabled:!(null===(a=i[n])||void 0===a||a),hasChanges:m,onReset:t=>{t.stopPropagation(),c(e.reduce(((e,t)=>(e[t]=o[t],e)),{}))},onClose:s}})(n,s,l);return((e,t)=>{const{attributes:n}=L(),a=!n[t];(0,V.useEffect)((()=>{a&&e()}),[a,e])})(u,l),r?(0,a.createElement)("div",{className:An},(0,a.createElement)("div",{className:Vn},(0,a.createElement)("div",{className:Bn,onClick:c?null:i},(0,a.createElement)("div",{className:T()(Dn,{[In]:c,"with-switch":Boolean(l)})},(0,a.createElement)("div",null,e),(0,a.createElement)(A,{condition:d&&!c},(0,a.createElement)("div",{className:Ln}))),(0,a.createElement)("div",{className:T()(Fn,{[Zn]:c})},(0,a.createElement)(H.Dashicon,{icon:o?"arrow-up-alt2":"arrow-down-alt2",className:Pn,size:16}))),(0,a.createElement)(A,{condition:Boolean(l)},(0,a.createElement)(bt,{name:l})),(0,a.createElement)(A,{condition:d&&!c},(0,a.createElement)(Se,{onReset:m}))),o&&(0,a.createElement)("div",{className:Rn},t)):null};O("preset-picker"),O("preset-picker__label"),O("preset-picker__remove"),O("preset-picker__presets-list"),O("preset-picker__presets-list__item"),O("preset-picker__presets-list__item__preset"),O("preset-picker__presets-list__item__preset-active");const jn=({categories:e})=>{const{min:t,max:n}=((e,t=!1)=>{const n=R(),[a,l]=(0,V.useState)(e.default||{min:3,max:6});return(0,V.useEffect)((()=>{if(n===c.DESKTOP){const n=e.desktop||{min:t?2:3,max:6};l(n)}if(n===c.TABLET){const n=e.tablet||{min:t?1:2,max:3};l(n)}if(n===c.MOBILE){const t=e.mobile||{min:1,max:1};l(t)}}),[n,t,e]),a})({default:{min:3,max:6}});return(0,a.createElement)(a.Fragment,null,(0,a.createElement)(Un,{title:s.__("Layout","masterstudy-lms-learning-management-system"),accordionFields:h},(0,a.createElement)(yt,{name:"coursesPerPage",label:s.__("Courses per page","masterstudy-lms-learning-management-system"),min:2,max:24}),(0,a.createElement)(yt,{name:"coursesPerRow",label:s.__("Courses per row","masterstudy-lms-learning-management-system"),min:t,max:n,isAdaptive:!0}),(0,a.createElement)(Bt,{name:"coursesOrderBy",label:s.__("Sort By","masterstudy-lms-learning-management-system"),options:p}),e.length&&(0,a.createElement)(Bt,{name:"coursesCategory",multiple:!0,label:s.__("Course Category","masterstudy-lms-learning-management-system"),options:e})))},zn=()=>{const{widthOptions:e}=(()=>{const e=[{label:s.__("Auto","masterstudy-lms-learning-management-system"),value:"alignauto"},{label:s.__("Full width","masterstudy-lms-learning-management-system"),value:"alignfull"}],t=[{label:s.__("Cover","masterstudy-lms-learning-management-system"),value:"cover"},{label:s.__("Contain","masterstudy-lms-learning-management-system"),value:"contain"},{label:s.__("Inherit","masterstudy-lms-learning-management-system"),value:"inherit"},{label:s.__("Initial","masterstudy-lms-learning-management-system"),value:"initial"},{label:s.__("Revert","masterstudy-lms-learning-management-system"),value:"revert"},{label:s.__("Revert-layer","masterstudy-lms-learning-management-system"),value:"revert-layer"},{label:s.__("Unset","masterstudy-lms-learning-management-system"),value:"unset"}],n=[{label:s.__("Center center","masterstudy-lms-learning-management-system"),value:"center center"},{label:s.__("Center left","masterstudy-lms-learning-management-system"),value:"center left"},{label:s.__("Center right","masterstudy-lms-learning-management-system"),value:"center right"},{label:s.__("Top center","masterstudy-lms-learning-management-system"),value:"top center"},{label:s.__("Top left","masterstudy-lms-learning-management-system"),value:"top left"},{label:s.__("Top right","masterstudy-lms-learning-management-system"),value:"top right"},{label:s.__("Bottom center","masterstudy-lms-learning-management-system"),value:"bottom center"},{label:s.__("Bottom left","masterstudy-lms-learning-management-system"),value:"bottom left"},{label:s.__("Bottom right","masterstudy-lms-learning-management-system"),value:"bottom right"}],a=[{label:s.__("Center","masterstudy-lms-learning-management-system"),value:"center"},{label:s.__("Start","masterstudy-lms-learning-management-system"),value:"flex-start"},{label:s.__("End","masterstudy-lms-learning-management-system"),value:"flex-end"},{label:s.__("Space Between","masterstudy-lms-learning-management-system"),value:"space-between"},{label:s.__("Space Around","masterstudy-lms-learning-management-system"),value:"space-around"},{label:s.__("Space Evenly","masterstudy-lms-learning-management-system"),value:"space-evenly"}];return{filterOptions:p,widthOptions:e,backgroundSizeOptions:t,backgroundPositionOptions:n,alignContentOptions:a}})();return(0,a.createElement)(Un,{title:s.__("Layout","masterstudy-lms-learning-management-system"),accordionFields:_},(0,a.createElement)(Je,{name:"layoutMargin",label:s.__("Margin","masterstudy-lms-learning-management-system"),unitName:"layoutMarginUnit",isAdaptive:!0,dependencies:[{name:"layoutWidth",value:"alignauto"}]}),(0,a.createElement)(Je,{name:"layoutPadding",label:s.__("Padding","masterstudy-lms-learning-management-system"),unitName:"layoutPaddingUnit",isAdaptive:!0}),(0,a.createElement)(ze,{name:"layoutBackground",label:s.__("Background","masterstudy-lms-learning-management-system"),placeholder:s.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(it,{label:s.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"layoutBorderStyle",borderColorName:"layoutBorderColor",borderWidthName:"layoutBorderWidth",isAdaptive:!0}),(0,a.createElement)(dt,{name:"layoutBorderRadius",label:s.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,a.createElement)(It,{name:"layoutWidth",label:s.__("Width","masterstudy-lms-learning-management-system"),options:e}),(0,a.createElement)(yt,{name:"layoutZIndex",label:s.__("Z-Index","masterstudy-lms-learning-management-system"),min:0,max:100,isAdaptive:!0}))},$n=({attributes:e,setAttributes:t,categories:n})=>{const{onResetByFieldName:l,changedFieldsByName:s}=((e,t,n,a=[])=>{const l=(e=>{const t={};return Object.entries(e).forEach((([e,n])=>{e.includes("UAG")||(t[e]=n)})),t})(t),r=!C(e,l),s=((e,t,n)=>{const a=new Map;return n.forEach((n=>{a.has(n)||a.set(n,(()=>t({[n]:e[n]})))})),a})(e,n,a),o=((e,t,n)=>{const a=new Map;return n.forEach((n=>{a.has(n)?a.set(n,!1):a.set(n,!C(e[n],t[n]))})),a})(e,l,a);return{hasChanges:r,onResetByFieldName:s,changedFieldsByName:o}})(b,e,t,Object.keys(b));return(0,a.createElement)(r.InspectorControls,null,(0,a.createElement)(I,{attributes:e,setAttributes:t,defaultValues:b,onResetByFieldName:l,changedFieldsByName:s},(0,a.createElement)(qt,{generalTab:(0,a.createElement)(jn,{categories:n}),styleTab:(0,a.createElement)(zn,null),advancedTab:(0,a.createElement)(a.Fragment,null)})))},Wn=[["core/group",{className:"lms-courses-group-header"},[["core/group",{layout:{type:"flex",orientation:"vertical",justifyContent:"stretch"},templateLock:!0},[["core/group",{layout:{type:"constrained"},templateLock:"insert"},[["core/heading",{textAlign:"center",style:{typography:{fontSize:"48px"},color:{text:"#001931"},spacing:{margin:{top:"20px",bottom:"10px"}}},content:s.__("Courses Grid","masterstudy-lms-learning-management-system"),placeholder:s.__("Courses Container Title","masterstudy-lms-learning-management-system")}]]],["core/group",{layout:{type:"flex",flexWrap:"nowrap"},templateLock:"insert"},[["masterstudy/courses-tab-options"]]]]],["core/group",{},[["masterstudy/courses-tab-category"]]]]],["core/group",{className:"lms-courses-group-presets"},[["masterstudy/courses-preset"]]],["core/group",{className:"lms-courses-group-load-more"},[["masterstudy/courses-load-more"]]]],Gn=window.wp.apiFetch;var Kn=n.n(Gn);const Xn=JSON.parse('{"UU":"masterstudy/courses-container"}');(0,l.registerBlockType)(Xn.UU,{icon:{src:(0,a.createElement)("svg",{width:"512",height:"513",viewBox:"0 0 512 513",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,a.createElement)("g",{clipPath:"url(#clip0_708_21983)"},(0,a.createElement)("path",{d:"M471.767 1.93774H40.2229C18.5675 1.95767 1.01603 19.5091 0.996109 41.1645V472.709C1.01603 494.364 18.5675 511.916 40.2229 511.946H471.767C493.423 511.926 510.974 494.374 511.004 472.709V41.1645C510.984 19.5091 493.433 1.95767 471.767 1.93774ZM40.2229 21.5511H471.767C482.595 21.5611 491.371 30.3368 491.381 41.1645V80.3913H20.6095V41.1645C20.6195 30.3368 29.3952 21.5611 40.2229 21.5511ZM471.767 492.332H40.2229C29.3952 492.322 20.6195 483.546 20.6095 472.719V100.015H491.391V472.709C491.381 483.546 482.605 492.322 471.767 492.332Z",fill:"black"}),(0,a.createElement)("path",{d:"M471.777 512.942H40.2229C18.0694 512.922 0.0199222 494.872 0 472.719V41.1645C0.0199222 19.0111 18.0694 0.961573 40.2229 0.94165H471.767C493.931 0.961573 511.97 19.0111 511.99 41.1645V472.709C511.98 494.872 493.931 512.922 471.777 512.942ZM40.2229 2.93387C19.1651 2.95379 2.01214 20.1068 1.99222 41.1745V472.719C2.01214 493.777 19.1651 510.93 40.2328 510.949H471.777C492.835 510.93 509.988 493.776 510.008 472.709V41.1645C509.988 20.1068 492.835 2.95379 471.767 2.93387H40.2229ZM471.777 493.328H40.2229C28.8672 493.318 19.6233 484.074 19.6134 472.719V99.0185H492.377V472.709C492.377 484.074 483.133 493.318 471.777 493.328ZM21.6056 101.011V472.709C21.6156 482.969 29.9729 491.316 40.2229 491.326H471.767C482.027 491.316 490.374 482.969 490.384 472.709V101.011H21.6056ZM492.387 81.3974H19.6134V41.1645C19.6233 29.8089 28.8672 20.565 40.2229 20.555H471.767C483.123 20.565 492.367 29.8089 492.377 41.1645V81.3974H492.387ZM21.6056 79.4052H490.384V41.1645C490.374 30.9046 482.017 22.5572 471.767 22.5473H40.2229C29.963 22.5572 21.6156 30.9146 21.6056 41.1645V79.4052Z",fill:"black"}),(0,a.createElement)("path",{d:"M128.817 60.9272C134.247 60.9272 138.648 56.5255 138.648 51.0956C138.648 45.6658 134.247 41.264 128.817 41.264C123.387 41.264 118.985 45.6658 118.985 51.0956C118.985 56.5255 123.387 60.9272 128.817 60.9272Z",fill:"black"}),(0,a.createElement)("path",{d:"M128.817 61.9334C122.85 61.9334 117.989 57.0723 117.989 51.1057C117.989 45.139 122.85 40.278 128.817 40.278C134.784 40.278 139.645 45.139 139.645 51.1057C139.645 57.0723 134.784 61.9334 128.817 61.9334ZM128.817 42.2602C123.946 42.2602 119.981 46.2247 119.981 51.0957C119.981 55.9667 123.946 59.9312 128.817 59.9312C133.688 59.9312 137.652 55.9667 137.652 51.0957C137.652 46.2247 133.688 42.2602 128.817 42.2602Z",fill:"black"}),(0,a.createElement)("path",{d:"M89.4904 60.9272C94.9203 60.9272 99.322 56.5255 99.322 51.0956C99.322 45.6658 94.9203 41.264 89.4904 41.264C84.0606 41.264 79.6588 45.6658 79.6588 51.0956C79.6588 56.5255 84.0606 60.9272 89.4904 60.9272Z",fill:"black"}),(0,a.createElement)("path",{d:"M89.4904 61.9334C83.5237 61.9334 78.6627 57.0723 78.6627 51.1057C78.6627 45.139 83.5237 40.278 89.4904 40.278C95.4571 40.278 100.318 45.139 100.318 51.1057C100.318 57.0723 95.4571 61.9334 89.4904 61.9334ZM89.4904 42.2602C84.6195 42.2602 80.6549 46.2247 80.6549 51.0957C80.6549 55.9667 84.6195 59.9312 89.4904 59.9312C94.3614 59.9312 98.3259 55.9667 98.3259 51.0957C98.3259 46.2247 94.3614 42.2602 89.4904 42.2602Z",fill:"black"}),(0,a.createElement)("path",{d:"M50.1541 60.9272C55.5839 60.9272 59.9857 56.5255 59.9857 51.0956C59.9857 45.6658 55.5839 41.264 50.1541 41.264C44.7242 41.264 40.3225 45.6658 40.3225 51.0956C40.3225 56.5255 44.7242 60.9272 50.1541 60.9272Z",fill:"black"}),(0,a.createElement)("path",{d:"M50.1541 61.9334C44.1874 61.9334 39.3264 57.0723 39.3264 51.1057C39.3264 45.139 44.1874 40.278 50.1541 40.278C56.1208 40.278 60.9818 45.139 60.9818 51.1057C60.9818 57.0723 56.1307 61.9334 50.1541 61.9334ZM50.1541 42.2602C45.2831 42.2602 41.3186 46.2247 41.3186 51.0957C41.3186 55.9667 45.2831 59.9312 50.1541 59.9312C55.0251 59.9312 58.9896 55.9667 58.9896 51.0957C58.9896 46.2247 55.035 42.2602 50.1541 42.2602Z",fill:"black"}),(0,a.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M63 168.942C63 159.553 70.6112 151.942 80 151.942H136C145.389 151.942 153 159.553 153 168.942V216.942C153 226.33 145.389 233.942 136 233.942H80C70.6112 233.942 63 226.33 63 216.942V168.942ZM81 169.942V215.942H135V169.942H81ZM167 168.942C167 159.553 174.611 151.942 184 151.942H232C241.389 151.942 249 159.553 249 168.942V216.942C249 226.33 241.389 233.942 232 233.942H184C174.611 233.942 167 226.33 167 216.942V168.942ZM185 169.942V215.942H231V169.942H185ZM263 168.942C263 159.553 270.611 151.942 280 151.942H328C337.389 151.942 345 159.553 345 168.942V216.942C345 226.33 337.389 233.942 328 233.942H280C270.611 233.942 263 226.33 263 216.942V168.942ZM281 169.942V215.942H327V169.942H281ZM63 272.942C63 263.553 70.6112 255.942 80 255.942H136C145.389 255.942 153 263.553 153 272.942V320.942C153 330.33 145.389 337.942 136 337.942H80C70.6112 337.942 63 330.33 63 320.942V272.942ZM81 273.942V319.942H135V273.942H81ZM167 272.942C167 263.553 174.611 255.942 184 255.942H232C241.389 255.942 249 263.553 249 272.942V320.942C249 330.33 241.389 337.942 232 337.942H184C174.611 337.942 167 330.33 167 320.942V272.942ZM185 273.942V319.942H231V273.942H185ZM263 272.942C263 263.553 270.611 255.942 280 255.942H328C337.389 255.942 345 263.553 345 272.942V320.942C345 330.33 337.389 337.942 328 337.942H280C270.611 337.942 263 330.33 263 320.942V272.942ZM281 273.942V319.942H327V273.942H281ZM63 376.942C63 367.553 70.6112 359.942 80 359.942H136C145.389 359.942 153 367.553 153 376.942V424.942C153 434.331 145.389 441.942 136 441.942H80C70.6112 441.942 63 434.331 63 424.942V376.942ZM81 377.942V423.942H135V377.942H81ZM167 376.942C167 367.553 174.611 359.942 184 359.942H232C241.389 359.942 249 367.553 249 376.942V424.942C249 434.331 241.389 441.942 232 441.942H184C174.611 441.942 167 434.331 167 424.942V376.942ZM185 377.942V423.942H231V377.942H185ZM263 376.942C263 367.553 270.611 359.942 280 359.942H328C337.389 359.942 345 367.553 345 376.942V424.942C345 434.331 337.389 441.942 328 441.942H280C270.611 441.942 263 434.331 263 424.942V376.942ZM281 377.942V423.942H327V377.942H281Z",fill:"black"}),(0,a.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M359 168.942C359 159.553 366.611 151.942 376 151.942H432C441.389 151.942 449 159.553 449 168.942V216.942C449 226.33 441.389 233.942 432 233.942H376C366.611 233.942 359 226.33 359 216.942V168.942ZM377 169.942V215.942H431V169.942H377ZM359 272.942C359 263.553 366.611 255.942 376 255.942H432C441.389 255.942 449 263.553 449 272.942V320.942C449 330.33 441.389 337.942 432 337.942H376C366.611 337.942 359 330.33 359 320.942V272.942ZM377 273.942V319.942H431V273.942H377ZM359 376.942C359 367.553 366.611 359.942 376 359.942H432C441.389 359.942 449 367.553 449 376.942V424.942C449 434.331 441.389 441.942 432 441.942H376C366.611 441.942 359 434.331 359 424.942V376.942ZM377 377.942V423.942H431V377.942H377Z",fill:"black"}),(0,a.createElement)("rect",{opacity:"0.3",x:"166.612",y:"256.942",width:"336.66",height:"247.271",rx:"30",fill:"#227AFF"})),(0,a.createElement)("defs",null,(0,a.createElement)("clipPath",{id:"clip0_708_21983"},(0,a.createElement)("rect",{width:"512",height:"512",fill:"white",transform:"translate(0 0.94165)"}))))},edit:({attributes:e,setAttributes:t})=>{const{categories:n}=((e=!1)=>{const[t,n]=(0,V.useState)([]),[a,l]=(0,V.useState)({}),[r,s]=(0,V.useState)({}),{setIsFetching:o,setError:i,isFetching:c,error:m}=(()=>{const[e,t]=(0,V.useState)(!0),[n,a]=(0,V.useState)("");return{isFetching:e,setIsFetching:t,error:n,setError:a}})();return(0,V.useEffect)((()=>{o(!0),(async(e=!1)=>{try{let t="?children=true";return e&&(t+="&details=true"),await Kn()({path:`masterstudy-lms/v2/course-categories${t}`})}catch(e){throw new Error(e)}})(e).then((({categories:e})=>{n((e=>e.map((e=>({label:e.name,value:e.id,image:e.image,icon:e.icon,color:e.color,children:e.children?f(e.children):[]}))))(e)),l(e.reduce(((e,t)=>(e[String(t.id)]=t.name,e)),{})),s(e.reduce(((e,t)=>(e[String(t.id)]={label:t.name,value:t.id,image:t.image,icon:t.icon,color:t.color,courses:t.courses,children:t.children},e)),{}))})).catch((e=>{i(e.message)})).finally((()=>{o(!1)}))}),[]),{categories:t,categoriesMap:a,categoriesMapFull:r,isFetching:c,error:m}})(),l=(0,r.useBlockProps)({className:`lms-courses-container ${e.layoutWidth}`,style:x("courses",e,E)}),s=(0,r.useInnerBlocksProps)({...l},{template:Wn,allowedBlocks:["masterstudy/courses-tab-options","masterstudy/courses-tab-category"],templateLock:"all"});return(0,a.createElement)(a.Fragment,null,(0,a.createElement)($n,{attributes:e,setAttributes:t,categories:n}),(0,a.createElement)("div",{...s}))},save:({attributes:e})=>{const t=r.useBlockProps.save({className:`lms-courses-container ${e.layoutWidth}`,style:x("courses",e,E)}),n=r.useInnerBlocksProps.save(t);return(0,a.createElement)("div",{...n})}})},6942:(e,t)=>{var n;!function(){"use strict";var a={}.hasOwnProperty;function l(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=s(e,r(n)))}return e}function r(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return l.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)a.call(e,n)&&e[n]&&(t=s(t,n));return t}function s(e,t){return t?e?e+" "+t:e+t:e}e.exports?(l.default=l,e.exports=l):void 0===(n=function(){return l}.apply(t,[]))||(e.exports=n)}()}},n={};function a(e){var l=n[e];if(void 0!==l)return l.exports;var r=n[e]={exports:{}};return t[e](r,r.exports,a),r.exports}a.m=t,e=[],a.O=(t,n,l,r)=>{if(!n){var s=1/0;for(m=0;m<e.length;m++){for(var[n,l,r]=e[m],o=!0,i=0;i<n.length;i++)(!1&r||s>=r)&&Object.keys(a.O).every((e=>a.O[e](n[i])))?n.splice(i--,1):(o=!1,r<s&&(s=r));if(o){e.splice(m--,1);var c=l();void 0!==c&&(t=c)}}return t}r=r||0;for(var m=e.length;m>0&&e[m-1][2]>r;m--)e[m]=e[m-1];e[m]=[n,l,r]},a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={5863:0,6319:0};a.O.j=t=>0===e[t];var t=(t,n)=>{var l,r,[s,o,i]=n,c=0;if(s.some((t=>0!==e[t]))){for(l in o)a.o(o,l)&&(a.m[l]=o[l]);if(i)var m=i(a)}for(t&&t(n);c<s.length;c++)r=s[c],a.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return a.O(m)},n=globalThis.webpackChunkmasterstudy_lms_learning_management_system=globalThis.webpackChunkmasterstudy_lms_learning_management_system||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})();var l=a.O(void 0,[6319],(()=>a(2886)));l=a.O(l)})();