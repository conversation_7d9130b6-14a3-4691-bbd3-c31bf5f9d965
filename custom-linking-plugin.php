<?php
/**
 * Plugin Name: Custom Linking Plugin
 * Plugin URI: https://yourwebsite.com/custom-linking-plugin
 * Description: Links MasterStudy LMS courses to WooCommerce products
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://yourwebsite.com
 * Text Domain: custom-linking-plugin
 * Domain Path: /languages
 * Requires at least: 5.0
 * Requires PHP: 7.2
 */

/**
 * Direct debug log function to be available immediately
 * before any includes are loaded
 */
if (!function_exists('custom_linking_direct_log')) {
    /**
     * Simple direct logging function for debugging activation issues
     * This function does not depend on any other code or includes
     * 
     * @param string $message Message to log
     */
    function custom_linking_direct_log($message) {
        $plugin_dir = dirname(__FILE__);
        $log_file = $plugin_dir . '/debug.log';
        
        // Create timestamp
        $timestamp = date('[Y-m-d H:i:s]');
        
        // Format message
        if (is_array($message) || is_object($message)) {
            $message = print_r($message, true);
        }
        
        // Create log message
        $log_message = $timestamp . ' ' . $message . "\n";
        
        // Write to file using direct method
        $handle = @fopen($log_file, 'a');
        if ($handle) {
            @fwrite($handle, $log_message);
            @fclose($handle);
        }
        
        // Also write to PHP error log as backup
        error_log('Custom Linking Plugin: ' . $message);
    }
}

// Log plugin load
custom_linking_direct_log('Plugin file loaded');

// Test if we can write to the debug.log file
try {
    custom_linking_direct_log('Testing debug log writing');
} catch (Exception $e) {
    error_log('Custom Linking Plugin: Error writing to debug log: ' . $e->getMessage());
}

// Exit if accessed directly
if (!defined('ABSPATH')) {
    custom_linking_direct_log('Direct access detected and blocked');
    exit;
}

custom_linking_direct_log('ABSPATH check passed: Plugin properly loaded through WordPress');

// Define plugin constants
if (!defined('CUSTOM_LINKING_PLUGIN_VERSION')) {
    define('CUSTOM_LINKING_PLUGIN_VERSION', '1.0.0');
}

if (!defined('CUSTOM_LINKING_PLUGIN_FILE')) {
    define('CUSTOM_LINKING_PLUGIN_FILE', __FILE__);
}

if (!defined('CUSTOM_LINKING_PLUGIN_PATH')) {
    define('CUSTOM_LINKING_PLUGIN_PATH', plugin_dir_path(__FILE__));
}

if (!defined('CUSTOM_LINKING_PLUGIN_URL')) {
    define('CUSTOM_LINKING_PLUGIN_URL', plugin_dir_url(__FILE__));
}

// Include the main plugin class
if (!class_exists('Custom_Linking_Core')) {
    require_once CUSTOM_LINKING_PLUGIN_PATH . 'class-core.php';
}

/**
 * Returns the main instance of the plugin
 *
 * @return Custom_Linking_Core The main plugin instance
 */
function custom_linking_plugin() {
    return Custom_Linking_Core::instance();
}

/**
 * Load debug utilities
 * 
 * We already have a direct log function but we'll still load the full debug utilities
 */
custom_linking_direct_log('About to load debug utilities');

// Check if the file exists before trying to include it
if (file_exists(dirname(__FILE__) . '/includes/debug.php')) {
    require_once dirname(__FILE__) . '/includes/debug.php';
    custom_linking_direct_log('Debug utilities loaded successfully');
} else {
    custom_linking_direct_log('ERROR: Could not load debug.php - file missing');
}

/**
 * Register activation and deactivation hooks directly in main file
 * This is more reliable than registering them in the class
 */
custom_linking_direct_log('Registering activation/deactivation hooks');
register_activation_hook(__FILE__, 'custom_linking_activate');
register_deactivation_hook(__FILE__, 'custom_linking_deactivate');
custom_linking_direct_log('Activation/deactivation hooks registered successfully');

/**
 * Activation function
 */
function custom_linking_activate() {
    try {
        // Log activation process using direct log to avoid dependencies
        custom_linking_direct_log('ACTIVATION: Starting activation process');
        
        // Create the database table - check if file exists first
        $activator_file = dirname(__FILE__) . '/database/Activator.php';
        custom_linking_direct_log('ACTIVATION: Checking for Activator.php file at: ' . $activator_file);
        
        if (file_exists($activator_file)) {
            custom_linking_direct_log('ACTIVATION: Activator.php file found, requiring it');
            require_once $activator_file;
            
            custom_linking_direct_log('ACTIVATION: Checking if Custom_Linking_Activator class exists');
            if (class_exists('Custom_Linking_Activator')) {
                custom_linking_direct_log('ACTIVATION: Custom_Linking_Activator class found, calling activate()');
                $result = Custom_Linking_Activator::activate();
                custom_linking_direct_log('ACTIVATION: Activation process completed with result: ' . ($result ? 'success' : 'failure'));
            } else {
                custom_linking_direct_log('ACTIVATION ERROR: Custom_Linking_Activator class does not exist');
            }
        } else {
            custom_linking_direct_log('ACTIVATION ERROR: Activator.php file not found');
        }
    } catch (Exception $e) {
        custom_linking_direct_log('ACTIVATION ERROR: Exception during activation: ' . $e->getMessage());
        // Re-throw to help WordPress display the error
        throw $e;
    }
}

/**
 * Deactivation function
 */
function custom_linking_deactivate() {
    try {
        // Log deactivation process using direct log to avoid dependencies
        custom_linking_direct_log('DEACTIVATION: Starting deactivation process');
        
        // Drop the database table - check if file exists first
        $deactivator_file = dirname(__FILE__) . '/database/Deactivator.php';
        custom_linking_direct_log('DEACTIVATION: Checking for Deactivator.php file at: ' . $deactivator_file);
        
        if (file_exists($deactivator_file)) {
            custom_linking_direct_log('DEACTIVATION: Deactivator.php file found, requiring it');
            require_once $deactivator_file;
            
            custom_linking_direct_log('DEACTIVATION: Checking if Custom_Linking_Deactivator class exists');
            if (class_exists('Custom_Linking_Deactivator')) {
                custom_linking_direct_log('DEACTIVATION: Custom_Linking_Deactivator class found, calling deactivate()');
                Custom_Linking_Deactivator::deactivate();
                custom_linking_direct_log('DEACTIVATION: Deactivation process completed successfully');
            } else {
                custom_linking_direct_log('DEACTIVATION ERROR: Custom_Linking_Deactivator class does not exist');
            }
        } else {
            custom_linking_direct_log('DEACTIVATION ERROR: Deactivator.php file not found');
        }
    } catch (Exception $e) {
        custom_linking_direct_log('DEACTIVATION ERROR: Exception during deactivation: ' . $e->getMessage());
    }
}

// Initialize the plugin
add_action('plugins_loaded', 'custom_linking_plugin_init');

/**
 * Initialize the plugin
 */
function custom_linking_plugin_init() {
    // Load the main plugin class
    $GLOBALS['custom_linking_plugin'] = custom_linking_plugin();
}
