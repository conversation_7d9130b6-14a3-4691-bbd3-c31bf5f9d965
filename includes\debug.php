<?php
/**
 * Debug utilities for Custom Linking Plugin
 *
 * This file contains debug-related functions for the plugin
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Log messages to the debug.log file in the plugin root directory
 * 
 * This function writes log messages to a debug.log file in the plugin's
 * root directory for easy access during development and debugging
 * 
 * @param mixed $message The message to log (can be string, array, or object)
 */
function custom_linking_debug_log($message) {
    // Make sure the debug log directory exists
    $log_dir = dirname(dirname(__FILE__));
    $log_file = $log_dir . '/debug.log';
    
    // Create file with proper permissions if it doesn't exist
    if (!file_exists($log_file)) {
        touch($log_file);
        chmod($log_file, 0666); // Make sure it's writable
    }
    
    $timestamp = date('[Y-m-d H:i:s]');
    
    if (is_array($message) || is_object($message)) {
        $message = print_r($message, true);
    }
    
    $log_message = $timestamp . ' ' . $message . "\n";
    
    // Log to file using direct file writing
    $handle = @fopen($log_file, 'a');
    if ($handle) {
        @fwrite($handle, $log_message);
        @fclose($handle);
    }
    
    // Also write to PHP error log as a backup
    error_log("Custom Linking Plugin: " . $message);
}
