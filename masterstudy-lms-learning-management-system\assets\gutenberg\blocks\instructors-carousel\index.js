(()=>{var e,t={5363:(e,t,n)=>{"use strict";const a=window.React,l=window.wp.blocks,i=window.wp.i18n,r=window.wp.blockEditor;var s=n(6942),o=n.n(s);const m=window.wp.element,c=(e,t)=>{if(typeof e!=typeof t)return!1;if(Number.isNaN(e)&&Number.isNaN(t))return!0;if("object"!=typeof e||null===e||null===t)return e===t;if(Object.keys(e).length!==Object.keys(t).length)return!1;if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;const n=e.slice().sort(),a=t.slice().sort();return n.every(((e,t)=>c(e,a[t])))}for(const n of Object.keys(e))if(!c(e[n],t[n]))return!1;return!0};let d=function(e){return e.ALL="all",e.SOME="some",e}({}),u=function(e){return e.NORMAL="Normal",e.HOVER="Hover",e.ACTIVE="Active",e.FOCUS="Focus",e}({}),g=function(e){return e.DESKTOP="Desktop",e.TABLET="Tablet",e.MOBILE="Mobile",e}({}),p=function(e){return e.TOP_lEFT="top-left",e.TOP_CENTER="top-center",e.TOP_RIGHT="top-right",e.BOTTOM_lEFT="bottom-left",e.BOTTOM_CENTER="bottom-center",e.BOTTOM_RIGHT="bottom-right",e}({});const v=["",null,void 0,"null","undefined"],h=[".jpg",".jpeg",".png",".gif"],_=e=>v.includes(e),b=(e,t,n="")=>{const a=e[t];return"object"==typeof a&&null!==a?((e,t)=>{return n=e,Object.values(n).every((e=>v.includes(e)))?null:((e,t="")=>{const n=Object.entries(e).reduce(((e,[n,a])=>(e[n]=(a||"0")+t,e)),{});return`${n.top} ${n.right} ${n.bottom} ${n.left}`})(e,t);var n})(a,n):((e,t)=>_(e)?e:(e=>{if("string"!=typeof e)return!1;const t=e.slice(e.lastIndexOf("."));return h.includes(t)||e.startsWith("blob")})(e)?`url('${e}')`:String(e+t))(a,n)},y=(e,t,n)=>{const a={};return n.forEach((({isAdaptive:n,hasHover:l,unit:i},r)=>{if(t.hasOwnProperty(r)){const{unitMeasureDesktop:o,unitMeasureTablet:m,unitMeasureMobile:c}=((e,t)=>{var n;return{unitMeasureDesktop:null!==(n=e[t])&&void 0!==n?n:"",unitMeasureTablet:t?e[t+"Tablet"]:"",unitMeasureMobile:t?e[t+"Mobile"]:""}})(t,i);if(n&&l){const{desktopHoverPropertyName:n,mobileHoverPropertyName:l,tabletHoverPropertyName:i}=(e=>{const t=e+u.HOVER;return{desktopHoverPropertyName:t,tabletHoverPropertyName:t+"Tablet",mobileHoverPropertyName:t+"Mobile"}})(r),s=b(t,n,o);_(s)||(a[`--lms-${e}-${n}`]=s);const d=b(t,i,m);_(d)||(a[`--lms-${e}-${i}`]=d);const g=b(t,l,c);_(g)||(a[`--lms-${e}-${l}`]=g)}if(l){const n=r+u.HOVER,l=b(t,n,o);_(l)||(a[`--lms-${e}-${n}`]=l)}if(n){const{desktopPropertyName:n,mobilePropertyName:l,tabletPropertyName:i}={desktopPropertyName:s=r,tabletPropertyName:s+"Tablet",mobilePropertyName:s+"Mobile"},d=b(t,n,o);_(d)||(a[`--lms-${e}-${n}`]=d);const u=b(t,i,m);_(u)||(a[`--lms-${e}-${i}`]=u);const g=b(t,l,c);_(g)||(a[`--lms-${e}-${l}`]=g)}const d=b(t,r,o);_(d)||(a[`--lms-${e}-${r}`]=d)}var s})),a},E=(i.__("Small","masterstudy-lms-learning-management-system"),i.__("Normal","masterstudy-lms-learning-management-system"),i.__("Large","masterstudy-lms-learning-management-system"),i.__("Extra Large","masterstudy-lms-learning-management-system"),"wp-block-masterstudy-settings__"),C={top:"",right:"",bottom:"",left:""},f=[p.TOP_lEFT,p.TOP_CENTER,p.TOP_RIGHT],N=[p.BOTTOM_lEFT,p.BOTTOM_CENTER,p.BOTTOM_RIGHT],T=[{label:i.__("Newest","masterstudy-lms-learning-management-system"),value:"date_high"},{label:i.__("Oldest","masterstudy-lms-learning-management-system"),value:"date_low"},{label:i.__("Overall rating","masterstudy-lms-learning-management-system"),value:"rating"},{label:i.__("Popular","masterstudy-lms-learning-management-system"),value:"popular"},{label:i.__("Price low","masterstudy-lms-learning-management-system"),value:"price_low"},{label:i.__("Price high","masterstudy-lms-learning-management-system"),value:"price_high"}];function w(e){return Array.isArray(e)?e.map((e=>E+e)):E+e}const M=window.wp.apiFetch;var S=n.n(M);const x=window.wp.components,O=({condition:e,fallback:t=null,children:n})=>(0,a.createElement)(a.Fragment,null,e?n:t),B=window.wp.data,k=()=>(0,B.useSelect)((e=>e("core/editor").getDeviceType()||e("core/edit-site")?.__experimentalGetPreviewDeviceType()||e("core/edit-post")?.__experimentalGetPreviewDeviceType()||e("masterstudy/store")?.getDeviceType()),[])||"Desktop",R=(e,t,n)=>{const a=k();return a===g.TABLET?t||e:a===g.MOBILE&&(n||t)||e},A=(e=!1)=>{const[t,n]=(0,m.useState)(e),a=(0,m.useCallback)((()=>{n(!0)}),[]);return{isOpen:t,onClose:(0,m.useCallback)((()=>{n(!1)}),[]),onOpen:a,onToggle:(0,m.useCallback)((()=>{n((e=>!e))}),[])}},P=(0,m.createContext)(null),H=({children:e,...t})=>(0,a.createElement)(P.Provider,{value:{...t}},e),L=()=>{const e=(0,m.useContext)(P);if(!e)throw new Error("No settings context provided");return e},D=(e="")=>{const{attributes:t,setAttributes:n,onResetByFieldName:a,changedFieldsByName:l}=L();return{value:t[e],onChange:t=>n({[e]:t}),onReset:a.get(e),isChanged:l.get(e)}},I=(e,t=!1,n=!1)=>{const{hoverName:a,onChangeHoverName:l}=(()=>{const[e,t]=(0,m.useState)(u.NORMAL);return{hoverName:e,onChangeHoverName:(0,m.useCallback)((e=>{t(e)}),[])}})(),i=k();return{fieldName:(0,m.useMemo)((()=>{const l=a===u.HOVER?a:"",r=i===g.DESKTOP?"":i;return n&&t?e+l+r:n&&!t?e+l:t&&!n?e+r:e}),[e,n,t,a,i]),hoverName:a,onChangeHoverName:l}},F=(e,t=!1,n="Normal")=>{const a=k(),l=(0,m.useMemo)((()=>{const l=n===u.NORMAL?"":n,i=a===g.DESKTOP?"":a;return l&&t?e+l+i:l&&!t?e+l:t&&!l?e+i:e}),[e,t,n,a]),{value:i,isChanged:r,onReset:s}=D(l);return{fieldName:l,value:i,isChanged:r,onReset:s}},U=(e=[],t=d.ALL)=>{const{attributes:n}=L();return!e.length||(t===d.ALL?e.every((({name:e,value:t})=>{const a=n[e];return Array.isArray(t)?Array.isArray(a)?c(t,a):t.includes(a):t===a})):t!==d.SOME||e.some((({name:e,value:t})=>{const a=n[e];return Array.isArray(t)?Array.isArray(a)?c(t,a):t.includes(a):t===a})))},V=e=>{const t=(0,m.useRef)(null);return(0,m.useEffect)((()=>{const n=n=>{t.current&&!t.current.contains(n.target)&&e()};return document.addEventListener("click",n),()=>{document.removeEventListener("click",n)}}),[t,e]),t},W=e=>(0,a.createElement)(x.SVG,{width:"16",height:"16",viewBox:"0 0 12 13",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(x.Path,{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})),G=e=>(0,a.createElement)(x.SVG,{width:"16",height:"16",viewBox:"0 0 14 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(x.G,{"clip-path":"url(#clip0_1068_38993)"},(0,a.createElement)(x.Path,{d:"M11.1973 8.60005L8.74967 8.11005V5.67171C8.74967 5.517 8.68822 5.36863 8.57882 5.25923C8.46942 5.14984 8.32105 5.08838 8.16634 5.08838H6.99967C6.84496 5.08838 6.69659 5.14984 6.5872 5.25923C6.4778 5.36863 6.41634 5.517 6.41634 5.67171V8.58838H5.24967C5.15021 8.58844 5.05241 8.61391 4.96555 8.66238C4.87869 8.71084 4.80565 8.78068 4.75336 8.86529C4.70106 8.9499 4.67125 9.04646 4.66674 9.14582C4.66223 9.24518 4.68317 9.34405 4.72759 9.43305L6.47759 12.933C6.57676 13.1302 6.77859 13.255 6.99967 13.255H11.083C11.2377 13.255 11.3861 13.1936 11.4955 13.0842C11.6049 12.9748 11.6663 12.8264 11.6663 12.6717V9.17171C11.6665 9.03685 11.6198 8.90612 11.5343 8.80186C11.4487 8.69759 11.3296 8.62626 11.1973 8.60005Z"}),(0,a.createElement)(x.Path,{d:"M10.4997 1.58838H3.49967C2.85626 1.58838 2.33301 2.11221 2.33301 2.75505V6.83838C2.33301 7.4818 2.85626 8.00505 3.49967 8.00505H5.24967V6.83838H3.49967V2.75505H10.4997V6.83838H11.6663V2.75505C11.6663 2.11221 11.1431 1.58838 10.4997 1.58838Z"})),(0,a.createElement)("defs",null,(0,a.createElement)("clipPath",{id:"clip0_1068_38993"},(0,a.createElement)(x.Rect,{width:"14",height:"14",transform:"translate(0 0.421875)"})))),z=[{value:u.NORMAL,label:i.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(W,{onClick:e})},{value:u.HOVER,label:i.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(G,{onClick:e})},{value:u.ACTIVE,label:i.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(G,{onClick:e})},{value:u.FOCUS,label:i.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(G,{onClick:e})}],Z={[u.NORMAL]:{icon:(0,a.createElement)(W,null),label:i.__("Normal State","masterstudy-lms-learning-management-system")},[u.HOVER]:{icon:(0,a.createElement)(G,null),label:i.__("Hovered State","masterstudy-lms-learning-management-system")},[u.ACTIVE]:{icon:(0,a.createElement)(G,null),label:i.__("Active State","masterstudy-lms-learning-management-system")},[u.FOCUS]:{icon:(0,a.createElement)(G,null),label:i.__("Focus State","masterstudy-lms-learning-management-system")}},j=(e,t)=>{let n=[];return n=e.length?z.filter((t=>e.includes(t.value))):z,n=n.filter((e=>e.value!==t)),{ICONS_MAP:Z,options:n}},[$,K,X,q,Y]=w(["hover-state","hover-state__selected","hover-state__selected__opened-menu","hover-state__menu","hover-state__menu__item"]),J=({stateOptions:e,currentState:t,onSelect:n})=>{const{isOpen:l,onOpen:i,onClose:r}=A(),s=V(r),{ICONS_MAP:m,options:c}=j(e,t);return(0,a.createElement)("div",{className:$,ref:s},(0,a.createElement)("div",{className:o()([K],{[X]:l}),onClick:i,title:m[t]?.label},m[t]?.icon),(0,a.createElement)(O,{condition:l},(0,a.createElement)("div",{className:q},c.map((({value:e,icon:t,label:l})=>(0,a.createElement)("div",{key:e,className:Y,title:l},t((()=>n(e)))))))))},Q=w("color-indicator"),ee=(0,m.memo)((({color:e,onChange:t})=>(0,a.createElement)("div",{className:Q},(0,a.createElement)(r.PanelColorSettings,{enableAlpha:!0,disableCustomColors:!1,__experimentalHasMultipleOrigins:!0,__experimentalIsRenderedInSidebar:!0,colorSettings:[{label:"",value:e,onChange:t}]}))));var te;function ne(){return ne=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},ne.apply(null,arguments)}var ae,le,ie=function(e){return a.createElement("svg",ne({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 12 13"},e),te||(te=a.createElement("path",{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})))};function re(){return re=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},re.apply(null,arguments)}var se=function(e){return a.createElement("svg",re({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 14 15"},e),ae||(ae=a.createElement("g",{clipPath:"url(#state-hover_svg__a)"},a.createElement("path",{d:"M11.197 8.6 8.75 8.11V5.672a.583.583 0 0 0-.584-.584H7a.583.583 0 0 0-.584.584v2.916H5.25a.584.584 0 0 0-.522.845l1.75 3.5c.099.197.3.322.522.322h4.083a.583.583 0 0 0 .583-.583v-3.5a.58.58 0 0 0-.469-.572"}),a.createElement("path",{d:"M10.5 1.588h-7c-.644 0-1.167.524-1.167 1.167v4.083c0 .644.523 1.167 1.167 1.167h1.75V6.838H3.5V2.755h7v4.083h1.166V2.755c0-.643-.523-1.167-1.166-1.167"}))),le||(le=a.createElement("defs",null,a.createElement("clipPath",{id:"state-hover_svg__a"},a.createElement("path",{d:"M0 .422h14v14H0z"})))))};const oe=[{value:u.NORMAL,label:i.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(ie,{onClick:e})},{value:u.HOVER,label:i.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(se,{onClick:e})}],me={[u.NORMAL]:{icon:(0,a.createElement)(ie,null),label:i.__("Normal State","masterstudy-lms-learning-management-system")},[u.HOVER]:{icon:(0,a.createElement)(se,null),label:i.__("Hovered State","masterstudy-lms-learning-management-system")}},ce=w("hover-state"),de=w("hover-state__selected"),ue=w("hover-state__selected__opened-menu"),ge=w("has-changes"),pe=w("hover-state__menu"),ve=w("hover-state__menu__item"),he=(0,m.memo)((e=>{const{hoverName:t,onChangeHoverName:n,fieldName:l}=e,{changedFieldsByName:i}=L(),r=i.get(l),{isOpen:s,onOpen:c,onClose:d}=A(),u=V(d),{ICONS_MAP:g,options:p}=(e=>{const t=(0,m.useMemo)((()=>oe.filter((t=>t.value!==e))),[e]);return{ICONS_MAP:me,options:t}})(t),v=(0,m.useCallback)((e=>{n(e),d()}),[n,d]);return(0,a.createElement)("div",{className:ce,ref:u},(0,a.createElement)("div",{className:o()([de],{[ue]:s,[ge]:r}),onClick:c,title:g[t]?.label},g[t]?.icon),(0,a.createElement)(O,{condition:s},(0,a.createElement)("div",{className:pe},p.map((({value:e,icon:t,label:n})=>(0,a.createElement)("div",{key:e,className:ve,title:n},t((()=>v(e)))))))))})),_e={Desktop:{icon:"desktop",label:i.__("Desktop","masterstudy-lms-learning-management-system")},Tablet:{icon:"tablet",label:i.__("Tablet","masterstudy-lms-learning-management-system")},Mobile:{icon:"smartphone",label:i.__("Mobile","masterstudy-lms-learning-management-system")}},be=[{value:g.DESKTOP,icon:"desktop",label:i.__("Desktop","masterstudy-lms-learning-management-system")},{value:g.TABLET,icon:"tablet",label:i.__("Tablet","masterstudy-lms-learning-management-system")},{value:g.MOBILE,icon:"smartphone",label:i.__("Mobile","masterstudy-lms-learning-management-system")}],ye=w("device-picker"),Ee=w("device-picker__selected"),Ce=w("device-picker__selected__opened-menu"),fe=w("device-picker__menu"),Ne=w("device-picker__menu__item"),Te=()=>{const{isOpen:e,onOpen:t,onClose:n}=A(),{value:l,onChange:i}=(e=>{const t=k(),n=(0,B.useDispatch)();return{value:(0,m.useMemo)((()=>_e[t]),[t]),onChange:t=>{n("core/edit-site")&&n("core/edit-site").__experimentalSetPreviewDeviceType?n("core/edit-site").__experimentalSetPreviewDeviceType(t):n("core/edit-post")&&n("core/edit-post").__experimentalSetPreviewDeviceType?n("core/edit-post").__experimentalSetPreviewDeviceType(t):n("masterstudy/store").setDeviceType(t),e()}}})(n),r=(e=>(0,m.useMemo)((()=>be.filter((t=>t.icon!==e))),[e]))(l.icon),s=V(n);return(0,a.createElement)("div",{className:ye,ref:s},(0,a.createElement)(x.Dashicon,{className:o()([Ee],{[Ce]:e}),icon:l.icon,size:16,onClick:t,title:l.label}),(0,a.createElement)(O,{condition:e},(0,a.createElement)("div",{className:fe},r.map((e=>(0,a.createElement)(x.Dashicon,{key:e.value,icon:e.icon,size:16,onClick:()=>i(e.value),className:Ne,title:e.label}))))))},we=w("reset-button"),Me=({onReset:e})=>(0,a.createElement)(x.Dashicon,{icon:"undo",onClick:e,className:we,size:16}),Se=[{label:"PX",value:"px"},{label:"%",value:"%"},{label:"EM",value:"em"},{label:"REM",value:"rem"},{label:"VW",value:"vw"},{label:"VH",value:"vh"}],xe=w("unit"),Oe=w("unit__single"),Be=w("unit__list"),ke=({name:e,isAdaptive:t})=>{const{isOpen:n,onOpen:l,onClose:i}=A(),{fieldName:r}=I(e,t),{value:s,onChange:o}=D(r),m=V(i);return(0,a.createElement)("div",{className:xe,ref:m},(0,a.createElement)("div",{className:Oe,onClick:l},s),(0,a.createElement)(O,{condition:n},(0,a.createElement)("div",{className:Be},Se.map((({value:e,label:t})=>(0,a.createElement)("div",{key:e,onClick:()=>(o(e),void i())},t))))))},Re=w("popover-modal"),Ae=w("popover-modal__close dashicon dashicons dashicons-no-alt"),Pe=e=>{const{isOpen:t,onClose:n,popoverContent:l}=e;return(0,a.createElement)(O,{condition:t},(0,a.createElement)(x.Popover,{position:"middle left",onClose:n,className:Re},l,(0,a.createElement)("span",{onClick:n,className:Ae})))},He=w("setting-label"),Le=w("setting-label__content"),De=e=>{const{label:t,isChanged:n=!1,onReset:l,showDevicePicker:i=!0,HoverStateControl:r=null,unitName:s,popoverContent:o=null,dependencies:m}=e,{isOpen:c,onClose:d,onToggle:u}=A();return U(m)?(0,a.createElement)("div",{className:He},(0,a.createElement)("div",{className:Le},(0,a.createElement)("div",{onClick:u},t),(0,a.createElement)(O,{condition:Boolean(o)},(0,a.createElement)(Pe,{isOpen:c,onClose:d,popoverContent:o})),(0,a.createElement)(O,{condition:i},(0,a.createElement)(Te,null)),(0,a.createElement)(O,{condition:Boolean(r)},r)),(0,a.createElement)(O,{condition:Boolean(s)},(0,a.createElement)(ke,{name:s,isAdaptive:i})),(0,a.createElement)(O,{condition:n},(0,a.createElement)(Me,{onReset:l}))):null},Ie=w("suffix"),Fe=()=>(0,a.createElement)("div",{className:Ie},(0,a.createElement)(x.Dashicon,{icon:"color-picker",size:16})),Ue=w("color-picker"),Ve=e=>{const{name:t,label:n,placeholder:l,dependencyMode:i,dependencies:r,isAdaptive:s=!1,hasHover:o=!1}=e,{fieldName:m,hoverName:c,onChangeHoverName:d}=I(t,s,o),{value:u,isChanged:g,onChange:p,onReset:v}=D(m);return U(r,i)?(0,a.createElement)("div",{className:Ue},(0,a.createElement)(O,{condition:Boolean(n)},(0,a.createElement)(De,{label:n,isChanged:g,onReset:v,showDevicePicker:s,HoverStateControl:(0,a.createElement)(O,{condition:o},(0,a.createElement)(he,{hoverName:c,onChangeHoverName:d,fieldName:m}))})),(0,a.createElement)(x.__experimentalInputControl,{prefix:(0,a.createElement)(ee,{color:u,onChange:p}),suffix:(0,a.createElement)(Fe,null),onChange:p,value:u,placeholder:l})):null},We=w("number-steppers"),Ge=w("indent-steppers"),ze=w("indent-stepper-plus"),Ze=w("indent-stepper-minus"),je=({onIncrement:e,onDecrement:t,withArrows:n=!1})=>n?(0,a.createElement)("span",{className:Ge},(0,a.createElement)("button",{onClick:e,className:ze}),(0,a.createElement)("button",{onClick:t,className:Ze})):(0,a.createElement)("span",{className:We},(0,a.createElement)("button",{onClick:e},"+"),(0,a.createElement)("button",{onClick:t},"-")),[$e,Ke]=w(["indents","indents-control"]),Xe=({name:e,label:t,unitName:n,popoverContent:l,dependencyMode:r,dependencies:s,isAdaptive:o=!1})=>{const{fieldName:c}=I(e,o),{value:d,onResetSegmentedBox:u,hasChanges:g,handleInputIncrement:p,handleInputDecrement:v,updateDirectionsValues:h,lastFieldValue:_}=((e,t)=>{const{value:n,isChanged:a,onChange:l,onReset:i}=D(e),{onResetByFieldName:r,changedFieldsByName:s}=L(),o=a||s.get(t),c=e=>{l({...n,...e})},[d,u]=(0,m.useState)(!1);return{value:n,onResetSegmentedBox:()=>{i(),r.get(t)()},hasChanges:o,handleInputIncrement:e=>Number(n[e])+1,handleInputDecrement:e=>Number(n[e])-1,updateDirectionsValues:(e,t,n)=>{e?(u(!1),c({top:n,right:n,bottom:n,left:n})):(u(n),c({[t]:n}))},lastFieldValue:d}})(c,n),[b,y]=(0,m.useState)((()=>{const{left:e,right:t,top:n,bottom:a}=d;return""!==e&&e===t&&t===n&&n===a})),E=e=>{const[t,n]=Object.entries(e)[0];h(b,t,n)},C=e=>()=>{const t=p(e);h(b,e,String(t))},f=e=>()=>{const t=v(e);h(b,e,String(t))};return U(s,r)?(0,a.createElement)("div",{className:$e},(0,a.createElement)(O,{condition:Boolean(t)},(0,a.createElement)(De,{label:null!=t?t:"",isChanged:g,onReset:u,unitName:n,popoverContent:l,showDevicePicker:o})),(0,a.createElement)("div",{className:`${Ke} ${b?"active":""}`},(0,a.createElement)("div",null,(0,a.createElement)(x.__experimentalNumberControl,{value:d.top,onChange:e=>{E({top:e})},spinControls:"none",suffix:(0,a.createElement)(je,{onIncrement:C("top"),onDecrement:f("top"),withArrows:!0})}),(0,a.createElement)("div",null,i.__("Top","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(x.__experimentalNumberControl,{value:d.right,onChange:e=>{E({right:e})},spinControls:"none",suffix:(0,a.createElement)(je,{onIncrement:C("right"),onDecrement:f("right"),withArrows:!0})}),(0,a.createElement)("div",null,i.__("Right","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(x.__experimentalNumberControl,{value:d.bottom,onChange:e=>{E({bottom:e})},spinControls:"none",suffix:(0,a.createElement)(je,{onIncrement:C("bottom"),onDecrement:f("bottom"),withArrows:!0})}),(0,a.createElement)("div",null,i.__("Bottom","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(x.__experimentalNumberControl,{value:d.left,onChange:e=>{E({left:e})},spinControls:"none",suffix:(0,a.createElement)(je,{onIncrement:C("left"),onDecrement:f("left"),withArrows:!0})}),(0,a.createElement)("div",null,i.__("Left","masterstudy-lms-learning-management-system"))),(0,a.createElement)(x.Dashicon,{icon:"dashicons "+(b?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{b||!1===_||h(!0,"left",_),y((e=>!e))}}))):null},[qe,Ye,Je,Qe]=w(["toggle-group-wrapper","toggle-group","toggle-group__toggle","toggle-group__active-toggle"]),et=e=>{const{name:t,options:n,label:l,isAdaptive:i=!1,dependencyMode:r,dependencies:s}=e,{fieldName:m}=I(t,i),{value:c,isChanged:d,onChange:u,onReset:g}=D(m);return U(s,r)?(0,a.createElement)("div",{className:qe},(0,a.createElement)(O,{condition:Boolean(l)},(0,a.createElement)(De,{label:l,isChanged:d,onReset:g,showDevicePicker:i})),(0,a.createElement)("div",{className:Ye},n.map((e=>(0,a.createElement)("div",{key:e.value,className:o()([Je],{[Qe]:e.value===c}),onClick:()=>u(e.value)},e.label))))):null},[tt,nt,at,lt]=w(["border-control","border-control-solid","border-control-dashed","border-control-dotted"]),it=e=>{const{label:t,borderStyleName:n,borderColorName:l,borderWidthName:r,dependencyMode:s,dependencies:c,isAdaptive:d=!1,hasHover:u=!1}=e,[g,p]=(0,m.useState)("Normal"),{fieldName:v,value:h,isChanged:_,onReset:b}=F(n,d,g),{fieldName:y,isChanged:E,onReset:C}=F(l,d,g),{fieldName:f,isChanged:N,onReset:T}=F(r,d,g);if(!U(c,s))return null;const w=_||E||N;return(0,a.createElement)("div",{className:o()([tt],{"has-reset-button":w})},(0,a.createElement)(De,{label:t,isChanged:w,onReset:()=>{b(),C(),T()},showDevicePicker:d,HoverStateControl:(0,a.createElement)(O,{condition:u},(0,a.createElement)(J,{stateOptions:["Normal","Hover"],currentState:g,onSelect:p}))}),(0,a.createElement)(et,{options:[{label:(0,a.createElement)("span",null,i.__("None","masterstudy-lms-learning-management-system")),value:"none"},{label:(0,a.createElement)("span",{className:nt}),value:"solid"},{label:(0,a.createElement)("span",{className:at},(0,a.createElement)("span",null)),value:"dashed"},{label:(0,a.createElement)("span",{className:lt},(0,a.createElement)("span",null,(0,a.createElement)("span",null))),value:"dotted"}],name:v}),(0,a.createElement)(O,{condition:"none"!==h},(0,a.createElement)(Ve,{name:y,placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Xe,{name:f})))},rt=w("border-radius"),st=w("border-radius-control"),ot=({name:e,label:t,unitName:n,popoverContent:l,dependencyMode:i,dependencies:r,isAdaptive:s=!1,hasHover:c=!1})=>{const{fieldName:d}=I(e,s,c),{value:u,onResetBorderRadius:g,hasChanges:p,handleInputIncrement:v,handleInputDecrement:h,updateDirectionsValues:_,lastFieldValue:b}=((e,t)=>{const[n,a]=(0,m.useState)(!1),{value:l,isChanged:i,onChange:r,onReset:s}=D(e),{onResetByFieldName:o,changedFieldsByName:c}=L(),d=i||c.get(t),u=e=>{r({...l,...e})};return{value:l,onResetBorderRadius:()=>{s(),o.get(t)()},hasChanges:d,handleInputIncrement:e=>Number(l[e])+1,handleInputDecrement:e=>Number(l[e])-1,updateDirectionsValues:(e,t,n)=>{e?(u({top:n,right:n,bottom:n,left:n}),a(!1)):(u({[t]:n}),a(n))},lastFieldValue:n}})(d,n),[y,E]=(0,m.useState)((()=>{const{left:e,right:t,top:n,bottom:a}=u;return""!==e&&e===t&&t===n&&n===a})),C=e=>{const[t,n]=Object.entries(e)[0];_(y,t,n)},f=e=>()=>{const t=v(e);_(y,e,String(t))},N=e=>()=>{const t=h(e);_(y,e,String(t))};return U(r,i)?(0,a.createElement)("div",{className:rt},(0,a.createElement)(De,{label:t,isChanged:p,onReset:g,unitName:n,popoverContent:l,showDevicePicker:s}),(0,a.createElement)("div",{className:o()([st],{"has-reset-button":p,active:y})},(0,a.createElement)("div",{className:"number-control-top"},(0,a.createElement)(x.__experimentalNumberControl,{value:u.top,onChange:e=>{C({top:e})},spinControls:"none",suffix:(0,a.createElement)(je,{onIncrement:f("top"),onDecrement:N("top"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-right"},(0,a.createElement)(x.__experimentalNumberControl,{value:u.right,onChange:e=>{C({right:e})},spinControls:"none",suffix:(0,a.createElement)(je,{onIncrement:f("right"),onDecrement:N("right"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-left"},(0,a.createElement)(x.__experimentalNumberControl,{value:u.left,onChange:e=>{C({left:e})},spinControls:"none",suffix:(0,a.createElement)(je,{onIncrement:f("left"),onDecrement:N("left"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-bottom"},(0,a.createElement)(x.__experimentalNumberControl,{value:u.bottom,onChange:e=>{C({bottom:e})},spinControls:"none",suffix:(0,a.createElement)(je,{onIncrement:f("bottom"),onDecrement:N("bottom"),withArrows:!0})})),(0,a.createElement)(x.Dashicon,{icon:"dashicons "+(y?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{y||!1===b||_(!0,"left",b),E((e=>!e))}}))):null},mt=(w("box-shadow-preset"),w("presets")),ct=w("presets__item-wrapper"),dt=w("presets__item-wrapper__preset"),ut=w("presets__item-wrapper__name"),gt=((0,m.memo)((e=>{const{presets:t,activePreset:n,onSelectPreset:l,PresetItem:i,detectIsActive:r,detectByIndex:s=!1}=e;return(0,a.createElement)("div",{className:mt},t.map((({name:e,...t},m)=>(0,a.createElement)("div",{key:m,className:o()([ct],{active:r(n,s?m:t)}),onClick:()=>l(t)},(0,a.createElement)("div",{className:dt},(0,a.createElement)(i,{preset:t})),(0,a.createElement)("span",{className:ut},e)))))})),w("range-control")),pt=e=>{const{name:t,label:n,min:l,max:i,unitName:r,dependencyMode:s,dependencies:o,isAdaptive:m=!1}=e,{fieldName:c}=I(t,m),{value:d,onChange:u,onResetNumberField:g,hasChanges:p}=((e,t)=>{const{value:n,isChanged:a,onChange:l,onReset:i}=D(e),{onResetByFieldName:r,changedFieldsByName:s}=L();return{value:n,onChange:l,onResetNumberField:()=>{i(),r.get(t)()},hasChanges:a||s.get(t)}})(c,r);return U(o,s)?(0,a.createElement)("div",{className:gt},(0,a.createElement)(O,{condition:Boolean(n)},(0,a.createElement)(De,{label:n,isChanged:p,onReset:g,unitName:r,showDevicePicker:m})),(0,a.createElement)(x.RangeControl,{value:d,onChange:u,min:l,max:i})):null},vt=w("switch"),ht=e=>{const{name:t,label:n,dependencyMode:l,dependencies:i,isAdaptive:r=!1}=e,{fieldName:s}=I(t,r),{value:o,onChange:m}=D(s);return U(i,l)?(0,a.createElement)("div",{className:vt,"data-has-label":Boolean(n).toString()},(0,a.createElement)(x.ToggleControl,{label:n,checked:o,onChange:m}),(0,a.createElement)(O,{condition:r},(0,a.createElement)(Te,null))):null},_t=(w("box-shadow-settings"),w("box-shadow-presets-title"),w("input-field"),w("input-field-control"),w("number-field")),bt=w("number-field-control"),yt=e=>{const{name:t,label:n,unitName:l,help:i,popoverContent:r,dependencyMode:s,dependencies:o,isAdaptive:m=!1}=e,{fieldName:c}=I(t,m),{value:d,onResetNumberField:u,hasChanges:g,handleIncrement:p,handleDecrement:v,handleInputChange:h}=((e,t)=>{const{value:n,isChanged:a,onChange:l,onReset:i}=D(e),{onResetByFieldName:r,changedFieldsByName:s}=L(),o=a||s.get(t);return{value:n,onResetNumberField:()=>{i(),r.get(t)()},hasChanges:o,handleIncrement:()=>{l(n+1)},handleDecrement:()=>{l(n-1)},handleInputChange:e=>{const t=Number(""===e?0:e);l(t)}}})(c,l);return U(o,s)?(0,a.createElement)("div",{className:_t},(0,a.createElement)(De,{label:n,isChanged:g,onReset:u,unitName:l,showDevicePicker:m,popoverContent:r}),(0,a.createElement)("div",{className:bt},(0,a.createElement)(x.__experimentalNumberControl,{value:d,onChange:h,spinControls:"none",suffix:(0,a.createElement)(je,{onIncrement:p,onDecrement:v})})),i&&(0,a.createElement)("small",null,i)):null},Et=({className:e})=>(0,a.createElement)("div",{className:e},i.__("No options","masterstudy-lms-learning-management-system")),Ct=w("select__single-item"),ft=w("select__container"),Nt=w("select__container__multi-item"),Tt=({multiple:e,value:t,options:n,onChange:l})=>{const{singleValue:i,multipleValue:r}=((e,t,n)=>({singleValue:(0,m.useMemo)((()=>t?null:n.find((t=>t.value===e))?.label),[t,e,n]),multipleValue:(0,m.useMemo)((()=>t?e:null),[t,e])}))(t,e,n);return(0,a.createElement)(O,{condition:e,fallback:(0,a.createElement)("div",{className:Ct},i)},(0,a.createElement)("div",{className:ft},r?.map((e=>{const t=n.find((t=>t.value===e));return t?(0,a.createElement)("div",{key:t.value,className:Nt},(0,a.createElement)("div",null,t.label),(0,a.createElement)(x.Dashicon,{icon:"no-alt",onClick:()=>l(t.value),size:16})):null}))))},wt=w("select"),Mt=w("select__select-box"),St=w("select__placeholder"),xt=w("select__select-box-multiple"),Ot=w("select__menu"),Bt=w("select__menu__options-container"),kt=w("select__menu__item"),Rt=e=>{const{options:t,multiple:n=!1,placeholder:l="Select",value:i,onSelect:r}=e,{isOpen:s,onToggle:c,onClose:d}=A(),u=V(d),g=((e,t,n)=>(0,m.useMemo)((()=>n&&Array.isArray(e)?t.filter((t=>!e.includes(t.value))):t.filter((t=>t.value!==e))),[e,t,n]))(i,t,n),p=((e,t,n,a)=>(0,m.useCallback)((l=>{if(t&&Array.isArray(e)){const t=e.includes(l)?e.filter((e=>e!==l)):[...e,l];n(t)}else n(l),a()}),[t,e,n,a]))(i,n,r,d),v=((e,t)=>(0,m.useMemo)((()=>t&&Array.isArray(e)?Boolean(e.length):Boolean(e)),[t,e]))(i,n),h=n&&Array.isArray(i)&&i?.length>0;return(0,a.createElement)("div",{className:wt,ref:u},(0,a.createElement)("div",{className:o()([Mt],{[xt]:h}),onClick:c},(0,a.createElement)(O,{condition:v,fallback:(0,a.createElement)("div",{className:St},l)},(0,a.createElement)(Tt,{onChange:p,options:t,multiple:n,value:i})),(0,a.createElement)(x.Dashicon,{icon:s?"arrow-up-alt2":"arrow-down-alt2",size:16,style:{color:"#4D5E6F"}})),(0,a.createElement)(O,{condition:s},(0,a.createElement)("div",{className:Ot},(0,a.createElement)(O,{condition:Boolean(g.length),fallback:(0,a.createElement)(Et,{className:kt})},(0,a.createElement)("div",{className:Bt},g.map((e=>(0,a.createElement)("div",{key:e.value,onClick:()=>p(e.value),className:kt},e.label))))))))},At=w("setting-select"),Pt=e=>{const{name:t,options:n,label:l,multiple:i=!1,placeholder:r,isAdaptive:s=!1,dependencyMode:o,dependencies:m}=e,{fieldName:c}=I(t,s),{value:d,isChanged:u,onChange:g,onReset:p}=D(c);return U(m,o)?(0,a.createElement)("div",{className:At},(0,a.createElement)(O,{condition:Boolean(l)},(0,a.createElement)(De,{label:l,isChanged:u,onReset:p,showDevicePicker:s})),(0,a.createElement)(Rt,{options:n,value:d,onSelect:g,multiple:i,placeholder:r})):null},Ht=w("row-select"),Lt=w("row-select__label"),Dt=w("row-select__control"),It=e=>{const{name:t,label:n,options:l,isAdaptive:i=!1}=e,{fieldName:r}=I(t,i),{isChanged:s,onReset:o}=D(r);return(0,a.createElement)("div",{className:Ht},(0,a.createElement)("div",{className:Lt},(0,a.createElement)("div",null,n),(0,a.createElement)(O,{condition:i},(0,a.createElement)(Te,null))),(0,a.createElement)("div",{className:Dt},(0,a.createElement)(Pt,{name:t,options:l,isAdaptive:i}),(0,a.createElement)(O,{condition:s},(0,a.createElement)(Me,{onReset:o}))))},Ft=w("typography-select"),Ut=w("typography-select-label"),Vt=e=>{const{name:t,label:n,options:l,isAdaptive:i=!1}=e,{fieldName:r}=I(t,i),{isChanged:s,onReset:o}=D(r);return(0,a.createElement)("div",{className:Ft},(0,a.createElement)("div",{className:Ut},(0,a.createElement)("div",null,n),(0,a.createElement)(O,{condition:i},(0,a.createElement)(Te,null))),(0,a.createElement)(Pt,{name:t,options:l,isAdaptive:i}),(0,a.createElement)(O,{condition:s},(0,a.createElement)(Me,{onReset:o})))},Wt=w("typography"),Gt=e=>{const{fontSizeName:t,fontWeightName:n,textTransformName:l,fontStyleName:r,textDecorationName:s,lineHeightName:o,letterSpacingName:m,wordSpacingName:c,fontSizeUnitName:d,lineHeightUnitName:u,letterSpacingUnitName:g,wordSpacingUnitName:p,dependencyMode:v,dependencies:h,isAdaptive:_=!1}=e,{fontWeightOptions:b,textTransformOptions:y,fontStyleOptions:E,textDecorationOptions:C}={fontWeightOptions:[{label:i.__("100 (Thin)","masterstudy-lms-learning-management-system"),value:"100"},{label:i.__("200 (Extra Light)","masterstudy-lms-learning-management-system"),value:"200"},{label:i.__("300 (Light)","masterstudy-lms-learning-management-system"),value:"300"},{label:i.__("400 (Normal)","masterstudy-lms-learning-management-system"),value:"400"},{label:i.__("500 (Medium)","masterstudy-lms-learning-management-system"),value:"500"},{label:i.__("600 (Semi Bold)","masterstudy-lms-learning-management-system"),value:"600"},{label:i.__("700 (Bold)","masterstudy-lms-learning-management-system"),value:"700"},{label:i.__("800 (Extra Bold)","masterstudy-lms-learning-management-system"),value:"800"},{label:i.__("900 (Extra)","masterstudy-lms-learning-management-system"),value:"900"}],textTransformOptions:[{label:i.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:i.__("Uppercase","masterstudy-lms-learning-management-system"),value:"uppercase"},{label:i.__("Lowercase","masterstudy-lms-learning-management-system"),value:"lowercase"},{label:i.__("Capitalize","masterstudy-lms-learning-management-system"),value:"capitalize"},{label:i.__("Normal","masterstudy-lms-learning-management-system"),value:"none"}],fontStyleOptions:[{label:i.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:i.__("Normal","masterstudy-lms-learning-management-system"),value:"none"},{label:i.__("Italic","masterstudy-lms-learning-management-system"),value:"italic"},{label:i.__("Oblique","masterstudy-lms-learning-management-system"),value:"oblique"}],textDecorationOptions:[{label:i.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:i.__("Underline","masterstudy-lms-learning-management-system"),value:"underline"},{label:i.__("Line Through","masterstudy-lms-learning-management-system"),value:"line-through"},{label:i.__("None","masterstudy-lms-learning-management-system"),value:"none"}]};return U(h,v)?(0,a.createElement)("div",{className:Wt},(0,a.createElement)(pt,{name:t,label:i.__("Size","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:d,isAdaptive:_}),(0,a.createElement)(Vt,{name:n,label:i.__("Weight","masterstudy-lms-learning-management-system"),options:b}),(0,a.createElement)(Vt,{name:l,label:i.__("Transform","masterstudy-lms-learning-management-system"),options:y}),(0,a.createElement)(Vt,{name:r,label:i.__("Style","masterstudy-lms-learning-management-system"),options:E}),(0,a.createElement)(Vt,{name:s,label:i.__("Decoration","masterstudy-lms-learning-management-system"),options:C}),(0,a.createElement)(pt,{name:o,label:i.__("Line Height","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:u,isAdaptive:_}),(0,a.createElement)(pt,{name:m,label:i.__("Letter Spacing","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:g,isAdaptive:_}),c&&(0,a.createElement)(pt,{name:c,label:i.__("Word Spacing","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:p,isAdaptive:_})):null},zt=(w("file-upload"),w("file-upload__wrap"),w("file-upload__image"),w("file-upload__remove"),w("file-upload__replace"),(0,m.createContext)({activeTab:0,setActiveTab:()=>{}})),Zt=()=>{const e=(0,m.useContext)(zt);if(!e)throw new Error("useTabs should be used inside Tabs");return e},jt=({children:e})=>{const[t,n]=(0,m.useState)(0);return(0,a.createElement)(zt.Provider,{value:{activeTab:t,setActiveTab:n}},(0,a.createElement)("div",{className:`active-tab-${t}`},e))},$t=w("tab-list"),Kt=({children:e})=>(0,a.createElement)("div",{className:$t},m.Children.map(e,((e,t)=>(0,m.cloneElement)(e,{index:t})))),Xt=w("tab"),qt=w("tab-active"),Yt=w("content"),Jt=({index:e,title:t,icon:n})=>{const{activeTab:l,setActiveTab:i}=Zt();return(0,a.createElement)("div",{className:o()([Xt],{[qt]:l===e}),onClick:()=>i(e)},(0,a.createElement)("div",{className:Yt},(0,a.createElement)("div",null,n),(0,a.createElement)("div",null,t)))},Qt=({children:e})=>(0,a.createElement)("div",null,m.Children.map(e,((e,t)=>(0,m.cloneElement)(e,{index:t})))),en=w("tab-panel"),tn=({index:e,children:t})=>{const{activeTab:n}=Zt();return n===e?(0,a.createElement)("div",{className:en},t):null},nn=({generalTab:e,styleTab:t,advancedTab:n})=>(0,a.createElement)(jt,null,(0,a.createElement)(Kt,null,(0,a.createElement)(Jt,{title:i.__("General","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(x.Dashicon,{icon:"layout"})}),(0,a.createElement)(Jt,{title:i.__("Style","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(x.Dashicon,{icon:"admin-appearance"})}),(0,a.createElement)(Jt,{title:i.__("Advanced","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(x.Dashicon,{icon:"admin-settings"})})),(0,a.createElement)(Qt,null,(0,a.createElement)(tn,null,e),(0,a.createElement)(tn,null,t),(0,a.createElement)(tn,null,n)));window.ReactDOM;"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement;function an(e){const t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function ln(e){return"nodeType"in e}function rn(e){var t,n;return e?an(e)?e:ln(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function sn(e){const{Document:t}=rn(e);return e instanceof t}function on(e){return!an(e)&&e instanceof rn(e).HTMLElement}function mn(e){return e instanceof rn(e).SVGElement}function cn(e){return e?an(e)?e.document:ln(e)?sn(e)?e:on(e)||mn(e)?e.ownerDocument:document:document:document}function dn(e){return function(t){for(var n=arguments.length,a=new Array(n>1?n-1:0),l=1;l<n;l++)a[l-1]=arguments[l];return a.reduce(((t,n)=>{const a=Object.entries(n);for(const[n,l]of a){const a=t[n];null!=a&&(t[n]=a+e*l)}return t}),{...t})}}const un=dn(-1);function gn(e){if(function(e){if(!e)return!1;const{TouchEvent:t}=rn(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return function(e){return"clientX"in e&&"clientY"in e}(e)?{x:e.clientX,y:e.clientY}:null}var pn;!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(pn||(pn={}));const vn=Object.freeze({x:0,y:0});var hn,bn,yn,En;!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(hn||(hn={}));class Cn{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach((e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)}))},this.target=e}add(e,t,n){var a;null==(a=this.target)||a.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function fn(e,t){const n=Math.abs(e.x),a=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+a**2)>t:"x"in t&&"y"in t?n>t.x&&a>t.y:"x"in t?n>t.x:"y"in t&&a>t.y}function Nn(e){e.preventDefault()}function Tn(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(bn||(bn={})),(En=yn||(yn={})).Space="Space",En.Down="ArrowDown",En.Right="ArrowRight",En.Left="ArrowLeft",En.Up="ArrowUp",En.Esc="Escape",En.Enter="Enter";yn.Space,yn.Enter,yn.Esc,yn.Space,yn.Enter;function wn(e){return Boolean(e&&"distance"in e)}function Mn(e){return Boolean(e&&"delay"in e)}class Sn{constructor(e,t,n){var a;void 0===n&&(n=function(e){const{EventTarget:t}=rn(e);return e instanceof t?e:cn(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;const{event:l}=e,{target:i}=l;this.props=e,this.events=t,this.document=cn(i),this.documentListeners=new Cn(this.document),this.listeners=new Cn(n),this.windowListeners=new Cn(rn(i)),this.initialCoordinates=null!=(a=gn(l))?a:vn,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),this.windowListeners.add(bn.Resize,this.handleCancel),this.windowListeners.add(bn.DragStart,Nn),this.windowListeners.add(bn.VisibilityChange,this.handleCancel),this.windowListeners.add(bn.ContextMenu,Nn),this.documentListeners.add(bn.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(Mn(t))return void(this.timeoutId=setTimeout(this.handleStart,t.delay));if(wn(t))return}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handleStart(){const{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(bn.Click,Tn,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(bn.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;const{activated:n,initialCoordinates:a,props:l}=this,{onMove:i,options:{activationConstraint:r}}=l;if(!a)return;const s=null!=(t=gn(e))?t:vn,o=un(a,s);if(!n&&r){if(wn(r)){if(null!=r.tolerance&&fn(o,r.tolerance))return this.handleCancel();if(fn(o,r.distance))return this.handleStart()}return Mn(r)&&fn(o,r.tolerance)?this.handleCancel():void 0}e.cancelable&&e.preventDefault(),i(s)}handleEnd(){const{onEnd:e}=this.props;this.detach(),e()}handleCancel(){const{onCancel:e}=this.props;this.detach(),e()}handleKeydown(e){e.code===yn.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}const xn={move:{name:"pointermove"},end:{name:"pointerup"}};(class extends Sn{constructor(e){const{event:t}=e,n=cn(t.target);super(e,xn,n)}}).activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;return!(!n.isPrimary||0!==n.button||(null==a||a({event:n}),0))}}];const On={move:{name:"mousemove"},end:{name:"mouseup"}};var Bn;!function(e){e[e.RightClick=2]="RightClick"}(Bn||(Bn={})),class extends Sn{constructor(e){super(e,On,cn(e.event.target))}}.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;return n.button!==Bn.RightClick&&(null==a||a({event:n}),!0)}}];const kn={move:{name:"touchmove"},end:{name:"touchend"}};var Rn,An,Pn,Hn,Ln;(class extends Sn{constructor(e){super(e,kn)}static setup(){return window.addEventListener(kn.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(kn.move.name,e)};function e(){}}}).activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;const{touches:l}=n;return!(l.length>1||(null==a||a({event:n}),0))}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(Rn||(Rn={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(An||(An={})),hn.Backward,hn.Forward,hn.Backward,hn.Forward,function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(Pn||(Pn={})),function(e){e.Optimized="optimized"}(Hn||(Hn={})),Pn.WhileDragging,Hn.Optimized,Map,function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(Ln||(Ln={})),yn.Down,yn.Right,yn.Up,yn.Left,i.__("Lectures","masterstudy-lms-learning-management-system"),i.__("Duration","masterstudy-lms-learning-management-system"),i.__("Views","masterstudy-lms-learning-management-system"),i.__("Level","masterstudy-lms-learning-management-system"),i.__("Members","masterstudy-lms-learning-management-system"),i.__("Empty","masterstudy-lms-learning-management-system"),w("sortable__item"),w("sortable__item__disabled"),w("sortable__item__content"),w("sortable__item__content__drag-item"),w("sortable__item__content__drag-item__disabled"),w("sortable__item__content__title"),w("sortable__item__control"),w("sortable__item__icon"),w("nested-sortable"),w("nested-sortable__item"),w("sortable");const Dn=w("accordion"),In=w("accordion__header"),Fn=w("accordion__header-flex"),Un=w("accordion__content"),Vn=w("accordion__icon"),Wn=w("accordion__title"),Gn=w("accordion__title-disabled"),zn=w("accordion__indicator"),Zn=w("accordion__controls"),jn=w("accordion__controls-disabled"),$n=({title:e,children:t,accordionFields:n,switchName:l,visible:i=!0,isDefaultOpen:r=!1})=>{const{isOpen:s,onToggle:d,disabled:u,onReset:g,hasChanges:p,onClose:v}=((e,t,n)=>{var a;const{isOpen:l,onToggle:i,onClose:r}=A(t),{defaultValues:s,attributes:o,setAttributes:m}=L(),d=((e,t,n)=>{for(const a of n)if(!c(e[a],t[a]))return!0;return!1})(s,o,e);return{isOpen:l,onToggle:i,disabled:!(null===(a=o[n])||void 0===a||a),hasChanges:d,onReset:t=>{t.stopPropagation(),m(e.reduce(((e,t)=>(e[t]=s[t],e)),{}))},onClose:r}})(n,r,l);return((e,t)=>{const{attributes:n}=L(),a=!n[t];(0,m.useEffect)((()=>{a&&e()}),[a,e])})(v,l),i?(0,a.createElement)("div",{className:Dn},(0,a.createElement)("div",{className:In},(0,a.createElement)("div",{className:Fn,onClick:u?null:d},(0,a.createElement)("div",{className:o()(Wn,{[Gn]:u,"with-switch":Boolean(l)})},(0,a.createElement)("div",null,e),(0,a.createElement)(O,{condition:p&&!u},(0,a.createElement)("div",{className:zn}))),(0,a.createElement)("div",{className:o()(Zn,{[jn]:u})},(0,a.createElement)(x.Dashicon,{icon:s?"arrow-up-alt2":"arrow-down-alt2",className:Vn,size:16}))),(0,a.createElement)(O,{condition:Boolean(l)},(0,a.createElement)(ht,{name:l})),(0,a.createElement)(O,{condition:p&&!u},(0,a.createElement)(Me,{onReset:g}))),s&&(0,a.createElement)("div",{className:Un},t)):null},Kn=({type:e})=>(0,a.createElement)(x.SVG,{xmlns:"http://www.w3.org/2000/svg",width:"8",height:"14",viewBox:"0 0 8 14",fill:"none"},(0,a.createElement)(x.Rect,{width:"40",height:"40"}),(0,a.createElement)(x.Path,{d:e,stroke:"currentColor","stroke-width":"2","stroke-miterlimit":"10","stroke-linecap":"round","stroke-linejoin":"round"}));let Xn=function(e){return e.LEFT="M7 1L1 7L7 13",e.RIGHT="M0.999999 1L7 7L1 13",e}({});const qn=({position:e,prevSlide:t,nextSlide:n,isVisible:l=!0,disabledNext:i,disabledPrev:r,containerClassName:s,navItemClassName:m,disabledNavItemClassName:c})=>l?(0,a.createElement)("div",{className:o()(s,{"bottom-left":e===p.BOTTOM_lEFT,"bottom-right":e===p.BOTTOM_RIGHT,"bottom-center":e===p.BOTTOM_CENTER,"top-left":e===p.TOP_lEFT,"top-right":e===p.TOP_RIGHT})},(0,a.createElement)("div",{className:o()(m,{[c]:r}),onClick:r?null:t},(0,a.createElement)(Kn,{type:Xn.LEFT})),(0,a.createElement)("div",{className:o()(m,{[c]:i}),onClick:i?null:n},(0,a.createElement)(Kn,{type:Xn.RIGHT}))):null,Yn=(w("preset-picker"),w("preset-picker__label"),w("preset-picker__remove"),w("preset-picker__presets-list"),w("preset-picker__presets-list__item"),w("preset-picker__presets-list__item__preset"),w("preset-picker__presets-list__item__preset-active"),{slidesToShow:4,slidesToShowTablet:2,slidesToShowMobile:1,slidesCount:8,orderBy:"registered_date desc",instructors:[],autoplay:!1,loop:!0,showNavigation:!0,navigationPosition:p.BOTTOM_CENTER}),Jn=Object.keys(Yn),Qn={layoutMargin:C,layoutMarginTablet:C,layoutMarginMobile:C,layoutMarginUnit:"px",layoutMarginUnitTablet:"px",layoutMarginUnitMobile:"px",layoutPadding:{top:"100",right:"104",bottom:"100",left:"104"},layoutPaddingTablet:{top:"70",right:"74",bottom:"70",left:"74"},layoutPaddingMobile:{top:"50",right:"50",bottom:"50",left:"50"},layoutPaddingUnit:"px",layoutPaddingUnitTablet:"px",layoutPaddingUnitMobile:"px",cardGap:30,cardGapTablet:null,cardGapMobile:null,cardGapUnit:"px",cardGapUnitTablet:"px",cardGapUnitMobile:"px",layoutBackground:"#EEF1F7",layoutBorderStyle:"none",layoutBorderStyleTablet:"",layoutBorderStyleMobile:"",layoutBorderColor:"",layoutBorderColorTablet:"",layoutBorderColorMobile:"",layoutBorderWidth:C,layoutBorderWidthTablet:C,layoutBorderWidthMobile:C,layoutBorderWidthUnit:"px",layoutBorderWidthUnitTablet:"px",layoutBorderWidthUnitMobile:"px",layoutBorderRadius:C,layoutBorderRadiusTablet:C,layoutBorderRadiusMobile:C,layoutBorderRadiusUnit:"px",layoutBorderRadiusUnitTablet:"px",layoutBorderRadiusUnitMobile:"px",layoutWidth:"alignfull",layoutZIndex:null,layoutZIndexTablet:null,layoutZIndexMobile:null},ea=Object.keys(Qn),ta={titleTag:"h1",titleFontSize:36,titleFontSizeTablet:null,titleFontSizeMobile:null,titleFontSizeUnit:"px",titleFontSizeUnitTablet:"px",titleFontSizeUnitMobile:"px",titleFontWeight:"700",titleTextTransform:"none",titleFontStyle:"inherit",titleTextDecoration:"inherit",titleLineHeight:null,titleLineHeightTablet:null,titleLineHeightMobile:null,titleLineHeightUnit:"px",titleLineHeightUnitTablet:"px",titleLineHeightUnitMobile:"px",titleLetterSpacing:0,titleLetterSpacingTablet:null,titleLetterSpacingMobile:null,titleLetterSpacingUnit:"px",titleLetterSpacingUnitTablet:"px",titleLetterSpacingUnitMobile:"px",titleWordSpacing:0,titleWordSpacingTablet:null,titleWordSpacingMobile:null,titleWordSpacingUnit:"px",titleWordSpacingUnitTablet:"px",titleWordSpacingUnitMobile:"px",titleMargin:C,titleMarginTablet:C,titleMarginMobile:C,titleMarginUnit:"px",titleMarginUnitTablet:"px",titleMarginUnitMobile:"px"},na=Object.keys(ta),aa={navigationBackground:"#EEF1F7",navigationBackgroundHover:"",navigationColor:"#4D5E6F",navigationColorHover:"",navigationBorderStyle:"none",navigationBorderStyleTablet:"",navigationBorderStyleMobile:"",navigationBorderStyleHover:"",navigationBorderStyleHoverTablet:"",navigationBorderStyleHoverMobile:"",navigationBorderColor:"",navigationBorderColorTablet:"",navigationBorderColorMobile:"",navigationBorderColorHover:"",navigationBorderColorHoverTablet:"",navigationBorderColorHoverMobile:"",navigationBorderWidth:C,navigationBorderWidthTablet:C,navigationBorderWidthMobile:C,navigationBorderWidthHover:C,navigationBorderWidthHoverTablet:C,navigationBorderWidthHoverMobile:C,navigationBorderWidthUnit:"px",navigationBorderWidthUnitTablet:"px",navigationBorderWidthUnitMobile:"px",navigationBorderRadius:{top:"4",right:"4",bottom:"4",left:"4"},navigationBorderRadiusTablet:C,navigationBorderRadiusMobile:C,navigationBorderRadiusUnit:"px",navigationBorderRadiusUnitTablet:"px",navigationBorderRadiusUnitMobile:"px",navigationMargin:C,navigationMarginTablet:C,navigationMarginMobile:C,navigationMarginUnit:"px",navigationMarginUnitTablet:"px",navigationMarginUnitMobile:"px",navigationPadding:{top:"13",right:"16",bottom:"13",left:"16"},navigationPaddingTablet:C,navigationPaddingMobile:C,navigationPaddingUnit:"px",navigationPaddingUnitTablet:"px",navigationPaddingUnitMobile:"px"},la=Object.keys(aa),ia={...Qn,...ta,...aa},ra={...Yn,...ia},sa=new Map([["layoutMargin",{unit:"layoutMarginUnit",isAdaptive:!0}],["layoutPadding",{unit:"layoutPaddingUnit",isAdaptive:!0}],["cardGap",{unit:"cardGapUnit",isAdaptive:!0}],["slidesToShow",{isAdaptive:!0}],["slidesCount",{}],["layoutBackground",{}],["layoutBorderStyle",{isAdaptive:!0}],["layoutBorderColor",{isAdaptive:!0}],["layoutBorderWidth",{isAdaptive:!0,unit:"layoutBorderWidthUnit"}],["layoutBorderRadius",{isAdaptive:!0,unit:"layoutBorderRadiusUnit"}],["layoutZIndex",{isAdaptive:!0}],["titleFontSize",{unit:"titleFontSizeUnit",isAdaptive:!0}],["titleFontWeight",{}],["titleTextTransform",{}],["titleFontStyle",{}],["titleTextDecoration",{}],["titleLineHeight",{unit:"titleLineHeightUnit",isAdaptive:!0}],["titleLetterSpacing",{unit:"titleLetterSpacingUnit",isAdaptive:!0}],["titleWordSpacing",{unit:"titleWordSpacingUnit",isAdaptive:!0}],["titleMargin",{unit:"titleMarginUnit",isAdaptive:!0}],["navigationBackground",{hasHover:!0}],["navigationColor",{hasHover:!0}],["navigationBorderStyle",{isAdaptive:!0,hasHover:!0}],["navigationBorderColor",{isAdaptive:!0,hasHover:!0}],["navigationBorderWidth",{isAdaptive:!0,unit:"navigationBorderWidthUnit",hasHover:!0}],["navigationBorderRadius",{isAdaptive:!0,unit:"navigationBorderRadiusUnit"}],["navigationMargin",{unit:"navigationMarginUnit",isAdaptive:!0}],["navigationPadding",{unit:"navigationPaddingUnit",isAdaptive:!0}]]),oa=[["core/group",{className:"lms-instructors-carousel-presets"},[["masterstudy/instructors-preset",{presetMargin:{top:"50",right:"0",bottom:"50",left:"0"}},[["masterstudy/instructors-preset-classic",{}]]]]]],ma=({value:e,onChange:t,navigationPosition:n,prevSlide:l,nextSlide:s,disabledNext:m,disabledPrev:c,titleTag:d,showNavigation:u})=>(0,a.createElement)("div",{className:o()("lms-instructors-carousel-header",{"top-center":n===p.TOP_CENTER,"space-between":[p.TOP_RIGHT,p.TOP_lEFT].includes(n)})},(0,a.createElement)(r.RichText,{tagName:d,className:o()("lms-instructors-carousel-title","wp-block-heading",{"top-left":n===p.TOP_RIGHT,"top-right":n===p.TOP_lEFT}),value:e,onChange:t,placeholder:i.__("Instructors Carousel","masterstudy-lms-learning-management-system")}),(0,a.createElement)(O,{condition:u},(0,a.createElement)(qn,{position:n,isVisible:f.includes(n),nextSlide:s,prevSlide:l,disabledNext:m,disabledPrev:c,containerClassName:"lms-instructors-carousel-navigations",navItemClassName:"lms-instructors-carousel-navigations_item",disabledNavItemClassName:"lms-instructors-carousel-navigations_item_disabled"}))),ca=({instructorOptions:e})=>{const{min:t,max:n}=((e,t=!1)=>{const n=k(),[a,l]=(0,m.useState)(e.default||{min:3,max:6});return(0,m.useEffect)((()=>{if(n===g.DESKTOP){const n=e.desktop||{min:t?2:3,max:6};l(n)}if(n===g.TABLET){const n=e.tablet||{min:t?1:2,max:3};l(n)}if(n===g.MOBILE){const t=e.mobile||{min:1,max:1};l(t)}}),[n,t,e]),a})({default:{min:3,max:6}},!0),l=[{label:i.__("Newest","masterstudy-lms-learning-management-system"),value:"registered_date desc"},{label:i.__("Oldest","masterstudy-lms-learning-management-system"),value:"registered_date asc"},{label:i.__("Rating","masterstudy-lms-learning-management-system"),value:"rating"}],r=[{label:i.__("Top-Left","masterstudy-lms-learning-management-system"),value:p.TOP_lEFT},{label:i.__("Top-Center","masterstudy-lms-learning-management-system"),value:p.TOP_CENTER},{label:i.__("Top-Right","masterstudy-lms-learning-management-system"),value:p.TOP_RIGHT},{label:i.__("Bottom-Left","masterstudy-lms-learning-management-system"),value:p.BOTTOM_lEFT},{label:i.__("Bottom-Center","masterstudy-lms-learning-management-system"),value:p.BOTTOM_CENTER},{label:i.__("Bottom-Right","masterstudy-lms-learning-management-system"),value:p.BOTTOM_RIGHT}];return(0,a.createElement)($n,{title:i.__("Layout","masterstudy-lms-learning-management-system"),accordionFields:Jn},(0,a.createElement)(pt,{name:"slidesToShow",label:i.__("Slides to show","masterstudy-lms-learning-management-system"),min:t,max:n,isAdaptive:!0}),(0,a.createElement)(pt,{name:"slidesCount",label:i.__("Slides in carousel","masterstudy-lms-learning-management-system"),min:1,max:24}),(0,a.createElement)(Pt,{name:"orderBy",label:i.__("Order by","masterstudy-lms-learning-management-system"),options:l}),(0,a.createElement)(O,{condition:Boolean(e.length)},(0,a.createElement)(Pt,{name:"instructors",multiple:!0,label:i.__("Instructors","masterstudy-lms-learning-management-system"),options:e})),(0,a.createElement)(ht,{name:"autoplay",label:i.__("Autoplay","masterstudy-lms-learning-management-system")}),(0,a.createElement)(ht,{name:"loop",label:i.__("Loop","masterstudy-lms-learning-management-system")}),(0,a.createElement)(ht,{name:"showNavigation",label:i.__("Navigation","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Pt,{name:"navigationPosition",label:i.__("Nav arrows position","masterstudy-lms-learning-management-system"),options:r,dependencies:[{name:"showNavigation",value:!0}]}))},da=()=>{const{attributes:e}=L(),{widthOptions:t}=(()=>{const e=[{label:i.__("Auto","masterstudy-lms-learning-management-system"),value:"alignauto"},{label:i.__("Full width","masterstudy-lms-learning-management-system"),value:"alignfull"}],t=[{label:i.__("Cover","masterstudy-lms-learning-management-system"),value:"cover"},{label:i.__("Contain","masterstudy-lms-learning-management-system"),value:"contain"},{label:i.__("Inherit","masterstudy-lms-learning-management-system"),value:"inherit"},{label:i.__("Initial","masterstudy-lms-learning-management-system"),value:"initial"},{label:i.__("Revert","masterstudy-lms-learning-management-system"),value:"revert"},{label:i.__("Revert-layer","masterstudy-lms-learning-management-system"),value:"revert-layer"},{label:i.__("Unset","masterstudy-lms-learning-management-system"),value:"unset"}],n=[{label:i.__("Center center","masterstudy-lms-learning-management-system"),value:"center center"},{label:i.__("Center left","masterstudy-lms-learning-management-system"),value:"center left"},{label:i.__("Center right","masterstudy-lms-learning-management-system"),value:"center right"},{label:i.__("Top center","masterstudy-lms-learning-management-system"),value:"top center"},{label:i.__("Top left","masterstudy-lms-learning-management-system"),value:"top left"},{label:i.__("Top right","masterstudy-lms-learning-management-system"),value:"top right"},{label:i.__("Bottom center","masterstudy-lms-learning-management-system"),value:"bottom center"},{label:i.__("Bottom left","masterstudy-lms-learning-management-system"),value:"bottom left"},{label:i.__("Bottom right","masterstudy-lms-learning-management-system"),value:"bottom right"}],a=[{label:i.__("Center","masterstudy-lms-learning-management-system"),value:"center"},{label:i.__("Start","masterstudy-lms-learning-management-system"),value:"flex-start"},{label:i.__("End","masterstudy-lms-learning-management-system"),value:"flex-end"},{label:i.__("Space Between","masterstudy-lms-learning-management-system"),value:"space-between"},{label:i.__("Space Around","masterstudy-lms-learning-management-system"),value:"space-around"},{label:i.__("Space Evenly","masterstudy-lms-learning-management-system"),value:"space-evenly"}];return{filterOptions:T,widthOptions:e,backgroundSizeOptions:t,backgroundPositionOptions:n,alignContentOptions:a}})();return(0,a.createElement)(a.Fragment,null,(0,a.createElement)($n,{title:i.__("Layout","masterstudy-lms-learning-management-system"),accordionFields:ea},(0,a.createElement)(Xe,{name:"layoutMargin",label:i.__("Margin","masterstudy-lms-learning-management-system"),unitName:"layoutMarginUnit",isAdaptive:!0,dependencies:[{name:"layoutWidth",value:"alignauto"}]}),(0,a.createElement)(Xe,{name:"layoutPadding",label:i.__("Padding","masterstudy-lms-learning-management-system"),unitName:"layoutPaddingUnit",isAdaptive:!0}),(0,a.createElement)(yt,{name:"cardGap",label:i.__("Space between cards","masterstudy-lms-learning-management-system"),unitName:"cardGapUnit",isAdaptive:!0}),(0,a.createElement)(Ve,{name:"layoutBackground",label:i.__("Background","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(it,{label:i.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"layoutBorderStyle",borderColorName:"layoutBorderColor",borderWidthName:"layoutBorderWidth",isAdaptive:!0}),(0,a.createElement)(ot,{name:"layoutBorderRadius",label:i.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,a.createElement)(It,{name:"layoutWidth",label:i.__("Width","masterstudy-lms-learning-management-system"),options:t}),(0,a.createElement)(pt,{name:"layoutZIndex",label:i.__("Z-Index","masterstudy-lms-learning-management-system"),min:0,max:100,isAdaptive:!0})),(0,a.createElement)($n,{title:i.__("Title","masterstudy-lms-learning-management-system"),accordionFields:na},(0,a.createElement)(et,{name:"titleTag",label:i.__("Title tag","masterstudy-lms-learning-management-system"),options:[{label:"H1",value:"h1"},{label:"H2",value:"h2"},{label:"H3",value:"h3"},{label:"H4",value:"h4"},{label:"H5",value:"h5"},{label:"H6",value:"h6"},{label:"P",value:"p"}]}),(0,a.createElement)(Gt,{fontSizeName:"titleFontSize",fontSizeUnitName:"titleFontSizeUnit",fontWeightName:"titleFontWeight",textTransformName:"titleTextTransform",fontStyleName:"titleFontStyle",textDecorationName:"titleTextDecoration",lineHeightName:"titleLineHeight",lineHeightUnitName:"titleLineHeightUnit",letterSpacingName:"titleLetterSpacing",letterSpacingUnitName:"titleLetterSpacingUnit",wordSpacingName:"titleWordSpacing",wordSpacingUnitName:"titleWordSpacingUnit",isAdaptive:!0}),(0,a.createElement)(Xe,{name:"titleMargin",label:i.__("Margin","masterstudy-lms-learning-management-system"),unitName:"titleMarginUnit",isAdaptive:!0})),(0,a.createElement)($n,{title:i.__("Arrows","masterstudy-lms-learning-management-system"),accordionFields:la,visible:!!e.showNavigation},(0,a.createElement)(Ve,{name:"navigationBackground",label:i.__("Background","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,a.createElement)(Ve,{name:"navigationColor",label:i.__("Color","masterstudy-lms-learning-management-system"),placeholder:i.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,a.createElement)(it,{label:i.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"navigationBorderStyle",borderColorName:"navigationBorderColor",borderWidthName:"navigationBorderWidth",isAdaptive:!0,hasHover:!0}),(0,a.createElement)(ot,{name:"navigationBorderRadius",label:i.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,a.createElement)(Xe,{name:"navigationMargin",label:i.__("Margin","masterstudy-lms-learning-management-system"),unitName:"navigationMarginUnit",isAdaptive:!0}),(0,a.createElement)(Xe,{name:"navigationPadding",label:i.__("Padding","masterstudy-lms-learning-management-system"),unitName:"navigationMarginUnit",isAdaptive:!0})))},ua=({attributes:e,setAttributes:t,instructorOptions:n})=>{const{onResetByFieldName:l,changedFieldsByName:i}=((e,t,n,a=[])=>{const l=(e=>{const t={};return Object.entries(e).forEach((([e,n])=>{e.includes("UAG")||(t[e]=n)})),t})(t),i=!c(e,l),r=((e,t,n)=>{const a=new Map;return n.forEach((n=>{a.has(n)||a.set(n,(()=>t({[n]:e[n]})))})),a})(e,n,a),s=((e,t,n)=>{const a=new Map;return n.forEach((n=>{a.has(n)?a.set(n,!1):a.set(n,!c(e[n],t[n]))})),a})(e,l,a);return{hasChanges:i,onResetByFieldName:r,changedFieldsByName:s}})(ra,e,t,Object.keys(ra));return(0,m.useEffect)((()=>{e.showNavigation||t({navigationPosition:p.BOTTOM_CENTER})}),[e.showNavigation,t]),(0,a.createElement)(r.InspectorControls,null,(0,a.createElement)(H,{attributes:e,setAttributes:t,defaultValues:ra,onResetByFieldName:l,changedFieldsByName:i},(0,a.createElement)(nn,{generalTab:(0,a.createElement)(ca,{instructorOptions:n}),styleTab:(0,a.createElement)(da,null),advancedTab:(0,a.createElement)(a.Fragment,null)})))},ga=JSON.parse('{"UU":"masterstudy/instructors-carousel"}');(0,l.registerBlockType)(ga.UU,{title:i._x("MasterStudy Instructors Carousel","block title","masterstudy-lms-learning-management-system"),description:i._x("Use this block to show your instructors in a carousel layout and set up its look.","block description","masterstudy-lms-learning-management-system"),category:"masterstudy-lms-blocks",icon:{src:(0,a.createElement)("svg",{width:"512",height:"512",viewBox:"0 0 512 512",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,a.createElement)("g",{clipPath:"url(#clip0_708_21917)"},(0,a.createElement)("path",{d:"M471.767 0.996094H40.2229C18.5675 1.01602 1.01602 18.5675 0.996094 40.2229V471.767C1.01602 493.423 18.5675 510.974 40.2229 511.004H471.767C493.423 510.984 510.974 493.432 511.004 471.767V40.2229C510.984 18.5675 493.432 1.01602 471.767 0.996094ZM40.2229 20.6095H471.767C482.595 20.6194 491.371 29.3952 491.381 40.2229V79.4496H20.6095V40.2229C20.6194 29.3952 29.3952 20.6194 40.2229 20.6095ZM471.767 491.391H40.2229C29.3952 491.381 20.6194 482.605 20.6095 471.777V99.073H491.391V471.767C491.381 482.605 482.605 491.381 471.767 491.391Z",fill:"black"}),(0,a.createElement)("path",{d:"M471.777 512H40.2229C18.0694 511.98 0.0199222 493.931 0 471.777V40.2229C0.0199222 18.0694 18.0694 0.0199222 40.2229 0H471.767C493.931 0.0199222 511.97 18.0694 511.99 40.2229V471.767C511.98 493.931 493.931 511.98 471.777 512ZM40.2229 1.99222C19.1651 2.01214 2.01214 19.1651 1.99222 40.2328V471.777C2.01214 492.835 19.1651 509.988 40.2328 510.008H471.777C492.835 509.988 509.988 492.835 510.008 471.767V40.2229C509.988 19.1651 492.835 2.01214 471.767 1.99222H40.2229ZM471.777 492.387H40.2229C28.8672 492.377 19.6233 483.133 19.6134 471.777V98.0769H492.377V471.767C492.377 483.133 483.133 492.377 471.777 492.387ZM21.6056 100.069V471.767C21.6156 482.027 29.9729 490.374 40.2229 490.384H471.767C482.027 490.374 490.374 482.027 490.384 471.767V100.069H21.6056ZM492.387 80.4557H19.6134V40.2229C19.6233 28.8672 28.8672 19.6233 40.2229 19.6134H471.767C483.123 19.6233 492.367 28.8672 492.377 40.2229V80.4557H492.387ZM21.6056 78.4635H490.384V40.2229C490.374 29.963 482.017 21.6156 471.767 21.6056H40.2229C29.963 21.6156 21.6156 29.9729 21.6056 40.2229V78.4635Z",fill:"black"}),(0,a.createElement)("path",{d:"M128.817 59.9857C134.247 59.9857 138.648 55.5839 138.648 50.1541C138.648 44.7243 134.247 40.3225 128.817 40.3225C123.387 40.3225 118.985 44.7243 118.985 50.1541C118.985 55.5839 123.387 59.9857 128.817 59.9857Z",fill:"black"}),(0,a.createElement)("path",{d:"M128.817 60.9918C122.85 60.9918 117.989 56.1308 117.989 50.1641C117.989 44.1974 122.85 39.3364 128.817 39.3364C134.784 39.3364 139.645 44.1974 139.645 50.1641C139.645 56.1308 134.784 60.9918 128.817 60.9918ZM128.817 41.3187C123.946 41.3187 119.981 45.2832 119.981 50.1542C119.981 55.0251 123.946 58.9897 128.817 58.9897C133.688 58.9897 137.652 55.0251 137.652 50.1542C137.652 45.2832 133.688 41.3187 128.817 41.3187Z",fill:"black"}),(0,a.createElement)("path",{d:"M89.4904 59.9857C94.9202 59.9857 99.322 55.5839 99.322 50.1541C99.322 44.7243 94.9202 40.3225 89.4904 40.3225C84.0606 40.3225 79.6588 44.7243 79.6588 50.1541C79.6588 55.5839 84.0606 59.9857 89.4904 59.9857Z",fill:"black"}),(0,a.createElement)("path",{d:"M89.4904 60.9918C83.5237 60.9918 78.6627 56.1308 78.6627 50.1641C78.6627 44.1974 83.5237 39.3364 89.4904 39.3364C95.4571 39.3364 100.318 44.1974 100.318 50.1641C100.318 56.1308 95.4571 60.9918 89.4904 60.9918ZM89.4904 41.3187C84.6194 41.3187 80.6549 45.2832 80.6549 50.1542C80.6549 55.0251 84.6194 58.9897 89.4904 58.9897C94.3614 58.9897 98.3259 55.0251 98.3259 50.1542C98.3259 45.2832 94.3614 41.3187 89.4904 41.3187Z",fill:"black"}),(0,a.createElement)("path",{d:"M50.154 59.9857C55.5839 59.9857 59.9856 55.5839 59.9856 50.1541C59.9856 44.7243 55.5839 40.3225 50.154 40.3225C44.7242 40.3225 40.3224 44.7243 40.3224 50.1541C40.3224 55.5839 44.7242 59.9857 50.154 59.9857Z",fill:"black"}),(0,a.createElement)("path",{d:"M50.1541 60.9918C44.1874 60.9918 39.3264 56.1308 39.3264 50.1641C39.3264 44.1974 44.1874 39.3364 50.1541 39.3364C56.1208 39.3364 60.9818 44.1974 60.9818 50.1641C60.9818 56.1308 56.1307 60.9918 50.1541 60.9918ZM50.1541 41.3187C45.2831 41.3187 41.3186 45.2832 41.3186 50.1542C41.3186 55.0251 45.2831 58.9897 50.1541 58.9897C55.025 58.9897 58.9895 55.0251 58.9895 50.1542C58.9895 45.2832 55.035 41.3187 50.1541 41.3187Z",fill:"black"}),(0,a.createElement)("path",{d:"M200.074 375.956H312.022C317.336 375.956 321.519 371.076 321.519 365.762V324.743C321.555 304.595 305.242 288.233 285.093 288.197C285.081 288.197 285.069 288.197 285.057 288.197C282.003 288.185 279.118 289.627 277.303 292.092C268.647 303.825 252.117 306.326 240.384 297.67C238.256 296.107 236.38 294.22 234.805 292.092C232.99 289.627 230.117 288.185 227.051 288.197C206.867 288.197 190.493 304.559 190.469 324.743V365.774C190.481 371.076 194.773 375.956 200.074 375.956ZM209.716 324.743C209.728 316.725 215.21 309.752 222.988 307.865C240.155 326.102 268.851 326.979 287.089 309.824C287.762 309.187 288.411 308.538 289.048 307.865C296.826 309.764 302.296 316.737 302.284 324.743V356.721H209.716V324.743Z",fill:"black"}),(0,a.createElement)("path",{d:"M366 189.617C366 184.304 361.696 180 356.383 180H155.617C150.304 180 146 184.304 146 189.617V396.393C146 401.707 150.304 406.011 155.617 406.011H356.383C361.696 406.011 366 401.707 366 396.393V189.617ZM346.765 386.776H165.235V199.235H346.765V386.776Z",fill:"black"}),(0,a.createElement)("path",{d:"M256 217.268C236.801 217.268 221.172 233.449 221.172 253.321C221.172 273.206 236.789 289.375 256 289.375C275.198 289.375 290.827 273.193 290.827 253.321C290.827 233.449 275.198 217.268 256 217.268ZM256 273.145C245.06 273.145 236.188 264.273 236.188 253.333C236.188 242.394 245.06 233.521 256 233.521C266.939 233.521 275.812 242.394 275.812 253.333C275.812 264.273 266.939 273.145 256 273.145Z",fill:"black"}),(0,a.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M440.096 299.559L410.799 266.816C408.673 264.44 408.876 260.784 411.253 258.657C413.63 256.531 417.285 256.734 419.412 259.11L452.156 295.706C454.118 297.9 454.118 301.219 452.156 303.412L419.412 340.008C417.285 342.384 413.63 342.587 411.253 340.461C408.876 338.335 408.673 334.678 410.799 332.302L440.096 299.559Z",fill:"black"}),(0,a.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M419.412 340.008C417.285 342.384 413.63 342.587 411.253 340.461C408.876 338.335 408.673 334.678 410.799 332.302L440.096 299.559L410.799 266.816C408.673 264.44 408.876 260.784 411.253 258.657C413.63 256.531 417.285 256.734 419.412 259.11L452.156 295.706C454.118 297.9 454.118 301.219 452.156 303.412L419.412 340.008ZM409.253 256.422C412.864 253.19 418.416 253.499 421.648 257.11L454.391 293.706C457.373 297.038 457.373 302.08 454.391 305.412L421.648 342.008C418.416 345.619 412.864 345.928 409.253 342.697C405.641 339.465 405.333 333.913 408.564 330.301L436.07 299.559L408.564 268.817C405.333 265.205 405.641 259.653 409.253 256.422Z",fill:"black"}),(0,a.createElement)("g",{clipPath:"url(#clip1_708_21917)"},(0,a.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M72.1076 299.56L101.404 332.302C103.53 334.679 103.327 338.335 100.95 340.461C98.5735 342.588 94.9178 342.385 92.7911 340.008L60.0476 303.413C58.0849 301.219 58.0849 297.9 60.0476 295.707L92.7911 259.111C94.9178 256.734 98.5736 256.531 100.95 258.658C103.327 260.784 103.53 264.44 101.404 266.817L72.1076 299.56Z",fill:"black"}),(0,a.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M92.791 259.111C94.9178 256.734 98.5735 256.531 100.95 258.658C103.327 260.784 103.53 264.44 101.404 266.817L72.1076 299.56L101.404 332.302C103.53 334.679 103.327 338.335 100.95 340.461C98.5735 342.588 94.9178 342.385 92.791 340.008L60.0476 303.413C58.0849 301.219 58.0849 297.9 60.0476 295.707L92.791 259.111ZM102.951 342.697C99.3388 345.929 93.7868 345.619 90.5556 342.009L57.8119 305.413C54.8304 302.08 54.83 297.039 57.8119 293.706L90.5553 257.111C93.7869 253.499 99.3391 253.191 102.951 256.422C106.562 259.653 106.87 265.206 103.64 268.817L76.1331 299.56L103.639 330.302C106.87 333.913 106.562 339.466 102.951 342.697Z",fill:"black"}))),(0,a.createElement)("defs",null,(0,a.createElement)("clipPath",{id:"clip0_708_21917"},(0,a.createElement)("rect",{width:"512",height:"512",fill:"white"})),(0,a.createElement)("clipPath",{id:"clip1_708_21917"},(0,a.createElement)("rect",{width:"65.0847",height:"91.1186",fill:"white",transform:"translate(115.085 345.119) rotate(-180)"}))))},edit:({attributes:e,setAttributes:t})=>{const{cardGap:n,cardGapTablet:l,cardGapMobile:i,layoutWidth:s,navigationPosition:c,slidesCount:d,slidesToShow:u,slidesToShowTablet:g,slidesToShowMobile:p,title:v,titleTag:h,showNavigation:_}=e,b=R(u,g,p),E=R(n,l,i),C=(0,r.useBlockProps)({className:o()("lms-instructors-carousel-container",s,`instructors-visible--${b}`),style:y("instructors-carousel",e,sa)}),f=(0,r.useInnerBlocksProps)({},{template:oa,templateLock:"all"}),{ref:T,isDisabled:w,nextSlide:M,prevSlide:x}=((e,t,n,a)=>{const[l,i]=(0,m.useState)(0),r=(0,m.useRef)(null),s=e=>{const a=r.current.querySelector(".lms-instructor-classic__list"),s=Array.from(a.children),o=s[0].offsetWidth+n,m="next"===e?l+1:l-1,c=-m*o;a.style.transform=`translateX(${c}px)`,s.forEach(((e,n)=>{e.classList.remove("slide-visible"),n>=m&&n<m+t&&e.classList.add("slide-visible")})),i(m)},o=(0,m.useMemo)((()=>({isDisabledPrev:0===l,isDisabledNext:l===e-t})),[e,l,t]);return{ref:r,isDisabled:o,nextSlide:()=>{s("next")},prevSlide:()=>{s("prev")}}})(d,b,E),{instructorOptions:B}=((e=!0)=>{const[t,n]=(0,m.useState)([]),{setIsFetching:a,setError:l,isFetching:i,error:r}=(()=>{const[e,t]=(0,m.useState)(!0),[n,a]=(0,m.useState)("");return{isFetching:e,setIsFetching:t,error:n,setError:a}})();return(0,m.useEffect)((()=>{a(!0),(async e=>{let t="stm_lms_instructor";e&&(t+=",administrator");try{return await S()({path:`masterstudy-lms/v2/users?roles=${t}&context=edit`})}catch(e){throw new Error(e)}})(e).then((e=>{n(e.map((e=>({label:e.name,value:e.id}))))})).catch((e=>{l(e.message)})).finally((()=>{a(!1)}))}),[]),{instructorOptions:t,isFetching:i,error:r}})(!1);return(0,a.createElement)(a.Fragment,null,(0,a.createElement)(ua,{attributes:e,setAttributes:t,instructorOptions:B}),(0,a.createElement)("div",{...C},(0,a.createElement)(ma,{titleTag:h,value:v,onChange:e=>t({title:e}),navigationPosition:c,nextSlide:M,prevSlide:x,disabledNext:w.isDisabledNext,disabledPrev:w.isDisabledPrev,showNavigation:_}),(0,a.createElement)("div",{...f,ref:T}),(0,a.createElement)(O,{condition:_},(0,a.createElement)(qn,{position:c,isVisible:N.includes(c),nextSlide:M,prevSlide:x,disabledNext:w.isDisabledNext,disabledPrev:w.isDisabledPrev,containerClassName:"lms-instructors-carousel-navigations",navItemClassName:"lms-instructors-carousel-navigations_item",disabledNavItemClassName:"lms-instructors-carousel-navigations_item_disabled"}))))},save:({attributes:e})=>{const{autoplay:t,cardGap:n,cardGapTablet:l,cardGapMobile:i,layoutWidth:s,loop:m,navigationPosition:c,slidesToShow:d,slidesToShowTablet:u,slidesToShowMobile:g,title:v,titleTag:h,showNavigation:_}=e,b=null!=n?n:0,E=l||b,C=i||E,T=r.useBlockProps.save({className:`lms-instructors-carousel-container ${s}`,style:y("instructors-carousel",e,sa)}),w=r.useInnerBlocksProps.save({});return(0,a.createElement)("div",{...T,"data-gap":b,"data-gap-tablet":E,"data-gap-mobile":C,"data-slides":d,"data-slides-tablet":u,"data-slides-mobile":g,"data-loop":m,"data-auto-play":t},(0,a.createElement)("div",{className:o()("lms-instructors-carousel-header",{"top-center":c===p.TOP_CENTER,"space-between":[p.TOP_RIGHT,p.TOP_lEFT].includes(c)})},(0,a.createElement)(r.RichText.Content,{className:o()("lms-instructors-carousel-title","wp-block-heading",{"top-left":c===p.TOP_RIGHT,"top-right":c===p.TOP_lEFT}),tagName:h,value:v}),(0,a.createElement)(O,{condition:_&&f.includes(c)},(0,a.createElement)("div",{className:o()("lms-instructors-carousel-navigations",{"bottom-left":c===p.BOTTOM_lEFT,"bottom-right":c===p.BOTTOM_RIGHT,"bottom-center":c===p.BOTTOM_CENTER,"top-left":c===p.TOP_lEFT,"top-right":c===p.TOP_RIGHT})},(0,a.createElement)("div",{className:"lms-instructors-carousel-navigations_item instructor-swiper-button-prev"},(0,a.createElement)(Kn,{type:Xn.LEFT})),(0,a.createElement)("div",{className:"lms-instructors-carousel-navigations_item instructor-swiper-button-next"},(0,a.createElement)(Kn,{type:Xn.RIGHT}))))),(0,a.createElement)("div",{...w}),(0,a.createElement)(O,{condition:_&&N.includes(c)},(0,a.createElement)("div",{className:o()("lms-instructors-carousel-navigations",{"bottom-left":c===p.BOTTOM_lEFT,"bottom-right":c===p.BOTTOM_RIGHT,"bottom-center":c===p.BOTTOM_CENTER,"top-left":c===p.TOP_lEFT,"top-right":c===p.TOP_RIGHT})},(0,a.createElement)("div",{className:"lms-instructors-carousel-navigations_item instructor-swiper-button-prev"},(0,a.createElement)(Kn,{type:Xn.LEFT})),(0,a.createElement)("div",{className:"lms-instructors-carousel-navigations_item instructor-swiper-button-next"},(0,a.createElement)(Kn,{type:Xn.RIGHT})))))}})},6942:(e,t)=>{var n;!function(){"use strict";var a={}.hasOwnProperty;function l(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=r(e,i(n)))}return e}function i(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return l.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)a.call(e,n)&&e[n]&&(t=r(t,n));return t}function r(e,t){return t?e?e+" "+t:e+t:e}e.exports?(l.default=l,e.exports=l):void 0===(n=function(){return l}.apply(t,[]))||(e.exports=n)}()}},n={};function a(e){var l=n[e];if(void 0!==l)return l.exports;var i=n[e]={exports:{}};return t[e](i,i.exports,a),i.exports}a.m=t,e=[],a.O=(t,n,l,i)=>{if(!n){var r=1/0;for(c=0;c<e.length;c++){for(var[n,l,i]=e[c],s=!0,o=0;o<n.length;o++)(!1&i||r>=i)&&Object.keys(a.O).every((e=>a.O[e](n[o])))?n.splice(o--,1):(s=!1,i<r&&(r=i));if(s){e.splice(c--,1);var m=l();void 0!==m&&(t=m)}}return t}i=i||0;for(var c=e.length;c>0&&e[c-1][2]>i;c--)e[c]=e[c-1];e[c]=[n,l,i]},a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={7573:0,553:0};a.O.j=t=>0===e[t];var t=(t,n)=>{var l,i,[r,s,o]=n,m=0;if(r.some((t=>0!==e[t]))){for(l in s)a.o(s,l)&&(a.m[l]=s[l]);if(o)var c=o(a)}for(t&&t(n);m<r.length;m++)i=r[m],a.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return a.O(c)},n=globalThis.webpackChunkmasterstudy_lms_learning_management_system=globalThis.webpackChunkmasterstudy_lms_learning_management_system||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})();var l=a.O(void 0,[553],(()=>a(5363)));l=a.O(l)})();