(()=>{var e,t={4714:(e,t,n)=>{"use strict";const a=window.React,l=window.wp.i18n,r=window.wp.blocks,s=window.wp.blockEditor,o=[["core/group",{className:"lms-instructors-group-header"},[["core/heading",{textAlign:"center",style:{typography:{fontSize:"48px"},color:{text:"#001931"},spacing:{margin:{top:"20px",bottom:"10px"}}},content:l.__("Instructors Grid","masterstudy-lms-learning-management-system"),placeholder:l.__("Instructors Grid Title","masterstudy-lms-learning-management-system")}]]],["core/group",{className:"lms-instructors-group-presets"},[["masterstudy/instructors-preset",{},[["masterstudy/instructors-preset-classic"]]]]],["core/group",{className:"lms-instructors-group-load-more"},[["masterstudy/courses-load-more"]]]];let i=function(e){return e.ALL="all",e.SOME="some",e}({}),m=function(e){return e.NORMAL="Normal",e.HOVER="Hover",e.ACTIVE="Active",e.FOCUS="Focus",e}({}),c=function(e){return e.DESKTOP="Desktop",e.TABLET="Tablet",e.MOBILE="Mobile",e}({}),d=function(e){return e.TOP_lEFT="top-left",e.TOP_CENTER="top-center",e.TOP_RIGHT="top-right",e.BOTTOM_lEFT="bottom-left",e.BOTTOM_CENTER="bottom-center",e.BOTTOM_RIGHT="bottom-right",e}({});const u=window.wp.data,p=()=>(0,u.useSelect)((e=>e("core/editor").getDeviceType()||e("core/edit-site")?.__experimentalGetPreviewDeviceType()||e("core/edit-post")?.__experimentalGetPreviewDeviceType()||e("masterstudy/store")?.getDeviceType()),[])||"Desktop",g=(l.__("Small","masterstudy-lms-learning-management-system"),l.__("Normal","masterstudy-lms-learning-management-system"),l.__("Large","masterstudy-lms-learning-management-system"),l.__("Extra Large","masterstudy-lms-learning-management-system"),"wp-block-masterstudy-settings__"),v={top:"",right:"",bottom:"",left:""},h=(d.TOP_lEFT,d.TOP_CENTER,d.TOP_RIGHT,d.BOTTOM_lEFT,d.BOTTOM_CENTER,d.BOTTOM_RIGHT,[{label:l.__("Newest","masterstudy-lms-learning-management-system"),value:"date_high"},{label:l.__("Oldest","masterstudy-lms-learning-management-system"),value:"date_low"},{label:l.__("Overall rating","masterstudy-lms-learning-management-system"),value:"rating"},{label:l.__("Popular","masterstudy-lms-learning-management-system"),value:"popular"},{label:l.__("Price low","masterstudy-lms-learning-management-system"),value:"price_low"},{label:l.__("Price high","masterstudy-lms-learning-management-system"),value:"price_high"}]),y=(e,t)=>{if(typeof e!=typeof t)return!1;if(Number.isNaN(e)&&Number.isNaN(t))return!0;if("object"!=typeof e||null===e||null===t)return e===t;if(Object.keys(e).length!==Object.keys(t).length)return!1;if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;const n=e.slice().sort(),a=t.slice().sort();return n.every(((e,t)=>y(e,a[t])))}for(const n of Object.keys(e))if(!y(e[n],t[n]))return!1;return!0},_=["",null,void 0,"null","undefined"],b=[".jpg",".jpeg",".png",".gif"],E=e=>_.includes(e),C=(e,t,n="")=>{const a=e[t];return"object"==typeof a&&null!==a?((e,t)=>{return n=e,Object.values(n).every((e=>_.includes(e)))?null:((e,t="")=>{const n=Object.entries(e).reduce(((e,[n,a])=>(e[n]=(a||"0")+t,e)),{});return`${n.top} ${n.right} ${n.bottom} ${n.left}`})(e,t);var n})(a,n):((e,t)=>E(e)?e:(e=>{if("string"!=typeof e)return!1;const t=e.slice(e.lastIndexOf("."));return b.includes(t)||e.startsWith("blob")})(e)?`url('${e}')`:String(e+t))(a,n)},f=(e,t,n)=>{const a={};return n.forEach((({isAdaptive:n,hasHover:l,unit:r},s)=>{if(t.hasOwnProperty(s)){const{unitMeasureDesktop:i,unitMeasureTablet:c,unitMeasureMobile:d}=((e,t)=>{var n;return{unitMeasureDesktop:null!==(n=e[t])&&void 0!==n?n:"",unitMeasureTablet:t?e[t+"Tablet"]:"",unitMeasureMobile:t?e[t+"Mobile"]:""}})(t,r);if(n&&l){const{desktopHoverPropertyName:n,mobileHoverPropertyName:l,tabletHoverPropertyName:r}=(e=>{const t=e+m.HOVER;return{desktopHoverPropertyName:t,tabletHoverPropertyName:t+"Tablet",mobileHoverPropertyName:t+"Mobile"}})(s),o=C(t,n,i);E(o)||(a[`--lms-${e}-${n}`]=o);const u=C(t,r,c);E(u)||(a[`--lms-${e}-${r}`]=u);const p=C(t,l,d);E(p)||(a[`--lms-${e}-${l}`]=p)}if(l){const n=s+m.HOVER,l=C(t,n,i);E(l)||(a[`--lms-${e}-${n}`]=l)}if(n){const{desktopPropertyName:n,mobilePropertyName:l,tabletPropertyName:r}={desktopPropertyName:o=s,tabletPropertyName:o+"Tablet",mobilePropertyName:o+"Mobile"},m=C(t,n,i);E(m)||(a[`--lms-${e}-${n}`]=m);const u=C(t,r,c);E(u)||(a[`--lms-${e}-${r}`]=u);const p=C(t,l,d);E(p)||(a[`--lms-${e}-${l}`]=p)}const u=C(t,s,i);E(u)||(a[`--lms-${e}-${s}`]=u)}var o})),a};function N(e){return Array.isArray(e)?e.map((e=>g+e)):g+e}const w=window.wp.element,k=(e=!1)=>{const[t,n]=(0,w.useState)(e),a=(0,w.useCallback)((()=>{n(!0)}),[]);return{isOpen:t,onClose:(0,w.useCallback)((()=>{n(!1)}),[]),onOpen:a,onToggle:(0,w.useCallback)((()=>{n((e=>!e))}),[])}},O=(0,w.createContext)(null),T=({children:e,...t})=>(0,a.createElement)(O.Provider,{value:{...t}},e),x=()=>{const e=(0,w.useContext)(O);if(!e)throw new Error("No settings context provided");return e},A=(e="")=>{const{attributes:t,setAttributes:n,onResetByFieldName:a,changedFieldsByName:l}=x();return{value:t[e],onChange:t=>n({[e]:t}),onReset:a.get(e),isChanged:l.get(e)}},M=(e,t=!1,n=!1)=>{const{hoverName:a,onChangeHoverName:l}=(()=>{const[e,t]=(0,w.useState)(m.NORMAL);return{hoverName:e,onChangeHoverName:(0,w.useCallback)((e=>{t(e)}),[])}})(),r=p();return{fieldName:(0,w.useMemo)((()=>{const l=a===m.HOVER?a:"",s=r===c.DESKTOP?"":r;return n&&t?e+l+s:n&&!t?e+l:t&&!n?e+s:e}),[e,n,t,a,r]),hoverName:a,onChangeHoverName:l}},S=(e,t=!1,n="Normal")=>{const a=p(),l=(0,w.useMemo)((()=>{const l=n===m.NORMAL?"":n,r=a===c.DESKTOP?"":a;return l&&t?e+l+r:l&&!t?e+l:t&&!l?e+r:e}),[e,t,n,a]),{value:r,isChanged:s,onReset:o}=A(l);return{fieldName:l,value:r,isChanged:s,onReset:o}},R=(e=[],t=i.ALL)=>{const{attributes:n}=x();return!e.length||(t===i.ALL?e.every((({name:e,value:t})=>{const a=n[e];return Array.isArray(t)?Array.isArray(a)?y(t,a):t.includes(a):t===a})):t!==i.SOME||e.some((({name:e,value:t})=>{const a=n[e];return Array.isArray(t)?Array.isArray(a)?y(t,a):t.includes(a):t===a})))},B=e=>{const t=(0,w.useRef)(null);return(0,w.useEffect)((()=>{const n=n=>{t.current&&!t.current.contains(n.target)&&e()};return document.addEventListener("click",n),()=>{document.removeEventListener("click",n)}}),[t,e]),t},D=window.wp.components;var P=n(6942),I=n.n(P);const L=({condition:e,fallback:t=null,children:n})=>(0,a.createElement)(a.Fragment,null,e?n:t),H=e=>(0,a.createElement)(D.SVG,{width:"16",height:"16",viewBox:"0 0 12 13",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(D.Path,{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})),F=e=>(0,a.createElement)(D.SVG,{width:"16",height:"16",viewBox:"0 0 14 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(D.G,{"clip-path":"url(#clip0_1068_38993)"},(0,a.createElement)(D.Path,{d:"M11.1973 8.60005L8.74967 8.11005V5.67171C8.74967 5.517 8.68822 5.36863 8.57882 5.25923C8.46942 5.14984 8.32105 5.08838 8.16634 5.08838H6.99967C6.84496 5.08838 6.69659 5.14984 6.5872 5.25923C6.4778 5.36863 6.41634 5.517 6.41634 5.67171V8.58838H5.24967C5.15021 8.58844 5.05241 8.61391 4.96555 8.66238C4.87869 8.71084 4.80565 8.78068 4.75336 8.86529C4.70106 8.9499 4.67125 9.04646 4.66674 9.14582C4.66223 9.24518 4.68317 9.34405 4.72759 9.43305L6.47759 12.933C6.57676 13.1302 6.77859 13.255 6.99967 13.255H11.083C11.2377 13.255 11.3861 13.1936 11.4955 13.0842C11.6049 12.9748 11.6663 12.8264 11.6663 12.6717V9.17171C11.6665 9.03685 11.6198 8.90612 11.5343 8.80186C11.4487 8.69759 11.3296 8.62626 11.1973 8.60005Z"}),(0,a.createElement)(D.Path,{d:"M10.4997 1.58838H3.49967C2.85626 1.58838 2.33301 2.11221 2.33301 2.75505V6.83838C2.33301 7.4818 2.85626 8.00505 3.49967 8.00505H5.24967V6.83838H3.49967V2.75505H10.4997V6.83838H11.6663V2.75505C11.6663 2.11221 11.1431 1.58838 10.4997 1.58838Z"})),(0,a.createElement)("defs",null,(0,a.createElement)("clipPath",{id:"clip0_1068_38993"},(0,a.createElement)(D.Rect,{width:"14",height:"14",transform:"translate(0 0.421875)"})))),V=[{value:m.NORMAL,label:l.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(H,{onClick:e})},{value:m.HOVER,label:l.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(F,{onClick:e})},{value:m.ACTIVE,label:l.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(F,{onClick:e})},{value:m.FOCUS,label:l.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(F,{onClick:e})}],U={[m.NORMAL]:{icon:(0,a.createElement)(H,null),label:l.__("Normal State","masterstudy-lms-learning-management-system")},[m.HOVER]:{icon:(0,a.createElement)(F,null),label:l.__("Hovered State","masterstudy-lms-learning-management-system")},[m.ACTIVE]:{icon:(0,a.createElement)(F,null),label:l.__("Active State","masterstudy-lms-learning-management-system")},[m.FOCUS]:{icon:(0,a.createElement)(F,null),label:l.__("Focus State","masterstudy-lms-learning-management-system")}},j=(e,t)=>{let n=[];return n=e.length?V.filter((t=>e.includes(t.value))):V,n=n.filter((e=>e.value!==t)),{ICONS_MAP:U,options:n}},[z,G,$,W,Z]=N(["hover-state","hover-state__selected","hover-state__selected__opened-menu","hover-state__menu","hover-state__menu__item"]),K=({stateOptions:e,currentState:t,onSelect:n})=>{const{isOpen:l,onOpen:r,onClose:s}=k(),o=B(s),{ICONS_MAP:i,options:m}=j(e,t);return(0,a.createElement)("div",{className:z,ref:o},(0,a.createElement)("div",{className:I()([G],{[$]:l}),onClick:r,title:i[t]?.label},i[t]?.icon),(0,a.createElement)(L,{condition:l},(0,a.createElement)("div",{className:W},m.map((({value:e,icon:t,label:l})=>(0,a.createElement)("div",{key:e,className:Z,title:l},t((()=>n(e)))))))))},Y=N("color-indicator"),q=(0,w.memo)((({color:e,onChange:t})=>(0,a.createElement)("div",{className:Y},(0,a.createElement)(s.PanelColorSettings,{enableAlpha:!0,disableCustomColors:!1,__experimentalHasMultipleOrigins:!0,__experimentalIsRenderedInSidebar:!0,colorSettings:[{label:"",value:e,onChange:t}]}))));var X;function Q(){return Q=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Q.apply(null,arguments)}var J,ee,te=function(e){return a.createElement("svg",Q({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 12 13"},e),X||(X=a.createElement("path",{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})))};function ne(){return ne=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},ne.apply(null,arguments)}var ae=function(e){return a.createElement("svg",ne({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 14 15"},e),J||(J=a.createElement("g",{clipPath:"url(#state-hover_svg__a)"},a.createElement("path",{d:"M11.197 8.6 8.75 8.11V5.672a.583.583 0 0 0-.584-.584H7a.583.583 0 0 0-.584.584v2.916H5.25a.584.584 0 0 0-.522.845l1.75 3.5c.099.197.3.322.522.322h4.083a.583.583 0 0 0 .583-.583v-3.5a.58.58 0 0 0-.469-.572"}),a.createElement("path",{d:"M10.5 1.588h-7c-.644 0-1.167.524-1.167 1.167v4.083c0 .644.523 1.167 1.167 1.167h1.75V6.838H3.5V2.755h7v4.083h1.166V2.755c0-.643-.523-1.167-1.166-1.167"}))),ee||(ee=a.createElement("defs",null,a.createElement("clipPath",{id:"state-hover_svg__a"},a.createElement("path",{d:"M0 .422h14v14H0z"})))))};const le=[{value:m.NORMAL,label:l.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(te,{onClick:e})},{value:m.HOVER,label:l.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(ae,{onClick:e})}],re={[m.NORMAL]:{icon:(0,a.createElement)(te,null),label:l.__("Normal State","masterstudy-lms-learning-management-system")},[m.HOVER]:{icon:(0,a.createElement)(ae,null),label:l.__("Hovered State","masterstudy-lms-learning-management-system")}},se=N("hover-state"),oe=N("hover-state__selected"),ie=N("hover-state__selected__opened-menu"),me=N("has-changes"),ce=N("hover-state__menu"),de=N("hover-state__menu__item"),ue=(0,w.memo)((e=>{const{hoverName:t,onChangeHoverName:n,fieldName:l}=e,{changedFieldsByName:r}=x(),s=r.get(l),{isOpen:o,onOpen:i,onClose:m}=k(),c=B(m),{ICONS_MAP:d,options:u}=(e=>{const t=(0,w.useMemo)((()=>le.filter((t=>t.value!==e))),[e]);return{ICONS_MAP:re,options:t}})(t),p=(0,w.useCallback)((e=>{n(e),m()}),[n,m]);return(0,a.createElement)("div",{className:se,ref:c},(0,a.createElement)("div",{className:I()([oe],{[ie]:o,[me]:s}),onClick:i,title:d[t]?.label},d[t]?.icon),(0,a.createElement)(L,{condition:o},(0,a.createElement)("div",{className:ce},u.map((({value:e,icon:t,label:n})=>(0,a.createElement)("div",{key:e,className:de,title:n},t((()=>p(e)))))))))})),pe={Desktop:{icon:"desktop",label:l.__("Desktop","masterstudy-lms-learning-management-system")},Tablet:{icon:"tablet",label:l.__("Tablet","masterstudy-lms-learning-management-system")},Mobile:{icon:"smartphone",label:l.__("Mobile","masterstudy-lms-learning-management-system")}},ge=[{value:c.DESKTOP,icon:"desktop",label:l.__("Desktop","masterstudy-lms-learning-management-system")},{value:c.TABLET,icon:"tablet",label:l.__("Tablet","masterstudy-lms-learning-management-system")},{value:c.MOBILE,icon:"smartphone",label:l.__("Mobile","masterstudy-lms-learning-management-system")}],ve=N("device-picker"),he=N("device-picker__selected"),ye=N("device-picker__selected__opened-menu"),_e=N("device-picker__menu"),be=N("device-picker__menu__item"),Ee=()=>{const{isOpen:e,onOpen:t,onClose:n}=k(),{value:l,onChange:r}=(e=>{const t=p(),n=(0,u.useDispatch)();return{value:(0,w.useMemo)((()=>pe[t]),[t]),onChange:t=>{n("core/edit-site")&&n("core/edit-site").__experimentalSetPreviewDeviceType?n("core/edit-site").__experimentalSetPreviewDeviceType(t):n("core/edit-post")&&n("core/edit-post").__experimentalSetPreviewDeviceType?n("core/edit-post").__experimentalSetPreviewDeviceType(t):n("masterstudy/store").setDeviceType(t),e()}}})(n),s=(e=>(0,w.useMemo)((()=>ge.filter((t=>t.icon!==e))),[e]))(l.icon),o=B(n);return(0,a.createElement)("div",{className:ve,ref:o},(0,a.createElement)(D.Dashicon,{className:I()([he],{[ye]:e}),icon:l.icon,size:16,onClick:t,title:l.label}),(0,a.createElement)(L,{condition:e},(0,a.createElement)("div",{className:_e},s.map((e=>(0,a.createElement)(D.Dashicon,{key:e.value,icon:e.icon,size:16,onClick:()=>r(e.value),className:be,title:e.label}))))))},Ce=N("reset-button"),fe=({onReset:e})=>(0,a.createElement)(D.Dashicon,{icon:"undo",onClick:e,className:Ce,size:16}),Ne=[{label:"PX",value:"px"},{label:"%",value:"%"},{label:"EM",value:"em"},{label:"REM",value:"rem"},{label:"VW",value:"vw"},{label:"VH",value:"vh"}],we=N("unit"),ke=N("unit__single"),Oe=N("unit__list"),Te=({name:e,isAdaptive:t})=>{const{isOpen:n,onOpen:l,onClose:r}=k(),{fieldName:s}=M(e,t),{value:o,onChange:i}=A(s),m=B(r);return(0,a.createElement)("div",{className:we,ref:m},(0,a.createElement)("div",{className:ke,onClick:l},o),(0,a.createElement)(L,{condition:n},(0,a.createElement)("div",{className:Oe},Ne.map((({value:e,label:t})=>(0,a.createElement)("div",{key:e,onClick:()=>(i(e),void r())},t))))))},xe=N("popover-modal"),Ae=N("popover-modal__close dashicon dashicons dashicons-no-alt"),Me=e=>{const{isOpen:t,onClose:n,popoverContent:l}=e;return(0,a.createElement)(L,{condition:t},(0,a.createElement)(D.Popover,{position:"middle left",onClose:n,className:xe},l,(0,a.createElement)("span",{onClick:n,className:Ae})))},Se=N("setting-label"),Re=N("setting-label__content"),Be=e=>{const{label:t,isChanged:n=!1,onReset:l,showDevicePicker:r=!0,HoverStateControl:s=null,unitName:o,popoverContent:i=null,dependencies:m}=e,{isOpen:c,onClose:d,onToggle:u}=k();return R(m)?(0,a.createElement)("div",{className:Se},(0,a.createElement)("div",{className:Re},(0,a.createElement)("div",{onClick:u},t),(0,a.createElement)(L,{condition:Boolean(i)},(0,a.createElement)(Me,{isOpen:c,onClose:d,popoverContent:i})),(0,a.createElement)(L,{condition:r},(0,a.createElement)(Ee,null)),(0,a.createElement)(L,{condition:Boolean(s)},s)),(0,a.createElement)(L,{condition:Boolean(o)},(0,a.createElement)(Te,{name:o,isAdaptive:r})),(0,a.createElement)(L,{condition:n},(0,a.createElement)(fe,{onReset:l}))):null},De=N("suffix"),Pe=()=>(0,a.createElement)("div",{className:De},(0,a.createElement)(D.Dashicon,{icon:"color-picker",size:16})),Ie=N("color-picker"),Le=e=>{const{name:t,label:n,placeholder:l,dependencyMode:r,dependencies:s,isAdaptive:o=!1,hasHover:i=!1}=e,{fieldName:m,hoverName:c,onChangeHoverName:d}=M(t,o,i),{value:u,isChanged:p,onChange:g,onReset:v}=A(m);return R(s,r)?(0,a.createElement)("div",{className:Ie},(0,a.createElement)(L,{condition:Boolean(n)},(0,a.createElement)(Be,{label:n,isChanged:p,onReset:v,showDevicePicker:o,HoverStateControl:(0,a.createElement)(L,{condition:i},(0,a.createElement)(ue,{hoverName:c,onChangeHoverName:d,fieldName:m}))})),(0,a.createElement)(D.__experimentalInputControl,{prefix:(0,a.createElement)(q,{color:u,onChange:g}),suffix:(0,a.createElement)(Pe,null),onChange:g,value:u,placeholder:l})):null},He=N("number-steppers"),Fe=N("indent-steppers"),Ve=N("indent-stepper-plus"),Ue=N("indent-stepper-minus"),je=({onIncrement:e,onDecrement:t,withArrows:n=!1})=>n?(0,a.createElement)("span",{className:Fe},(0,a.createElement)("button",{onClick:e,className:Ve}),(0,a.createElement)("button",{onClick:t,className:Ue})):(0,a.createElement)("span",{className:He},(0,a.createElement)("button",{onClick:e},"+"),(0,a.createElement)("button",{onClick:t},"-")),[ze,Ge]=N(["indents","indents-control"]),$e=({name:e,label:t,unitName:n,popoverContent:r,dependencyMode:s,dependencies:o,isAdaptive:i=!1})=>{const{fieldName:m}=M(e,i),{value:c,onResetSegmentedBox:d,hasChanges:u,handleInputIncrement:p,handleInputDecrement:g,updateDirectionsValues:v,lastFieldValue:h}=((e,t)=>{const{value:n,isChanged:a,onChange:l,onReset:r}=A(e),{onResetByFieldName:s,changedFieldsByName:o}=x(),i=a||o.get(t),m=e=>{l({...n,...e})},[c,d]=(0,w.useState)(!1);return{value:n,onResetSegmentedBox:()=>{r(),s.get(t)()},hasChanges:i,handleInputIncrement:e=>Number(n[e])+1,handleInputDecrement:e=>Number(n[e])-1,updateDirectionsValues:(e,t,n)=>{e?(d(!1),m({top:n,right:n,bottom:n,left:n})):(d(n),m({[t]:n}))},lastFieldValue:c}})(m,n),[y,_]=(0,w.useState)((()=>{const{left:e,right:t,top:n,bottom:a}=c;return""!==e&&e===t&&t===n&&n===a})),b=e=>{const[t,n]=Object.entries(e)[0];v(y,t,n)},E=e=>()=>{const t=p(e);v(y,e,String(t))},C=e=>()=>{const t=g(e);v(y,e,String(t))};return R(o,s)?(0,a.createElement)("div",{className:ze},(0,a.createElement)(L,{condition:Boolean(t)},(0,a.createElement)(Be,{label:null!=t?t:"",isChanged:u,onReset:d,unitName:n,popoverContent:r,showDevicePicker:i})),(0,a.createElement)("div",{className:`${Ge} ${y?"active":""}`},(0,a.createElement)("div",null,(0,a.createElement)(D.__experimentalNumberControl,{value:c.top,onChange:e=>{b({top:e})},spinControls:"none",suffix:(0,a.createElement)(je,{onIncrement:E("top"),onDecrement:C("top"),withArrows:!0})}),(0,a.createElement)("div",null,l.__("Top","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(D.__experimentalNumberControl,{value:c.right,onChange:e=>{b({right:e})},spinControls:"none",suffix:(0,a.createElement)(je,{onIncrement:E("right"),onDecrement:C("right"),withArrows:!0})}),(0,a.createElement)("div",null,l.__("Right","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(D.__experimentalNumberControl,{value:c.bottom,onChange:e=>{b({bottom:e})},spinControls:"none",suffix:(0,a.createElement)(je,{onIncrement:E("bottom"),onDecrement:C("bottom"),withArrows:!0})}),(0,a.createElement)("div",null,l.__("Bottom","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(D.__experimentalNumberControl,{value:c.left,onChange:e=>{b({left:e})},spinControls:"none",suffix:(0,a.createElement)(je,{onIncrement:E("left"),onDecrement:C("left"),withArrows:!0})}),(0,a.createElement)("div",null,l.__("Left","masterstudy-lms-learning-management-system"))),(0,a.createElement)(D.Dashicon,{icon:"dashicons "+(y?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{y||!1===h||v(!0,"left",h),_((e=>!e))}}))):null},[We,Ze,Ke,Ye]=N(["toggle-group-wrapper","toggle-group","toggle-group__toggle","toggle-group__active-toggle"]),qe=e=>{const{name:t,options:n,label:l,isAdaptive:r=!1,dependencyMode:s,dependencies:o}=e,{fieldName:i}=M(t,r),{value:m,isChanged:c,onChange:d,onReset:u}=A(i);return R(o,s)?(0,a.createElement)("div",{className:We},(0,a.createElement)(L,{condition:Boolean(l)},(0,a.createElement)(Be,{label:l,isChanged:c,onReset:u,showDevicePicker:r})),(0,a.createElement)("div",{className:Ze},n.map((e=>(0,a.createElement)("div",{key:e.value,className:I()([Ke],{[Ye]:e.value===m}),onClick:()=>d(e.value)},e.label))))):null},[Xe,Qe,Je,et]=N(["border-control","border-control-solid","border-control-dashed","border-control-dotted"]),tt=e=>{const{label:t,borderStyleName:n,borderColorName:r,borderWidthName:s,dependencyMode:o,dependencies:i,isAdaptive:m=!1,hasHover:c=!1}=e,[d,u]=(0,w.useState)("Normal"),{fieldName:p,value:g,isChanged:v,onReset:h}=S(n,m,d),{fieldName:y,isChanged:_,onReset:b}=S(r,m,d),{fieldName:E,isChanged:C,onReset:f}=S(s,m,d);if(!R(i,o))return null;const N=v||_||C;return(0,a.createElement)("div",{className:I()([Xe],{"has-reset-button":N})},(0,a.createElement)(Be,{label:t,isChanged:N,onReset:()=>{h(),b(),f()},showDevicePicker:m,HoverStateControl:(0,a.createElement)(L,{condition:c},(0,a.createElement)(K,{stateOptions:["Normal","Hover"],currentState:d,onSelect:u}))}),(0,a.createElement)(qe,{options:[{label:(0,a.createElement)("span",null,l.__("None","masterstudy-lms-learning-management-system")),value:"none"},{label:(0,a.createElement)("span",{className:Qe}),value:"solid"},{label:(0,a.createElement)("span",{className:Je},(0,a.createElement)("span",null)),value:"dashed"},{label:(0,a.createElement)("span",{className:et},(0,a.createElement)("span",null,(0,a.createElement)("span",null))),value:"dotted"}],name:p}),(0,a.createElement)(L,{condition:"none"!==g},(0,a.createElement)(Le,{name:y,placeholder:l.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)($e,{name:E})))},nt=N("border-radius"),at=N("border-radius-control"),lt=({name:e,label:t,unitName:n,popoverContent:l,dependencyMode:r,dependencies:s,isAdaptive:o=!1,hasHover:i=!1})=>{const{fieldName:m}=M(e,o,i),{value:c,onResetBorderRadius:d,hasChanges:u,handleInputIncrement:p,handleInputDecrement:g,updateDirectionsValues:v,lastFieldValue:h}=((e,t)=>{const[n,a]=(0,w.useState)(!1),{value:l,isChanged:r,onChange:s,onReset:o}=A(e),{onResetByFieldName:i,changedFieldsByName:m}=x(),c=r||m.get(t),d=e=>{s({...l,...e})};return{value:l,onResetBorderRadius:()=>{o(),i.get(t)()},hasChanges:c,handleInputIncrement:e=>Number(l[e])+1,handleInputDecrement:e=>Number(l[e])-1,updateDirectionsValues:(e,t,n)=>{e?(d({top:n,right:n,bottom:n,left:n}),a(!1)):(d({[t]:n}),a(n))},lastFieldValue:n}})(m,n),[y,_]=(0,w.useState)((()=>{const{left:e,right:t,top:n,bottom:a}=c;return""!==e&&e===t&&t===n&&n===a})),b=e=>{const[t,n]=Object.entries(e)[0];v(y,t,n)},E=e=>()=>{const t=p(e);v(y,e,String(t))},C=e=>()=>{const t=g(e);v(y,e,String(t))};return R(s,r)?(0,a.createElement)("div",{className:nt},(0,a.createElement)(Be,{label:t,isChanged:u,onReset:d,unitName:n,popoverContent:l,showDevicePicker:o}),(0,a.createElement)("div",{className:I()([at],{"has-reset-button":u,active:y})},(0,a.createElement)("div",{className:"number-control-top"},(0,a.createElement)(D.__experimentalNumberControl,{value:c.top,onChange:e=>{b({top:e})},spinControls:"none",suffix:(0,a.createElement)(je,{onIncrement:E("top"),onDecrement:C("top"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-right"},(0,a.createElement)(D.__experimentalNumberControl,{value:c.right,onChange:e=>{b({right:e})},spinControls:"none",suffix:(0,a.createElement)(je,{onIncrement:E("right"),onDecrement:C("right"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-left"},(0,a.createElement)(D.__experimentalNumberControl,{value:c.left,onChange:e=>{b({left:e})},spinControls:"none",suffix:(0,a.createElement)(je,{onIncrement:E("left"),onDecrement:C("left"),withArrows:!0})})),(0,a.createElement)("div",{className:"number-control-bottom"},(0,a.createElement)(D.__experimentalNumberControl,{value:c.bottom,onChange:e=>{b({bottom:e})},spinControls:"none",suffix:(0,a.createElement)(je,{onIncrement:E("bottom"),onDecrement:C("bottom"),withArrows:!0})})),(0,a.createElement)(D.Dashicon,{icon:"dashicons "+(y?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{y||!1===h||v(!0,"left",h),_((e=>!e))}}))):null},rt=(N("box-shadow-preset"),N("presets")),st=N("presets__item-wrapper"),ot=N("presets__item-wrapper__preset"),it=N("presets__item-wrapper__name"),mt=((0,w.memo)((e=>{const{presets:t,activePreset:n,onSelectPreset:l,PresetItem:r,detectIsActive:s,detectByIndex:o=!1}=e;return(0,a.createElement)("div",{className:rt},t.map((({name:e,...t},i)=>(0,a.createElement)("div",{key:i,className:I()([st],{active:s(n,o?i:t)}),onClick:()=>l(t)},(0,a.createElement)("div",{className:ot},(0,a.createElement)(r,{preset:t})),(0,a.createElement)("span",{className:it},e)))))})),N("range-control")),ct=e=>{const{name:t,label:n,min:l,max:r,unitName:s,dependencyMode:o,dependencies:i,isAdaptive:m=!1}=e,{fieldName:c}=M(t,m),{value:d,onChange:u,onResetNumberField:p,hasChanges:g}=((e,t)=>{const{value:n,isChanged:a,onChange:l,onReset:r}=A(e),{onResetByFieldName:s,changedFieldsByName:o}=x();return{value:n,onChange:l,onResetNumberField:()=>{r(),s.get(t)()},hasChanges:a||o.get(t)}})(c,s);return R(i,o)?(0,a.createElement)("div",{className:mt},(0,a.createElement)(L,{condition:Boolean(n)},(0,a.createElement)(Be,{label:n,isChanged:g,onReset:p,unitName:s,showDevicePicker:m})),(0,a.createElement)(D.RangeControl,{value:d,onChange:u,min:l,max:r})):null},dt=N("switch"),ut=e=>{const{name:t,label:n,dependencyMode:l,dependencies:r,isAdaptive:s=!1}=e,{fieldName:o}=M(t,s),{value:i,onChange:m}=A(o);return R(r,l)?(0,a.createElement)("div",{className:dt,"data-has-label":Boolean(n).toString()},(0,a.createElement)(D.ToggleControl,{label:n,checked:i,onChange:m}),(0,a.createElement)(L,{condition:s},(0,a.createElement)(Ee,null))):null},pt=(N("box-shadow-settings"),N("box-shadow-presets-title"),N("input-field"),N("input-field-control"),N("number-field")),gt=N("number-field-control"),vt=e=>{const{name:t,label:n,unitName:l,help:r,popoverContent:s,dependencyMode:o,dependencies:i,isAdaptive:m=!1}=e,{fieldName:c}=M(t,m),{value:d,onResetNumberField:u,hasChanges:p,handleIncrement:g,handleDecrement:v,handleInputChange:h}=((e,t)=>{const{value:n,isChanged:a,onChange:l,onReset:r}=A(e),{onResetByFieldName:s,changedFieldsByName:o}=x(),i=a||o.get(t);return{value:n,onResetNumberField:()=>{r(),s.get(t)()},hasChanges:i,handleIncrement:()=>{l(n+1)},handleDecrement:()=>{l(n-1)},handleInputChange:e=>{const t=Number(""===e?0:e);l(t)}}})(c,l);return R(i,o)?(0,a.createElement)("div",{className:pt},(0,a.createElement)(Be,{label:n,isChanged:p,onReset:u,unitName:l,showDevicePicker:m,popoverContent:s}),(0,a.createElement)("div",{className:gt},(0,a.createElement)(D.__experimentalNumberControl,{value:d,onChange:h,spinControls:"none",suffix:(0,a.createElement)(je,{onIncrement:g,onDecrement:v})})),r&&(0,a.createElement)("small",null,r)):null},ht=({className:e})=>(0,a.createElement)("div",{className:e},l.__("No options","masterstudy-lms-learning-management-system")),yt=N("select__single-item"),_t=N("select__container"),bt=N("select__container__multi-item"),Et=({multiple:e,value:t,options:n,onChange:l})=>{const{singleValue:r,multipleValue:s}=((e,t,n)=>({singleValue:(0,w.useMemo)((()=>t?null:n.find((t=>t.value===e))?.label),[t,e,n]),multipleValue:(0,w.useMemo)((()=>t?e:null),[t,e])}))(t,e,n);return(0,a.createElement)(L,{condition:e,fallback:(0,a.createElement)("div",{className:yt},r)},(0,a.createElement)("div",{className:_t},s?.map((e=>{const t=n.find((t=>t.value===e));return t?(0,a.createElement)("div",{key:t.value,className:bt},(0,a.createElement)("div",null,t.label),(0,a.createElement)(D.Dashicon,{icon:"no-alt",onClick:()=>l(t.value),size:16})):null}))))},Ct=N("select"),ft=N("select__select-box"),Nt=N("select__placeholder"),wt=N("select__select-box-multiple"),kt=N("select__menu"),Ot=N("select__menu__options-container"),Tt=N("select__menu__item"),xt=e=>{const{options:t,multiple:n=!1,placeholder:l="Select",value:r,onSelect:s}=e,{isOpen:o,onToggle:i,onClose:m}=k(),c=B(m),d=((e,t,n)=>(0,w.useMemo)((()=>n&&Array.isArray(e)?t.filter((t=>!e.includes(t.value))):t.filter((t=>t.value!==e))),[e,t,n]))(r,t,n),u=((e,t,n,a)=>(0,w.useCallback)((l=>{if(t&&Array.isArray(e)){const t=e.includes(l)?e.filter((e=>e!==l)):[...e,l];n(t)}else n(l),a()}),[t,e,n,a]))(r,n,s,m),p=((e,t)=>(0,w.useMemo)((()=>t&&Array.isArray(e)?Boolean(e.length):Boolean(e)),[t,e]))(r,n),g=n&&Array.isArray(r)&&r?.length>0;return(0,a.createElement)("div",{className:Ct,ref:c},(0,a.createElement)("div",{className:I()([ft],{[wt]:g}),onClick:i},(0,a.createElement)(L,{condition:p,fallback:(0,a.createElement)("div",{className:Nt},l)},(0,a.createElement)(Et,{onChange:u,options:t,multiple:n,value:r})),(0,a.createElement)(D.Dashicon,{icon:o?"arrow-up-alt2":"arrow-down-alt2",size:16,style:{color:"#4D5E6F"}})),(0,a.createElement)(L,{condition:o},(0,a.createElement)("div",{className:kt},(0,a.createElement)(L,{condition:Boolean(d.length),fallback:(0,a.createElement)(ht,{className:Tt})},(0,a.createElement)("div",{className:Ot},d.map((e=>(0,a.createElement)("div",{key:e.value,onClick:()=>u(e.value),className:Tt},e.label))))))))},At=N("setting-select"),Mt=e=>{const{name:t,options:n,label:l,multiple:r=!1,placeholder:s,isAdaptive:o=!1,dependencyMode:i,dependencies:m}=e,{fieldName:c}=M(t,o),{value:d,isChanged:u,onChange:p,onReset:g}=A(c);return R(m,i)?(0,a.createElement)("div",{className:At},(0,a.createElement)(L,{condition:Boolean(l)},(0,a.createElement)(Be,{label:l,isChanged:u,onReset:g,showDevicePicker:o})),(0,a.createElement)(xt,{options:n,value:d,onSelect:p,multiple:r,placeholder:s})):null},St=N("row-select"),Rt=N("row-select__label"),Bt=N("row-select__control"),Dt=e=>{const{name:t,label:n,options:l,isAdaptive:r=!1}=e,{fieldName:s}=M(t,r),{isChanged:o,onReset:i}=A(s);return(0,a.createElement)("div",{className:St},(0,a.createElement)("div",{className:Rt},(0,a.createElement)("div",null,n),(0,a.createElement)(L,{condition:r},(0,a.createElement)(Ee,null))),(0,a.createElement)("div",{className:Bt},(0,a.createElement)(Mt,{name:t,options:l,isAdaptive:r}),(0,a.createElement)(L,{condition:o},(0,a.createElement)(fe,{onReset:i}))))},Pt=(N("typography-select"),N("typography-select-label"),N("typography"),N("file-upload"),N("file-upload__wrap"),N("file-upload__image"),N("file-upload__remove"),N("file-upload__replace"),(0,w.createContext)({activeTab:0,setActiveTab:()=>{}})),It=()=>{const e=(0,w.useContext)(Pt);if(!e)throw new Error("useTabs should be used inside Tabs");return e},Lt=({children:e})=>{const[t,n]=(0,w.useState)(0);return(0,a.createElement)(Pt.Provider,{value:{activeTab:t,setActiveTab:n}},(0,a.createElement)("div",{className:`active-tab-${t}`},e))},Ht=N("tab-list"),Ft=({children:e})=>(0,a.createElement)("div",{className:Ht},w.Children.map(e,((e,t)=>(0,w.cloneElement)(e,{index:t})))),Vt=N("tab"),Ut=N("tab-active"),jt=N("content"),zt=({index:e,title:t,icon:n})=>{const{activeTab:l,setActiveTab:r}=It();return(0,a.createElement)("div",{className:I()([Vt],{[Ut]:l===e}),onClick:()=>r(e)},(0,a.createElement)("div",{className:jt},(0,a.createElement)("div",null,n),(0,a.createElement)("div",null,t)))},Gt=({children:e})=>(0,a.createElement)("div",null,w.Children.map(e,((e,t)=>(0,w.cloneElement)(e,{index:t})))),$t=N("tab-panel"),Wt=({index:e,children:t})=>{const{activeTab:n}=It();return n===e?(0,a.createElement)("div",{className:$t},t):null},Zt=({generalTab:e,styleTab:t,advancedTab:n})=>(0,a.createElement)(Lt,null,(0,a.createElement)(Ft,null,(0,a.createElement)(zt,{title:l.__("General","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(D.Dashicon,{icon:"layout"})}),(0,a.createElement)(zt,{title:l.__("Style","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(D.Dashicon,{icon:"admin-appearance"})}),(0,a.createElement)(zt,{title:l.__("Advanced","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(D.Dashicon,{icon:"admin-settings"})})),(0,a.createElement)(Gt,null,(0,a.createElement)(Wt,null,e),(0,a.createElement)(Wt,null,t),(0,a.createElement)(Wt,null,n)));window.ReactDOM;"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement;function Kt(e){const t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function Yt(e){return"nodeType"in e}function qt(e){var t,n;return e?Kt(e)?e:Yt(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function Xt(e){const{Document:t}=qt(e);return e instanceof t}function Qt(e){return!Kt(e)&&e instanceof qt(e).HTMLElement}function Jt(e){return e instanceof qt(e).SVGElement}function en(e){return e?Kt(e)?e.document:Yt(e)?Xt(e)?e:Qt(e)||Jt(e)?e.ownerDocument:document:document:document}function tn(e){return function(t){for(var n=arguments.length,a=new Array(n>1?n-1:0),l=1;l<n;l++)a[l-1]=arguments[l];return a.reduce(((t,n)=>{const a=Object.entries(n);for(const[n,l]of a){const a=t[n];null!=a&&(t[n]=a+e*l)}return t}),{...t})}}const nn=tn(-1);function an(e){if(function(e){if(!e)return!1;const{TouchEvent:t}=qt(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return function(e){return"clientX"in e&&"clientY"in e}(e)?{x:e.clientX,y:e.clientY}:null}var ln;!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(ln||(ln={}));const rn=Object.freeze({x:0,y:0});var sn,on,mn,cn;!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(sn||(sn={}));class dn{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach((e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)}))},this.target=e}add(e,t,n){var a;null==(a=this.target)||a.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function un(e,t){const n=Math.abs(e.x),a=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+a**2)>t:"x"in t&&"y"in t?n>t.x&&a>t.y:"x"in t?n>t.x:"y"in t&&a>t.y}function pn(e){e.preventDefault()}function gn(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(on||(on={})),(cn=mn||(mn={})).Space="Space",cn.Down="ArrowDown",cn.Right="ArrowRight",cn.Left="ArrowLeft",cn.Up="ArrowUp",cn.Esc="Escape",cn.Enter="Enter";mn.Space,mn.Enter,mn.Esc,mn.Space,mn.Enter;function vn(e){return Boolean(e&&"distance"in e)}function hn(e){return Boolean(e&&"delay"in e)}class yn{constructor(e,t,n){var a;void 0===n&&(n=function(e){const{EventTarget:t}=qt(e);return e instanceof t?e:en(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;const{event:l}=e,{target:r}=l;this.props=e,this.events=t,this.document=en(r),this.documentListeners=new dn(this.document),this.listeners=new dn(n),this.windowListeners=new dn(qt(r)),this.initialCoordinates=null!=(a=an(l))?a:rn,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),this.windowListeners.add(on.Resize,this.handleCancel),this.windowListeners.add(on.DragStart,pn),this.windowListeners.add(on.VisibilityChange,this.handleCancel),this.windowListeners.add(on.ContextMenu,pn),this.documentListeners.add(on.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(hn(t))return void(this.timeoutId=setTimeout(this.handleStart,t.delay));if(vn(t))return}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handleStart(){const{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(on.Click,gn,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(on.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;const{activated:n,initialCoordinates:a,props:l}=this,{onMove:r,options:{activationConstraint:s}}=l;if(!a)return;const o=null!=(t=an(e))?t:rn,i=nn(a,o);if(!n&&s){if(vn(s)){if(null!=s.tolerance&&un(i,s.tolerance))return this.handleCancel();if(un(i,s.distance))return this.handleStart()}return hn(s)&&un(i,s.tolerance)?this.handleCancel():void 0}e.cancelable&&e.preventDefault(),r(o)}handleEnd(){const{onEnd:e}=this.props;this.detach(),e()}handleCancel(){const{onCancel:e}=this.props;this.detach(),e()}handleKeydown(e){e.code===mn.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}const bn={move:{name:"pointermove"},end:{name:"pointerup"}};(class extends yn{constructor(e){const{event:t}=e,n=en(t.target);super(e,bn,n)}}).activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;return!(!n.isPrimary||0!==n.button||(null==a||a({event:n}),0))}}];const En={move:{name:"mousemove"},end:{name:"mouseup"}};var Cn;!function(e){e[e.RightClick=2]="RightClick"}(Cn||(Cn={})),class extends yn{constructor(e){super(e,En,en(e.event.target))}}.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;return n.button!==Cn.RightClick&&(null==a||a({event:n}),!0)}}];const fn={move:{name:"touchmove"},end:{name:"touchend"}};var Nn,wn,kn,On,Tn;(class extends yn{constructor(e){super(e,fn)}static setup(){return window.addEventListener(fn.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(fn.move.name,e)};function e(){}}}).activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;const{touches:l}=n;return!(l.length>1||(null==a||a({event:n}),0))}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(Nn||(Nn={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(wn||(wn={})),sn.Backward,sn.Forward,sn.Backward,sn.Forward,function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(kn||(kn={})),function(e){e.Optimized="optimized"}(On||(On={})),kn.WhileDragging,On.Optimized,Map,function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(Tn||(Tn={})),mn.Down,mn.Right,mn.Up,mn.Left,l.__("Lectures","masterstudy-lms-learning-management-system"),l.__("Duration","masterstudy-lms-learning-management-system"),l.__("Views","masterstudy-lms-learning-management-system"),l.__("Level","masterstudy-lms-learning-management-system"),l.__("Members","masterstudy-lms-learning-management-system"),l.__("Empty","masterstudy-lms-learning-management-system"),N("sortable__item"),N("sortable__item__disabled"),N("sortable__item__content"),N("sortable__item__content__drag-item"),N("sortable__item__content__drag-item__disabled"),N("sortable__item__content__title"),N("sortable__item__control"),N("sortable__item__icon"),N("nested-sortable"),N("nested-sortable__item"),N("sortable");const xn=N("accordion"),An=N("accordion__header"),Mn=N("accordion__header-flex"),Sn=N("accordion__content"),Rn=N("accordion__icon"),Bn=N("accordion__title"),Dn=N("accordion__title-disabled"),Pn=N("accordion__indicator"),In=N("accordion__controls"),Ln=N("accordion__controls-disabled"),Hn=({title:e,children:t,accordionFields:n,switchName:l,visible:r=!0,isDefaultOpen:s=!1})=>{const{isOpen:o,onToggle:i,disabled:m,onReset:c,hasChanges:d,onClose:u}=((e,t,n)=>{var a;const{isOpen:l,onToggle:r,onClose:s}=k(t),{defaultValues:o,attributes:i,setAttributes:m}=x(),c=((e,t,n)=>{for(const a of n)if(!y(e[a],t[a]))return!0;return!1})(o,i,e);return{isOpen:l,onToggle:r,disabled:!(null===(a=i[n])||void 0===a||a),hasChanges:c,onReset:t=>{t.stopPropagation(),m(e.reduce(((e,t)=>(e[t]=o[t],e)),{}))},onClose:s}})(n,s,l);return((e,t)=>{const{attributes:n}=x(),a=!n[t];(0,w.useEffect)((()=>{a&&e()}),[a,e])})(u,l),r?(0,a.createElement)("div",{className:xn},(0,a.createElement)("div",{className:An},(0,a.createElement)("div",{className:Mn,onClick:m?null:i},(0,a.createElement)("div",{className:I()(Bn,{[Dn]:m,"with-switch":Boolean(l)})},(0,a.createElement)("div",null,e),(0,a.createElement)(L,{condition:d&&!m},(0,a.createElement)("div",{className:Pn}))),(0,a.createElement)("div",{className:I()(In,{[Ln]:m})},(0,a.createElement)(D.Dashicon,{icon:o?"arrow-up-alt2":"arrow-down-alt2",className:Rn,size:16}))),(0,a.createElement)(L,{condition:Boolean(l)},(0,a.createElement)(ut,{name:l})),(0,a.createElement)(L,{condition:d&&!m},(0,a.createElement)(fe,{onReset:c}))),o&&(0,a.createElement)("div",{className:Sn},t)):null};N("preset-picker"),N("preset-picker__label"),N("preset-picker__remove"),N("preset-picker__presets-list"),N("preset-picker__presets-list__item"),N("preset-picker__presets-list__item__preset"),N("preset-picker__presets-list__item__preset-active");let Fn=function(e){return e.ALL="all",e.QUANTITY="quantity",e}({});const Vn={instructorPerPage:Fn.ALL,quantity:8,instructorsPerRow:4,instructorsPerRowTablet:2,instructorsPerRowMobile:1,orderBy:"registered_date desc",instructors:[]},Un=Object.keys(Vn),jn={layoutMargin:v,layoutMarginTablet:v,layoutMarginMobile:v,layoutMarginUnit:"px",layoutMarginUnitTablet:"px",layoutMarginUnitMobile:"px",layoutPadding:{top:"100",right:"0",bottom:"100",left:"0"},layoutPaddingTablet:{top:"70",right:"0",bottom:"70",left:"0"},layoutPaddingMobile:{top:"50",right:"0",bottom:"50",left:"0"},layoutPaddingUnit:"px",layoutPaddingUnitTablet:"px",layoutPaddingUnitMobile:"px",cardGap:30,cardGapTablet:null,cardGapMobile:null,cardGapUnit:"px",cardGapUnitTablet:"px",cardGapUnitMobile:"px",layoutBackground:"#EEF1F7",layoutBorderStyle:"none",layoutBorderStyleTablet:"",layoutBorderStyleMobile:"",layoutBorderColor:"",layoutBorderColorTablet:"",layoutBorderColorMobile:"",layoutBorderWidth:v,layoutBorderWidthTablet:v,layoutBorderWidthMobile:v,layoutBorderWidthUnit:"px",layoutBorderWidthUnitTablet:"px",layoutBorderWidthUnitMobile:"px",layoutBorderRadius:v,layoutBorderRadiusTablet:v,layoutBorderRadiusMobile:v,layoutBorderRadiusUnit:"px",layoutBorderRadiusUnitTablet:"px",layoutBorderRadiusUnitMobile:"px",layoutWidth:"alignfull",layoutZIndex:null,layoutZIndexTablet:null,layoutZIndexMobile:null},zn=Object.keys(jn),Gn={...jn},$n={...Vn,...Gn},Wn=new Map([["layoutMargin",{unit:"layoutMarginUnit",isAdaptive:!0}],["layoutPadding",{unit:"layoutPaddingUnit",isAdaptive:!0}],["cardGap",{unit:"cardGapUnit",isAdaptive:!0}],["layoutBackground",{}],["layoutBorderStyle",{isAdaptive:!0}],["layoutBorderColor",{isAdaptive:!0}],["layoutBorderWidth",{unit:"layoutBorderWidthUnit",isAdaptive:!0}],["layoutBorderRadius",{unit:"layoutBorderRadiusUnit",isAdaptive:!0}],["layoutZIndex",{isAdaptive:!0}]]),Zn=({instructorOptions:e})=>{const t=[{label:l.__("Newest","masterstudy-lms-learning-management-system"),value:"registered_date desc"},{label:l.__("Oldest","masterstudy-lms-learning-management-system"),value:"registered_date asc"},{label:l.__("Rating","masterstudy-lms-learning-management-system"),value:"rating"}],{min:n,max:r}=((e,t=!1)=>{const n=p(),[a,l]=(0,w.useState)(e.default||{min:3,max:6});return(0,w.useEffect)((()=>{if(n===c.DESKTOP){const n=e.desktop||{min:t?2:3,max:6};l(n)}if(n===c.TABLET){const n=e.tablet||{min:t?1:2,max:3};l(n)}if(n===c.MOBILE){const t=e.mobile||{min:1,max:1};l(t)}}),[n,t,e]),a})({default:{min:3,max:6}}),s=[{label:l.__("All","masterstudy-lms-learning-management-system"),value:Fn.ALL},{label:l.__("Select quantity","masterstudy-lms-learning-management-system"),value:Fn.QUANTITY}];return(0,a.createElement)(Hn,{title:l.__("Grid","masterstudy-lms-learning-management-system"),accordionFields:Un},(0,a.createElement)(Mt,{name:"instructorPerPage",label:l.__("Instructors per page","masterstudy-lms-learning-management-system"),options:s}),(0,a.createElement)(ct,{name:"quantity",label:l.__("Quantity","masterstudy-lms-learning-management-system"),min:1,max:100,dependencies:[{name:"instructorPerPage",value:Fn.QUANTITY}]}),(0,a.createElement)(ct,{name:"instructorsPerRow",label:l.__("Instructors per row","masterstudy-lms-learning-management-system"),min:n,max:r,isAdaptive:!0}),(0,a.createElement)(Mt,{name:"orderBy",label:l.__("Order by","masterstudy-lms-learning-management-system"),options:t}),(0,a.createElement)(L,{condition:Boolean(e.length)},(0,a.createElement)(Mt,{name:"instructors",multiple:!0,label:l.__("Instructors","masterstudy-lms-learning-management-system"),options:e})))},Kn=()=>{const{widthOptions:e}=(()=>{const e=[{label:l.__("Auto","masterstudy-lms-learning-management-system"),value:"alignauto"},{label:l.__("Full width","masterstudy-lms-learning-management-system"),value:"alignfull"}],t=[{label:l.__("Cover","masterstudy-lms-learning-management-system"),value:"cover"},{label:l.__("Contain","masterstudy-lms-learning-management-system"),value:"contain"},{label:l.__("Inherit","masterstudy-lms-learning-management-system"),value:"inherit"},{label:l.__("Initial","masterstudy-lms-learning-management-system"),value:"initial"},{label:l.__("Revert","masterstudy-lms-learning-management-system"),value:"revert"},{label:l.__("Revert-layer","masterstudy-lms-learning-management-system"),value:"revert-layer"},{label:l.__("Unset","masterstudy-lms-learning-management-system"),value:"unset"}],n=[{label:l.__("Center center","masterstudy-lms-learning-management-system"),value:"center center"},{label:l.__("Center left","masterstudy-lms-learning-management-system"),value:"center left"},{label:l.__("Center right","masterstudy-lms-learning-management-system"),value:"center right"},{label:l.__("Top center","masterstudy-lms-learning-management-system"),value:"top center"},{label:l.__("Top left","masterstudy-lms-learning-management-system"),value:"top left"},{label:l.__("Top right","masterstudy-lms-learning-management-system"),value:"top right"},{label:l.__("Bottom center","masterstudy-lms-learning-management-system"),value:"bottom center"},{label:l.__("Bottom left","masterstudy-lms-learning-management-system"),value:"bottom left"},{label:l.__("Bottom right","masterstudy-lms-learning-management-system"),value:"bottom right"}],a=[{label:l.__("Center","masterstudy-lms-learning-management-system"),value:"center"},{label:l.__("Start","masterstudy-lms-learning-management-system"),value:"flex-start"},{label:l.__("End","masterstudy-lms-learning-management-system"),value:"flex-end"},{label:l.__("Space Between","masterstudy-lms-learning-management-system"),value:"space-between"},{label:l.__("Space Around","masterstudy-lms-learning-management-system"),value:"space-around"},{label:l.__("Space Evenly","masterstudy-lms-learning-management-system"),value:"space-evenly"}];return{filterOptions:h,widthOptions:e,backgroundSizeOptions:t,backgroundPositionOptions:n,alignContentOptions:a}})();return(0,a.createElement)(Hn,{title:l.__("Layout","masterstudy-lms-learning-management-system"),accordionFields:zn},(0,a.createElement)($e,{name:"layoutMargin",label:l.__("Margin","masterstudy-lms-learning-management-system"),unitName:"layoutMarginUnit",isAdaptive:!0,dependencies:[{name:"layoutWidth",value:"alignauto"}]}),(0,a.createElement)($e,{name:"layoutPadding",label:l.__("Padding","masterstudy-lms-learning-management-system"),unitName:"layoutPaddingUnit",isAdaptive:!0}),(0,a.createElement)(vt,{name:"cardGap",label:l.__("Space between cards","masterstudy-lms-learning-management-system"),unitName:"cardGapUnit",isAdaptive:!0}),(0,a.createElement)(Le,{name:"layoutBackground",label:l.__("Background","masterstudy-lms-learning-management-system"),placeholder:l.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(tt,{label:l.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"layoutBorderStyle",borderColorName:"layoutBorderColor",borderWidthName:"layoutBorderWidth",isAdaptive:!0}),(0,a.createElement)(lt,{name:"layoutBorderRadius",label:l.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,a.createElement)(Dt,{name:"layoutWidth",label:l.__("Width","masterstudy-lms-learning-management-system"),options:e}),(0,a.createElement)(ct,{name:"layoutZIndex",label:l.__("Z-Index","masterstudy-lms-learning-management-system"),min:0,max:100,isAdaptive:!0}))},Yn=({attributes:e,setAttributes:t,instructorOptions:n})=>{const{onResetByFieldName:l,changedFieldsByName:r}=((e,t,n,a=[])=>{const l=(e=>{const t={};return Object.entries(e).forEach((([e,n])=>{e.includes("UAG")||(t[e]=n)})),t})(t),r=!y(e,l),s=((e,t,n)=>{const a=new Map;return n.forEach((n=>{a.has(n)||a.set(n,(()=>t({[n]:e[n]})))})),a})(e,n,a),o=((e,t,n)=>{const a=new Map;return n.forEach((n=>{a.has(n)?a.set(n,!1):a.set(n,!y(e[n],t[n]))})),a})(e,l,a);return{hasChanges:r,onResetByFieldName:s,changedFieldsByName:o}})($n,e,t,Object.keys($n));return(0,a.createElement)(s.InspectorControls,null,(0,a.createElement)(T,{attributes:e,setAttributes:t,defaultValues:$n,onResetByFieldName:l,changedFieldsByName:r},(0,a.createElement)(Zt,{generalTab:(0,a.createElement)(Zn,{instructorOptions:n}),styleTab:(0,a.createElement)(Kn,null),advancedTab:(0,a.createElement)(a.Fragment,null)})))},qn=window.wp.apiFetch;var Xn=n.n(qn);const Qn=JSON.parse('{"UU":"masterstudy/instructors-grid"}');(0,r.registerBlockType)(Qn.UU,{title:l._x("MasterStudy Instructors Grid","block title","masterstudy-lms-learning-management-system"),description:l._x("Use this block to display your instructors in a grid layout and set up its look.","block description","masterstudy-lms-learning-management-system"),category:"masterstudy-lms-blocks",icon:{src:(0,a.createElement)("svg",{width:"512",height:"512",viewBox:"0 0 512 512",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,a.createElement)("path",{opacity:"0.3",d:"M248.058 189.337C248.058 145.322 212.23 109.515 168.192 109.515C124.155 109.515 88.3267 145.322 88.3267 189.337C88.3267 233.328 124.155 269.117 168.192 269.117C212.23 269.117 248.058 233.328 248.058 189.337Z",fill:"#227AFF"}),(0,a.createElement)("path",{opacity:"0.3",fillRule:"evenodd",clipRule:"evenodd",d:"M168.023 420.192L262.316 420.192C282.345 420.192 296.945 401.318 292.047 381.733C277.856 324.987 225.521 284.28 167.063 284.279C167.062 284.279 167.061 284.28 167.061 284.281C167.061 284.282 167.06 284.283 167.059 284.283C108.298 284.791 57.4639 324.719 43.2053 381.733C38.3071 401.318 52.9073 420.192 72.9367 420.192H168.023Z",fill:"#227AFF"}),(0,a.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M479.225 420.486C495.909 420.486 508.014 404.774 503.962 388.558C495.077 353.026 465.49 327.158 429.934 322.436C455.764 317.005 475.214 294.064 475.214 266.661C475.214 235.216 449.627 209.643 418.17 209.643C386.714 209.643 361.126 235.216 361.126 266.661C361.126 294.078 380.595 317.028 406.445 322.444C392.107 324.374 378.78 329.79 367.36 337.839C347.822 316.86 320.063 304.076 289.765 304.076C272.216 304.076 255.568 308.442 240.839 316.126C215.632 289.41 179.998 273.194 141.175 273.194C78.2187 273.194 23.5855 315.788 8.33275 376.777C2.77455 399.001 19.3471 420.486 42.1511 420.486H479.225ZM289.765 324.968C328.92 324.968 362.873 351.445 372.354 389.357C373.677 394.644 369.811 399.592 364.593 399.592H272.106C275.236 392.503 275.957 384.532 274.018 376.775C269.986 360.65 263.126 345.88 254.163 332.793C265.067 327.77 277.133 324.968 289.765 324.968ZM418.17 230.537C438.104 230.537 454.321 246.746 454.321 266.661C454.321 286.56 438.106 302.761 418.17 302.761C398.234 302.761 382.019 286.56 382.019 266.661C382.019 246.746 398.236 230.537 418.17 230.537ZM418.17 342.552C449.236 342.552 476.172 363.556 483.694 393.629C484.471 396.738 482.222 399.592 479.225 399.592H392.352C393.785 394.683 393.908 389.423 392.622 384.285C389.929 373.513 385.643 363.483 380.058 354.412C390.995 346.909 404.136 342.552 418.17 342.552ZM28.6015 381.848C41.5237 330.177 87.8112 294.086 141.175 294.086C194.542 294.086 240.829 330.177 253.749 381.848C256.026 390.956 249.328 399.592 240.2 399.592H42.1511C33.0163 399.592 26.3259 390.95 28.6015 381.848Z",fill:"black"}),(0,a.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M289.765 301.809C327.016 301.809 357.322 271.534 357.322 234.315C357.322 197.076 327.017 166.792 289.765 166.792C252.513 166.792 222.205 197.076 222.205 234.315C222.205 271.535 252.514 301.809 289.765 301.809ZM289.765 187.684C315.495 187.684 336.429 208.606 336.429 234.314C336.429 260.002 315.497 280.915 289.765 280.915C264.031 280.915 243.099 260.001 243.099 234.314C243.099 208.606 264.033 187.684 289.765 187.684Z",fill:"black"}),(0,a.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M227.202 177.495C227.202 130.08 188.611 91.5149 141.176 91.5149C93.7409 91.5149 55.1501 130.08 55.1501 177.495C55.1501 224.887 93.7423 263.432 141.176 263.432C188.61 263.432 227.202 224.887 227.202 177.495ZM76.0431 177.495C76.0431 141.61 105.263 112.408 141.176 112.408C177.089 112.408 206.309 141.61 206.309 177.495C206.309 213.354 177.091 242.538 141.176 242.538C105.261 242.538 76.0431 213.353 76.0431 177.495Z",fill:"black"}))},edit:({attributes:e,setAttributes:t})=>{const n=(0,s.useBlockProps)({className:I()("lms-instructors-container",{alignfull:"alignfull"===e.layoutWidth}),style:f("instructors",e,Wn)}),l=(0,s.useInnerBlocksProps)({...n},{template:o,templateLock:"all"}),{instructorOptions:r}=((e=!0)=>{const[t,n]=(0,w.useState)([]),{setIsFetching:a,setError:l,isFetching:r,error:s}=(()=>{const[e,t]=(0,w.useState)(!0),[n,a]=(0,w.useState)("");return{isFetching:e,setIsFetching:t,error:n,setError:a}})();return(0,w.useEffect)((()=>{a(!0),(async e=>{let t="stm_lms_instructor";e&&(t+=",administrator");try{return await Xn()({path:`masterstudy-lms/v2/users?roles=${t}&context=edit`})}catch(e){throw new Error(e)}})(e).then((e=>{n(e.map((e=>({label:e.name,value:e.id}))))})).catch((e=>{l(e.message)})).finally((()=>{a(!1)}))}),[]),{instructorOptions:t,isFetching:r,error:s}})(!1);return(0,a.createElement)(a.Fragment,null,(0,a.createElement)("div",{...l}),(0,a.createElement)(Yn,{attributes:e,setAttributes:t,instructorOptions:r}))},save:({attributes:e})=>{const t=s.useBlockProps.save({className:I()("lms-instructors-container",{alignfull:"alignfull"===e.layoutWidth}),style:f("instructors",e,Wn)}),n=s.useInnerBlocksProps.save(t);return(0,a.createElement)("div",{...n})}})},6942:(e,t)=>{var n;!function(){"use strict";var a={}.hasOwnProperty;function l(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=s(e,r(n)))}return e}function r(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return l.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)a.call(e,n)&&e[n]&&(t=s(t,n));return t}function s(e,t){return t?e?e+" "+t:e+t:e}e.exports?(l.default=l,e.exports=l):void 0===(n=function(){return l}.apply(t,[]))||(e.exports=n)}()}},n={};function a(e){var l=n[e];if(void 0!==l)return l.exports;var r=n[e]={exports:{}};return t[e](r,r.exports,a),r.exports}a.m=t,e=[],a.O=(t,n,l,r)=>{if(!n){var s=1/0;for(c=0;c<e.length;c++){for(var[n,l,r]=e[c],o=!0,i=0;i<n.length;i++)(!1&r||s>=r)&&Object.keys(a.O).every((e=>a.O[e](n[i])))?n.splice(i--,1):(o=!1,r<s&&(s=r));if(o){e.splice(c--,1);var m=l();void 0!==m&&(t=m)}}return t}r=r||0;for(var c=e.length;c>0&&e[c-1][2]>r;c--)e[c]=e[c-1];e[c]=[n,l,r]},a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={4157:0,5905:0};a.O.j=t=>0===e[t];var t=(t,n)=>{var l,r,[s,o,i]=n,m=0;if(s.some((t=>0!==e[t]))){for(l in o)a.o(o,l)&&(a.m[l]=o[l]);if(i)var c=i(a)}for(t&&t(n);m<s.length;m++)r=s[m],a.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return a.O(c)},n=globalThis.webpackChunkmasterstudy_lms_learning_management_system=globalThis.webpackChunkmasterstudy_lms_learning_management_system||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})();var l=a.O(void 0,[5905],(()=>a(4714)));l=a.O(l)})();