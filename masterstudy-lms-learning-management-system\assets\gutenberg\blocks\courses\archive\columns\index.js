(()=>{"use strict";var e,t={2611:()=>{const e=window.React,t=window.wp.blocks,l=window.wp.element,r=window.wp.blockEditor,a=[["masterstudy/courses-filter",{className:"archive-courses__column-start"}],["core/group",{className:"archive-courses__column-end"},[["masterstudy/courses-preset"],["core/group",{className:"lms-courses-group-load-more"},[["masterstudy/courses-load-more"]]]]]],s=JSON.parse('{"UU":"masterstudy/archive-container-columns"}');(0,t.registerBlockType)(s.UU,{icon:{src:(0,e.createElement)("svg",{width:"513",height:"513",viewBox:"0 0 513 513",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("g",{clipPath:"url(#clip0_3567_45417)"},(0,e.createElement)("path",{d:"M472.275 1.93774H40.7307C19.0753 1.95767 1.52383 19.5091 1.50391 41.1645V472.709C1.52383 494.364 19.0753 511.916 40.7307 511.946H472.275C493.93 511.926 511.482 494.374 511.512 472.709V41.1645C511.492 19.5091 493.94 1.95767 472.275 1.93774ZM40.7307 21.5511H472.275C483.103 21.5611 491.878 30.3368 491.888 41.1645V80.3913H21.1173V41.1645C21.1273 30.3368 29.903 21.5611 40.7307 21.5511ZM472.275 492.332H40.7307C29.903 492.322 21.1273 483.546 21.1173 472.719V100.015H491.898V472.709C491.888 483.546 483.113 492.322 472.275 492.332Z",fill:"black"}),(0,e.createElement)("path",{d:"M472.285 512.942H40.7307C18.5772 512.922 0.527735 494.872 0.507812 472.719V41.1645C0.527735 19.0111 18.5772 0.961573 40.7307 0.94165H472.275C494.438 0.961573 512.478 19.0111 512.498 41.1645V472.709C512.488 494.872 494.438 512.922 472.285 512.942ZM40.7307 2.93387C19.673 2.95379 2.51995 20.1068 2.50003 41.1745V472.719C2.51995 493.777 19.673 510.93 40.7407 510.949H472.285C493.343 510.93 510.496 493.777 510.516 472.709V41.1645C510.496 20.1068 493.343 2.95379 472.275 2.93387H40.7307ZM472.285 493.328H40.7307C29.3751 493.318 20.1312 484.074 20.1212 472.719V99.0185H492.884V472.709C492.884 484.074 483.641 493.318 472.285 493.328ZM22.1134 101.011V472.709C22.1234 482.969 30.4807 491.316 40.7307 491.326H472.275C482.535 491.316 490.882 482.969 490.892 472.709V101.011H22.1134ZM492.894 81.3974H20.1212V41.1645C20.1312 29.8089 29.3751 20.565 40.7307 20.555H472.275C483.631 20.565 492.874 29.8089 492.884 41.1645V81.3974H492.894ZM22.1134 79.4052H490.892V41.1645C490.882 30.9046 482.525 22.5572 472.275 22.5473H40.7307C30.4708 22.5572 22.1234 30.9146 22.1134 41.1645V79.4052Z",fill:"black"}),(0,e.createElement)("path",{d:"M129.325 60.9272C134.755 60.9272 139.156 56.5255 139.156 51.0956C139.156 45.6658 134.755 41.264 129.325 41.264C123.895 41.264 119.493 45.6658 119.493 51.0956C119.493 56.5255 123.895 60.9272 129.325 60.9272Z",fill:"black"}),(0,e.createElement)("path",{d:"M129.325 61.9334C123.358 61.9334 118.497 57.0724 118.497 51.1057C118.497 45.139 123.358 40.278 129.325 40.278C135.291 40.278 140.152 45.139 140.152 51.1057C140.152 57.0724 135.291 61.9334 129.325 61.9334ZM129.325 42.2602C124.454 42.2602 120.489 46.2247 120.489 51.0957C120.489 55.9667 124.454 59.9312 129.325 59.9312C134.196 59.9312 138.16 55.9667 138.16 51.0957C138.16 46.2247 134.196 42.2602 129.325 42.2602Z",fill:"black"}),(0,e.createElement)("path",{d:"M89.9986 60.9272C95.4284 60.9272 99.8302 56.5255 99.8302 51.0956C99.8302 45.6658 95.4284 41.264 89.9986 41.264C84.5687 41.264 80.167 45.6658 80.167 51.0956C80.167 56.5255 84.5687 60.9272 89.9986 60.9272Z",fill:"black"}),(0,e.createElement)("path",{d:"M89.9986 61.9334C84.0319 61.9334 79.1709 57.0724 79.1709 51.1057C79.1709 45.139 84.0319 40.278 89.9986 40.278C95.9653 40.278 100.826 45.139 100.826 51.1057C100.826 57.0724 95.9653 61.9334 89.9986 61.9334ZM89.9986 42.2602C85.1276 42.2602 81.1631 46.2247 81.1631 51.0957C81.1631 55.9667 85.1276 59.9312 89.9986 59.9312C94.8696 59.9312 98.8341 55.9667 98.8341 51.0957C98.8341 46.2247 94.8696 42.2602 89.9986 42.2602Z",fill:"black"}),(0,e.createElement)("path",{d:"M50.6617 60.9272C56.0915 60.9272 60.4933 56.5255 60.4933 51.0956C60.4933 45.6658 56.0915 41.264 50.6617 41.264C45.2318 41.264 40.8301 45.6658 40.8301 51.0956C40.8301 56.5255 45.2318 60.9272 50.6617 60.9272Z",fill:"black"}),(0,e.createElement)("path",{d:"M50.6617 61.9334C44.695 61.9334 39.834 57.0724 39.834 51.1057C39.834 45.139 44.695 40.278 50.6617 40.278C56.6284 40.278 61.4894 45.139 61.4894 51.1057C61.4894 57.0724 56.6383 61.9334 50.6617 61.9334ZM50.6617 42.2602C45.7907 42.2602 41.8262 46.2247 41.8262 51.0957C41.8262 55.9667 45.7907 59.9312 50.6617 59.9312C55.5327 59.9312 59.4972 55.9667 59.4972 51.0957C59.4972 46.2247 55.5426 42.2602 50.6617 42.2602Z",fill:"black"}),(0,e.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M173.508 168.942C173.508 159.553 181.119 151.942 190.508 151.942H246.508C255.897 151.942 263.508 159.553 263.508 168.942V216.942C263.508 226.33 255.897 233.942 246.508 233.942H190.508C181.119 233.942 173.508 226.33 173.508 216.942V168.942ZM191.508 169.942V215.942H245.508V169.942H191.508ZM277.508 168.942C277.508 159.553 285.119 151.942 294.508 151.942H342.508C351.897 151.942 359.508 159.553 359.508 168.942V216.942C359.508 226.33 351.897 233.942 342.508 233.942H294.508C285.119 233.942 277.508 226.33 277.508 216.942V168.942ZM295.508 169.942V215.942H341.508V169.942H295.508ZM373.508 168.942C373.508 159.553 381.119 151.942 390.508 151.942H438.508C447.897 151.942 455.508 159.553 455.508 168.942V216.942C455.508 226.33 447.897 233.942 438.508 233.942H390.508C381.119 233.942 373.508 226.33 373.508 216.942V168.942ZM391.508 169.942V215.942H437.508V169.942H391.508ZM173.508 272.942C173.508 263.553 181.119 255.942 190.508 255.942H246.508C255.897 255.942 263.508 263.553 263.508 272.942V320.942C263.508 330.33 255.897 337.942 246.508 337.942H190.508C181.119 337.942 173.508 330.33 173.508 320.942V272.942ZM191.508 273.942V319.942H245.508V273.942H191.508ZM277.508 272.942C277.508 263.553 285.119 255.942 294.508 255.942H342.508C351.897 255.942 359.508 263.553 359.508 272.942V320.942C359.508 330.33 351.897 337.942 342.508 337.942H294.508C285.119 337.942 277.508 330.33 277.508 320.942V272.942ZM295.508 273.942V319.942H341.508V273.942H295.508ZM373.508 272.942C373.508 263.553 381.119 255.942 390.508 255.942H438.508C447.897 255.942 455.508 263.553 455.508 272.942V320.942C455.508 330.33 447.897 337.942 438.508 337.942H390.508C381.119 337.942 373.508 330.33 373.508 320.942V272.942ZM391.508 273.942V319.942H437.508V273.942H391.508ZM173.508 376.942C173.508 367.553 181.119 359.942 190.508 359.942H246.508C255.897 359.942 263.508 367.553 263.508 376.942V424.942C263.508 434.331 255.897 441.942 246.508 441.942H190.508C181.119 441.942 173.508 434.331 173.508 424.942V376.942ZM191.508 377.942V423.942H245.508V377.942H191.508ZM277.508 376.942C277.508 367.553 285.119 359.942 294.508 359.942H342.508C351.897 359.942 359.508 367.553 359.508 376.942V424.942C359.508 434.331 351.897 441.942 342.508 441.942H294.508C285.119 441.942 277.508 434.331 277.508 424.942V376.942ZM295.508 377.942V423.942H341.508V377.942H295.508ZM373.508 376.942C373.508 367.553 381.119 359.942 390.508 359.942H438.508C447.897 359.942 455.508 367.553 455.508 376.942V424.942C455.508 434.331 447.897 441.942 438.508 441.942H390.508C381.119 441.942 373.508 434.331 373.508 424.942V376.942ZM391.508 377.942V423.942H437.508V377.942H391.508Z",fill:"black"}),(0,e.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M64.5078 168.942C64.5078 159.553 72.119 151.942 81.5078 151.942H137.508C146.897 151.942 154.508 159.553 154.508 168.942V372.942C154.508 382.33 146.897 389.942 137.508 389.942H81.5078C72.119 389.942 64.5078 382.33 64.5078 372.942V168.942ZM82.5078 169.942V371.942H136.508V169.942H82.5078Z",fill:"black"}),(0,e.createElement)("rect",{opacity:"0.3",x:"167.119",y:"256.942",width:"336.66",height:"247.271",rx:"30",fill:"#227AFF"})),(0,e.createElement)("defs",null,(0,e.createElement)("clipPath",{id:"clip0_3567_45417"},(0,e.createElement)("rect",{width:"512",height:"512",fill:"white",transform:"translate(0.507812 0.94165)"}))))},edit:({attributes:t,setAttributes:s})=>{const c=(0,l.useRef)(null),C=(0,l.useCallback)((e=>{for(const t of e)if(t.target){const{width:e}=t.target.getBoundingClientRect();s({columnEndWidth:e-300})}}),[s]);(0,l.useEffect)((()=>{const e=new ResizeObserver(C);return c.current&&e.observe(c.current),()=>e.disconnect()}),[C,c.current]);const n=(0,r.useBlockProps)({className:"archive-courses__columns",style:{"--lms-archive-courses--width-dynamic":`${t.columnEndWidth}px`}}),o=(0,r.useInnerBlocksProps)({...n},{template:a,templateLock:"all"});return(0,e.createElement)("div",{...o,ref:c})},save:({attributes:t})=>{const l=r.useBlockProps.save({className:"archive-courses__columns"}),a=r.useInnerBlocksProps.save(l);return(0,e.createElement)("div",{...a})}})}},l={};function r(e){var a=l[e];if(void 0!==a)return a.exports;var s=l[e]={exports:{}};return t[e](s,s.exports,r),s.exports}r.m=t,e=[],r.O=(t,l,a,s)=>{if(!l){var c=1/0;for(i=0;i<e.length;i++){for(var[l,a,s]=e[i],C=!0,n=0;n<l.length;n++)(!1&s||c>=s)&&Object.keys(r.O).every((e=>r.O[e](l[n])))?l.splice(n--,1):(C=!1,s<c&&(c=s));if(C){e.splice(i--,1);var o=a();void 0!==o&&(t=o)}}return t}s=s||0;for(var i=e.length;i>0&&e[i-1][2]>s;i--)e[i]=e[i-1];e[i]=[l,a,s]},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={8517:0,8409:0};r.O.j=t=>0===e[t];var t=(t,l)=>{var a,s,[c,C,n]=l,o=0;if(c.some((t=>0!==e[t]))){for(a in C)r.o(C,a)&&(r.m[a]=C[a]);if(n)var i=n(r)}for(t&&t(l);o<c.length;o++)s=c[o],r.o(e,s)&&e[s]&&e[s][0](),e[s]=0;return r.O(i)},l=globalThis.webpackChunkmasterstudy_lms_learning_management_system=globalThis.webpackChunkmasterstudy_lms_learning_management_system||[];l.forEach(t.bind(null,0)),l.push=t.bind(null,l.push.bind(l))})();var a=r.O(void 0,[8409],(()=>r(2611)));a=r.O(a)})();