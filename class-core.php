<?php
/**
 * Core class for Custom Linking Plugin
 * Handles autoloading and plugin initialization
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Include debug utilities
require_once CUSTOM_LINKING_PLUGIN_PATH . 'includes/debug.php';

class Custom_Linking_Core {
    /**
     * The single instance of the class
     */
    private static $instance = null;

    /**
     * Plugin version
     */
    public $version = '1.0.0';

    /**
     * Plugin directory path
     */
    public $plugin_path;

    /**
     * Plugin URL
     */
    public $plugin_url;

    /**
     * Main instance of the plugin
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    public function __construct() {
        $this->define_constants();
        $this->includes();
        $this->init_hooks();
    }

    /**
     * Define plugin constants
     */
    private function define_constants() {
        // Use already defined constants from main plugin file
        $this->plugin_path = CUSTOM_LINKING_PLUGIN_PATH;
        $this->plugin_url = CUSTOM_LINKING_PLUGIN_URL;

        // Don't redefine constants that are already defined
        if (!defined('CUSTOM_LINKING_VERSION')) {
            define('CUSTOM_LINKING_VERSION', $this->version);
        }
    }

    /**
     * Include required files
     */
    private function includes() {
        // Files are already included in the main plugin file
        // No need to include them again here
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Initialize plugin components
        add_action('plugins_loaded', array($this, 'init_plugin'));
    }

    /**
     * Plugin activation
     * 
     * This method handles plugin activation tasks
     * Primarily creating the database table
     */
    public function activate() {
        // Debug log for troubleshooting
        custom_linking_debug_log('Core: Activating plugin');

        // Create necessary database tables
        require_once CUSTOM_LINKING_PLUGIN_PATH . 'database/Activator.php';
        Custom_Linking_Activator::activate();

        // Set plugin version
        update_option('custom_linking_plugin_version', CUSTOM_LINKING_VERSION);
        custom_linking_debug_log('Core: Plugin version set to ' . CUSTOM_LINKING_VERSION);
    }

    /**
     * Plugin deactivation
     * 
     * This method handles plugin deactivation tasks
     * Primarily dropping the database table
     */
    public function deactivate() {
        // Debug log for troubleshooting
        custom_linking_debug_log('Core: Deactivating plugin');

        // Drop the database table
        require_once CUSTOM_LINKING_PLUGIN_PATH . 'database/Deactivator.php';
        Custom_Linking_Deactivator::deactivate();
    }

    /**
     * Initialize the plugin
     */
    public function init_plugin() {
        // Load text domain for translations
        load_plugin_textdomain(
            'custom-linking-plugin',
            false,
            dirname(plugin_basename(CUSTOM_LINKING_PLUGIN_FILE)) . '/languages/'
        );

        // Initialize plugin components
        $this->init_components();
    }

    /**
     * Initialize plugin components
     *
     * This method initializes admin and frontend components
     * when the plugin is loaded
     */
    private function init_components() {
        if (function_exists('custom_linking_debug_log')) {
            custom_linking_debug_log('Core: Initializing plugin components');
        }

        // Admin hooks
        if (is_admin()) {
            if (function_exists('custom_linking_debug_log')) {
                custom_linking_debug_log('Core: Setting up admin hooks');
            }

            // Only add admin hooks if the functions exist
            if (function_exists('custom_linking_admin_menu')) {
                add_action('admin_menu', 'custom_linking_admin_menu');
                if (function_exists('custom_linking_debug_log')) {
                    custom_linking_debug_log('Core: Admin menu hook registered');
                }
            } else {
                if (function_exists('custom_linking_debug_log')) {
                    custom_linking_debug_log('Core: WARNING - custom_linking_admin_menu function not found');
                }
            }

            if (function_exists('custom_linking_admin_scripts')) {
                add_action('admin_enqueue_scripts', 'custom_linking_admin_scripts');
                if (function_exists('custom_linking_debug_log')) {
                    custom_linking_debug_log('Core: Admin scripts hook registered');
                }
            } else {
                if (function_exists('custom_linking_debug_log')) {
                    custom_linking_debug_log('Core: WARNING - custom_linking_admin_scripts function not found');
                }
            }
        } else {
            if (function_exists('custom_linking_debug_log')) {
                custom_linking_debug_log('Core: Not in admin area, skipping admin hooks');
            }
        }

        // Initialize frontend components
        // Initialize frontend functionality
    }
}

