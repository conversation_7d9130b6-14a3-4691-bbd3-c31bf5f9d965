<?php

if (!defined('ABSPATH')) {
    exit;
}

// MSCB_Activator handles plugin activation and table creation
class MSCB_Activator {
    /**
     * Creates all necessary custom tables for the course bundler plugin on activation.
     */
    public static function activate() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();

        /* Create the main bundles table for storing bundle info */
        $table_name = $wpdb->prefix . 'mscb_bundles';
        $sql1 = "CREATE TABLE IF NOT EXISTS $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            title varchar(255) NOT NULL,
            description longtext,
            price decimal(10,2) DEFAULT 0.00,
            sale_price decimal(10,2) DEFAULT NULL,
            status varchar(20) DEFAULT 'draft',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY  (id)
        ) $charset_collate;";

        /* Create the bundle-courses relationship table */
        $table_name_courses = $wpdb->prefix . 'mscb_bundle_courses';
        $sql2 = "CREATE TABLE IF NOT EXISTS $table_name_courses (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            bundle_id bigint(20) NOT NULL, 
            course_id bigint(20) NOT NULL, 
            order_index int(11) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY  (id),
            KEY bundle_id (bundle_id),
            KEY course_id (course_id)
        ) $charset_collate;";

        /* Create the bundle enrollments table for tracking user enrollments in bundles */
        $table_name_enrollments = $wpdb->prefix . 'mscb_bundle_enrollments';
        $sql3 = "CREATE TABLE IF NOT EXISTS $table_name_enrollments (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            bundle_id bigint(20) NOT NULL,
            user_id bigint(20) NOT NULL,
            status varchar(20) DEFAULT 'active',
            enrolled_at datetime DEFAULT CURRENT_TIMESTAMP,
            completed_at datetime DEFAULT NULL,
            PRIMARY KEY  (id),
            KEY bundle_id (bundle_id),
            KEY user_id (user_id)
        ) $charset_collate;";

        // Run the SQL to create each table
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql1); //* Create bundles table
        dbDelta($sql2); //* Create bundle-courses table
        dbDelta($sql3); //* Create enrollments table

        // Store the plugin version for future upgrades
        add_option('mscb_version', MSCB_VERSION);
    }
} 