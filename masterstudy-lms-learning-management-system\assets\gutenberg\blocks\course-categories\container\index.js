(()=>{var e,t={6913:(e,t,a)=>{"use strict";const r=window.React,n=window.wp.i18n,o=window.wp.blocks,s=window.wp.element,l=window.wp.blockEditor;var i=a(6942),c=a.n(i);const m=window.wp.components,d=({options:e,categories:t,...a})=>(0,r.createElement)("div",{...a},t.map((({value:e,label:t,icon:a})=>(0,r.createElement)("div",{className:"lms-courses-category-list__item",key:e},(0,r.createElement)("a",{href:"#",className:"lms-courses-category-list__link","data-value":e,key:e,onClick:e=>{e.preventDefault()}},a&&(0,r.createElement)("i",{className:a}),(0,r.createElement)("span",null,t)))))),u=({options:e,categories:t,...a})=>(0,r.createElement)("div",{...a},t.map((({value:e,label:t,icon:a,color:n})=>(0,r.createElement)("div",{className:"lms-courses-category-list__item",key:e},(0,r.createElement)("a",{href:"#",className:"lms-courses-category-list__link",style:{backgroundColor:n},"data-value":e,key:e,onClick:e=>{e.preventDefault()}},a&&(0,r.createElement)("i",{className:a}),(0,r.createElement)("span",null,t)))))),g=({options:e,categories:t,...a})=>(0,r.createElement)("div",{...a},t.map((({value:e,label:t,image:a})=>(0,r.createElement)("div",{className:"lms-courses-category-list__item",key:e},(0,r.createElement)("a",{href:"#",className:"lms-courses-category-list__link","data-value":e,key:e,onClick:e=>{e.preventDefault()}},a&&(0,r.createElement)("img",{src:a,width:"140",height:"120",alt:t}),(0,r.createElement)("span",null,t)))))),C=({category:e})=>{const{value:t,label:a,image:o,color:s,courses:l}=e;return(0,r.createElement)("div",{className:"lms-courses-category-list__item"},(0,r.createElement)("a",{href:"#",className:"lms-courses-category-list__link","data-value":t,onClick:e=>{e.preventDefault()}},(0,r.createElement)("span",{className:"lms-courses-category-list__link-image",style:{backgroundColor:s}},o&&(0,r.createElement)("img",{src:o,alt:a})),(0,r.createElement)("span",{className:"lms-courses-category-list__link-info-wrap"},(0,r.createElement)("span",{className:"lms-courses-category-list__link-info"},(0,r.createElement)("span",{className:"lms-courses-category-list__link-title"},a),(0,r.createElement)("span",{className:"lms-courses-category-list__link-posts"},l," ",n.__("Courses","masterstudy-lms-learning-management-system"))))))},y=({options:e,categories:t,...a})=>{const n=[];for(let e=0;e<t.length;e+=3)n.push(t.slice(e,e+3));return(0,r.createElement)("div",{...a},n.map(((e,t)=>(0,r.createElement)("div",{className:"row",key:t},e.map(((a,n)=>t%2==0&&1===n?(0,r.createElement)("div",{className:"two-columns",key:a.value},(0,r.createElement)(C,{category:a}),e[n+1]&&(0,r.createElement)(C,{category:e[n+1]})):t%2==0||1!==n&&2!==n?2!==n?(0,r.createElement)(C,{category:a,key:a.value}):null:(0,r.createElement)(C,{category:a,key:a.value})))))))},p=({condition:e,fallback:t=null,children:a})=>(0,r.createElement)(r.Fragment,null,e?a:t),h=(e,t)=>{if(typeof e!=typeof t)return!1;if(Number.isNaN(e)&&Number.isNaN(t))return!0;if("object"!=typeof e||null===e||null===t)return e===t;if(Object.keys(e).length!==Object.keys(t).length)return!1;if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;const a=e.slice().sort(),r=t.slice().sort();return a.every(((e,t)=>h(e,r[t])))}for(const a of Object.keys(e))if(!h(e[a],t[a]))return!1;return!0},v=(e=[])=>e.map((e=>({label:e.name,value:e.term_id})));let b=function(e){return e.ALL="all",e.SOME="some",e}({}),_=function(e){return e.NORMAL="Normal",e.HOVER="Hover",e.ACTIVE="Active",e.FOCUS="Focus",e}({}),E=function(e){return e.DESKTOP="Desktop",e.TABLET="Tablet",e.MOBILE="Mobile",e}({}),f=function(e){return e.TOP_lEFT="top-left",e.TOP_CENTER="top-center",e.TOP_RIGHT="top-right",e.BOTTOM_lEFT="bottom-left",e.BOTTOM_CENTER="bottom-center",e.BOTTOM_RIGHT="bottom-right",e}({});const S=["",null,void 0,"null","undefined"],N=[".jpg",".jpeg",".png",".gif"],w=e=>S.includes(e),x=(e,t,a="")=>{const r=e[t];return"object"==typeof r&&null!==r?((e,t)=>{return a=e,Object.values(a).every((e=>S.includes(e)))?null:((e,t="")=>{const a=Object.entries(e).reduce(((e,[a,r])=>(e[a]=(r||"0")+t,e)),{});return`${a.top} ${a.right} ${a.bottom} ${a.left}`})(e,t);var a})(r,a):((e,t)=>w(e)?e:(e=>{if("string"!=typeof e)return!1;const t=e.slice(e.lastIndexOf("."));return N.includes(t)||e.startsWith("blob")})(e)?`url('${e}')`:String(e+t))(r,a)},B=e=>({desktopPropertyName:e,tabletPropertyName:e+"Tablet",mobilePropertyName:e+"Mobile"}),k=(e,t,a)=>{const r={};return a.forEach((({isAdaptive:a,hasHover:n,unit:o},s)=>{if(t.hasOwnProperty(s)){const{unitMeasureDesktop:l,unitMeasureTablet:i,unitMeasureMobile:c}=((e,t)=>{var a;return{unitMeasureDesktop:null!==(a=e[t])&&void 0!==a?a:"",unitMeasureTablet:t?e[t+"Tablet"]:"",unitMeasureMobile:t?e[t+"Mobile"]:""}})(t,o);if(a&&n){const{desktopHoverPropertyName:a,mobileHoverPropertyName:n,tabletHoverPropertyName:o}=(e=>{const t=e+_.HOVER;return{desktopHoverPropertyName:t,tabletHoverPropertyName:t+"Tablet",mobileHoverPropertyName:t+"Mobile"}})(s),m=x(t,a,l);w(m)||(r[`--lms-${e}-${a}`]=m);const d=x(t,o,i);w(d)||(r[`--lms-${e}-${o}`]=d);const u=x(t,n,c);w(u)||(r[`--lms-${e}-${n}`]=u)}if(n){const a=s+_.HOVER,n=x(t,a,l);w(n)||(r[`--lms-${e}-${a}`]=n)}if(a){const{desktopPropertyName:a,mobilePropertyName:n,tabletPropertyName:o}=B(s),m=x(t,a,l);w(m)||(r[`--lms-${e}-${a}`]=m);const d=x(t,o,i);w(d)||(r[`--lms-${e}-${o}`]=d);const u=x(t,n,c);w(u)||(r[`--lms-${e}-${n}`]=u)}const m=x(t,s,l);w(m)||(r[`--lms-${e}-${s}`]=m)}})),r},T=(e,t,a,r,n,o)=>`${!0===e?"inset ":""} ${t}px ${a}px ${""!==r?`${r}px`:""} ${""!==n?`${n}px`:""} ${o}`,M=(e,t,a,r,n,o,s,l,i)=>{const c={};if(t[a]&&null!==t[r]&&null!==t[n]&&(c[`--lms-${e}-boxShadow`]=T(t[l],t[r],t[n],t[o],t[s],t[a])),i){const{tabletPropertyName:i,mobilePropertyName:m}=B(l),{tabletPropertyName:d,mobilePropertyName:u}=B(r),{tabletPropertyName:g,mobilePropertyName:C}=B(n),{tabletPropertyName:y,mobilePropertyName:p}=B(a),{tabletPropertyName:h,mobilePropertyName:v}=B(o),{tabletPropertyName:b,mobilePropertyName:_}=B(s);t[y]&&null!==t[d]&&null!==t[g]&&(c[`--lms-${e}-boxShadowTablet`]=T(t[i],t[d],t[g],t[h],t[b],t[y])),t[p]&&null!==t[u]&&null!==t[C]&&(c[`--lms-${e}-boxShadowMobile`]=T(t[m],t[u],t[C],t[v],t[_],t[p]))}return c},H=(n.__("Small","masterstudy-lms-learning-management-system"),n.__("Normal","masterstudy-lms-learning-management-system"),n.__("Large","masterstudy-lms-learning-management-system"),n.__("Extra Large","masterstudy-lms-learning-management-system"),"wp-block-masterstudy-settings__"),P={top:"",right:"",bottom:"",left:""},A=(f.TOP_lEFT,f.TOP_CENTER,f.TOP_RIGHT,f.BOTTOM_lEFT,f.BOTTOM_CENTER,f.BOTTOM_RIGHT,[{label:n.__("Newest","masterstudy-lms-learning-management-system"),value:"date_high"},{label:n.__("Oldest","masterstudy-lms-learning-management-system"),value:"date_low"},{label:n.__("Overall rating","masterstudy-lms-learning-management-system"),value:"rating"},{label:n.__("Popular","masterstudy-lms-learning-management-system"),value:"popular"},{label:n.__("Price low","masterstudy-lms-learning-management-system"),value:"price_low"},{label:n.__("Price high","masterstudy-lms-learning-management-system"),value:"price_high"}]);function O(e){return Array.isArray(e)?e.map((e=>H+e)):H+e}const R=window.wp.data,L=()=>(0,R.useSelect)((e=>e("core/editor").getDeviceType()||e("core/edit-site")?.__experimentalGetPreviewDeviceType()||e("core/edit-post")?.__experimentalGetPreviewDeviceType()||e("masterstudy/store")?.getDeviceType()),[])||"Desktop",F=(e=!1)=>{const[t,a]=(0,s.useState)(e),r=(0,s.useCallback)((()=>{a(!0)}),[]);return{isOpen:t,onClose:(0,s.useCallback)((()=>{a(!1)}),[]),onOpen:r,onToggle:(0,s.useCallback)((()=>{a((e=>!e))}),[])}},D=(0,s.createContext)(null),I=({children:e,...t})=>(0,r.createElement)(D.Provider,{value:{...t}},e),U=()=>{const e=(0,s.useContext)(D);if(!e)throw new Error("No settings context provided");return e},z=(e="")=>{const{attributes:t,setAttributes:a,onResetByFieldName:r,changedFieldsByName:n}=U();return{value:t[e],onChange:t=>a({[e]:t}),onReset:r.get(e),isChanged:n.get(e)}},W=(e,t=!1,a=!1)=>{const{hoverName:r,onChangeHoverName:n}=(()=>{const[e,t]=(0,s.useState)(_.NORMAL);return{hoverName:e,onChangeHoverName:(0,s.useCallback)((e=>{t(e)}),[])}})(),o=L();return{fieldName:(0,s.useMemo)((()=>{const n=r===_.HOVER?r:"",s=o===E.DESKTOP?"":o;return a&&t?e+n+s:a&&!t?e+n:t&&!a?e+s:e}),[e,a,t,r,o]),hoverName:r,onChangeHoverName:n}},V=(e,t=!1,a="Normal")=>{const r=L(),n=(0,s.useMemo)((()=>{const n=a===_.NORMAL?"":a,o=r===E.DESKTOP?"":r;return n&&t?e+n+o:n&&!t?e+n:t&&!n?e+o:e}),[e,t,a,r]),{value:o,isChanged:l,onReset:i}=z(n);return{fieldName:n,value:o,isChanged:l,onReset:i}},$=(e=[],t=b.ALL)=>{const{attributes:a}=U();return!e.length||(t===b.ALL?e.every((({name:e,value:t})=>{const r=a[e];return Array.isArray(t)?Array.isArray(r)?h(t,r):t.includes(r):t===r})):t!==b.SOME||e.some((({name:e,value:t})=>{const r=a[e];return Array.isArray(t)?Array.isArray(r)?h(t,r):t.includes(r):t===r})))},j=e=>{const t=(0,s.useRef)(null);return(0,s.useEffect)((()=>{const a=a=>{t.current&&!t.current.contains(a.target)&&e()};return document.addEventListener("click",a),()=>{document.removeEventListener("click",a)}}),[t,e]),t},G=()=>{const e=[{label:n.__("Auto","masterstudy-lms-learning-management-system"),value:"alignauto"},{label:n.__("Full width","masterstudy-lms-learning-management-system"),value:"alignfull"}],t=[{label:n.__("Cover","masterstudy-lms-learning-management-system"),value:"cover"},{label:n.__("Contain","masterstudy-lms-learning-management-system"),value:"contain"},{label:n.__("Inherit","masterstudy-lms-learning-management-system"),value:"inherit"},{label:n.__("Initial","masterstudy-lms-learning-management-system"),value:"initial"},{label:n.__("Revert","masterstudy-lms-learning-management-system"),value:"revert"},{label:n.__("Revert-layer","masterstudy-lms-learning-management-system"),value:"revert-layer"},{label:n.__("Unset","masterstudy-lms-learning-management-system"),value:"unset"}],a=[{label:n.__("Center center","masterstudy-lms-learning-management-system"),value:"center center"},{label:n.__("Center left","masterstudy-lms-learning-management-system"),value:"center left"},{label:n.__("Center right","masterstudy-lms-learning-management-system"),value:"center right"},{label:n.__("Top center","masterstudy-lms-learning-management-system"),value:"top center"},{label:n.__("Top left","masterstudy-lms-learning-management-system"),value:"top left"},{label:n.__("Top right","masterstudy-lms-learning-management-system"),value:"top right"},{label:n.__("Bottom center","masterstudy-lms-learning-management-system"),value:"bottom center"},{label:n.__("Bottom left","masterstudy-lms-learning-management-system"),value:"bottom left"},{label:n.__("Bottom right","masterstudy-lms-learning-management-system"),value:"bottom right"}],r=[{label:n.__("Center","masterstudy-lms-learning-management-system"),value:"center"},{label:n.__("Start","masterstudy-lms-learning-management-system"),value:"flex-start"},{label:n.__("End","masterstudy-lms-learning-management-system"),value:"flex-end"},{label:n.__("Space Between","masterstudy-lms-learning-management-system"),value:"space-between"},{label:n.__("Space Around","masterstudy-lms-learning-management-system"),value:"space-around"},{label:n.__("Space Evenly","masterstudy-lms-learning-management-system"),value:"space-evenly"}];return{filterOptions:A,widthOptions:e,backgroundSizeOptions:t,backgroundPositionOptions:a,alignContentOptions:r}},Z=e=>(0,r.createElement)(m.SVG,{width:"16",height:"16",viewBox:"0 0 12 13",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,r.createElement)(m.Path,{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})),K=e=>(0,r.createElement)(m.SVG,{width:"16",height:"16",viewBox:"0 0 14 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,r.createElement)(m.G,{"clip-path":"url(#clip0_1068_38993)"},(0,r.createElement)(m.Path,{d:"M11.1973 8.60005L8.74967 8.11005V5.67171C8.74967 5.517 8.68822 5.36863 8.57882 5.25923C8.46942 5.14984 8.32105 5.08838 8.16634 5.08838H6.99967C6.84496 5.08838 6.69659 5.14984 6.5872 5.25923C6.4778 5.36863 6.41634 5.517 6.41634 5.67171V8.58838H5.24967C5.15021 8.58844 5.05241 8.61391 4.96555 8.66238C4.87869 8.71084 4.80565 8.78068 4.75336 8.86529C4.70106 8.9499 4.67125 9.04646 4.66674 9.14582C4.66223 9.24518 4.68317 9.34405 4.72759 9.43305L6.47759 12.933C6.57676 13.1302 6.77859 13.255 6.99967 13.255H11.083C11.2377 13.255 11.3861 13.1936 11.4955 13.0842C11.6049 12.9748 11.6663 12.8264 11.6663 12.6717V9.17171C11.6665 9.03685 11.6198 8.90612 11.5343 8.80186C11.4487 8.69759 11.3296 8.62626 11.1973 8.60005Z"}),(0,r.createElement)(m.Path,{d:"M10.4997 1.58838H3.49967C2.85626 1.58838 2.33301 2.11221 2.33301 2.75505V6.83838C2.33301 7.4818 2.85626 8.00505 3.49967 8.00505H5.24967V6.83838H3.49967V2.75505H10.4997V6.83838H11.6663V2.75505C11.6663 2.11221 11.1431 1.58838 10.4997 1.58838Z"})),(0,r.createElement)("defs",null,(0,r.createElement)("clipPath",{id:"clip0_1068_38993"},(0,r.createElement)(m.Rect,{width:"14",height:"14",transform:"translate(0 0.421875)"})))),X=[{value:_.NORMAL,label:n.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,r.createElement)(Z,{onClick:e})},{value:_.HOVER,label:n.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,r.createElement)(K,{onClick:e})},{value:_.ACTIVE,label:n.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,r.createElement)(K,{onClick:e})},{value:_.FOCUS,label:n.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,r.createElement)(K,{onClick:e})}],Y={[_.NORMAL]:{icon:(0,r.createElement)(Z,null),label:n.__("Normal State","masterstudy-lms-learning-management-system")},[_.HOVER]:{icon:(0,r.createElement)(K,null),label:n.__("Hovered State","masterstudy-lms-learning-management-system")},[_.ACTIVE]:{icon:(0,r.createElement)(K,null),label:n.__("Active State","masterstudy-lms-learning-management-system")},[_.FOCUS]:{icon:(0,r.createElement)(K,null),label:n.__("Focus State","masterstudy-lms-learning-management-system")}},q=(e,t)=>{let a=[];return a=e.length?X.filter((t=>e.includes(t.value))):X,a=a.filter((e=>e.value!==t)),{ICONS_MAP:Y,options:a}},[J,Q,ee,te,ae]=O(["hover-state","hover-state__selected","hover-state__selected__opened-menu","hover-state__menu","hover-state__menu__item"]),re=({stateOptions:e,currentState:t,onSelect:a})=>{const{isOpen:n,onOpen:o,onClose:s}=F(),l=j(s),{ICONS_MAP:i,options:m}=q(e,t);return(0,r.createElement)("div",{className:J,ref:l},(0,r.createElement)("div",{className:c()([Q],{[ee]:n}),onClick:o,title:i[t]?.label},i[t]?.icon),(0,r.createElement)(p,{condition:n},(0,r.createElement)("div",{className:te},m.map((({value:e,icon:t,label:n})=>(0,r.createElement)("div",{key:e,className:ae,title:n},t((()=>a(e)))))))))},ne=O("color-indicator"),oe=(0,s.memo)((({color:e,onChange:t})=>(0,r.createElement)("div",{className:ne},(0,r.createElement)(l.PanelColorSettings,{enableAlpha:!0,disableCustomColors:!1,__experimentalHasMultipleOrigins:!0,__experimentalIsRenderedInSidebar:!0,colorSettings:[{label:"",value:e,onChange:t}]}))));var se;function le(){return le=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e},le.apply(null,arguments)}var ie,ce,me=function(e){return r.createElement("svg",le({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 12 13"},e),se||(se=r.createElement("path",{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})))};function de(){return de=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e},de.apply(null,arguments)}var ue=function(e){return r.createElement("svg",de({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 14 15"},e),ie||(ie=r.createElement("g",{clipPath:"url(#state-hover_svg__a)"},r.createElement("path",{d:"M11.197 8.6 8.75 8.11V5.672a.583.583 0 0 0-.584-.584H7a.583.583 0 0 0-.584.584v2.916H5.25a.584.584 0 0 0-.522.845l1.75 3.5c.099.197.3.322.522.322h4.083a.583.583 0 0 0 .583-.583v-3.5a.58.58 0 0 0-.469-.572"}),r.createElement("path",{d:"M10.5 1.588h-7c-.644 0-1.167.524-1.167 1.167v4.083c0 .644.523 1.167 1.167 1.167h1.75V6.838H3.5V2.755h7v4.083h1.166V2.755c0-.643-.523-1.167-1.166-1.167"}))),ce||(ce=r.createElement("defs",null,r.createElement("clipPath",{id:"state-hover_svg__a"},r.createElement("path",{d:"M0 .422h14v14H0z"})))))};const ge=[{value:_.NORMAL,label:n.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,r.createElement)(me,{onClick:e})},{value:_.HOVER,label:n.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,r.createElement)(ue,{onClick:e})}],Ce={[_.NORMAL]:{icon:(0,r.createElement)(me,null),label:n.__("Normal State","masterstudy-lms-learning-management-system")},[_.HOVER]:{icon:(0,r.createElement)(ue,null),label:n.__("Hovered State","masterstudy-lms-learning-management-system")}},ye=O("hover-state"),pe=O("hover-state__selected"),he=O("hover-state__selected__opened-menu"),ve=O("has-changes"),be=O("hover-state__menu"),_e=O("hover-state__menu__item"),Ee=(0,s.memo)((e=>{const{hoverName:t,onChangeHoverName:a,fieldName:n}=e,{changedFieldsByName:o}=U(),l=o.get(n),{isOpen:i,onOpen:m,onClose:d}=F(),u=j(d),{ICONS_MAP:g,options:C}=(e=>{const t=(0,s.useMemo)((()=>ge.filter((t=>t.value!==e))),[e]);return{ICONS_MAP:Ce,options:t}})(t),y=(0,s.useCallback)((e=>{a(e),d()}),[a,d]);return(0,r.createElement)("div",{className:ye,ref:u},(0,r.createElement)("div",{className:c()([pe],{[he]:i,[ve]:l}),onClick:m,title:g[t]?.label},g[t]?.icon),(0,r.createElement)(p,{condition:i},(0,r.createElement)("div",{className:be},C.map((({value:e,icon:t,label:a})=>(0,r.createElement)("div",{key:e,className:_e,title:a},t((()=>y(e)))))))))})),fe={Desktop:{icon:"desktop",label:n.__("Desktop","masterstudy-lms-learning-management-system")},Tablet:{icon:"tablet",label:n.__("Tablet","masterstudy-lms-learning-management-system")},Mobile:{icon:"smartphone",label:n.__("Mobile","masterstudy-lms-learning-management-system")}},Se=[{value:E.DESKTOP,icon:"desktop",label:n.__("Desktop","masterstudy-lms-learning-management-system")},{value:E.TABLET,icon:"tablet",label:n.__("Tablet","masterstudy-lms-learning-management-system")},{value:E.MOBILE,icon:"smartphone",label:n.__("Mobile","masterstudy-lms-learning-management-system")}],Ne=O("device-picker"),we=O("device-picker__selected"),xe=O("device-picker__selected__opened-menu"),Be=O("device-picker__menu"),ke=O("device-picker__menu__item"),Te=()=>{const{isOpen:e,onOpen:t,onClose:a}=F(),{value:n,onChange:o}=(e=>{const t=L(),a=(0,R.useDispatch)();return{value:(0,s.useMemo)((()=>fe[t]),[t]),onChange:t=>{a("core/edit-site")&&a("core/edit-site").__experimentalSetPreviewDeviceType?a("core/edit-site").__experimentalSetPreviewDeviceType(t):a("core/edit-post")&&a("core/edit-post").__experimentalSetPreviewDeviceType?a("core/edit-post").__experimentalSetPreviewDeviceType(t):a("masterstudy/store").setDeviceType(t),e()}}})(a),l=(e=>(0,s.useMemo)((()=>Se.filter((t=>t.icon!==e))),[e]))(n.icon),i=j(a);return(0,r.createElement)("div",{className:Ne,ref:i},(0,r.createElement)(m.Dashicon,{className:c()([we],{[xe]:e}),icon:n.icon,size:16,onClick:t,title:n.label}),(0,r.createElement)(p,{condition:e},(0,r.createElement)("div",{className:Be},l.map((e=>(0,r.createElement)(m.Dashicon,{key:e.value,icon:e.icon,size:16,onClick:()=>o(e.value),className:ke,title:e.label}))))))},Me=O("reset-button"),He=({onReset:e})=>(0,r.createElement)(m.Dashicon,{icon:"undo",onClick:e,className:Me,size:16}),Pe=[{label:"PX",value:"px"},{label:"%",value:"%"},{label:"EM",value:"em"},{label:"REM",value:"rem"},{label:"VW",value:"vw"},{label:"VH",value:"vh"}],Ae=O("unit"),Oe=O("unit__single"),Re=O("unit__list"),Le=({name:e,isAdaptive:t})=>{const{isOpen:a,onOpen:n,onClose:o}=F(),{fieldName:s}=W(e,t),{value:l,onChange:i}=z(s),c=j(o);return(0,r.createElement)("div",{className:Ae,ref:c},(0,r.createElement)("div",{className:Oe,onClick:n},l),(0,r.createElement)(p,{condition:a},(0,r.createElement)("div",{className:Re},Pe.map((({value:e,label:t})=>(0,r.createElement)("div",{key:e,onClick:()=>(i(e),void o())},t))))))},Fe=O("popover-modal"),De=O("popover-modal__close dashicon dashicons dashicons-no-alt"),Ie=e=>{const{isOpen:t,onClose:a,popoverContent:n}=e;return(0,r.createElement)(p,{condition:t},(0,r.createElement)(m.Popover,{position:"middle left",onClose:a,className:Fe},n,(0,r.createElement)("span",{onClick:a,className:De})))},Ue=O("setting-label"),ze=O("setting-label__content"),We=e=>{const{label:t,isChanged:a=!1,onReset:n,showDevicePicker:o=!0,HoverStateControl:s=null,unitName:l,popoverContent:i=null,dependencies:c}=e,{isOpen:m,onClose:d,onToggle:u}=F();return $(c)?(0,r.createElement)("div",{className:Ue},(0,r.createElement)("div",{className:ze},(0,r.createElement)("div",{onClick:u},t),(0,r.createElement)(p,{condition:Boolean(i)},(0,r.createElement)(Ie,{isOpen:m,onClose:d,popoverContent:i})),(0,r.createElement)(p,{condition:o},(0,r.createElement)(Te,null)),(0,r.createElement)(p,{condition:Boolean(s)},s)),(0,r.createElement)(p,{condition:Boolean(l)},(0,r.createElement)(Le,{name:l,isAdaptive:o})),(0,r.createElement)(p,{condition:a},(0,r.createElement)(He,{onReset:n}))):null},Ve=O("suffix"),$e=()=>(0,r.createElement)("div",{className:Ve},(0,r.createElement)(m.Dashicon,{icon:"color-picker",size:16})),je=O("color-picker"),Ge=e=>{const{name:t,label:a,placeholder:n,dependencyMode:o,dependencies:s,isAdaptive:l=!1,hasHover:i=!1}=e,{fieldName:c,hoverName:d,onChangeHoverName:u}=W(t,l,i),{value:g,isChanged:C,onChange:y,onReset:h}=z(c);return $(s,o)?(0,r.createElement)("div",{className:je},(0,r.createElement)(p,{condition:Boolean(a)},(0,r.createElement)(We,{label:a,isChanged:C,onReset:h,showDevicePicker:l,HoverStateControl:(0,r.createElement)(p,{condition:i},(0,r.createElement)(Ee,{hoverName:d,onChangeHoverName:u,fieldName:c}))})),(0,r.createElement)(m.__experimentalInputControl,{prefix:(0,r.createElement)(oe,{color:g,onChange:y}),suffix:(0,r.createElement)($e,null),onChange:y,value:g,placeholder:n})):null},Ze=O("number-steppers"),Ke=O("indent-steppers"),Xe=O("indent-stepper-plus"),Ye=O("indent-stepper-minus"),qe=({onIncrement:e,onDecrement:t,withArrows:a=!1})=>a?(0,r.createElement)("span",{className:Ke},(0,r.createElement)("button",{onClick:e,className:Xe}),(0,r.createElement)("button",{onClick:t,className:Ye})):(0,r.createElement)("span",{className:Ze},(0,r.createElement)("button",{onClick:e},"+"),(0,r.createElement)("button",{onClick:t},"-")),[Je,Qe]=O(["indents","indents-control"]),et=({name:e,label:t,unitName:a,popoverContent:o,dependencyMode:l,dependencies:i,isAdaptive:c=!1})=>{const{fieldName:d}=W(e,c),{value:u,onResetSegmentedBox:g,hasChanges:C,handleInputIncrement:y,handleInputDecrement:h,updateDirectionsValues:v,lastFieldValue:b}=((e,t)=>{const{value:a,isChanged:r,onChange:n,onReset:o}=z(e),{onResetByFieldName:l,changedFieldsByName:i}=U(),c=r||i.get(t),m=e=>{n({...a,...e})},[d,u]=(0,s.useState)(!1);return{value:a,onResetSegmentedBox:()=>{o(),l.get(t)()},hasChanges:c,handleInputIncrement:e=>Number(a[e])+1,handleInputDecrement:e=>Number(a[e])-1,updateDirectionsValues:(e,t,a)=>{e?(u(!1),m({top:a,right:a,bottom:a,left:a})):(u(a),m({[t]:a}))},lastFieldValue:d}})(d,a),[_,E]=(0,s.useState)((()=>{const{left:e,right:t,top:a,bottom:r}=u;return""!==e&&e===t&&t===a&&a===r})),f=e=>{const[t,a]=Object.entries(e)[0];v(_,t,a)},S=e=>()=>{const t=y(e);v(_,e,String(t))},N=e=>()=>{const t=h(e);v(_,e,String(t))};return $(i,l)?(0,r.createElement)("div",{className:Je},(0,r.createElement)(p,{condition:Boolean(t)},(0,r.createElement)(We,{label:null!=t?t:"",isChanged:C,onReset:g,unitName:a,popoverContent:o,showDevicePicker:c})),(0,r.createElement)("div",{className:`${Qe} ${_?"active":""}`},(0,r.createElement)("div",null,(0,r.createElement)(m.__experimentalNumberControl,{value:u.top,onChange:e=>{f({top:e})},spinControls:"none",suffix:(0,r.createElement)(qe,{onIncrement:S("top"),onDecrement:N("top"),withArrows:!0})}),(0,r.createElement)("div",null,n.__("Top","masterstudy-lms-learning-management-system"))),(0,r.createElement)("div",null,(0,r.createElement)(m.__experimentalNumberControl,{value:u.right,onChange:e=>{f({right:e})},spinControls:"none",suffix:(0,r.createElement)(qe,{onIncrement:S("right"),onDecrement:N("right"),withArrows:!0})}),(0,r.createElement)("div",null,n.__("Right","masterstudy-lms-learning-management-system"))),(0,r.createElement)("div",null,(0,r.createElement)(m.__experimentalNumberControl,{value:u.bottom,onChange:e=>{f({bottom:e})},spinControls:"none",suffix:(0,r.createElement)(qe,{onIncrement:S("bottom"),onDecrement:N("bottom"),withArrows:!0})}),(0,r.createElement)("div",null,n.__("Bottom","masterstudy-lms-learning-management-system"))),(0,r.createElement)("div",null,(0,r.createElement)(m.__experimentalNumberControl,{value:u.left,onChange:e=>{f({left:e})},spinControls:"none",suffix:(0,r.createElement)(qe,{onIncrement:S("left"),onDecrement:N("left"),withArrows:!0})}),(0,r.createElement)("div",null,n.__("Left","masterstudy-lms-learning-management-system"))),(0,r.createElement)(m.Dashicon,{icon:"dashicons "+(_?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{_||!1===b||v(!0,"left",b),E((e=>!e))}}))):null},[tt,at,rt,nt]=O(["toggle-group-wrapper","toggle-group","toggle-group__toggle","toggle-group__active-toggle"]),ot=e=>{const{name:t,options:a,label:n,isAdaptive:o=!1,dependencyMode:s,dependencies:l}=e,{fieldName:i}=W(t,o),{value:m,isChanged:d,onChange:u,onReset:g}=z(i);return $(l,s)?(0,r.createElement)("div",{className:tt},(0,r.createElement)(p,{condition:Boolean(n)},(0,r.createElement)(We,{label:n,isChanged:d,onReset:g,showDevicePicker:o})),(0,r.createElement)("div",{className:at},a.map((e=>(0,r.createElement)("div",{key:e.value,className:c()([rt],{[nt]:e.value===m}),onClick:()=>u(e.value)},e.label))))):null},[st,lt,it,ct]=O(["border-control","border-control-solid","border-control-dashed","border-control-dotted"]),mt=e=>{const{label:t,borderStyleName:a,borderColorName:o,borderWidthName:l,dependencyMode:i,dependencies:m,isAdaptive:d=!1,hasHover:u=!1}=e,[g,C]=(0,s.useState)("Normal"),{fieldName:y,value:h,isChanged:v,onReset:b}=V(a,d,g),{fieldName:_,isChanged:E,onReset:f}=V(o,d,g),{fieldName:S,isChanged:N,onReset:w}=V(l,d,g);if(!$(m,i))return null;const x=v||E||N;return(0,r.createElement)("div",{className:c()([st],{"has-reset-button":x})},(0,r.createElement)(We,{label:t,isChanged:x,onReset:()=>{b(),f(),w()},showDevicePicker:d,HoverStateControl:(0,r.createElement)(p,{condition:u},(0,r.createElement)(re,{stateOptions:["Normal","Hover"],currentState:g,onSelect:C}))}),(0,r.createElement)(ot,{options:[{label:(0,r.createElement)("span",null,n.__("None","masterstudy-lms-learning-management-system")),value:"none"},{label:(0,r.createElement)("span",{className:lt}),value:"solid"},{label:(0,r.createElement)("span",{className:it},(0,r.createElement)("span",null)),value:"dashed"},{label:(0,r.createElement)("span",{className:ct},(0,r.createElement)("span",null,(0,r.createElement)("span",null))),value:"dotted"}],name:y}),(0,r.createElement)(p,{condition:"none"!==h},(0,r.createElement)(Ge,{name:_,placeholder:n.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(et,{name:S})))},dt=O("border-radius"),ut=O("border-radius-control"),gt=({name:e,label:t,unitName:a,popoverContent:n,dependencyMode:o,dependencies:l,isAdaptive:i=!1,hasHover:d=!1})=>{const{fieldName:u}=W(e,i,d),{value:g,onResetBorderRadius:C,hasChanges:y,handleInputIncrement:p,handleInputDecrement:h,updateDirectionsValues:v,lastFieldValue:b}=((e,t)=>{const[a,r]=(0,s.useState)(!1),{value:n,isChanged:o,onChange:l,onReset:i}=z(e),{onResetByFieldName:c,changedFieldsByName:m}=U(),d=o||m.get(t),u=e=>{l({...n,...e})};return{value:n,onResetBorderRadius:()=>{i(),c.get(t)()},hasChanges:d,handleInputIncrement:e=>Number(n[e])+1,handleInputDecrement:e=>Number(n[e])-1,updateDirectionsValues:(e,t,a)=>{e?(u({top:a,right:a,bottom:a,left:a}),r(!1)):(u({[t]:a}),r(a))},lastFieldValue:a}})(u,a),[_,E]=(0,s.useState)((()=>{const{left:e,right:t,top:a,bottom:r}=g;return""!==e&&e===t&&t===a&&a===r})),f=e=>{const[t,a]=Object.entries(e)[0];v(_,t,a)},S=e=>()=>{const t=p(e);v(_,e,String(t))},N=e=>()=>{const t=h(e);v(_,e,String(t))};return $(l,o)?(0,r.createElement)("div",{className:dt},(0,r.createElement)(We,{label:t,isChanged:y,onReset:C,unitName:a,popoverContent:n,showDevicePicker:i}),(0,r.createElement)("div",{className:c()([ut],{"has-reset-button":y,active:_})},(0,r.createElement)("div",{className:"number-control-top"},(0,r.createElement)(m.__experimentalNumberControl,{value:g.top,onChange:e=>{f({top:e})},spinControls:"none",suffix:(0,r.createElement)(qe,{onIncrement:S("top"),onDecrement:N("top"),withArrows:!0})})),(0,r.createElement)("div",{className:"number-control-right"},(0,r.createElement)(m.__experimentalNumberControl,{value:g.right,onChange:e=>{f({right:e})},spinControls:"none",suffix:(0,r.createElement)(qe,{onIncrement:S("right"),onDecrement:N("right"),withArrows:!0})})),(0,r.createElement)("div",{className:"number-control-left"},(0,r.createElement)(m.__experimentalNumberControl,{value:g.left,onChange:e=>{f({left:e})},spinControls:"none",suffix:(0,r.createElement)(qe,{onIncrement:S("left"),onDecrement:N("left"),withArrows:!0})})),(0,r.createElement)("div",{className:"number-control-bottom"},(0,r.createElement)(m.__experimentalNumberControl,{value:g.bottom,onChange:e=>{f({bottom:e})},spinControls:"none",suffix:(0,r.createElement)(qe,{onIncrement:S("bottom"),onDecrement:N("bottom"),withArrows:!0})})),(0,r.createElement)(m.Dashicon,{icon:"dashicons "+(_?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{_||!1===b||v(!0,"left",b),E((e=>!e))}}))):null},Ct=O("box-shadow-preset"),yt=({preset:e})=>(0,r.createElement)("div",{className:Ct},(0,r.createElement)("div",{style:{boxShadow:`${e.horizontal}px ${e.vertical}px ${e.blur}px ${e.spread}px rgba(0, 0, 0, 0.25) ${e.inset?"inset":""}`}})),pt=O("presets"),ht=O("presets__item-wrapper"),vt=O("presets__item-wrapper__preset"),bt=O("presets__item-wrapper__name"),_t=(0,s.memo)((e=>{const{presets:t,activePreset:a,onSelectPreset:n,PresetItem:o,detectIsActive:s,detectByIndex:l=!1}=e;return(0,r.createElement)("div",{className:pt},t.map((({name:e,...t},i)=>(0,r.createElement)("div",{key:i,className:c()([ht],{active:s(a,l?i:t)}),onClick:()=>n(t)},(0,r.createElement)("div",{className:vt},(0,r.createElement)(o,{preset:t})),(0,r.createElement)("span",{className:bt},e)))))})),Et=O("range-control"),ft=e=>{const{name:t,label:a,min:n,max:o,unitName:s,dependencyMode:l,dependencies:i,isAdaptive:c=!1}=e,{fieldName:d}=W(t,c),{value:u,onChange:g,onResetNumberField:C,hasChanges:y}=((e,t)=>{const{value:a,isChanged:r,onChange:n,onReset:o}=z(e),{onResetByFieldName:s,changedFieldsByName:l}=U();return{value:a,onChange:n,onResetNumberField:()=>{o(),s.get(t)()},hasChanges:r||l.get(t)}})(d,s);return $(i,l)?(0,r.createElement)("div",{className:Et},(0,r.createElement)(p,{condition:Boolean(a)},(0,r.createElement)(We,{label:a,isChanged:y,onReset:C,unitName:s,showDevicePicker:c})),(0,r.createElement)(m.RangeControl,{value:u,onChange:g,min:n,max:o})):null},St=O("switch"),Nt=e=>{const{name:t,label:a,dependencyMode:n,dependencies:o,isAdaptive:s=!1}=e,{fieldName:l}=W(t,s),{value:i,onChange:c}=z(l);return $(o,n)?(0,r.createElement)("div",{className:St,"data-has-label":Boolean(a).toString()},(0,r.createElement)(m.ToggleControl,{label:a,checked:i,onChange:c}),(0,r.createElement)(p,{condition:s},(0,r.createElement)(Te,null))):null},wt=O("box-shadow-settings"),xt=O("box-shadow-presets-title"),Bt=[{name:"Drop",horizontal:0,vertical:2,blur:2,spread:0,inset:!1},{name:"Glow",horizontal:0,vertical:4,blur:20,spread:0,inset:!1},{name:"Outline",horizontal:0,vertical:2,blur:10,spread:0,inset:!1},{name:"Sparse",horizontal:0,vertical:10,blur:50,spread:0,inset:!1}],kt=e=>{const{label:t,min:a,max:o,shadowColorName:l,shadowHorizontalName:i,shadowVerticalName:c,shadowBlurName:m,shadowSpreadName:d,shadowInsetName:u,popoverContent:g,dependencyMode:C,dependencies:y,isAdaptive:v=!1,hasHover:b=!1,presets:_=Bt}=e,[E,f]=(0,s.useState)("Normal"),{fieldName:S}=V(l,v,E),{fieldName:N}=V(i,v,E),{fieldName:w}=V(c,v,E),{fieldName:x}=V(m,v,E),{fieldName:B}=V(d,v,E),{fieldName:k}=V(u,v,E),{isChanged:T,onReset:M,onSelectPreset:H,activePreset:P}=((e,t,a,r,n,o)=>{const{setAttributes:l}=U(),{value:i,isChanged:c,onReset:m}=z(e),{value:d,isChanged:u,onReset:g}=z(t),{value:C,isChanged:y,onReset:p}=z(a),{value:h,isChanged:v,onReset:b}=z(r),{value:_,isChanged:E,onReset:f}=z(n),{value:S,isChanged:N,onReset:w}=z(o),x=c||u||y||v||E||N,B=(0,s.useCallback)((e=>{const{horizontal:s,vertical:i,blur:c,spread:m,inset:d}=e;l({[t]:s,[a]:i,[r]:c,[n]:m,[o]:d})}),[t,a,r,n,l,o]);return{activePreset:(0,s.useMemo)((()=>({horizontal:d,vertical:C,blur:h,spread:_,inset:null!=S&&S})),[d,C,h,_,S]),onReset:()=>{[m,g,p,b,f,w].forEach((e=>e()))},isChanged:x,onSelectPreset:B}})(S,N,w,x,B,k);return $(y,C)?(0,r.createElement)("div",{className:wt},(0,r.createElement)(We,{label:t,isChanged:T,onReset:M,popoverContent:g,showDevicePicker:v,HoverStateControl:(0,r.createElement)(p,{condition:b},(0,r.createElement)(re,{stateOptions:["Normal","Hover"],currentState:E,onSelect:f}))}),(0,r.createElement)(_t,{presets:_,onSelectPreset:H,activePreset:P,PresetItem:yt,detectIsActive:h,detectByIndex:!1}),(0,r.createElement)(Ge,{name:S,label:n.__("Color","masterstudy-lms-learning-management-system"),placeholder:"Select color"}),(0,r.createElement)(ft,{name:N,label:n.__("Horizontal Offset","masterstudy-lms-learning-management-system"),min:a,max:o}),(0,r.createElement)(ft,{name:w,label:n.__("Vertical Offset","masterstudy-lms-learning-management-system"),min:a,max:o}),(0,r.createElement)(ft,{name:x,label:n.__("Blur","masterstudy-lms-learning-management-system"),min:a,max:o}),(0,r.createElement)(ft,{name:B,label:n.__("Spread","masterstudy-lms-learning-management-system"),min:a,max:o}),(0,r.createElement)("div",{className:xt},n.__("Position","masterstudy-lms-learning-management-system")),(0,r.createElement)(Nt,{name:k,label:n.__("Inset","masterstudy-lms-learning-management-system")})):null},Tt=(O("input-field"),O("input-field-control"),O("number-field")),Mt=O("number-field-control"),Ht=e=>{const{name:t,label:a,unitName:n,help:o,popoverContent:s,dependencyMode:l,dependencies:i,isAdaptive:c=!1}=e,{fieldName:d}=W(t,c),{value:u,onResetNumberField:g,hasChanges:C,handleIncrement:y,handleDecrement:p,handleInputChange:h}=((e,t)=>{const{value:a,isChanged:r,onChange:n,onReset:o}=z(e),{onResetByFieldName:s,changedFieldsByName:l}=U(),i=r||l.get(t);return{value:a,onResetNumberField:()=>{o(),s.get(t)()},hasChanges:i,handleIncrement:()=>{n(a+1)},handleDecrement:()=>{n(a-1)},handleInputChange:e=>{const t=Number(""===e?0:e);n(t)}}})(d,n);return $(i,l)?(0,r.createElement)("div",{className:Tt},(0,r.createElement)(We,{label:a,isChanged:C,onReset:g,unitName:n,showDevicePicker:c,popoverContent:s}),(0,r.createElement)("div",{className:Mt},(0,r.createElement)(m.__experimentalNumberControl,{value:u,onChange:h,spinControls:"none",suffix:(0,r.createElement)(qe,{onIncrement:y,onDecrement:p})})),o&&(0,r.createElement)("small",null,o)):null},Pt=({className:e})=>(0,r.createElement)("div",{className:e},n.__("No options","masterstudy-lms-learning-management-system")),At=O("select__single-item"),Ot=O("select__container"),Rt=O("select__container__multi-item"),Lt=({multiple:e,value:t,options:a,onChange:n})=>{const{singleValue:o,multipleValue:l}=((e,t,a)=>({singleValue:(0,s.useMemo)((()=>t?null:a.find((t=>t.value===e))?.label),[t,e,a]),multipleValue:(0,s.useMemo)((()=>t?e:null),[t,e])}))(t,e,a);return(0,r.createElement)(p,{condition:e,fallback:(0,r.createElement)("div",{className:At},o)},(0,r.createElement)("div",{className:Ot},l?.map((e=>{const t=a.find((t=>t.value===e));return t?(0,r.createElement)("div",{key:t.value,className:Rt},(0,r.createElement)("div",null,t.label),(0,r.createElement)(m.Dashicon,{icon:"no-alt",onClick:()=>n(t.value),size:16})):null}))))},Ft=O("select"),Dt=O("select__select-box"),It=O("select__placeholder"),Ut=O("select__select-box-multiple"),zt=O("select__menu"),Wt=O("select__menu__options-container"),Vt=O("select__menu__item"),$t=e=>{const{options:t,multiple:a=!1,placeholder:n="Select",value:o,onSelect:l}=e,{isOpen:i,onToggle:d,onClose:u}=F(),g=j(u),C=((e,t,a)=>(0,s.useMemo)((()=>a&&Array.isArray(e)?t.filter((t=>!e.includes(t.value))):t.filter((t=>t.value!==e))),[e,t,a]))(o,t,a),y=((e,t,a,r)=>(0,s.useCallback)((n=>{if(t&&Array.isArray(e)){const t=e.includes(n)?e.filter((e=>e!==n)):[...e,n];a(t)}else a(n),r()}),[t,e,a,r]))(o,a,l,u),h=((e,t)=>(0,s.useMemo)((()=>t&&Array.isArray(e)?Boolean(e.length):Boolean(e)),[t,e]))(o,a),v=a&&Array.isArray(o)&&o?.length>0;return(0,r.createElement)("div",{className:Ft,ref:g},(0,r.createElement)("div",{className:c()([Dt],{[Ut]:v}),onClick:d},(0,r.createElement)(p,{condition:h,fallback:(0,r.createElement)("div",{className:It},n)},(0,r.createElement)(Lt,{onChange:y,options:t,multiple:a,value:o})),(0,r.createElement)(m.Dashicon,{icon:i?"arrow-up-alt2":"arrow-down-alt2",size:16,style:{color:"#4D5E6F"}})),(0,r.createElement)(p,{condition:i},(0,r.createElement)("div",{className:zt},(0,r.createElement)(p,{condition:Boolean(C.length),fallback:(0,r.createElement)(Pt,{className:Vt})},(0,r.createElement)("div",{className:Wt},C.map((e=>(0,r.createElement)("div",{key:e.value,onClick:()=>y(e.value),className:Vt},e.label))))))))},jt=O("setting-select"),Gt=e=>{const{name:t,options:a,label:n,multiple:o=!1,placeholder:s,isAdaptive:l=!1,dependencyMode:i,dependencies:c}=e,{fieldName:m}=W(t,l),{value:d,isChanged:u,onChange:g,onReset:C}=z(m);return $(c,i)?(0,r.createElement)("div",{className:jt},(0,r.createElement)(p,{condition:Boolean(n)},(0,r.createElement)(We,{label:n,isChanged:u,onReset:C,showDevicePicker:l})),(0,r.createElement)($t,{options:a,value:d,onSelect:g,multiple:o,placeholder:s})):null},Zt=O("row-select"),Kt=O("row-select__label"),Xt=O("row-select__control"),Yt=e=>{const{name:t,label:a,options:n,isAdaptive:o=!1}=e,{fieldName:s}=W(t,o),{isChanged:l,onReset:i}=z(s);return(0,r.createElement)("div",{className:Zt},(0,r.createElement)("div",{className:Kt},(0,r.createElement)("div",null,a),(0,r.createElement)(p,{condition:o},(0,r.createElement)(Te,null))),(0,r.createElement)("div",{className:Xt},(0,r.createElement)(Gt,{name:t,options:n,isAdaptive:o}),(0,r.createElement)(p,{condition:l},(0,r.createElement)(He,{onReset:i}))))},qt=O("typography-select"),Jt=O("typography-select-label"),Qt=e=>{const{name:t,label:a,options:n,isAdaptive:o=!1}=e,{fieldName:s}=W(t,o),{isChanged:l,onReset:i}=z(s);return(0,r.createElement)("div",{className:qt},(0,r.createElement)("div",{className:Jt},(0,r.createElement)("div",null,a),(0,r.createElement)(p,{condition:o},(0,r.createElement)(Te,null))),(0,r.createElement)(Gt,{name:t,options:n,isAdaptive:o}),(0,r.createElement)(p,{condition:l},(0,r.createElement)(He,{onReset:i})))},ea=O("typography"),ta=e=>{const{fontSizeName:t,fontWeightName:a,textTransformName:o,fontStyleName:s,textDecorationName:l,lineHeightName:i,letterSpacingName:c,wordSpacingName:m,fontSizeUnitName:d,lineHeightUnitName:u,letterSpacingUnitName:g,wordSpacingUnitName:C,dependencyMode:y,dependencies:p,isAdaptive:h=!1}=e,{fontWeightOptions:v,textTransformOptions:b,fontStyleOptions:_,textDecorationOptions:E}={fontWeightOptions:[{label:n.__("100 (Thin)","masterstudy-lms-learning-management-system"),value:"100"},{label:n.__("200 (Extra Light)","masterstudy-lms-learning-management-system"),value:"200"},{label:n.__("300 (Light)","masterstudy-lms-learning-management-system"),value:"300"},{label:n.__("400 (Normal)","masterstudy-lms-learning-management-system"),value:"400"},{label:n.__("500 (Medium)","masterstudy-lms-learning-management-system"),value:"500"},{label:n.__("600 (Semi Bold)","masterstudy-lms-learning-management-system"),value:"600"},{label:n.__("700 (Bold)","masterstudy-lms-learning-management-system"),value:"700"},{label:n.__("800 (Extra Bold)","masterstudy-lms-learning-management-system"),value:"800"},{label:n.__("900 (Extra)","masterstudy-lms-learning-management-system"),value:"900"}],textTransformOptions:[{label:n.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:n.__("Uppercase","masterstudy-lms-learning-management-system"),value:"uppercase"},{label:n.__("Lowercase","masterstudy-lms-learning-management-system"),value:"lowercase"},{label:n.__("Capitalize","masterstudy-lms-learning-management-system"),value:"capitalize"},{label:n.__("Normal","masterstudy-lms-learning-management-system"),value:"none"}],fontStyleOptions:[{label:n.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:n.__("Normal","masterstudy-lms-learning-management-system"),value:"none"},{label:n.__("Italic","masterstudy-lms-learning-management-system"),value:"italic"},{label:n.__("Oblique","masterstudy-lms-learning-management-system"),value:"oblique"}],textDecorationOptions:[{label:n.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:n.__("Underline","masterstudy-lms-learning-management-system"),value:"underline"},{label:n.__("Line Through","masterstudy-lms-learning-management-system"),value:"line-through"},{label:n.__("None","masterstudy-lms-learning-management-system"),value:"none"}]};return $(p,y)?(0,r.createElement)("div",{className:ea},(0,r.createElement)(ft,{name:t,label:n.__("Size","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:d,isAdaptive:h}),(0,r.createElement)(Qt,{name:a,label:n.__("Weight","masterstudy-lms-learning-management-system"),options:v}),(0,r.createElement)(Qt,{name:o,label:n.__("Transform","masterstudy-lms-learning-management-system"),options:b}),(0,r.createElement)(Qt,{name:s,label:n.__("Style","masterstudy-lms-learning-management-system"),options:_}),(0,r.createElement)(Qt,{name:l,label:n.__("Decoration","masterstudy-lms-learning-management-system"),options:E}),(0,r.createElement)(ft,{name:i,label:n.__("Line Height","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:u,isAdaptive:h}),(0,r.createElement)(ft,{name:c,label:n.__("Letter Spacing","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:g,isAdaptive:h}),m&&(0,r.createElement)(ft,{name:m,label:n.__("Word Spacing","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:C,isAdaptive:h})):null},aa=(O("file-upload"),O("file-upload__wrap"),O("file-upload__image"),O("file-upload__remove"),O("file-upload__replace"),(0,s.createContext)({activeTab:0,setActiveTab:()=>{}})),ra=()=>{const e=(0,s.useContext)(aa);if(!e)throw new Error("useTabs should be used inside Tabs");return e},na=({children:e})=>{const[t,a]=(0,s.useState)(0);return(0,r.createElement)(aa.Provider,{value:{activeTab:t,setActiveTab:a}},(0,r.createElement)("div",{className:`active-tab-${t}`},e))},oa=O("tab-list"),sa=({children:e})=>(0,r.createElement)("div",{className:oa},s.Children.map(e,((e,t)=>(0,s.cloneElement)(e,{index:t})))),la=O("tab"),ia=O("tab-active"),ca=O("content"),ma=({index:e,title:t,icon:a})=>{const{activeTab:n,setActiveTab:o}=ra();return(0,r.createElement)("div",{className:c()([la],{[ia]:n===e}),onClick:()=>o(e)},(0,r.createElement)("div",{className:ca},(0,r.createElement)("div",null,a),(0,r.createElement)("div",null,t)))},da=({children:e})=>(0,r.createElement)("div",null,s.Children.map(e,((e,t)=>(0,s.cloneElement)(e,{index:t})))),ua=O("tab-panel"),ga=({index:e,children:t})=>{const{activeTab:a}=ra();return a===e?(0,r.createElement)("div",{className:ua},t):null},Ca=({generalTab:e,styleTab:t,advancedTab:a})=>(0,r.createElement)(na,null,(0,r.createElement)(sa,null,(0,r.createElement)(ma,{title:n.__("General","masterstudy-lms-learning-management-system"),icon:(0,r.createElement)(m.Dashicon,{icon:"layout"})}),(0,r.createElement)(ma,{title:n.__("Style","masterstudy-lms-learning-management-system"),icon:(0,r.createElement)(m.Dashicon,{icon:"admin-appearance"})}),(0,r.createElement)(ma,{title:n.__("Advanced","masterstudy-lms-learning-management-system"),icon:(0,r.createElement)(m.Dashicon,{icon:"admin-settings"})})),(0,r.createElement)(da,null,(0,r.createElement)(ga,null,e),(0,r.createElement)(ga,null,t),(0,r.createElement)(ga,null,a)));window.ReactDOM;"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement;function ya(e){const t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function pa(e){return"nodeType"in e}function ha(e){var t,a;return e?ya(e)?e:pa(e)&&null!=(t=null==(a=e.ownerDocument)?void 0:a.defaultView)?t:window:window}function va(e){const{Document:t}=ha(e);return e instanceof t}function ba(e){return!ya(e)&&e instanceof ha(e).HTMLElement}function _a(e){return e instanceof ha(e).SVGElement}function Ea(e){return e?ya(e)?e.document:pa(e)?va(e)?e:ba(e)||_a(e)?e.ownerDocument:document:document:document}function fa(e){return function(t){for(var a=arguments.length,r=new Array(a>1?a-1:0),n=1;n<a;n++)r[n-1]=arguments[n];return r.reduce(((t,a)=>{const r=Object.entries(a);for(const[a,n]of r){const r=t[a];null!=r&&(t[a]=r+e*n)}return t}),{...t})}}const Sa=fa(-1);function Na(e){if(function(e){if(!e)return!1;const{TouchEvent:t}=ha(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:a}=e.touches[0];return{x:t,y:a}}if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:a}=e.changedTouches[0];return{x:t,y:a}}}return function(e){return"clientX"in e&&"clientY"in e}(e)?{x:e.clientX,y:e.clientY}:null}var wa;!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(wa||(wa={}));const xa=Object.freeze({x:0,y:0});var Ba,ka,Ta,Ma;!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(Ba||(Ba={}));class Ha{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach((e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)}))},this.target=e}add(e,t,a){var r;null==(r=this.target)||r.addEventListener(e,t,a),this.listeners.push([e,t,a])}}function Pa(e,t){const a=Math.abs(e.x),r=Math.abs(e.y);return"number"==typeof t?Math.sqrt(a**2+r**2)>t:"x"in t&&"y"in t?a>t.x&&r>t.y:"x"in t?a>t.x:"y"in t&&r>t.y}function Aa(e){e.preventDefault()}function Oa(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(ka||(ka={})),(Ma=Ta||(Ta={})).Space="Space",Ma.Down="ArrowDown",Ma.Right="ArrowRight",Ma.Left="ArrowLeft",Ma.Up="ArrowUp",Ma.Esc="Escape",Ma.Enter="Enter";Ta.Space,Ta.Enter,Ta.Esc,Ta.Space,Ta.Enter;function Ra(e){return Boolean(e&&"distance"in e)}function La(e){return Boolean(e&&"delay"in e)}class Fa{constructor(e,t,a){var r;void 0===a&&(a=function(e){const{EventTarget:t}=ha(e);return e instanceof t?e:Ea(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;const{event:n}=e,{target:o}=n;this.props=e,this.events=t,this.document=Ea(o),this.documentListeners=new Ha(this.document),this.listeners=new Ha(a),this.windowListeners=new Ha(ha(o)),this.initialCoordinates=null!=(r=Na(n))?r:xa,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:a}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),this.windowListeners.add(ka.Resize,this.handleCancel),this.windowListeners.add(ka.DragStart,Aa),this.windowListeners.add(ka.VisibilityChange,this.handleCancel),this.windowListeners.add(ka.ContextMenu,Aa),this.documentListeners.add(ka.Keydown,this.handleKeydown),t){if(null!=a&&a({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(La(t))return void(this.timeoutId=setTimeout(this.handleStart,t.delay));if(Ra(t))return}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handleStart(){const{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(ka.Click,Oa,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(ka.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;const{activated:a,initialCoordinates:r,props:n}=this,{onMove:o,options:{activationConstraint:s}}=n;if(!r)return;const l=null!=(t=Na(e))?t:xa,i=Sa(r,l);if(!a&&s){if(Ra(s)){if(null!=s.tolerance&&Pa(i,s.tolerance))return this.handleCancel();if(Pa(i,s.distance))return this.handleStart()}return La(s)&&Pa(i,s.tolerance)?this.handleCancel():void 0}e.cancelable&&e.preventDefault(),o(l)}handleEnd(){const{onEnd:e}=this.props;this.detach(),e()}handleCancel(){const{onCancel:e}=this.props;this.detach(),e()}handleKeydown(e){e.code===Ta.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}const Da={move:{name:"pointermove"},end:{name:"pointerup"}};(class extends Fa{constructor(e){const{event:t}=e,a=Ea(t.target);super(e,Da,a)}}).activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:a}=e,{onActivation:r}=t;return!(!a.isPrimary||0!==a.button||(null==r||r({event:a}),0))}}];const Ia={move:{name:"mousemove"},end:{name:"mouseup"}};var Ua;!function(e){e[e.RightClick=2]="RightClick"}(Ua||(Ua={})),class extends Fa{constructor(e){super(e,Ia,Ea(e.event.target))}}.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:a}=e,{onActivation:r}=t;return a.button!==Ua.RightClick&&(null==r||r({event:a}),!0)}}];const za={move:{name:"touchmove"},end:{name:"touchend"}};var Wa,Va,$a,ja,Ga;(class extends Fa{constructor(e){super(e,za)}static setup(){return window.addEventListener(za.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(za.move.name,e)};function e(){}}}).activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:a}=e,{onActivation:r}=t;const{touches:n}=a;return!(n.length>1||(null==r||r({event:a}),0))}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(Wa||(Wa={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(Va||(Va={})),Ba.Backward,Ba.Forward,Ba.Backward,Ba.Forward,function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}($a||($a={})),function(e){e.Optimized="optimized"}(ja||(ja={})),$a.WhileDragging,ja.Optimized,Map,function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(Ga||(Ga={})),Ta.Down,Ta.Right,Ta.Up,Ta.Left,n.__("Lectures","masterstudy-lms-learning-management-system"),n.__("Duration","masterstudy-lms-learning-management-system"),n.__("Views","masterstudy-lms-learning-management-system"),n.__("Level","masterstudy-lms-learning-management-system"),n.__("Members","masterstudy-lms-learning-management-system"),n.__("Empty","masterstudy-lms-learning-management-system"),O("sortable__item"),O("sortable__item__disabled"),O("sortable__item__content"),O("sortable__item__content__drag-item"),O("sortable__item__content__drag-item__disabled"),O("sortable__item__content__title"),O("sortable__item__control"),O("sortable__item__icon"),O("nested-sortable"),O("nested-sortable__item"),O("sortable");const Za=O("accordion"),Ka=O("accordion__header"),Xa=O("accordion__header-flex"),Ya=O("accordion__content"),qa=O("accordion__icon"),Ja=O("accordion__title"),Qa=O("accordion__title-disabled"),er=O("accordion__indicator"),tr=O("accordion__controls"),ar=O("accordion__controls-disabled"),rr=({title:e,children:t,accordionFields:a,switchName:n,visible:o=!0,isDefaultOpen:l=!1})=>{const{isOpen:i,onToggle:d,disabled:u,onReset:g,hasChanges:C,onClose:y}=((e,t,a)=>{var r;const{isOpen:n,onToggle:o,onClose:s}=F(t),{defaultValues:l,attributes:i,setAttributes:c}=U(),m=((e,t,a)=>{for(const r of a)if(!h(e[r],t[r]))return!0;return!1})(l,i,e);return{isOpen:n,onToggle:o,disabled:!(null===(r=i[a])||void 0===r||r),hasChanges:m,onReset:t=>{t.stopPropagation(),c(e.reduce(((e,t)=>(e[t]=l[t],e)),{}))},onClose:s}})(a,l,n);return((e,t)=>{const{attributes:a}=U(),r=!a[t];(0,s.useEffect)((()=>{r&&e()}),[r,e])})(y,n),o?(0,r.createElement)("div",{className:Za},(0,r.createElement)("div",{className:Ka},(0,r.createElement)("div",{className:Xa,onClick:u?null:d},(0,r.createElement)("div",{className:c()(Ja,{[Qa]:u,"with-switch":Boolean(n)})},(0,r.createElement)("div",null,e),(0,r.createElement)(p,{condition:C&&!u},(0,r.createElement)("div",{className:er}))),(0,r.createElement)("div",{className:c()(tr,{[ar]:u})},(0,r.createElement)(m.Dashicon,{icon:i?"arrow-up-alt2":"arrow-down-alt2",className:qa,size:16}))),(0,r.createElement)(p,{condition:Boolean(n)},(0,r.createElement)(Nt,{name:n})),(0,r.createElement)(p,{condition:C&&!u},(0,r.createElement)(He,{onReset:g}))),i&&(0,r.createElement)("div",{className:Ya},t)):null};O("preset-picker"),O("preset-picker__label"),O("preset-picker__remove"),O("preset-picker__presets-list"),O("preset-picker__presets-list__item"),O("preset-picker__presets-list__item__preset"),O("preset-picker__presets-list__item__preset-active");const nr={coursesCategoryPerRow:4,coursesCategoryPerRowTablet:2,coursesCategoryPerRowMobile:1,coursesCategoryValues:[],coursesCategoryWrap:!0,coursesCategoryAlign:"center"},or=Object.keys(nr),sr={...nr},lr={coursesCategoryMargin:P,coursesCategoryMarginTablet:P,coursesCategoryMarginMobile:P,coursesCategoryMarginUnit:"px",coursesCategoryMarginUnitTablet:"px",coursesCategoryMarginUnitMobile:"px",coursesCategoryPadding:P,coursesCategoryPaddingTablet:P,coursesCategoryPaddingMobile:{top:"0",right:"15",bottom:"0",left:"15"},coursesCategoryPaddingUnit:"px",coursesCategoryPaddingUnitTablet:"px",coursesCategoryPaddingUnitMobile:"px",coursesCategoryGap:30,coursesCategoryGapTablet:null,coursesCategoryGapMobile:null,coursesCategoryGapUnit:"px",coursesCategoryGapUnitTablet:"px",coursesCategoryGapUnitMobile:"px",coursesCategoryBackground:"",coursesCategoryBorderStyle:"none",coursesCategoryBorderStyleTablet:"",coursesCategoryBorderStyleMobile:"",coursesCategoryBorderColor:"",coursesCategoryBorderColorTablet:"",coursesCategoryBorderColorMobile:"",coursesCategoryBorderWidth:P,coursesCategoryBorderWidthTablet:P,coursesCategoryBorderWidthMobile:P,coursesCategoryBorderWidthUnit:"px",coursesCategoryBorderWidthUnitTablet:"px",coursesCategoryBorderWidthUnitMobile:"px",coursesCategoryBorderRadius:P,coursesCategoryBorderRadiusTablet:P,coursesCategoryBorderRadiusMobile:P,coursesCategoryBorderRadiusUnit:"px",coursesCategoryBorderRadiusUnitTablet:"px",coursesCategoryBorderRadiusUnitMobile:"px",coursesCategoryWidth:"alignauto",coursesCategoryMaxWidth:1200,coursesCategoryMaxWidthUnit:"px",coursesCategoryZIndex:null,coursesCategoryZIndexTablet:null,coursesCategoryZIndexMobile:null},ir=Object.keys(lr),cr={coursesCategoryCardFontSize:16,coursesCategoryCardFontSizeTablet:null,coursesCategoryCardFontSizeMobile:null,coursesCategoryCardFontSizeUnit:"px",coursesCategoryCardFontSizeUnitTablet:"px",coursesCategoryCardFontSizeUnitMobile:"px",coursesCategoryCardFontWeight:"500",coursesCategoryCardTextTransform:"inherit",coursesCategoryCardFontStyle:"inherit",coursesCategoryCardTextDecoration:"inherit",coursesCategoryCardLineHeight:20,coursesCategoryCardLineHeightTablet:null,coursesCategoryCardLineHeightMobile:null,coursesCategoryCardLineHeightUnit:"px",coursesCategoryCardLineHeightUnitTablet:"px",coursesCategoryCardLineHeightUnitMobile:"px",coursesCategoryCardLetterSpacing:0,coursesCategoryCardLetterSpacingTablet:null,coursesCategoryCardLetterSpacingMobile:null,coursesCategoryCardLetterSpacingUnit:"px",coursesCategoryCardLetterSpacingUnitTablet:"px",coursesCategoryCardLetterSpacingUnitMobile:"px",coursesCategoryCardWordSpacing:0,coursesCategoryCardWordSpacingTablet:null,coursesCategoryCardWordSpacingMobile:null,coursesCategoryCardWordSpacingUnit:"px",coursesCategoryCardWordSpacingUnitTablet:"px",coursesCategoryCardWordSpacingUnitMobile:"px",coursesCategoryCardColor:"#001931",coursesCategoryCardColorHover:"#ffffff",coursesCategoryCardIconColor:"#4D5E6F",coursesCategoryCardIconColorHover:"#ffffff",coursesCategoryCardIconFontSize:30,coursesCategoryCardIconFontSizeTablet:null,coursesCategoryCardIconFontSizeMobile:null,coursesCategoryCardIconFontSizeUnit:"px",coursesCategoryCardIconFontSizeUnitTablet:"px",coursesCategoryCardIconFontSizeUnitMobile:"px",coursesCategoryCardBackground:"#EEF1F7",coursesCategoryCardBackgroundHover:"#227AFF",coursesCategoryCardImageBackground:"#00000080",coursesCategoryCardImageBackgroundHover:"#0E171EE5",coursesCategoryCardCountFontSize:16,coursesCategoryCardCountFontSizeTablet:null,coursesCategoryCardCountFontSizeMobile:null,coursesCategoryCardCountFontSizeUnit:"px",coursesCategoryCardCountFontSizeUnitTablet:"px",coursesCategoryCardCountFontSizeUnitMobile:"px",coursesCategoryCardCountFontWeight:"400",coursesCategoryCardCountTextTransform:"inherit",coursesCategoryCardCountFontStyle:"inherit",coursesCategoryCardCountTextDecoration:"inherit",coursesCategoryCardCountLineHeight:22,coursesCategoryCardCountLineHeightTablet:null,coursesCategoryCardCountLineHeightMobile:null,coursesCategoryCardCountLineHeightUnit:"px",coursesCategoryCardCountLineHeightUnitTablet:"px",coursesCategoryCardCountLineHeightUnitMobile:"px",coursesCategoryCardCountLetterSpacing:0,coursesCategoryCardCountLetterSpacingTablet:null,coursesCategoryCardCountLetterSpacingMobile:null,coursesCategoryCardCountLetterSpacingUnit:"px",coursesCategoryCardCountLetterSpacingUnitTablet:"px",coursesCategoryCardCountLetterSpacingUnitMobile:"px",coursesCategoryCardCountWordSpacing:0,coursesCategoryCardCountWordSpacingTablet:null,coursesCategoryCardCountWordSpacingMobile:null,coursesCategoryCardCountWordSpacingUnit:"px",coursesCategoryCardCountWordSpacingUnitTablet:"px",coursesCategoryCardCountWordSpacingUnitMobile:"px",coursesCategoryCardCountColor:"#001931",coursesCategoryCardCountColorHover:"#001931",coursesCategoryCardBorderStyle:"solid",coursesCategoryCardBorderStyleTablet:"",coursesCategoryCardBorderStyleMobile:"",coursesCategoryCardBorderColor:"#DBE0E9",coursesCategoryCardBorderColorTablet:"",coursesCategoryCardBorderColorMobile:"",coursesCategoryCardBorderWidth:{top:"1",right:"1",bottom:"1",left:"1"},coursesCategoryCardBorderWidthTablet:P,coursesCategoryCardBorderWidthMobile:P,coursesCategoryCardBorderWidthUnit:"px",coursesCategoryCardBorderWidthUnitTablet:"px",coursesCategoryCardBorderWidthUnitMobile:"px",coursesCategoryCardBorderStyleHover:"solid",coursesCategoryCardBorderStyleHoverTablet:"",coursesCategoryCardBorderStyleHoverMobile:"",coursesCategoryCardBorderColorHover:"#227AFF",coursesCategoryCardBorderColorHoverTablet:"",coursesCategoryCardBorderColorHoverMobile:"",coursesCategoryCardBorderWidthHover:{top:"1",right:"1",bottom:"1",left:"1"},coursesCategoryCardBorderWidthHoverTablet:P,coursesCategoryCardBorderWidthHoverMobile:P,coursesCategoryCardBorderRadius:{top:"100",right:"100",bottom:"100",left:"100"},coursesCategoryCardBorderRadiusTablet:P,coursesCategoryCardBorderRadiusMobile:P,coursesCategoryCardBorderRadiusUnit:"px",coursesCategoryCardBorderRadiusUnitTablet:"px",coursesCategoryCardBorderRadiusUnitMobile:"px",coursesCategoryCardMargin:P,coursesCategoryCardMarginTablet:P,coursesCategoryCardMarginMobile:P,coursesCategoryCardMarginUnit:"px",coursesCategoryCardMarginUnitTablet:"px",coursesCategoryCardMarginUnitMobile:"px",coursesCategoryCardPadding:{top:"16",right:"32",bottom:"16",left:"32"},coursesCategoryCardPaddingTablet:P,coursesCategoryCardPaddingMobile:P,coursesCategoryCardPaddingUnit:"px",coursesCategoryCardPaddingUnitTablet:"px",coursesCategoryCardPaddingUnitMobile:"px",coursesCategoryCardShadowColor:"",coursesCategoryCardShadowColorTablet:"",coursesCategoryCardShadowColorMobile:"",coursesCategoryCardShadowHorizontal:8,coursesCategoryCardShadowHorizontalTablet:null,coursesCategoryCardShadowHorizontalMobile:null,coursesCategoryCardShadowVertical:0,coursesCategoryCardShadowVerticalTablet:null,coursesCategoryCardShadowVerticalMobile:null,coursesCategoryCardShadowBlur:20,coursesCategoryCardShadowBlurTablet:null,coursesCategoryCardShadowBlurMobile:null,coursesCategoryCardShadowSpread:0,coursesCategoryCardShadowSpreadTablet:null,coursesCategoryCardShadowSpreadMobile:null,coursesCategoryCardShadowInset:!1,coursesCategoryCardShadowInsetTablet:!1,coursesCategoryCardShadowInsetMobile:!1,coursesCategoryCardShadowColorHover:"",coursesCategoryCardShadowColorHoverTablet:"",coursesCategoryCardShadowColorHoverMobile:"",coursesCategoryCardShadowHorizontalHover:8,coursesCategoryCardShadowHorizontalHoverTablet:null,coursesCategoryCardShadowHorizontalHoverMobile:null,coursesCategoryCardShadowVerticalHover:0,coursesCategoryCardShadowVerticalHoverTablet:null,coursesCategoryCardShadowVerticalHoverMobile:null,coursesCategoryCardShadowBlurHover:20,coursesCategoryCardShadowBlurHoverTablet:null,coursesCategoryCardShadowBlurHoverMobile:null,coursesCategoryCardShadowSpreadHover:0,coursesCategoryCardShadowSpreadHoverTablet:null,coursesCategoryCardShadowSpreadHoverMobile:null,coursesCategoryCardShadowInsetHover:!1,coursesCategoryCardShadowInsetHoverTablet:!1,coursesCategoryCardShadowInsetHoverMobile:!1},mr=Object.keys(cr),dr={...sr,...{...lr,...cr}},ur=new Map([["coursesCategoryAlign",{}],["coursesCategoryMargin",{unit:"coursesCategoryMarginUnit",isAdaptive:!0}],["coursesCategoryPadding",{unit:"coursesCategoryPaddingUnit",isAdaptive:!0}],["coursesCategoryGap",{unit:"coursesCategoryGapUnit",isAdaptive:!0}],["coursesCategoryBackground",{}],["coursesCategoryBorderStyle",{isAdaptive:!0}],["coursesCategoryBorderColor",{isAdaptive:!0}],["coursesCategoryBorderWidth",{isAdaptive:!0,unit:"coursesCategoryBorderWidthUnit"}],["coursesCategoryBorderRadius",{isAdaptive:!0,unit:"coursesCategoryBorderRadiusUnit"}],["coursesCategoryMaxWidth",{unit:"coursesCategoryMaxWidthUnit"}],["coursesCategoryZIndex",{isAdaptive:!0}],["coursesCategoryCardFontSize",{unit:"coursesCategoryCardFontSizeUnit",isAdaptive:!0}],["coursesCategoryCardFontWeight",{}],["coursesCategoryCardTextTransform",{}],["coursesCategoryCardFontStyle",{}],["coursesCategoryCardTextDecoration",{}],["coursesCategoryCardLineHeight",{unit:"coursesCategoryCardLineHeightUnit",isAdaptive:!0}],["coursesCategoryCardLetterSpacing",{unit:"coursesCategoryCardLetterSpacingUnit",isAdaptive:!0}],["coursesCategoryCardWordSpacing",{unit:"coursesCategoryCardWordSpacingUnit",isAdaptive:!0}],["coursesCategoryCardBackground",{hasHover:!0}],["coursesCategoryCardImageBackground",{hasHover:!0}],["coursesCategoryCardColor",{hasHover:!0}],["coursesCategoryCardIconColor",{hasHover:!0}],["coursesCategoryCardIconFontSize",{unit:"coursesCategoryCardIconFontSizeUnit",isAdaptive:!0}],["coursesCategoryCardCountFontSize",{unit:"coursesCategoryCardCountFontSizeUnit",isAdaptive:!0}],["coursesCategoryCardCountFontWeight",{}],["coursesCategoryCardCountTextTransform",{}],["coursesCategoryCardCountFontStyle",{}],["coursesCategoryCardCountTextDecoration",{}],["coursesCategoryCardCountLineHeight",{unit:"coursesCategoryCardCountLineHeightUnit",isAdaptive:!0}],["coursesCategoryCardCountLetterSpacing",{unit:"coursesCategoryCardCountLetterSpacingUnit",isAdaptive:!0}],["coursesCategoryCardCountWordSpacing",{unit:"coursesCategoryCardCountWordSpacingUnit",isAdaptive:!0}],["coursesCategoryCardCountColor",{hasHover:!0}],["coursesCategoryCardBorderStyle",{isAdaptive:!0,hasHover:!0}],["coursesCategoryCardBorderColor",{isAdaptive:!0,hasHover:!0}],["coursesCategoryCardBorderWidth",{isAdaptive:!0,unit:"coursesCategoryCardBorderWidthUnit",hasHover:!0}],["coursesCategoryCardBorderRadius",{isAdaptive:!0,unit:"coursesCategoryCardBorderRadiusUnit"}],["coursesCategoryCardMargin",{unit:"coursesCategoryCardMarginUnit",isAdaptive:!0}],["coursesCategoryCardPadding",{unit:"coursesCategoryCardPaddingUnit",isAdaptive:!0}]]),gr={classic:{coursesCategoryGap:30,coursesCategoryBackground:"",coursesCategoryPadding:{top:"",right:"",bottom:"",left:""},coursesCategoryCardPadding:{top:"16",right:"32",bottom:"16",left:"32"},coursesCategoryCardBorderRadius:{top:"100",right:"100",bottom:"100",left:"100"},coursesCategoryCardFontSize:16,coursesCategoryCardFontWeight:"500",coursesCategoryCardLineHeight:20,coursesCategoryCardColor:"#001931",coursesCategoryCardColorHover:"#ffffff",coursesCategoryCardIconColor:"#4D5E6F",coursesCategoryCardIconFontSize:30,coursesCategoryCardBackground:"#EEF1F7",coursesCategoryCardBackgroundHover:"#227AFF",coursesCategoryCardImageBackground:"",coursesCategoryCardImageBackgroundHover:"",coursesCategoryCardCountColor:"",coursesCategoryCardBorderStyle:"solid",coursesCategoryCardBorderColor:"#DBE0E9",coursesCategoryCardBorderStyleHover:"solid"},colorful:{coursesCategoryGap:15,coursesCategoryBackground:"",coursesCategoryPadding:{top:"",right:"",bottom:"",left:""},coursesCategoryCardPadding:{top:"45",right:"15",bottom:"45",left:"15"},coursesCategoryCardBorderRadius:{top:"10",right:"10",bottom:"10",left:"10"},coursesCategoryCardFontSize:18,coursesCategoryCardFontWeight:"600",coursesCategoryCardLineHeight:24,coursesCategoryCardColor:"#ffffff",coursesCategoryCardColorHover:"#ffffff",coursesCategoryCardIconColor:"#ffffff",coursesCategoryCardIconFontSize:64,coursesCategoryCardBackground:"#EEF1F7",coursesCategoryCardBackgroundHover:"#EEF1F7",coursesCategoryCardImageBackground:"",coursesCategoryCardCountColor:"",coursesCategoryCardBorderStyle:"none",coursesCategoryCardBorderStyleHover:"none",coursesCategoryCardBorderColor:"transparent"},sleek:{coursesCategoryGap:30,coursesCategoryBackground:"#EEF1F7",coursesCategoryPadding:{top:"100",right:"40",bottom:"100",left:"40"},coursesCategoryCardPadding:{top:"25",right:"25",bottom:"25",left:"25"},coursesCategoryCardBorderRadius:{top:"20",right:"20",bottom:"20",left:"20"},coursesCategoryCardFontSize:15,coursesCategoryCardFontWeight:"800",coursesCategoryCardLineHeight:20,coursesCategoryCardColor:"#001931",coursesCategoryCardColorHover:"#001931",coursesCategoryCardIconColor:"#4D5E6F",coursesCategoryCardBackground:"#ffffff",coursesCategoryCardBackgroundHover:"#ffffff",coursesCategoryCardImageBackground:"",coursesCategoryCardImageBackgroundHover:"",coursesCategoryCardIconFontSize:30,coursesCategoryCardCountColor:"",coursesCategoryCardBorderStyle:"solid",coursesCategoryCardBorderStyleHover:"solid",coursesCategoryCardBorderColor:"#ffffff"},dynamic:{coursesCategoryWrap:!0,coursesCategoryGap:30,coursesCategoryBackground:"",coursesCategoryPadding:{top:"",right:"",bottom:"",left:""},coursesCategoryCardPadding:{top:"25",right:"30",bottom:"25",left:"30"},coursesCategoryCardBorderRadius:{top:"0",right:"0",bottom:"0",left:"0"},coursesCategoryCardFontSize:22,coursesCategoryCardFontWeight:"700",coursesCategoryCardLineHeight:32,coursesCategoryCardColor:"#ffffff",coursesCategoryCardColorHover:"#ffffff",coursesCategoryCardIconColor:"#ffffff",coursesCategoryCardIconFontSize:22,coursesCategoryCardBackground:"",coursesCategoryCardBackgroundHover:"",coursesCategoryCardImageBackground:"#00000080",coursesCategoryCardImageBackgroundHover:"#0E171EE5",coursesCategoryCardCountColor:"#ffffff",coursesCategoryCardBorderStyle:"none",coursesCategoryCardBorderStyleHover:"none",coursesCategoryCardBorderColor:""},dynamic2:{coursesCategoryWrap:!0,coursesCategoryGap:30,coursesCategoryBackground:"",coursesCategoryPadding:{top:"",right:"",bottom:"",left:""},coursesCategoryCardPadding:{top:"15",right:"0",bottom:"0",left:"0"},coursesCategoryCardBorderRadius:{top:"0",right:"0",bottom:"0",left:"0"},coursesCategoryCardFontSize:16,coursesCategoryCardFontWeight:"500",coursesCategoryCardLineHeight:20,coursesCategoryCardColor:"#001931",coursesCategoryCardColorHover:"#001931",coursesCategoryCardIconColor:"#001931",coursesCategoryCardIconFontSize:20,coursesCategoryCardBackground:"",coursesCategoryCardBackgroundHover:"",coursesCategoryCardImageBackground:"",coursesCategoryCardImageBackgroundHover:"",coursesCategoryCardCountColor:"#001931",coursesCategoryCardBorderStyle:"none",coursesCategoryCardBorderStyleHover:"none",coursesCategoryCardBorderColor:""}},Cr=({src:e,alt:t,width:a="370",height:n="253"})=>(0,r.createElement)("img",{src:e,alt:t,width:a,height:n}),yr=O("course-category-preset"),pr=({preset:e})=>(0,r.createElement)("div",{className:yr},(0,r.createElement)("div",{className:`${yr}--${e.value}`},(0,r.createElement)(p,{condition:Boolean(e.src)},(0,r.createElement)(Cr,{src:e.src,alt:e.alt})))),hr=O("course-category-style-settings"),vr=(e,t)=>e.value===t.value,br=({label:e,attributeName:t,presets:a,popoverContent:n=null,isAdaptive:o=!1,dependencyMode:l,dependencies:i})=>{const{fieldName:c}=W(t,o),{isChanged:m,onReset:d,onSelectPreset:u,activePreset:g}=(e=>{const{setAttributes:t}=U(),{value:a,isChanged:r,onReset:n}=z(e),o=(0,s.useCallback)((a=>{const{value:r}=a;t({[e]:r})}),[e,t]);return{activePreset:(0,s.useMemo)((()=>({value:a})),[a]),onReset:n,isChanged:r,onSelectPreset:o}})(c);return $(i,l)?(0,r.createElement)("div",{className:hr},(0,r.createElement)(We,{label:e,isChanged:m,onReset:d,popoverContent:n,showDevicePicker:o}),(0,r.createElement)(_t,{presets:a,onSelectPreset:u,activePreset:g,PresetItem:pr,detectIsActive:vr,detectByIndex:!1})):null},_r=a.p+"images/classic.41c36312.png",Er=a.p+"images/colorful.950dfa51.png",fr=a.p+"images/sleek.e634b8dc.png",Sr=a.p+"images/dynamic.af503fb2.png",Nr=a.p+"images/dynamic2.f25a821e.png",wr=[{name:n.__("Classic","masterstudy-lms-learning-management-system"),value:"classic",src:_r,alt:"Course Category Classic"},{name:n.__("Colorful","masterstudy-lms-learning-management-system"),value:"colorful",src:Er,alt:"Course Category Colorful"},{name:n.__("Sleek","masterstudy-lms-learning-management-system"),value:"sleek",src:fr,alt:"Course Category Sleek"},{name:n.__("Dynamic","masterstudy-lms-learning-management-system"),value:"dynamic",src:Sr,alt:"Course Category Dynamic"},{name:n.__("Dynamic with Title Below","masterstudy-lms-learning-management-system"),value:"dynamic2",src:Nr,alt:"Course Category Dynamic2"}],xr=({categories:e})=>{const{min:t,max:a}=((e,t=!1)=>{const a=L(),[r,n]=(0,s.useState)(e.default||{min:3,max:6});return(0,s.useEffect)((()=>{if(a===E.DESKTOP){const a=e.desktop||{min:t?2:3,max:6};n(a)}if(a===E.TABLET){const a=e.tablet||{min:t?1:2,max:3};n(a)}if(a===E.MOBILE){const t=e.mobile||{min:1,max:1};n(t)}}),[a,t,e]),r})({default:{min:3,max:6},mobile:{min:1,max:2}}),{alignContentOptions:o}=G();return(0,r.createElement)(r.Fragment,null,(0,r.createElement)(rr,{title:n.__("Layout","masterstudy-lms-learning-management-system"),accordionFields:or},(0,r.createElement)(br,{label:n.__("Presets","masterstudy-lms-learning-management-system"),presets:wr,attributeName:"courseCategoryPresets"}),(0,r.createElement)(Gt,{name:"coursesCategoryValues",label:n.__("Select taxonomy","masterstudy-lms-learning-management-system"),options:e,multiple:!0}),(0,r.createElement)(ft,{name:"coursesCategoryPerRow",label:n.__("Categories per row","masterstudy-lms-learning-management-system"),dependencies:[{name:"courseCategoryPresets",value:["classic","colorful","sleek"]}],min:t,max:a,isAdaptive:!0}),(0,r.createElement)(Nt,{name:"coursesCategoryWrap",label:n.__("Wrap","masterstudy-lms-learning-management-system"),dependencies:[{name:"courseCategoryPresets",value:["classic","colorful","sleek"]}]}),(0,r.createElement)(Yt,{name:"coursesCategoryAlign",label:n.__("Align Content","masterstudy-lms-learning-management-system"),options:o})))},Br=()=>{const{widthOptions:e}=G();return(0,r.createElement)(r.Fragment,null,(0,r.createElement)(rr,{title:n.__("Layout","masterstudy-lms-learning-management-system"),accordionFields:ir},(0,r.createElement)(et,{name:"coursesCategoryMargin",label:n.__("Margin","masterstudy-lms-learning-management-system"),unitName:"coursesCategoryMarginUnit",isAdaptive:!0}),(0,r.createElement)(et,{name:"coursesCategoryPadding",label:n.__("Padding","masterstudy-lms-learning-management-system"),unitName:"coursesCategoryPaddingUnit",isAdaptive:!0}),(0,r.createElement)(Ht,{name:"coursesCategoryGap",label:n.__("Space between items","masterstudy-lms-learning-management-system"),unitName:"coursesCategoryGapUnit",isAdaptive:!0}),(0,r.createElement)(Ge,{name:"coursesCategoryBackground",label:n.__("Background","masterstudy-lms-learning-management-system"),placeholder:n.__("Select color","masterstudy-lms-learning-management-system")}),(0,r.createElement)(mt,{label:n.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"coursesCategoryBorderStyle",borderColorName:"coursesCategoryBorderColor",borderWidthName:"coursesCategoryBorderWidth",isAdaptive:!0}),(0,r.createElement)(gt,{name:"coursesCategoryBorderRadius",label:n.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,r.createElement)(Yt,{name:"coursesCategoryWidth",label:n.__("Width","masterstudy-lms-learning-management-system"),options:e}),(0,r.createElement)(Ht,{name:"coursesCategoryMaxWidth",label:n.__("Content Max Width","masterstudy-lms-learning-management-system"),unitName:"coursesCategoryMaxWidthUnit"}),(0,r.createElement)(ft,{name:"coursesCategoryZIndex",label:n.__("Z-Index","masterstudy-lms-learning-management-system"),min:0,max:100,isAdaptive:!0})),(0,r.createElement)(rr,{title:n.__("Card","masterstudy-lms-learning-management-system"),accordionFields:mr},(0,r.createElement)(ta,{fontSizeName:"coursesCategoryCardFontSize",fontSizeUnitName:"coursesCategoryCardFontSizeUnit",fontWeightName:"coursesCategoryCardFontWeight",textTransformName:"coursesCategoryCardTextTransform",fontStyleName:"coursesCategoryCardFontStyle",textDecorationName:"coursesCategoryCardTextDecoration",lineHeightName:"coursesCategoryCardLineHeight",lineHeightUnitName:"coursesCategoryCardLineHeightUnit",letterSpacingName:"coursesCategoryCardLetterSpacing",letterSpacingUnitName:"coursesCategoryCardLetterSpacingUnit",wordSpacingName:"coursesCategoryCardWordSpacing",wordSpacingUnitName:"coursesCategoryCardWordSpacingUnit",isAdaptive:!0}),(0,r.createElement)(Ge,{name:"coursesCategoryCardColor",label:n.__("Color","masterstudy-lms-learning-management-system"),placeholder:n.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0}),(0,r.createElement)(Ge,{name:"coursesCategoryCardIconColor",label:n.__("Icon Color","masterstudy-lms-learning-management-system"),placeholder:n.__("Select color","masterstudy-lms-learning-management-system"),dependencies:[{name:"courseCategoryPresets",value:["classic","colorful"]}],hasHover:!0}),(0,r.createElement)(ft,{name:"coursesCategoryCardIconFontSize",label:n.__("Icon Size","masterstudy-lms-learning-management-system"),unitName:"coursesCategoryCardIconFontSizeUnit",min:0,max:100,dependencies:[{name:"courseCategoryPresets",value:["classic","colorful"]}],isAdaptive:!0}),(0,r.createElement)(Ge,{name:"coursesCategoryCardBackground",label:n.__("Background","masterstudy-lms-learning-management-system"),placeholder:n.__("Select color","masterstudy-lms-learning-management-system"),dependencies:[{name:"courseCategoryPresets",value:["classic","colorful","slick"]}],hasHover:!0}),(0,r.createElement)(Ge,{name:"coursesCategoryCardImageBackground",label:n.__("Image Overlay","masterstudy-lms-learning-management-system"),placeholder:n.__("Select color","masterstudy-lms-learning-management-system"),dependencies:[{name:"courseCategoryPresets",value:["dynamic","dynamic2"]}],hasHover:!0}),(0,r.createElement)(We,{label:n.__("Count Typography","masterstudy-lms-learning-management-system"),isChanged:!1,onReset:()=>{},showDevicePicker:!1,dependencies:[{name:"courseCategoryPresets",value:["dynamic","dynamic2"]}]}),(0,r.createElement)(ta,{fontSizeName:"coursesCategoryCardCountFontSize",fontSizeUnitName:"coursesCategoryCardCountFontSizeUnit",fontWeightName:"coursesCategoryCardCountFontWeight",textTransformName:"coursesCategoryCardCountTextTransform",fontStyleName:"coursesCategoryCardCountFontStyle",textDecorationName:"coursesCategoryCardCountTextDecoration",lineHeightName:"coursesCategoryCardCountLineHeight",lineHeightUnitName:"coursesCategoryCardCountLineHeightUnit",letterSpacingName:"coursesCategoryCardCountLetterSpacing",letterSpacingUnitName:"coursesCategoryCardCountLetterSpacingUnit",wordSpacingName:"coursesCategoryCardCountWordSpacing",wordSpacingUnitName:"coursesCategoryCardCountWordSpacingUnit",isAdaptive:!0,dependencies:[{name:"courseCategoryPresets",value:["dynamic","dynamic2"]}]}),(0,r.createElement)(Ge,{name:"coursesCategoryCardCountColor",label:n.__("Count Color","masterstudy-lms-learning-management-system"),placeholder:n.__("Select color","masterstudy-lms-learning-management-system"),hasHover:!0,dependencies:[{name:"courseCategoryPresets",value:["dynamic","dynamic2"]}]}),(0,r.createElement)(mt,{label:n.__("Border","masterstudy-lms-learning-management-system"),borderStyleName:"coursesCategoryCardBorderStyle",borderColorName:"coursesCategoryCardBorderColor",borderWidthName:"coursesCategoryCardBorderWidth",isAdaptive:!0,hasHover:!0}),(0,r.createElement)(gt,{name:"coursesCategoryCardBorderRadius",label:n.__("Border radius","masterstudy-lms-learning-management-system"),isAdaptive:!0}),(0,r.createElement)(et,{name:"coursesCategoryCardMargin",label:n.__("Margin","masterstudy-lms-learning-management-system"),unitName:"coursesCategoryCardMarginUnit",isAdaptive:!0}),(0,r.createElement)(et,{name:"coursesCategoryCardPadding",label:n.__("Padding","masterstudy-lms-learning-management-system"),unitName:"coursesCategoryCardPaddingUnit",isAdaptive:!0}),(0,r.createElement)(kt,{label:n.__("Select Preset","masterstudy-lms-learning-management-system"),min:0,max:100,shadowColorName:"coursesCategoryCardShadowColor",shadowHorizontalName:"coursesCategoryCardShadowHorizontal",shadowVerticalName:"coursesCategoryCardShadowVertical",shadowBlurName:"coursesCategoryCardShadowBlur",shadowSpreadName:"coursesCategoryCardShadowSpread",shadowInsetName:"coursesCategoryCardShadowInset",popoverContent:null,hasHover:!0})))},kr=({attributes:e,setAttributes:t,categories:a})=>{const{onResetByFieldName:n,changedFieldsByName:o}=((e,t,a,r=[])=>{const n=(e=>{const t={};return Object.entries(e).forEach((([e,a])=>{e.includes("UAG")||(t[e]=a)})),t})(t),o=!h(e,n),s=((e,t,a)=>{const r=new Map;return a.forEach((a=>{r.has(a)||r.set(a,(()=>t({[a]:e[a]})))})),r})(e,a,r),l=((e,t,a)=>{const r=new Map;return a.forEach((a=>{r.has(a)?r.set(a,!1):r.set(a,!h(e[a],t[a]))})),r})(e,n,r);return{hasChanges:o,onResetByFieldName:s,changedFieldsByName:l}})(dr,e,t,Object.keys(dr));return(0,r.createElement)(l.InspectorControls,null,(0,r.createElement)(I,{attributes:e,setAttributes:t,defaultValues:dr,onResetByFieldName:n,changedFieldsByName:o},(0,r.createElement)(Ca,{generalTab:(0,r.createElement)(xr,{categories:a}),styleTab:(0,r.createElement)(Br,null),advancedTab:(0,r.createElement)(r.Fragment,null)})))},Tr=window.wp.apiFetch;var Mr=a.n(Tr);const Hr=[["core/group",{className:"lms-courses-categories-group-header"},[["core/heading",{textAlign:"center",style:{typography:{fontSize:"48px"},color:{text:"#001931"},spacing:{margin:{top:"0",bottom:"50px"}}},content:n.__("Course Categories","masterstudy-lms-learning-management-system"),placeholder:n.__("Course Categories Title","masterstudy-lms-learning-management-system")}]]]],Pr=JSON.parse('{"UU":"masterstudy/courses-categories-container"}');(0,o.registerBlockType)(Pr.UU,{title:n._x("MasterStudy Courses Categories","block title","masterstudy-lms-learning-management-system"),description:n._x("Customize how course categories will look on the page","block description","masterstudy-lms-learning-management-system"),category:"masterstudy-lms-blocks",icon:{src:(0,r.createElement)("svg",{width:"512",height:"512",viewBox:"0 0 512 512",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)("path",{opacity:"0.3",fillRule:"evenodd",clipRule:"evenodd",d:"M311.999 406C309.16 411.681 300.317 417.161 305.999 420L349.999 448L357.999 453.5C366.338 457.67 364.684 456.582 373.999 457C383.315 457.418 385.818 458.407 394.499 455C403.179 451.593 405.954 447.143 412.499 440.5C419.043 433.857 427.222 428.73 430.499 420L434.499 408.5L468.999 315.5L474.499 300.5C480.094 285.574 480.001 275.96 474.499 261C468.997 246.04 468.217 241.245 454.285 233.5L443.999 226L286.499 137.5C271.75 153.569 353.999 208 401.826 291.5C385.499 336 363.499 362 311.999 406Z",fill:"#227AFF"}),(0,r.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M185.524 82.5693C188.841 82.5682 192.022 83.8852 194.368 86.2305L374.294 266.156C374.294 266.157 374.294 266.157 374.295 266.158C382.457 274.323 387.043 285.397 387.043 296.943C387.043 308.489 382.457 319.563 374.294 327.729L286.995 415.028C278.829 423.191 267.755 427.777 256.209 427.777C244.662 427.777 233.588 423.191 225.422 415.028L45.381 234.987C43.0824 232.688 41.7942 229.605 41.7752 226.274L41.0274 95.1869C40.9879 88.2573 46.5931 82.618 53.5228 82.6156L185.524 82.5693ZM210.631 69.9671C203.971 63.3065 194.936 59.566 185.516 59.5693L53.5147 59.6156C33.8343 59.6225 17.9155 75.6381 18.0278 95.3181L18.7756 226.405C18.8287 235.71 22.4815 244.614 29.1176 251.25L209.16 431.292L209.161 431.294C221.64 443.769 238.563 450.777 256.209 450.777C273.854 450.777 290.777 443.769 303.256 431.294L303.257 431.292L390.558 343.991L390.56 343.99C403.035 331.511 410.043 314.588 410.043 296.943C410.043 279.297 403.035 262.374 390.56 249.895L390.558 249.894L210.631 69.9671Z",fill:"black"}),(0,r.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M232.395 112.461C229.31 118.013 231.31 125.015 236.862 128.099L443.113 242.702L443.115 242.703C452.23 247.771 459.224 255.943 462.824 265.731C466.424 275.52 466.391 286.276 462.731 296.042M462.73 296.044L418.283 414.551L418.282 414.554C416.138 420.266 412.817 425.463 408.535 429.81C404.253 434.157 399.104 437.555 393.424 439.784C387.744 442.014 381.658 443.024 375.563 442.751C369.467 442.477 363.496 440.925 358.038 438.196L358.036 438.195L302.633 410.507C296.952 407.668 290.044 409.972 287.205 415.653C284.366 421.335 286.67 428.242 292.351 431.081L347.751 458.767L347.754 458.769C356.094 462.939 365.216 465.31 374.531 465.728C383.847 466.146 393.147 464.601 401.827 461.194C410.508 457.787 418.375 452.594 424.92 445.951C431.464 439.308 436.539 431.364 439.816 422.633L439.817 422.631L484.266 304.119L484.267 304.117C489.861 289.191 489.912 272.753 484.411 257.793C478.909 242.833 468.22 230.344 454.288 222.599L454.286 222.598L248.033 107.995C242.481 104.91 235.48 106.91 232.395 112.461",fill:"black"}),(0,r.createElement)("path",{d:"M67.5527 135.808C67.5527 120.607 79.875 108.285 95.0752 108.285C110.275 108.285 122.598 120.607 122.598 135.808C122.598 151.008 110.275 163.33 95.0752 163.33C79.875 163.33 67.5527 151.008 67.5527 135.808Z",fill:"black"}))},edit:({attributes:e,setAttributes:t})=>{const{isFetching:a,categories:n,categoriesMapFull:o}=((e,t)=>{const{isFetching:a,categories:r,categoriesMap:n,categoriesMapFull:o}=((e=!1)=>{const[t,a]=(0,s.useState)([]),[r,n]=(0,s.useState)({}),[o,l]=(0,s.useState)({}),{setIsFetching:i,setError:c,isFetching:m,error:d}=(()=>{const[e,t]=(0,s.useState)(!0),[a,r]=(0,s.useState)("");return{isFetching:e,setIsFetching:t,error:a,setError:r}})();return(0,s.useEffect)((()=>{i(!0),(async(e=!1)=>{try{let t="?children=true";return e&&(t+="&details=true"),await Mr()({path:`masterstudy-lms/v2/course-categories${t}`})}catch(e){throw new Error(e)}})(e).then((({categories:e})=>{a((e=>e.map((e=>({label:e.name,value:e.id,image:e.image,icon:e.icon,color:e.color,children:e.children?v(e.children):[]}))))(e)),n(e.reduce(((e,t)=>(e[String(t.id)]=t.name,e)),{})),l(e.reduce(((e,t)=>(e[String(t.id)]={label:t.name,value:t.id,image:t.image,icon:t.icon,color:t.color,courses:t.courses,children:t.children},e)),{}))})).catch((e=>{c(e.message)})).finally((()=>{i(!1)}))}),[]),{categories:t,categoriesMap:r,categoriesMapFull:o,isFetching:m,error:d}})(!0);return(0,s.useEffect)((()=>{e.coursesCategoryValues.length?t({coursesCategoryOptions:e.coursesCategoryValues.map((e=>({value:e,label:n[e]})))}):t({coursesCategoryOptions:[]})}),[e.coursesCategoryValues,n,t]),{isFetching:a,categories:r,categoriesMapFull:o}})(e,t),[i,C]=(0,s.useState)(e.courseCategoryPresets);(0,s.useEffect)((()=>{e.courseCategoryPresets!==i&&(t({...gr[e.courseCategoryPresets]}),C(e.courseCategoryPresets))}),[e.courseCategoryPresets,t,i]);const h=(0,l.useBlockProps)({className:c()("lms-courses-category-container",{alignfull:"alignfull"===e.coursesCategoryWidth},`lms-courses-category-container__item-${e.coursesCategoryPerRow}`,`lms-courses-category-container__item-tablet-${e.coursesCategoryPerRowTablet}`,`lms-courses-category-container__item-mobile-${e.coursesCategoryPerRowMobile}`,e.coursesCategoryWrap&&"lms-courses-category-container__inline",e.textAlign),style:{...k("courses",e,ur),...M("courses-coursesCategoryCard",e,"coursesCategoryCardShadowColor","coursesCategoryCardShadowHorizontal","coursesCategoryCardShadowVertical","coursesCategoryCardShadowBlur","coursesCategoryCardShadowSpread","coursesCategoryCardShadowInset",!0),...M("courses-coursesCategoryCard-hover",e,"coursesCategoryCardShadowColorHover","coursesCategoryCardShadowHorizontalHover","coursesCategoryCardShadowVerticalHover","coursesCategoryCardShadowBlurHover","coursesCategoryCardShadowSpreadHover","coursesCategoryCardShadowInsetHover",!0)}}),b=(0,l.useInnerBlocksProps)({},{template:Hr,templateLock:"all"}),_=((e,t,a)=>e.length&&t.length?e.filter((({value:e})=>a.hasOwnProperty(e))).map((({value:e})=>a[e])):t)(e.coursesCategoryOptions,n,o);return(0,r.createElement)("div",{...h},(0,r.createElement)("div",{className:"lms-courses-category-container__wrap"},(0,r.createElement)(kr,{attributes:e,setAttributes:t,categories:n}),(0,r.createElement)("div",{...b}),(0,r.createElement)(p,{condition:!a,fallback:(0,r.createElement)(m.Spinner,null)},(()=>{switch(e.courseCategoryPresets){case"colorful":return(0,r.createElement)(u,{options:e.coursesCategoryOptions,categories:_,className:"lms-courses-category-list lms-courses-category-list__colorful"});case"sleek":return(0,r.createElement)(g,{options:e.coursesCategoryOptions,categories:_,className:"lms-courses-category-list lms-courses-category-list__sleek"});case"dynamic":return(0,r.createElement)(y,{options:e.coursesCategoryOptions,categories:_,className:"lms-courses-category-list lms-courses-category-list__dynamic"});case"dynamic2":return(0,r.createElement)(y,{options:e.coursesCategoryOptions,categories:_,className:"lms-courses-category-list lms-courses-category-list__dynamic2"});default:return(0,r.createElement)(d,{options:e.coursesCategoryOptions,categories:_,className:"lms-courses-category-list"})}})())))},save:({attributes:e})=>{const t=l.useBlockProps.save({className:c()(`lms-courses-category-container ${e.coursesCategoryWidth}`,`lms-courses-category-container__item-${e.coursesCategoryPerRow}`,`lms-courses-category-container__item-tablet-${e.coursesCategoryPerRowTablet}`,`lms-courses-category-container__item-mobile-${e.coursesCategoryPerRowMobile}`,e.coursesCategoryWrap&&"lms-courses-category-container__inline",e.textAlign),style:{...k("courses",e,ur),...M("courses-coursesCategoryCard",e,"coursesCategoryCardShadowColor","coursesCategoryCardShadowHorizontal","coursesCategoryCardShadowVertical","coursesCategoryCardShadowBlur","coursesCategoryCardShadowSpread","coursesCategoryCardShadowInset",!0),...M("courses-coursesCategoryCard-hover",e,"coursesCategoryCardShadowColorHover","coursesCategoryCardShadowHorizontalHover","coursesCategoryCardShadowVerticalHover","coursesCategoryCardShadowBlurHover","coursesCategoryCardShadowSpreadHover","coursesCategoryCardShadowInsetHover",!0)}}),a=l.useInnerBlocksProps.save(),n=e.coursesCategoryOptions?e.coursesCategoryOptions.map((e=>e.value)).join(","):"";return(0,r.createElement)("div",{...t},(0,r.createElement)("div",{className:"lms-course-preloader"},(0,r.createElement)("div",{className:"lms-course-preloader-item"})),(0,r.createElement)("div",{className:"lms-courses-category-container__wrap"},(0,r.createElement)("div",{...a}),(0,r.createElement)("input",{type:"hidden",className:"lms-courses-category-list-data","data-categories":n,"data-style":e.courseCategoryPresets})))}})},6942:(e,t)=>{var a;!function(){"use strict";var r={}.hasOwnProperty;function n(){for(var e="",t=0;t<arguments.length;t++){var a=arguments[t];a&&(e=s(e,o(a)))}return e}function o(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return n.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var a in e)r.call(e,a)&&e[a]&&(t=s(t,a));return t}function s(e,t){return t?e?e+" "+t:e+t:e}e.exports?(n.default=n,e.exports=n):void 0===(a=function(){return n}.apply(t,[]))||(e.exports=a)}()}},a={};function r(e){var n=a[e];if(void 0!==n)return n.exports;var o=a[e]={exports:{}};return t[e](o,o.exports,r),o.exports}r.m=t,e=[],r.O=(t,a,n,o)=>{if(!a){var s=1/0;for(m=0;m<e.length;m++){for(var[a,n,o]=e[m],l=!0,i=0;i<a.length;i++)(!1&o||s>=o)&&Object.keys(r.O).every((e=>r.O[e](a[i])))?a.splice(i--,1):(l=!1,o<s&&(s=o));if(l){e.splice(m--,1);var c=n();void 0!==c&&(t=c)}}return t}o=o||0;for(var m=e.length;m>0&&e[m-1][2]>o;m--)e[m]=e[m-1];e[m]=[a,n,o]},r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var a in t)r.o(t,a)&&!r.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e;r.g.importScripts&&(e=r.g.location+"");var t=r.g.document;if(!e&&t&&(t.currentScript&&(e=t.currentScript.src),!e)){var a=t.getElementsByTagName("script");if(a.length)for(var n=a.length-1;n>-1&&(!e||!/^http(s?):/.test(e));)e=a[n--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),r.p=e+"../../"})(),(()=>{var e={7400:0,3600:0};r.O.j=t=>0===e[t];var t=(t,a)=>{var n,o,[s,l,i]=a,c=0;if(s.some((t=>0!==e[t]))){for(n in l)r.o(l,n)&&(r.m[n]=l[n]);if(i)var m=i(r)}for(t&&t(a);c<s.length;c++)o=s[c],r.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return r.O(m)},a=globalThis.webpackChunkmasterstudy_lms_learning_management_system=globalThis.webpackChunkmasterstudy_lms_learning_management_system||[];a.forEach(t.bind(null,0)),a.push=t.bind(null,a.push.bind(a))})();var n=r.O(void 0,[3600],(()=>r(6913)));n=r.O(n)})();