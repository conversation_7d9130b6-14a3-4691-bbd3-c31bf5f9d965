<?php
/**
 * Debug file to check price values
 */

// Load WordPress
define('WP_USE_THEMES', false);
require_once('../../../wp-load.php');

// Get all bundles
$bundles = get_posts(array(
    'post_type' => 'mscb_bundle',
    'posts_per_page' => -1,
    'post_status' => 'publish',
));

echo '<h1>Bundle Price Debug</h1>';
echo '<table border="1" cellpadding="10">';
echo '<tr><th>Bundle ID</th><th>Title</th><th>_mscb_price</th><th>_mscb_sale_price</th><th>_mscb_regular_price</th><th>bundle_price</th><th>bundle_regular_price</th><th>bundle_sale_price</th></tr>';

foreach ($bundles as $bundle) {
    $bundle_id = $bundle->ID;
    
    // Get all price meta fields to determine which one is actually used
    $mscb_price = get_post_meta($bundle_id, '_mscb_price', true);
    $mscb_sale_price = get_post_meta($bundle_id, '_mscb_sale_price', true);
    $mscb_regular_price = get_post_meta($bundle_id, '_mscb_regular_price', true);
    
    $bundle_price = get_post_meta($bundle_id, 'bundle_price', true);
    $bundle_regular_price = get_post_meta($bundle_id, 'bundle_regular_price', true);
    $bundle_sale_price = get_post_meta($bundle_id, 'bundle_sale_price', true);
    
    echo "<tr>";
    echo "<td>{$bundle_id}</td>";
    echo "<td>{$bundle->post_title}</td>";
    echo "<td>{$mscb_price}</td>";
    echo "<td>{$mscb_sale_price}</td>";
    echo "<td>{$mscb_regular_price}</td>";
    echo "<td>{$bundle_price}</td>";
    echo "<td>{$bundle_regular_price}</td>";
    echo "<td>{$bundle_sale_price}</td>";
    echo "</tr>";
}

echo '</table>'; 