<?php
/**
 * Handles plugin activation tasks
 * 
 * This file creates the database table needed for storing course-product links
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Include debug utilities
require_once dirname(dirname(__FILE__)) . '/includes/debug.php';

/**
 * Activator class for Custom Linking Plugin
 * 
 * Only handles creating the database table on activation
 */
class Custom_Linking_Activator {
    /**
     * Create necessary database tables on plugin activation
     * 
     * This method creates the required database table for the plugin
     * and logs any errors during the process
     */
    public static function activate() {
        try {
            global $wpdb;
            $table_name = $wpdb->prefix . 'linking_table';
            
            // Log activation process
            self::log_message("Beginning table creation process for: {$table_name}");
            
            // Get proper charset
            $charset_collate = $wpdb->get_charset_collate();
            
            // Check if table already exists
            if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") != $table_name) {
                $sql = "CREATE TABLE {$table_name} (
                    id mediumint(9) NOT NULL AUTO_INCREMENT,
                    course_id bigint(20) NOT NULL,
                    product_id bigint(20) NOT NULL,
                    type enum('course','bundle') NOT NULL DEFAULT 'course',
                    PRIMARY KEY (id),
                    KEY course_id (course_id),
                    KEY product_id (product_id)
                ) {$charset_collate};";
                
                // Include WordPress database upgrade functions
                require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
                
                // Attempt to create the table
                $result = dbDelta($sql);
                self::log_message("Table creation result: " . print_r($result, true));
            } else {
                self::log_message("Table {$table_name} already exists, skipping creation.");
            }
            
            self::log_message("Activation completed successfully.");
            return true;
        } catch (Exception $e) {
            self::log_message("ERROR during activation: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Log messages to the debug log
     * 
     * Uses the centralized debug logging function
     * 
     * @param string $message The message to log
     */
    private static function log_message($message) {
        // Use the centralized debug logging function
        custom_linking_debug_log('Activator: ' . $message);
    }
}