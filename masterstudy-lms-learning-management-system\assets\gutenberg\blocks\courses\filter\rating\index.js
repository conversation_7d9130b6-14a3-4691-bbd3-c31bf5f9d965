(()=>{var e,t={821:(e,t,n)=>{"use strict";const a=window.React,l=window.wp.blocks,r=window.wp.i18n,s=window.wp.element,i=window.wp.apiFetch;var o=n.n(i);var m=n(6942),c=n.n(m);const d=window.wp.blockEditor,u=(0,s.createContext)(null),g=({children:e,...t})=>(0,a.createElement)(u.Provider,{value:{...t}},e),p=window.wp.components,h=({condition:e,fallback:t=null,children:n})=>(0,a.createElement)(a.Fragment,null,e?n:t),v=(e,t)=>{if(typeof e!=typeof t)return!1;if(Number.isNaN(e)&&Number.isNaN(t))return!0;if("object"!=typeof e||null===e||null===t)return e===t;if(Object.keys(e).length!==Object.keys(t).length)return!1;if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;const n=e.slice().sort(),a=t.slice().sort();return n.every(((e,t)=>v(e,a[t])))}for(const n of Object.keys(e))if(!v(e[n],t[n]))return!1;return!0};let _=function(e){return e.ALL="all",e.SOME="some",e}({}),y=function(e){return e.NORMAL="Normal",e.HOVER="Hover",e.ACTIVE="Active",e.FOCUS="Focus",e}({}),b=function(e){return e.DESKTOP="Desktop",e.TABLET="Tablet",e.MOBILE="Mobile",e}({}),E=function(e){return e.TOP_lEFT="top-left",e.TOP_CENTER="top-center",e.TOP_RIGHT="top-right",e.BOTTOM_lEFT="bottom-left",e.BOTTOM_CENTER="bottom-center",e.BOTTOM_RIGHT="bottom-right",e}({});const f=["",null,void 0,"null","undefined"],C=[".jpg",".jpeg",".png",".gif"],N=e=>f.includes(e),w=(e,t,n="")=>{const a=e[t];return"object"==typeof a&&null!==a?((e,t)=>{return n=e,Object.values(n).every((e=>f.includes(e)))?null:((e,t="")=>{const n=Object.entries(e).reduce(((e,[n,a])=>(e[n]=(a||"0")+t,e)),{});return`${n.top} ${n.right} ${n.bottom} ${n.left}`})(e,t);var n})(a,n):((e,t)=>N(e)?e:(e=>{if("string"!=typeof e)return!1;const t=e.slice(e.lastIndexOf("."));return C.includes(t)||e.startsWith("blob")})(e)?`url('${e}')`:String(e+t))(a,n)},S=(e,t,n)=>{const a={};return n.forEach((({isAdaptive:n,hasHover:l,unit:r},s)=>{if(t.hasOwnProperty(s)){const{unitMeasureDesktop:o,unitMeasureTablet:m,unitMeasureMobile:c}=((e,t)=>{var n;return{unitMeasureDesktop:null!==(n=e[t])&&void 0!==n?n:"",unitMeasureTablet:t?e[t+"Tablet"]:"",unitMeasureMobile:t?e[t+"Mobile"]:""}})(t,r);if(n&&l){const{desktopHoverPropertyName:n,mobileHoverPropertyName:l,tabletHoverPropertyName:r}=(e=>{const t=e+y.HOVER;return{desktopHoverPropertyName:t,tabletHoverPropertyName:t+"Tablet",mobileHoverPropertyName:t+"Mobile"}})(s),i=w(t,n,o);N(i)||(a[`--lms-${e}-${n}`]=i);const d=w(t,r,m);N(d)||(a[`--lms-${e}-${r}`]=d);const u=w(t,l,c);N(u)||(a[`--lms-${e}-${l}`]=u)}if(l){const n=s+y.HOVER,l=w(t,n,o);N(l)||(a[`--lms-${e}-${n}`]=l)}if(n){const{desktopPropertyName:n,mobilePropertyName:l,tabletPropertyName:r}={desktopPropertyName:i=s,tabletPropertyName:i+"Tablet",mobilePropertyName:i+"Mobile"},d=w(t,n,o);N(d)||(a[`--lms-${e}-${n}`]=d);const u=w(t,r,m);N(u)||(a[`--lms-${e}-${r}`]=u);const g=w(t,l,c);N(g)||(a[`--lms-${e}-${l}`]=g)}const d=w(t,s,o);N(d)||(a[`--lms-${e}-${s}`]=d)}var i})),a},T=(r.__("Small","masterstudy-lms-learning-management-system"),r.__("Normal","masterstudy-lms-learning-management-system"),r.__("Large","masterstudy-lms-learning-management-system"),r.__("Extra Large","masterstudy-lms-learning-management-system"),"wp-block-masterstudy-settings__"),x={top:"",right:"",bottom:"",left:""};function O(e){return Array.isArray(e)?e.map((e=>T+e)):T+e}E.TOP_lEFT,E.TOP_CENTER,E.TOP_RIGHT,E.BOTTOM_lEFT,E.BOTTOM_CENTER,E.BOTTOM_RIGHT,r.__("Newest","masterstudy-lms-learning-management-system"),r.__("Oldest","masterstudy-lms-learning-management-system"),r.__("Overall rating","masterstudy-lms-learning-management-system"),r.__("Popular","masterstudy-lms-learning-management-system"),r.__("Price low","masterstudy-lms-learning-management-system"),r.__("Price high","masterstudy-lms-learning-management-system");const k=window.wp.data,L=()=>(0,k.useSelect)((e=>e("core/editor").getDeviceType()||e("core/edit-site")?.__experimentalGetPreviewDeviceType()||e("core/edit-post")?.__experimentalGetPreviewDeviceType()||e("masterstudy/store")?.getDeviceType()),[])||"Desktop",A=(e=!1)=>{const[t,n]=(0,s.useState)(e),a=(0,s.useCallback)((()=>{n(!0)}),[]);return{isOpen:t,onClose:(0,s.useCallback)((()=>{n(!1)}),[]),onOpen:a,onToggle:(0,s.useCallback)((()=>{n((e=>!e))}),[])}},D=()=>{const e=(0,s.useContext)(u);if(!e)throw new Error("No settings context provided");return e},M=(e="")=>{const{attributes:t,setAttributes:n,onResetByFieldName:a,changedFieldsByName:l}=D();return{value:t[e],onChange:t=>n({[e]:t}),onReset:a.get(e),isChanged:l.get(e)}},R=(e,t=!1,n=!1)=>{const{hoverName:a,onChangeHoverName:l}=(()=>{const[e,t]=(0,s.useState)(y.NORMAL);return{hoverName:e,onChangeHoverName:(0,s.useCallback)((e=>{t(e)}),[])}})(),r=L();return{fieldName:(0,s.useMemo)((()=>{const l=a===y.HOVER?a:"",s=r===b.DESKTOP?"":r;return n&&t?e+l+s:n&&!t?e+l:t&&!n?e+s:e}),[e,n,t,a,r]),hoverName:a,onChangeHoverName:l}},B=(e=[],t=_.ALL)=>{const{attributes:n}=D();return!e.length||(t===_.ALL?e.every((({name:e,value:t})=>{const a=n[e];return Array.isArray(t)?Array.isArray(a)?v(t,a):t.includes(a):t===a})):t!==_.SOME||e.some((({name:e,value:t})=>{const a=n[e];return Array.isArray(t)?Array.isArray(a)?v(t,a):t.includes(a):t===a})))},H=e=>{const t=(0,s.useRef)(null);return(0,s.useEffect)((()=>{const n=n=>{t.current&&!t.current.contains(n.target)&&e()};return document.addEventListener("click",n),()=>{document.removeEventListener("click",n)}}),[t,e]),t},F=e=>(0,a.createElement)(p.SVG,{width:"16",height:"16",viewBox:"0 0 14 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(p.G,{"clip-path":"url(#clip0_1068_38993)"},(0,a.createElement)(p.Path,{d:"M11.1973 8.60005L8.74967 8.11005V5.67171C8.74967 5.517 8.68822 5.36863 8.57882 5.25923C8.46942 5.14984 8.32105 5.08838 8.16634 5.08838H6.99967C6.84496 5.08838 6.69659 5.14984 6.5872 5.25923C6.4778 5.36863 6.41634 5.517 6.41634 5.67171V8.58838H5.24967C5.15021 8.58844 5.05241 8.61391 4.96555 8.66238C4.87869 8.71084 4.80565 8.78068 4.75336 8.86529C4.70106 8.9499 4.67125 9.04646 4.66674 9.14582C4.66223 9.24518 4.68317 9.34405 4.72759 9.43305L6.47759 12.933C6.57676 13.1302 6.77859 13.255 6.99967 13.255H11.083C11.2377 13.255 11.3861 13.1936 11.4955 13.0842C11.6049 12.9748 11.6663 12.8264 11.6663 12.6717V9.17171C11.6665 9.03685 11.6198 8.90612 11.5343 8.80186C11.4487 8.69759 11.3296 8.62626 11.1973 8.60005Z"}),(0,a.createElement)(p.Path,{d:"M10.4997 1.58838H3.49967C2.85626 1.58838 2.33301 2.11221 2.33301 2.75505V6.83838C2.33301 7.4818 2.85626 8.00505 3.49967 8.00505H5.24967V6.83838H3.49967V2.75505H10.4997V6.83838H11.6663V2.75505C11.6663 2.11221 11.1431 1.58838 10.4997 1.58838Z"})),(0,a.createElement)("defs",null,(0,a.createElement)("clipPath",{id:"clip0_1068_38993"},(0,a.createElement)(p.Rect,{width:"14",height:"14",transform:"translate(0 0.421875)"})))),[P,U,z,V,I]=(y.NORMAL,r.__("Normal State","masterstudy-lms-learning-management-system"),y.HOVER,r.__("Hovered State","masterstudy-lms-learning-management-system"),y.ACTIVE,r.__("Hovered State","masterstudy-lms-learning-management-system"),y.FOCUS,r.__("Hovered State","masterstudy-lms-learning-management-system"),y.NORMAL,(0,a.createElement)((e=>(0,a.createElement)(p.SVG,{width:"16",height:"16",viewBox:"0 0 12 13",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,a.createElement)(p.Path,{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"}))),null),r.__("Normal State","masterstudy-lms-learning-management-system"),y.HOVER,(0,a.createElement)(F,null),r.__("Hovered State","masterstudy-lms-learning-management-system"),y.ACTIVE,(0,a.createElement)(F,null),r.__("Active State","masterstudy-lms-learning-management-system"),y.FOCUS,(0,a.createElement)(F,null),r.__("Focus State","masterstudy-lms-learning-management-system"),O(["hover-state","hover-state__selected","hover-state__selected__opened-menu","hover-state__menu","hover-state__menu__item"])),j=O("color-indicator"),W=(0,s.memo)((({color:e,onChange:t})=>(0,a.createElement)("div",{className:j},(0,a.createElement)(d.PanelColorSettings,{enableAlpha:!0,disableCustomColors:!1,__experimentalHasMultipleOrigins:!0,__experimentalIsRenderedInSidebar:!0,colorSettings:[{label:"",value:e,onChange:t}]}))));var $;function G(){return G=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},G.apply(null,arguments)}var K,X,Z=function(e){return a.createElement("svg",G({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 12 13"},e),$||($=a.createElement("path",{d:"M5.053 12.422a.63.63 0 0 1-.584-.391L.05 1.294A.632.632 0 0 1 .871.469L11.61 4.89a.633.633 0 0 1-.088 1.198l-4.685 1.17-1.17 4.686a.63.63 0 0 1-.614.478M1.793 2.214l3.113 7.56.797-3.19a.63.63 0 0 1 .46-.46l3.19-.797z"})))};function Y(){return Y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Y.apply(null,arguments)}var q=function(e){return a.createElement("svg",Y({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,fill:"none",viewBox:"0 0 14 15"},e),K||(K=a.createElement("g",{clipPath:"url(#state-hover_svg__a)"},a.createElement("path",{d:"M11.197 8.6 8.75 8.11V5.672a.583.583 0 0 0-.584-.584H7a.583.583 0 0 0-.584.584v2.916H5.25a.584.584 0 0 0-.522.845l1.75 3.5c.099.197.3.322.522.322h4.083a.583.583 0 0 0 .583-.583v-3.5a.58.58 0 0 0-.469-.572"}),a.createElement("path",{d:"M10.5 1.588h-7c-.644 0-1.167.524-1.167 1.167v4.083c0 .644.523 1.167 1.167 1.167h1.75V6.838H3.5V2.755h7v4.083h1.166V2.755c0-.643-.523-1.167-1.166-1.167"}))),X||(X=a.createElement("defs",null,a.createElement("clipPath",{id:"state-hover_svg__a"},a.createElement("path",{d:"M0 .422h14v14H0z"})))))};const J=[{value:y.NORMAL,label:r.__("Normal State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(Z,{onClick:e})},{value:y.HOVER,label:r.__("Hovered State","masterstudy-lms-learning-management-system"),icon:e=>(0,a.createElement)(q,{onClick:e})}],Q={[y.NORMAL]:{icon:(0,a.createElement)(Z,null),label:r.__("Normal State","masterstudy-lms-learning-management-system")},[y.HOVER]:{icon:(0,a.createElement)(q,null),label:r.__("Hovered State","masterstudy-lms-learning-management-system")}},ee=O("hover-state"),te=O("hover-state__selected"),ne=O("hover-state__selected__opened-menu"),ae=O("has-changes"),le=O("hover-state__menu"),re=O("hover-state__menu__item"),se=(0,s.memo)((e=>{const{hoverName:t,onChangeHoverName:n,fieldName:l}=e,{changedFieldsByName:r}=D(),i=r.get(l),{isOpen:o,onOpen:m,onClose:d}=A(),u=H(d),{ICONS_MAP:g,options:p}=(e=>{const t=(0,s.useMemo)((()=>J.filter((t=>t.value!==e))),[e]);return{ICONS_MAP:Q,options:t}})(t),v=(0,s.useCallback)((e=>{n(e),d()}),[n,d]);return(0,a.createElement)("div",{className:ee,ref:u},(0,a.createElement)("div",{className:c()([te],{[ne]:o,[ae]:i}),onClick:m,title:g[t]?.label},g[t]?.icon),(0,a.createElement)(h,{condition:o},(0,a.createElement)("div",{className:le},p.map((({value:e,icon:t,label:n})=>(0,a.createElement)("div",{key:e,className:re,title:n},t((()=>v(e)))))))))})),ie={Desktop:{icon:"desktop",label:r.__("Desktop","masterstudy-lms-learning-management-system")},Tablet:{icon:"tablet",label:r.__("Tablet","masterstudy-lms-learning-management-system")},Mobile:{icon:"smartphone",label:r.__("Mobile","masterstudy-lms-learning-management-system")}},oe=[{value:b.DESKTOP,icon:"desktop",label:r.__("Desktop","masterstudy-lms-learning-management-system")},{value:b.TABLET,icon:"tablet",label:r.__("Tablet","masterstudy-lms-learning-management-system")},{value:b.MOBILE,icon:"smartphone",label:r.__("Mobile","masterstudy-lms-learning-management-system")}],me=O("device-picker"),ce=O("device-picker__selected"),de=O("device-picker__selected__opened-menu"),ue=O("device-picker__menu"),ge=O("device-picker__menu__item"),pe=()=>{const{isOpen:e,onOpen:t,onClose:n}=A(),{value:l,onChange:r}=(e=>{const t=L(),n=(0,k.useDispatch)();return{value:(0,s.useMemo)((()=>ie[t]),[t]),onChange:t=>{n("core/edit-site")&&n("core/edit-site").__experimentalSetPreviewDeviceType?n("core/edit-site").__experimentalSetPreviewDeviceType(t):n("core/edit-post")&&n("core/edit-post").__experimentalSetPreviewDeviceType?n("core/edit-post").__experimentalSetPreviewDeviceType(t):n("masterstudy/store").setDeviceType(t),e()}}})(n),i=(e=>(0,s.useMemo)((()=>oe.filter((t=>t.icon!==e))),[e]))(l.icon),o=H(n);return(0,a.createElement)("div",{className:me,ref:o},(0,a.createElement)(p.Dashicon,{className:c()([ce],{[de]:e}),icon:l.icon,size:16,onClick:t,title:l.label}),(0,a.createElement)(h,{condition:e},(0,a.createElement)("div",{className:ue},i.map((e=>(0,a.createElement)(p.Dashicon,{key:e.value,icon:e.icon,size:16,onClick:()=>r(e.value),className:ge,title:e.label}))))))},he=O("reset-button"),ve=({onReset:e})=>(0,a.createElement)(p.Dashicon,{icon:"undo",onClick:e,className:he,size:16}),_e=[{label:"PX",value:"px"},{label:"%",value:"%"},{label:"EM",value:"em"},{label:"REM",value:"rem"},{label:"VW",value:"vw"},{label:"VH",value:"vh"}],ye=O("unit"),be=O("unit__single"),Ee=O("unit__list"),fe=({name:e,isAdaptive:t})=>{const{isOpen:n,onOpen:l,onClose:r}=A(),{fieldName:s}=R(e,t),{value:i,onChange:o}=M(s),m=H(r);return(0,a.createElement)("div",{className:ye,ref:m},(0,a.createElement)("div",{className:be,onClick:l},i),(0,a.createElement)(h,{condition:n},(0,a.createElement)("div",{className:Ee},_e.map((({value:e,label:t})=>(0,a.createElement)("div",{key:e,onClick:()=>(o(e),void r())},t))))))},Ce=O("popover-modal"),Ne=O("popover-modal__close dashicon dashicons dashicons-no-alt"),we=e=>{const{isOpen:t,onClose:n,popoverContent:l}=e;return(0,a.createElement)(h,{condition:t},(0,a.createElement)(p.Popover,{position:"middle left",onClose:n,className:Ce},l,(0,a.createElement)("span",{onClick:n,className:Ne})))},Se=O("setting-label"),Te=O("setting-label__content"),xe=e=>{const{label:t,isChanged:n=!1,onReset:l,showDevicePicker:r=!0,HoverStateControl:s=null,unitName:i,popoverContent:o=null,dependencies:m}=e,{isOpen:c,onClose:d,onToggle:u}=A();return B(m)?(0,a.createElement)("div",{className:Se},(0,a.createElement)("div",{className:Te},(0,a.createElement)("div",{onClick:u},t),(0,a.createElement)(h,{condition:Boolean(o)},(0,a.createElement)(we,{isOpen:c,onClose:d,popoverContent:o})),(0,a.createElement)(h,{condition:r},(0,a.createElement)(pe,null)),(0,a.createElement)(h,{condition:Boolean(s)},s)),(0,a.createElement)(h,{condition:Boolean(i)},(0,a.createElement)(fe,{name:i,isAdaptive:r})),(0,a.createElement)(h,{condition:n},(0,a.createElement)(ve,{onReset:l}))):null},Oe=O("suffix"),ke=()=>(0,a.createElement)("div",{className:Oe},(0,a.createElement)(p.Dashicon,{icon:"color-picker",size:16})),Le=O("color-picker"),Ae=e=>{const{name:t,label:n,placeholder:l,dependencyMode:r,dependencies:s,isAdaptive:i=!1,hasHover:o=!1}=e,{fieldName:m,hoverName:c,onChangeHoverName:d}=R(t,i,o),{value:u,isChanged:g,onChange:v,onReset:_}=M(m);return B(s,r)?(0,a.createElement)("div",{className:Le},(0,a.createElement)(h,{condition:Boolean(n)},(0,a.createElement)(xe,{label:n,isChanged:g,onReset:_,showDevicePicker:i,HoverStateControl:(0,a.createElement)(h,{condition:o},(0,a.createElement)(se,{hoverName:c,onChangeHoverName:d,fieldName:m}))})),(0,a.createElement)(p.__experimentalInputControl,{prefix:(0,a.createElement)(W,{color:u,onChange:v}),suffix:(0,a.createElement)(ke,null),onChange:v,value:u,placeholder:l})):null},De=O("number-steppers"),Me=O("indent-steppers"),Re=O("indent-stepper-plus"),Be=O("indent-stepper-minus"),He=({onIncrement:e,onDecrement:t,withArrows:n=!1})=>n?(0,a.createElement)("span",{className:Me},(0,a.createElement)("button",{onClick:e,className:Re}),(0,a.createElement)("button",{onClick:t,className:Be})):(0,a.createElement)("span",{className:De},(0,a.createElement)("button",{onClick:e},"+"),(0,a.createElement)("button",{onClick:t},"-")),[Fe,Pe]=O(["indents","indents-control"]),Ue=({name:e,label:t,unitName:n,popoverContent:l,dependencyMode:i,dependencies:o,isAdaptive:m=!1})=>{const{fieldName:c}=R(e,m),{value:d,onResetSegmentedBox:u,hasChanges:g,handleInputIncrement:v,handleInputDecrement:_,updateDirectionsValues:y,lastFieldValue:b}=((e,t)=>{const{value:n,isChanged:a,onChange:l,onReset:r}=M(e),{onResetByFieldName:i,changedFieldsByName:o}=D(),m=a||o.get(t),c=e=>{l({...n,...e})},[d,u]=(0,s.useState)(!1);return{value:n,onResetSegmentedBox:()=>{r(),i.get(t)()},hasChanges:m,handleInputIncrement:e=>Number(n[e])+1,handleInputDecrement:e=>Number(n[e])-1,updateDirectionsValues:(e,t,n)=>{e?(u(!1),c({top:n,right:n,bottom:n,left:n})):(u(n),c({[t]:n}))},lastFieldValue:d}})(c,n),[E,f]=(0,s.useState)((()=>{const{left:e,right:t,top:n,bottom:a}=d;return""!==e&&e===t&&t===n&&n===a})),C=e=>{const[t,n]=Object.entries(e)[0];y(E,t,n)},N=e=>()=>{const t=v(e);y(E,e,String(t))},w=e=>()=>{const t=_(e);y(E,e,String(t))};return B(o,i)?(0,a.createElement)("div",{className:Fe},(0,a.createElement)(h,{condition:Boolean(t)},(0,a.createElement)(xe,{label:null!=t?t:"",isChanged:g,onReset:u,unitName:n,popoverContent:l,showDevicePicker:m})),(0,a.createElement)("div",{className:`${Pe} ${E?"active":""}`},(0,a.createElement)("div",null,(0,a.createElement)(p.__experimentalNumberControl,{value:d.top,onChange:e=>{C({top:e})},spinControls:"none",suffix:(0,a.createElement)(He,{onIncrement:N("top"),onDecrement:w("top"),withArrows:!0})}),(0,a.createElement)("div",null,r.__("Top","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(p.__experimentalNumberControl,{value:d.right,onChange:e=>{C({right:e})},spinControls:"none",suffix:(0,a.createElement)(He,{onIncrement:N("right"),onDecrement:w("right"),withArrows:!0})}),(0,a.createElement)("div",null,r.__("Right","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(p.__experimentalNumberControl,{value:d.bottom,onChange:e=>{C({bottom:e})},spinControls:"none",suffix:(0,a.createElement)(He,{onIncrement:N("bottom"),onDecrement:w("bottom"),withArrows:!0})}),(0,a.createElement)("div",null,r.__("Bottom","masterstudy-lms-learning-management-system"))),(0,a.createElement)("div",null,(0,a.createElement)(p.__experimentalNumberControl,{value:d.left,onChange:e=>{C({left:e})},spinControls:"none",suffix:(0,a.createElement)(He,{onIncrement:N("left"),onDecrement:w("left"),withArrows:!0})}),(0,a.createElement)("div",null,r.__("Left","masterstudy-lms-learning-management-system"))),(0,a.createElement)(p.Dashicon,{icon:"dashicons "+(E?"dashicons-admin-links":"dashicons-editor-unlink"),size:16,onClick:()=>{E||!1===b||y(!0,"left",b),f((e=>!e))}}))):null},[ze,Ve,Ie,je]=O(["toggle-group-wrapper","toggle-group","toggle-group__toggle","toggle-group__active-toggle"]),[We,$e,Ge,Ke]=O(["border-control","border-control-solid","border-control-dashed","border-control-dotted"]),Xe=(O("border-radius"),O("border-radius-control"),O("box-shadow-preset"),O("presets")),Ze=O("presets__item-wrapper"),Ye=O("presets__item-wrapper__preset"),qe=O("presets__item-wrapper__name"),Je=((0,s.memo)((e=>{const{presets:t,activePreset:n,onSelectPreset:l,PresetItem:r,detectIsActive:s,detectByIndex:i=!1}=e;return(0,a.createElement)("div",{className:Xe},t.map((({name:e,...t},o)=>(0,a.createElement)("div",{key:o,className:c()([Ze],{active:s(n,i?o:t)}),onClick:()=>l(t)},(0,a.createElement)("div",{className:Ye},(0,a.createElement)(r,{preset:t})),(0,a.createElement)("span",{className:qe},e)))))})),O("range-control")),Qe=e=>{const{name:t,label:n,min:l,max:r,unitName:s,dependencyMode:i,dependencies:o,isAdaptive:m=!1}=e,{fieldName:c}=R(t,m),{value:d,onChange:u,onResetNumberField:g,hasChanges:v}=((e,t)=>{const{value:n,isChanged:a,onChange:l,onReset:r}=M(e),{onResetByFieldName:s,changedFieldsByName:i}=D();return{value:n,onChange:l,onResetNumberField:()=>{r(),s.get(t)()},hasChanges:a||i.get(t)}})(c,s);return B(o,i)?(0,a.createElement)("div",{className:Je},(0,a.createElement)(h,{condition:Boolean(n)},(0,a.createElement)(xe,{label:n,isChanged:v,onReset:g,unitName:s,showDevicePicker:m})),(0,a.createElement)(p.RangeControl,{value:d,onChange:u,min:l,max:r})):null},et=O("switch"),tt=e=>{const{name:t,label:n,dependencyMode:l,dependencies:r,isAdaptive:s=!1}=e,{fieldName:i}=R(t,s),{value:o,onChange:m}=M(i);return B(r,l)?(0,a.createElement)("div",{className:et,"data-has-label":Boolean(n).toString()},(0,a.createElement)(p.ToggleControl,{label:n,checked:o,onChange:m}),(0,a.createElement)(h,{condition:s},(0,a.createElement)(pe,null))):null},nt=(O("box-shadow-settings"),O("box-shadow-presets-title"),O("input-field"),O("input-field-control"),O("number-field"),O("number-field-control"),({className:e})=>(0,a.createElement)("div",{className:e},r.__("No options","masterstudy-lms-learning-management-system"))),at=O("select__single-item"),lt=O("select__container"),rt=O("select__container__multi-item"),st=({multiple:e,value:t,options:n,onChange:l})=>{const{singleValue:r,multipleValue:i}=((e,t,n)=>({singleValue:(0,s.useMemo)((()=>t?null:n.find((t=>t.value===e))?.label),[t,e,n]),multipleValue:(0,s.useMemo)((()=>t?e:null),[t,e])}))(t,e,n);return(0,a.createElement)(h,{condition:e,fallback:(0,a.createElement)("div",{className:at},r)},(0,a.createElement)("div",{className:lt},i?.map((e=>{const t=n.find((t=>t.value===e));return t?(0,a.createElement)("div",{key:t.value,className:rt},(0,a.createElement)("div",null,t.label),(0,a.createElement)(p.Dashicon,{icon:"no-alt",onClick:()=>l(t.value),size:16})):null}))))},it=O("select"),ot=O("select__select-box"),mt=O("select__placeholder"),ct=O("select__select-box-multiple"),dt=O("select__menu"),ut=O("select__menu__options-container"),gt=O("select__menu__item"),pt=e=>{const{options:t,multiple:n=!1,placeholder:l="Select",value:r,onSelect:i}=e,{isOpen:o,onToggle:m,onClose:d}=A(),u=H(d),g=((e,t,n)=>(0,s.useMemo)((()=>n&&Array.isArray(e)?t.filter((t=>!e.includes(t.value))):t.filter((t=>t.value!==e))),[e,t,n]))(r,t,n),v=((e,t,n,a)=>(0,s.useCallback)((l=>{if(t&&Array.isArray(e)){const t=e.includes(l)?e.filter((e=>e!==l)):[...e,l];n(t)}else n(l),a()}),[t,e,n,a]))(r,n,i,d),_=((e,t)=>(0,s.useMemo)((()=>t&&Array.isArray(e)?Boolean(e.length):Boolean(e)),[t,e]))(r,n),y=n&&Array.isArray(r)&&r?.length>0;return(0,a.createElement)("div",{className:it,ref:u},(0,a.createElement)("div",{className:c()([ot],{[ct]:y}),onClick:m},(0,a.createElement)(h,{condition:_,fallback:(0,a.createElement)("div",{className:mt},l)},(0,a.createElement)(st,{onChange:v,options:t,multiple:n,value:r})),(0,a.createElement)(p.Dashicon,{icon:o?"arrow-up-alt2":"arrow-down-alt2",size:16,style:{color:"#4D5E6F"}})),(0,a.createElement)(h,{condition:o},(0,a.createElement)("div",{className:dt},(0,a.createElement)(h,{condition:Boolean(g.length),fallback:(0,a.createElement)(nt,{className:gt})},(0,a.createElement)("div",{className:ut},g.map((e=>(0,a.createElement)("div",{key:e.value,onClick:()=>v(e.value),className:gt},e.label))))))))},ht=O("setting-select"),vt=e=>{const{name:t,options:n,label:l,multiple:r=!1,placeholder:s,isAdaptive:i=!1,dependencyMode:o,dependencies:m}=e,{fieldName:c}=R(t,i),{value:d,isChanged:u,onChange:g,onReset:p}=M(c);return B(m,o)?(0,a.createElement)("div",{className:ht},(0,a.createElement)(h,{condition:Boolean(l)},(0,a.createElement)(xe,{label:l,isChanged:u,onReset:p,showDevicePicker:i})),(0,a.createElement)(pt,{options:n,value:d,onSelect:g,multiple:r,placeholder:s})):null},_t=(O("row-select"),O("row-select__label"),O("row-select__control"),O("typography-select")),yt=O("typography-select-label"),bt=e=>{const{name:t,label:n,options:l,isAdaptive:r=!1}=e,{fieldName:s}=R(t,r),{isChanged:i,onReset:o}=M(s);return(0,a.createElement)("div",{className:_t},(0,a.createElement)("div",{className:yt},(0,a.createElement)("div",null,n),(0,a.createElement)(h,{condition:r},(0,a.createElement)(pe,null))),(0,a.createElement)(vt,{name:t,options:l,isAdaptive:r}),(0,a.createElement)(h,{condition:i},(0,a.createElement)(ve,{onReset:o})))},Et=O("typography"),ft=e=>{const{fontSizeName:t,fontWeightName:n,textTransformName:l,fontStyleName:s,textDecorationName:i,lineHeightName:o,letterSpacingName:m,wordSpacingName:c,fontSizeUnitName:d,lineHeightUnitName:u,letterSpacingUnitName:g,wordSpacingUnitName:p,dependencyMode:h,dependencies:v,isAdaptive:_=!1}=e,{fontWeightOptions:y,textTransformOptions:b,fontStyleOptions:E,textDecorationOptions:f}={fontWeightOptions:[{label:r.__("100 (Thin)","masterstudy-lms-learning-management-system"),value:"100"},{label:r.__("200 (Extra Light)","masterstudy-lms-learning-management-system"),value:"200"},{label:r.__("300 (Light)","masterstudy-lms-learning-management-system"),value:"300"},{label:r.__("400 (Normal)","masterstudy-lms-learning-management-system"),value:"400"},{label:r.__("500 (Medium)","masterstudy-lms-learning-management-system"),value:"500"},{label:r.__("600 (Semi Bold)","masterstudy-lms-learning-management-system"),value:"600"},{label:r.__("700 (Bold)","masterstudy-lms-learning-management-system"),value:"700"},{label:r.__("800 (Extra Bold)","masterstudy-lms-learning-management-system"),value:"800"},{label:r.__("900 (Extra)","masterstudy-lms-learning-management-system"),value:"900"}],textTransformOptions:[{label:r.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:r.__("Uppercase","masterstudy-lms-learning-management-system"),value:"uppercase"},{label:r.__("Lowercase","masterstudy-lms-learning-management-system"),value:"lowercase"},{label:r.__("Capitalize","masterstudy-lms-learning-management-system"),value:"capitalize"},{label:r.__("Normal","masterstudy-lms-learning-management-system"),value:"none"}],fontStyleOptions:[{label:r.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:r.__("Normal","masterstudy-lms-learning-management-system"),value:"none"},{label:r.__("Italic","masterstudy-lms-learning-management-system"),value:"italic"},{label:r.__("Oblique","masterstudy-lms-learning-management-system"),value:"oblique"}],textDecorationOptions:[{label:r.__("Default","masterstudy-lms-learning-management-system"),value:"inherit"},{label:r.__("Underline","masterstudy-lms-learning-management-system"),value:"underline"},{label:r.__("Line Through","masterstudy-lms-learning-management-system"),value:"line-through"},{label:r.__("None","masterstudy-lms-learning-management-system"),value:"none"}]};return B(v,h)?(0,a.createElement)("div",{className:Et},(0,a.createElement)(Qe,{name:t,label:r.__("Size","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:d,isAdaptive:_}),(0,a.createElement)(bt,{name:n,label:r.__("Weight","masterstudy-lms-learning-management-system"),options:y}),(0,a.createElement)(bt,{name:l,label:r.__("Transform","masterstudy-lms-learning-management-system"),options:b}),(0,a.createElement)(bt,{name:s,label:r.__("Style","masterstudy-lms-learning-management-system"),options:E}),(0,a.createElement)(bt,{name:i,label:r.__("Decoration","masterstudy-lms-learning-management-system"),options:f}),(0,a.createElement)(Qe,{name:o,label:r.__("Line Height","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:u,isAdaptive:_}),(0,a.createElement)(Qe,{name:m,label:r.__("Letter Spacing","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:g,isAdaptive:_}),c&&(0,a.createElement)(Qe,{name:c,label:r.__("Word Spacing","masterstudy-lms-learning-management-system"),min:0,max:100,unitName:p,isAdaptive:_})):null},Ct=(O("file-upload"),O("file-upload__wrap"),O("file-upload__image"),O("file-upload__remove"),O("file-upload__replace"),(0,s.createContext)({activeTab:0,setActiveTab:()=>{}})),Nt=()=>{const e=(0,s.useContext)(Ct);if(!e)throw new Error("useTabs should be used inside Tabs");return e},wt=({children:e})=>{const[t,n]=(0,s.useState)(0);return(0,a.createElement)(Ct.Provider,{value:{activeTab:t,setActiveTab:n}},(0,a.createElement)("div",{className:`active-tab-${t}`},e))},St=O("tab-list"),Tt=({children:e})=>(0,a.createElement)("div",{className:St},s.Children.map(e,((e,t)=>(0,s.cloneElement)(e,{index:t})))),xt=O("tab"),Ot=O("tab-active"),kt=O("content"),Lt=({index:e,title:t,icon:n})=>{const{activeTab:l,setActiveTab:r}=Nt();return(0,a.createElement)("div",{className:c()([xt],{[Ot]:l===e}),onClick:()=>r(e)},(0,a.createElement)("div",{className:kt},(0,a.createElement)("div",null,n),(0,a.createElement)("div",null,t)))},At=({children:e})=>(0,a.createElement)("div",null,s.Children.map(e,((e,t)=>(0,s.cloneElement)(e,{index:t})))),Dt=O("tab-panel"),Mt=({index:e,children:t})=>{const{activeTab:n}=Nt();return n===e?(0,a.createElement)("div",{className:Dt},t):null},Rt=({generalTab:e,styleTab:t,advancedTab:n})=>(0,a.createElement)(wt,null,(0,a.createElement)(Tt,null,(0,a.createElement)(Lt,{title:r.__("General","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(p.Dashicon,{icon:"layout"})}),(0,a.createElement)(Lt,{title:r.__("Style","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(p.Dashicon,{icon:"admin-appearance"})}),(0,a.createElement)(Lt,{title:r.__("Advanced","masterstudy-lms-learning-management-system"),icon:(0,a.createElement)(p.Dashicon,{icon:"admin-settings"})})),(0,a.createElement)(At,null,(0,a.createElement)(Mt,null,e),(0,a.createElement)(Mt,null,t),(0,a.createElement)(Mt,null,n)));window.ReactDOM;"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement;function Bt(e){const t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function Ht(e){return"nodeType"in e}function Ft(e){var t,n;return e?Bt(e)?e:Ht(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function Pt(e){const{Document:t}=Ft(e);return e instanceof t}function Ut(e){return!Bt(e)&&e instanceof Ft(e).HTMLElement}function zt(e){return e instanceof Ft(e).SVGElement}function Vt(e){return e?Bt(e)?e.document:Ht(e)?Pt(e)?e:Ut(e)||zt(e)?e.ownerDocument:document:document:document}function It(e){return function(t){for(var n=arguments.length,a=new Array(n>1?n-1:0),l=1;l<n;l++)a[l-1]=arguments[l];return a.reduce(((t,n)=>{const a=Object.entries(n);for(const[n,l]of a){const a=t[n];null!=a&&(t[n]=a+e*l)}return t}),{...t})}}const jt=It(-1);function Wt(e){if(function(e){if(!e)return!1;const{TouchEvent:t}=Ft(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return function(e){return"clientX"in e&&"clientY"in e}(e)?{x:e.clientX,y:e.clientY}:null}var $t;!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}($t||($t={}));const Gt=Object.freeze({x:0,y:0});var Kt,Xt,Zt,Yt;!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(Kt||(Kt={}));class qt{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach((e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)}))},this.target=e}add(e,t,n){var a;null==(a=this.target)||a.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function Jt(e,t){const n=Math.abs(e.x),a=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+a**2)>t:"x"in t&&"y"in t?n>t.x&&a>t.y:"x"in t?n>t.x:"y"in t&&a>t.y}function Qt(e){e.preventDefault()}function en(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(Xt||(Xt={})),(Yt=Zt||(Zt={})).Space="Space",Yt.Down="ArrowDown",Yt.Right="ArrowRight",Yt.Left="ArrowLeft",Yt.Up="ArrowUp",Yt.Esc="Escape",Yt.Enter="Enter";Zt.Space,Zt.Enter,Zt.Esc,Zt.Space,Zt.Enter;function tn(e){return Boolean(e&&"distance"in e)}function nn(e){return Boolean(e&&"delay"in e)}class an{constructor(e,t,n){var a;void 0===n&&(n=function(e){const{EventTarget:t}=Ft(e);return e instanceof t?e:Vt(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;const{event:l}=e,{target:r}=l;this.props=e,this.events=t,this.document=Vt(r),this.documentListeners=new qt(this.document),this.listeners=new qt(n),this.windowListeners=new qt(Ft(r)),this.initialCoordinates=null!=(a=Wt(l))?a:Gt,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),this.windowListeners.add(Xt.Resize,this.handleCancel),this.windowListeners.add(Xt.DragStart,Qt),this.windowListeners.add(Xt.VisibilityChange,this.handleCancel),this.windowListeners.add(Xt.ContextMenu,Qt),this.documentListeners.add(Xt.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(nn(t))return void(this.timeoutId=setTimeout(this.handleStart,t.delay));if(tn(t))return}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handleStart(){const{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(Xt.Click,en,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(Xt.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;const{activated:n,initialCoordinates:a,props:l}=this,{onMove:r,options:{activationConstraint:s}}=l;if(!a)return;const i=null!=(t=Wt(e))?t:Gt,o=jt(a,i);if(!n&&s){if(tn(s)){if(null!=s.tolerance&&Jt(o,s.tolerance))return this.handleCancel();if(Jt(o,s.distance))return this.handleStart()}return nn(s)&&Jt(o,s.tolerance)?this.handleCancel():void 0}e.cancelable&&e.preventDefault(),r(i)}handleEnd(){const{onEnd:e}=this.props;this.detach(),e()}handleCancel(){const{onCancel:e}=this.props;this.detach(),e()}handleKeydown(e){e.code===Zt.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}const ln={move:{name:"pointermove"},end:{name:"pointerup"}};(class extends an{constructor(e){const{event:t}=e,n=Vt(t.target);super(e,ln,n)}}).activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;return!(!n.isPrimary||0!==n.button||(null==a||a({event:n}),0))}}];const rn={move:{name:"mousemove"},end:{name:"mouseup"}};var sn;!function(e){e[e.RightClick=2]="RightClick"}(sn||(sn={})),class extends an{constructor(e){super(e,rn,Vt(e.event.target))}}.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;return n.button!==sn.RightClick&&(null==a||a({event:n}),!0)}}];const on={move:{name:"touchmove"},end:{name:"touchend"}};var mn,cn,dn,un,gn;(class extends an{constructor(e){super(e,on)}static setup(){return window.addEventListener(on.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(on.move.name,e)};function e(){}}}).activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:a}=t;const{touches:l}=n;return!(l.length>1||(null==a||a({event:n}),0))}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(mn||(mn={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(cn||(cn={})),Kt.Backward,Kt.Forward,Kt.Backward,Kt.Forward,function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(dn||(dn={})),function(e){e.Optimized="optimized"}(un||(un={})),dn.WhileDragging,un.Optimized,Map,function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(gn||(gn={})),Zt.Down,Zt.Right,Zt.Up,Zt.Left,r.__("Lectures","masterstudy-lms-learning-management-system"),r.__("Duration","masterstudy-lms-learning-management-system"),r.__("Views","masterstudy-lms-learning-management-system"),r.__("Level","masterstudy-lms-learning-management-system"),r.__("Members","masterstudy-lms-learning-management-system"),r.__("Empty","masterstudy-lms-learning-management-system"),O("sortable__item"),O("sortable__item__disabled"),O("sortable__item__content"),O("sortable__item__content__drag-item"),O("sortable__item__content__drag-item__disabled"),O("sortable__item__content__title"),O("sortable__item__control"),O("sortable__item__icon"),O("nested-sortable"),O("nested-sortable__item"),O("sortable");const pn=O("accordion"),hn=O("accordion__header"),vn=O("accordion__header-flex"),yn=O("accordion__content"),bn=O("accordion__icon"),En=O("accordion__title"),fn=O("accordion__title-disabled"),Cn=O("accordion__indicator"),Nn=O("accordion__controls"),wn=O("accordion__controls-disabled"),Sn=({title:e,children:t,accordionFields:n,switchName:l,visible:r=!0,isDefaultOpen:i=!1})=>{const{isOpen:o,onToggle:m,disabled:d,onReset:u,hasChanges:g,onClose:_}=((e,t,n)=>{var a;const{isOpen:l,onToggle:r,onClose:s}=A(t),{defaultValues:i,attributes:o,setAttributes:m}=D(),c=((e,t,n)=>{for(const a of n)if(!v(e[a],t[a]))return!0;return!1})(i,o,e);return{isOpen:l,onToggle:r,disabled:!(null===(a=o[n])||void 0===a||a),hasChanges:c,onReset:t=>{t.stopPropagation(),m(e.reduce(((e,t)=>(e[t]=i[t],e)),{}))},onClose:s}})(n,i,l);return((e,t)=>{const{attributes:n}=D(),a=!n[t];(0,s.useEffect)((()=>{a&&e()}),[a,e])})(_,l),r?(0,a.createElement)("div",{className:pn},(0,a.createElement)("div",{className:hn},(0,a.createElement)("div",{className:vn,onClick:d?null:m},(0,a.createElement)("div",{className:c()(En,{[fn]:d,"with-switch":Boolean(l)})},(0,a.createElement)("div",null,e),(0,a.createElement)(h,{condition:g&&!d},(0,a.createElement)("div",{className:Cn}))),(0,a.createElement)("div",{className:c()(Nn,{[wn]:d})},(0,a.createElement)(p.Dashicon,{icon:o?"arrow-up-alt2":"arrow-down-alt2",className:bn,size:16}))),(0,a.createElement)(h,{condition:Boolean(l)},(0,a.createElement)(tt,{name:l})),(0,a.createElement)(h,{condition:g&&!d},(0,a.createElement)(ve,{onReset:u}))),o&&(0,a.createElement)("div",{className:yn},t)):null};O("preset-picker"),O("preset-picker__label"),O("preset-picker__remove"),O("preset-picker__presets-list"),O("preset-picker__presets-list__item"),O("preset-picker__presets-list__item__preset"),O("preset-picker__presets-list__item__preset-active");const Tn={hideDefault:!1},xn=Object.keys(Tn),On={padding:x,paddingTablet:x,paddingMobile:x,paddingUnit:"px",paddingUnitTablet:"px",paddingUnitMobile:"px",background:""},kn=Object.keys(On),Ln={titleColor:"",titleFontSize:16,titleFontSizeUnit:"px",titleFontWeight:"700",titleTextTransform:"",titleFontStyle:"",titleTextDecoration:"",titleLineHeight:24,titleLineHeightUnit:"px",titleLetterSpacing:null,titleLetterSpacingUnit:"px",titleWordSpacing:null,titleWordSpacingUnit:"px",togglerColor:""},An=Object.keys(Ln),Dn={itemColor:"",itemFontSize:14,itemFontSizeUnit:"px",itemFontWeight:"500",itemTextTransform:"",itemFontStyle:"",itemTextDecoration:"",itemLineHeight:16,itemLineHeightUnit:"px",itemLetterSpacing:null,itemLetterSpacingUnit:"px",itemWordSpacing:null,itemWordSpacingUnit:"px",starColor:"",starColorActive:"",inputColor:"",inputColorActive:"",inputBgColor:"",inputBgColorActive:"",inputBorderColor:"",inputBorderColorActive:""},Mn=Object.keys(Dn),Rn={...{...Tn},...{...On,...Ln,...Dn}},Bn=new Map([["padding",{unit:"paddingUnit",isAdaptive:!0}],["background",{}],["titleColor",{}],["titleFontSize",{unit:"titleFontSizeUnit"}],["titleFontWeight",{}],["titleTextTransform",{}],["titleFontStyle",{}],["titleTextDecoration",{}],["titleLineHeight",{unit:"titleLineHeightUnit"}],["titleLetterSpacing",{unit:"titleLetterSpacingUnit"}],["titleWordSpacing",{unit:"titleWordSpacingUnit"}],["togglerColor",{}],["itemColor",{}],["itemFontSize",{unit:"itemFontSizeUnit"}],["itemFontWeight",{}],["itemTextTransform",{}],["itemFontStyle",{}],["itemTextDecoration",{}],["itemLineHeight",{unit:"itemLineHeightUnit"}],["itemLetterSpacing",{unit:"itemLetterSpacingUnit"}],["itemWordSpacing",{unit:"itemWordSpacingUnit"}],["starColor",{}],["starColorActive",{}],["inputColor",{}],["inputColorActive",{}],["inputBgColor",{}],["inputBgColorActive",{}],["inputBorderColor",{}],["inputBorderColorActive",{}]]),Hn=()=>(0,a.createElement)(a.Fragment,null,(0,a.createElement)(Sn,{title:r.__("Layout","masterstudy-lms-learning-management-system"),accordionFields:xn},(0,a.createElement)(tt,{name:"hideDefault",label:r.__("Hide default","masterstudy-lms-learning-management-system")}))),Fn=()=>(0,a.createElement)(a.Fragment,null,(0,a.createElement)(Sn,{title:r.__("Layout","masterstudy-lms-learning-management-system"),accordionFields:kn},(0,a.createElement)(Ue,{name:"padding",label:r.__("Padding","masterstudy-lms-learning-management-system"),unitName:"paddingUnit",isAdaptive:!0}),(0,a.createElement)(Ae,{name:"background",label:r.__("Background","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")})),(0,a.createElement)(Sn,{title:r.__("Header","masterstudy-lms-learning-management-system"),accordionFields:An},(0,a.createElement)(Ae,{name:"titleColor",label:r.__("Title color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(ft,{fontSizeName:"titleFontSize",fontSizeUnitName:"titleFontSizeUnit",fontWeightName:"titleFontWeight",textTransformName:"titleTextTransform",fontStyleName:"titleFontStyle",textDecorationName:"titleTextDecoration",lineHeightName:"titleLineHeight",lineHeightUnitName:"titleLineHeightUnit",letterSpacingName:"titleLetterSpacing",letterSpacingUnitName:"titleLetterSpacingUnit",wordSpacingName:"titleWordSpacing",wordSpacingUnitName:"titleWordSpacingUnit"}),(0,a.createElement)(Ae,{name:"togglerColor",label:r.__("Toggler color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")})),(0,a.createElement)(Sn,{title:r.__("Items","masterstudy-lms-learning-management-system"),accordionFields:Mn},(0,a.createElement)(Ae,{name:"itemColor",label:r.__("Text color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Ae,{name:"starColor",label:r.__("Star color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Ae,{name:"starColorActive",label:r.__("Star active color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(ft,{fontSizeName:"itemFontSize",fontSizeUnitName:"itemFontSizeUnit",fontWeightName:"itemFontWeight",textTransformName:"itemTextTransform",fontStyleName:"itemFontStyle",textDecorationName:"itemTextDecoration",lineHeightName:"itemLineHeight",lineHeightUnitName:"itemLineHeightUnit",letterSpacingName:"itemLetterSpacing",letterSpacingUnitName:"itemLetterSpacingUnit",wordSpacingName:"itemWordSpacing",wordSpacingUnitName:"itemWordSpacingUnit"}),(0,a.createElement)(Ae,{name:"inputColor",label:r.__("Radio button color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Ae,{name:"inputColorActive",label:r.__("Radio button selected color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Ae,{name:"inputBgColor",label:r.__("Radio button background color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Ae,{name:"inputBgColorActive",label:r.__("Radio button selected background color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Ae,{name:"inputBorderColor",label:r.__("Radio button border color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}),(0,a.createElement)(Ae,{name:"inputBorderColorActive",label:r.__("Radio button selected border color","masterstudy-lms-learning-management-system"),placeholder:r.__("Select color","masterstudy-lms-learning-management-system")}))),Pn=({attributes:e,setAttributes:t})=>{const{onResetByFieldName:n,changedFieldsByName:l}=((e,t,n,a=[])=>{const l=(e=>{const t={};return Object.entries(e).forEach((([e,n])=>{e.includes("UAG")||(t[e]=n)})),t})(t),r=!v(e,l),s=((e,t,n)=>{const a=new Map;return n.forEach((n=>{a.has(n)||a.set(n,(()=>t({[n]:e[n]})))})),a})(e,n,a),i=((e,t,n)=>{const a=new Map;return n.forEach((n=>{a.has(n)?a.set(n,!1):a.set(n,!v(e[n],t[n]))})),a})(e,l,a);return{hasChanges:r,onResetByFieldName:s,changedFieldsByName:i}})(Rn,e,t,Object.keys(Rn));return(0,a.createElement)(d.InspectorControls,null,(0,a.createElement)(g,{attributes:e,setAttributes:t,defaultValues:Rn,onResetByFieldName:n,changedFieldsByName:l},(0,a.createElement)(Rt,{generalTab:(0,a.createElement)(Hn,null),styleTab:(0,a.createElement)(Fn,null),advancedTab:(0,a.createElement)(a.Fragment,null)})))},Un=[{value:"4.5",label:r.__("4.5 & up","masterstudy-lms-learning-management-system"),width:"89%"},{value:"4",label:r.__("4 & up","masterstudy-lms-learning-management-system"),width:"80%"},{value:"3.5",label:r.__("3.5 & up","masterstudy-lms-learning-management-system"),width:"69%"},{value:"3",label:r.__("3 & up","masterstudy-lms-learning-management-system"),width:"60%"}],zn=JSON.parse('{"UU":"masterstudy/courses-filter-rating"}');(0,l.registerBlockType)(zn.UU,{edit:({attributes:e,setAttributes:t})=>{(0,s.useEffect)((()=>{(async()=>{try{const{rating_visibility:e}=await(async(e=!1)=>{try{let t="?children=true";return e&&(t+="&details=true"),await o()({path:`masterstudy-lms/v2/course-categories${t}`})}catch(e){throw new Error(e)}})(!0);t({rating_visibility:e})}catch(e){console.error(e)}})()}),[t]);const n=(0,d.useBlockProps)({className:c()("archive-courses-filter-rating","archive-courses-filter-item",{"hide-filter":e.hideDefault}),style:S("filter-item",e,Bn)});return e.rating_visibility?(0,a.createElement)(a.Fragment,null,(0,a.createElement)(Pn,{attributes:e,setAttributes:t}),(0,a.createElement)("div",{...n},(0,a.createElement)("div",{className:"lms-courses-filter-option-title"},r.__("Rating","masterstudy-lms-learning-management-system"),(0,a.createElement)("div",{className:"lms-courses-filter-option-switcher"})),(0,a.createElement)("div",{className:"lms-courses-filter-option-collapse"},(0,a.createElement)("ul",{className:"lms-courses-filter-option-list"},Un.map((e=>(0,a.createElement)("li",{key:e.value,className:"lms-courses-filter-option-item"},(0,a.createElement)("label",{className:"lms-courses-filter-radio"},(0,a.createElement)("input",{type:"radio",value:e.value,name:"rating"}),(0,a.createElement)("span",{className:"lms-courses-filter-radio-label"},(0,a.createElement)("span",{className:"lms-courses-filter-option-rating-stars"},(0,a.createElement)("span",{className:"lms-courses-filter-option-rating-stars-filled",style:{width:e.width}})),e.label))))))))):null},save:({attributes:e})=>{const t=d.useBlockProps.save({className:c()("archive-courses-filter-rating","archive-courses-filter-item",{"hide-filter":e.hideDefault}),style:S("filter-item",e,Bn)});return e.rating_visibility?(0,a.createElement)("div",{...t},(0,a.createElement)("div",{className:"lms-courses-filter-option-title"},r.__("Rating","masterstudy-lms-learning-management-system"),(0,a.createElement)("div",{className:"lms-courses-filter-option-switcher"})),(0,a.createElement)("div",{className:"lms-courses-filter-option-collapse"},(0,a.createElement)("ul",{className:"lms-courses-filter-option-list"},Un.map((e=>(0,a.createElement)("li",{key:e.value,className:"lms-courses-filter-option-item"},(0,a.createElement)("label",{className:"lms-courses-filter-radio"},(0,a.createElement)("input",{type:"radio",value:e.value,name:"rating"}),(0,a.createElement)("span",{className:"lms-courses-filter-radio-label"},(0,a.createElement)("span",{className:"lms-courses-filter-option-rating-stars"},(0,a.createElement)("span",{className:"lms-courses-filter-option-rating-stars-filled",style:{width:e.width}})),e.label)))))))):null},icon:(0,a.createElement)("svg",{width:"512",height:"512",viewBox:"0 0 512 512",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,a.createElement)("path",{opacity:"0.3",d:"M371.839 239.153C372.305 235.985 370.113 233.039 366.944 232.574L293.872 221.924L261.195 155.518C260.676 154.635 259.94 153.899 259.057 153.38C256.188 151.692 252.493 152.649 250.805 155.518L218.129 221.924L145.057 232.574C143.802 232.758 142.641 233.349 141.754 234.255C139.514 236.544 139.554 240.215 141.842 242.455L194.722 294.158L182.238 367.174C182.026 368.428 182.231 369.717 182.824 370.842C184.315 373.677 187.823 374.766 190.659 373.275L256 338.821L321.342 373.275C322.177 373.711 323.105 373.94 324.047 373.942C324.37 373.942 324.691 373.915 325.009 373.862C328.168 373.328 330.297 370.333 329.763 367.174L317.279 294.158L370.159 242.455C371.065 241.568 371.655 240.408 371.839 239.153Z",fill:"#227AFF"}),(0,a.createElement)("path",{d:"M469.219 211.901C470.076 206.069 466.04 200.647 460.208 199.792L325.708 180.187L265.563 57.9583C264.607 56.3333 263.251 54.9778 261.626 54.0221C256.345 50.9153 249.544 52.677 246.438 57.9583L186.292 180.187L51.7917 199.792C49.4818 200.13 47.3464 201.217 45.7136 202.885C41.5899 207.098 41.6628 213.855 45.875 217.979L143.208 313.146L120.229 447.542C119.839 449.85 120.218 452.223 121.309 454.294C124.053 459.512 130.51 461.517 135.729 458.771L256 395.354L376.271 458.771C377.807 459.574 379.516 459.996 381.25 460C381.844 460 382.435 459.95 383.021 459.852C388.836 458.868 392.754 453.357 391.771 447.542L368.792 313.146L466.125 217.979C467.793 216.346 468.88 214.211 469.219 211.901ZM349.875 301.811C347.375 304.26 346.231 307.777 346.813 311.228L367.083 429.811L260.979 373.854C259.443 373.051 257.734 372.629 256 372.625C254.266 372.629 252.559 373.051 251.021 373.855L144.918 429.812L165.188 311.229C165.77 307.779 164.625 304.262 162.125 301.812L76.2084 217.792L194.917 200.5C198.389 199.984 201.388 197.796 202.938 194.646L256 86.8333L309.063 194.646C310.612 197.797 313.611 199.986 317.083 200.5L435.792 217.792L349.875 301.811Z",fill:"black"}))})},6942:(e,t)=>{var n;!function(){"use strict";var a={}.hasOwnProperty;function l(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=s(e,r(n)))}return e}function r(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return l.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)a.call(e,n)&&e[n]&&(t=s(t,n));return t}function s(e,t){return t?e?e+" "+t:e+t:e}e.exports?(l.default=l,e.exports=l):void 0===(n=function(){return l}.apply(t,[]))||(e.exports=n)}()}},n={};function a(e){var l=n[e];if(void 0!==l)return l.exports;var r=n[e]={exports:{}};return t[e](r,r.exports,a),r.exports}a.m=t,e=[],a.O=(t,n,l,r)=>{if(!n){var s=1/0;for(c=0;c<e.length;c++){for(var[n,l,r]=e[c],i=!0,o=0;o<n.length;o++)(!1&r||s>=r)&&Object.keys(a.O).every((e=>a.O[e](n[o])))?n.splice(o--,1):(i=!1,r<s&&(s=r));if(i){e.splice(c--,1);var m=l();void 0!==m&&(t=m)}}return t}r=r||0;for(var c=e.length;c>0&&e[c-1][2]>r;c--)e[c]=e[c-1];e[c]=[n,l,r]},a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={3397:0,3801:0};a.O.j=t=>0===e[t];var t=(t,n)=>{var l,r,[s,i,o]=n,m=0;if(s.some((t=>0!==e[t]))){for(l in i)a.o(i,l)&&(a.m[l]=i[l]);if(o)var c=o(a)}for(t&&t(n);m<s.length;m++)r=s[m],a.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return a.O(c)},n=globalThis.webpackChunkmasterstudy_lms_learning_management_system=globalThis.webpackChunkmasterstudy_lms_learning_management_system||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})();var l=a.O(void 0,[3801],(()=>a(821)));l=a.O(l)})();