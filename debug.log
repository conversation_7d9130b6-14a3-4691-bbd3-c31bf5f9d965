[PLUGIN SIMPLIFIED] Ready for testing
[2025-06-04 08:30:32] Plugin file loaded
[2025-06-04 08:30:32] Testing debug log writing
[2025-06-04 08:30:32] ABSPATH check passed: Plugin properly loaded through WordPress
[2025-06-04 08:30:32] About to load debug utilities
[2025-06-04 08:30:32] Debug utilities loaded successfully
[2025-06-04 08:30:32] Registering activation/deactivation hooks
[2025-06-04 08:30:32] Activation/deactivation hooks registered successfully
[2025-06-04 08:30:32] ACTIVATION: Starting activation process
[2025-06-04 08:30:32] ACTIVATION: Checking for Activator.php file at: C:\xampp\htdocs\paylearn\wp-content\plugins\custom-linking-plugin/database/Activator.php
[2025-06-04 08:30:32] ACTIVATION: Activator.php file found, requiring it
[2025-06-04 08:30:32] ACTIVATION: Checking if Custom_Linking_Activator class exists
[2025-06-04 08:30:32] ACTIVATION: Custom_Linking_Activator class found, calling activate()
[2025-06-04 08:30:32] Activator: Beginning table creation process for: wp_linking_table
[2025-06-04 08:30:32] Activator: Table creation result: Array
(
    [wp_linking_table] => Created table wp_linking_table
)

[2025-06-04 08:30:32] Activator: Activation completed successfully.
[2025-06-04 08:30:32] ACTIVATION: Activation process completed with result: success
[2025-06-04 08:30:33] Plugin file loaded
[2025-06-04 08:30:33] Testing debug log writing
[2025-06-04 08:30:33] ABSPATH check passed: Plugin properly loaded through WordPress
[2025-06-04 08:30:33] About to load debug utilities
[2025-06-04 08:30:33] Debug utilities loaded successfully
[2025-06-04 08:30:33] Registering activation/deactivation hooks
[2025-06-04 08:30:33] Activation/deactivation hooks registered successfully
